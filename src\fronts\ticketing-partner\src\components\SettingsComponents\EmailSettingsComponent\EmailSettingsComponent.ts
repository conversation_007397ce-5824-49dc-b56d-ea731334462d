import {Component, Vue} from "vue-facing-decorator";
import {
    LayoutContentWithRightPanelComponent,
} from "@groupk/vue3-interface-sdk";
import {EstablishmentData} from "../../../../../../shared/mastodonCoreFront/EstablishmentData";
import {AutoWired} from "@groupk/horizon2-core";
import {EstablishmentRepository} from "../../../../../../shared/repositories/EstablishmentRepository";
import {AppState} from "../../../../../../shared/AppState";
import {translateResponseError} from "../../../../../../shared/RepositoryExtensions";
import {MastodonEstablishmentContractAggregate} from "@groupk/mastodon-core";
import {AppBus} from "../../../AppBus";

@Component({
    components: {
        'layout': LayoutContentWithRightPanelComponent
    }
})
export default class EmailSettingsComponent extends Vue {
    establishment: EstablishmentData = new EstablishmentData();

    error: string|null = null;
    opened: boolean = false;

    @AutoWired(EstablishmentRepository) private accessor establishmentRepository!: EstablishmentRepository;
    @AutoWired(AppState) private accessor appState!: AppState;
    @AutoWired(AppBus) private accessor appBus!: AppBus;

    mounted() {
        this.establishment.name = this.appState.requireEstablishment().name;
        this.establishment.publicEmail = this.appState.requireEstablishment().publicEmail;
        this.establishment.mainImageUploadUid = this.appState.requireEstablishment().mainImageUploadUid;
    }

    async save() {
        const response = await this.establishmentRepository.callContract('update', {establishmentUid: this.appState.requireUrlEstablishmentUid()}, this.establishment.toUpdateApi());

        if(response.isSuccess()) {
            this.appBus.emit('emit-toast', {
                title: 'Sauvergarde réussie',
                description: 'L\'email à bien été mis à jour',
                duration: 3000,
                type: 'SUCCESS',
                closable: true
            });
        } else {
            this.error = translateResponseError<typeof MastodonEstablishmentContractAggregate, 'update'>(response, {
                invalid_data: undefined,
                establishment_ownTooMany: undefined,
                unknown_reseller: undefined,
                upload_invalid: undefined
            });
        }
    }
}