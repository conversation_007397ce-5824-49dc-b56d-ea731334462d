<script lang="ts" src="./account.ts">
</script>

<style lang="sass">
@import './account.scss'
</style>

<template>
    <div id="account-page">
        <div class="back-button grey button" @click="router.previous()">
            <i class="fa-regular fa-arrow-left"></i>
            Retour
        </div>

        <div class="form">
            <div class="input" v-if="specificState.customer">
                <label> Email </label>
                <input disabled type="text" :value="specificState.customer.email" />
            </div>

<!--            <div class="refund">-->
<!--                <div class="icon">-->
<!--                    <i class="fa-regular fa-clock"></i>-->
<!--                </div>-->
<!--                Remboursement en cours de traitement-->
<!--            </div>-->

            <div class="form-error green" v-if="createdRefund">
                <i class="fa-regular fa-circle-check"></i>
                <div class="details">
                    <span class="title">
                        Demande envoyée !
                    </span>

                    Votre demande de remboursement a bien été prise en compte.
                </div>
            </div>

            <div class="success" v-if="!areRefundsAvailable">
                <div class="form-error">
                    <i class="fa-solid fa-circle-xmark"></i>
                    <div class="details">
                        <span class="title"> Les remboursements ne sont pas actifs </span>
                        <span class="description"> L'établissement n'autorise pas les remboursements pour le moment </span>
                    </div>
                </div>
            </div>
            <button v-else class="add-button button" @click="showRefundModal = true">
                Demander un remboursement
            </button>
        </div>

        <form-modal-or-drawer
            :state="showRefundModal"
            title="Demande de remboursement"
            subtitle="Vous pouvez demander le remboursement des fonds restants sur vos supports Cashless en renseignant l'IBAN sur lequel vous souhaitez être remboursé"
            @close="showRefundModal = false"
        >
            <template v-slot:content>
                <div class="input-group">
                    <label> Puce </label>
                    <div class="input">
                        <dropdown
                            placeholder="Puce à rembourser"
                            :values="[{
                                name: $filters.Chip(specificState.currentWallet?.chipVisualId ?? ''),
                                value: specificState.currentWallet?.chipVisualId
                            }]"
                            :default-selected="data.chipPublicId"
                            @update="data.chipPublicId = $event"
                        ></dropdown>
                    </div>
                </div>

                <div class="input-group" v-if="!specificState.customer?.firstname">
                    <label>
                        Prénom
                        <span class="characters"> {{ data.firstname.length }}/{{ PublicRefundApiInDefinition.getFieldString('firstname').maxLength }} </span>
                    </label>
                    <input type="text" v-model="data.firstname" placeholder="Prénom" :maxlength="PublicRefundApiInDefinition.getFieldString('firstname').maxLength ?? '999'" />
                </div>

                <div class="input-group" v-if="!specificState.customer?.lastname">
                    <label>
                        Nom
                        <span class="characters"> {{ data.lastname.length }}/{{ PublicRefundApiInDefinition.getFieldString('lastname').maxLength }} </span>
                    </label>
                    <input type="text" v-model="data.lastname" placeholder="Nom" :maxlength="PublicRefundApiInDefinition.getFieldString('lastname').maxLength ?? '999'" />
                </div>

                <div class="input-group" v-if="!specificState.customer?.email">
                    <label> Email </label>
                    <input type="text" v-model="data.email" placeholder="Email de contact" />
                </div>

                <div class="input-group">
                    <label> Numéro de tél. </label>
                    <input type="text" placeholder="Numéro de tél. de contact" v-cleave="{obj: data, key: 'phone', options: cleavePhone}" />
                </div>

                <div class="input">
                    <label> IBAN </label>
                    <input type="text" placeholder="FRXX-XXXX-XXXX-XXXX-XXX" v-cleave="{obj: data, key: 'iban', options: cleaveIban}" />
                </div>

                <div class="input-group">
                    <label> BIC </label>
                    <input type="text" placeholder="BIC de votre banque" v-model="data.bic" />
                </div>

                <div class="form-error" v-if="error">
                    <i class="fa-solid fa-exclamation-circle"></i>
                    <div class="details">
                        <span class="title"> Erreur </span>
                        <span class="description">{{ error }}</span>
                    </div>
                </div>
            </template>

            <template v-slot:buttons>
                <button class="button" @click="sendRefundRequest()">
                    Valider ma demande
                </button>
            </template>
        </form-modal-or-drawer>

    </div>
</template>