
:root {
    --primary-hover-color: #F0FAF4 !important;
    --primary-color: #04724D !important;
    --primary-button-hover-color: #026443 !important;
    --primary-hover-text-color: black !important;
    --primary-text-color: white !important;
}


html, body {
    height: 100%;
    margin: 0;
    padding: 0;
    overflow: hidden;
}

*:not(i, text) {
    font-family: 'Montserrat', sans-serif !important;
}

.page {
    overflow: hidden;
    height: 100%;
    width: 100%;
    box-sizing: border-box;
}

#mainRouterContainer {
    width: 100%;
    height: 100%;
}

// Computer
@media (min-width: 900px) {
    body, #mainRouterContainer {
        display: flex;
    }
}

// Mobile & Tablet
@media (max-width: 900px) {
    #mainRouterContainer .page {
        padding-top: 60px;
    }
}

h4{
    margin: 0;
}

.date-restrictions {
    display: flex;
    flex-direction: column;
    gap: 20px;
    padding: 20px;
    border-radius: 8px;
    background: #EBEBEB;
    margin-top: 15px;

    .top {
        display: flex;
        align-items: center;
        gap: 20px;

        .header {
            flex-grow: 2;

            .title {
                font-size: 15px;
                font-weight: 500;
            }

            .subtitle {
                font-size: 14px;
            }
        }
    }
}

.dates-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px;
}