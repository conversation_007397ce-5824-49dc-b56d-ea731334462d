<script lang="ts" src="./wallets.ts">
</script>

<style lang="sass">
@use './wallets.scss' as *
</style>

<template>
    <div id="wallets-page" class="page">
        <forbidden-message v-if="forbidden"></forbidden-message>
        <filter-table-layout
            v-else
            :header-parameters="headerParameters"
            :allowed-filters="allowedFilters"
            :table-columns="tableColumns"
            :filters="filters"
            :pagination="pagination"
            :drawer-opened="selectedWallet !== null"
            :filter-parameters="filterParameters"
            @search="search($event)"
            @sorted="sorted($event)"
            @changed-column-preferences="saveColumnPreferences($event)"
            @filters-changed="searchWallets($event);"
        >
            <template v-slot:table-data>
                <tr class="table-dimmer" :class="{relative: wallets.length === 0}" v-if="loading">
                    <td colspan="100%">
                        <div class="dimmer">
                            <div class="loader-container">
                                <div class="loader"></div>
                            </div>
                        </div>
                    </td>
                </tr>
                <tr class="table-no-data" v-else-if="wallets.length === 0">
                    <td colspan="100%">Aucune donnée</td>
                </tr>
                <tr v-else v-for="wallet in wallets" :class="{selected: selectedWallet && wallet.chipId === selectedWallet.chipId}" @click="toggleWallet(wallet)">
                    <td :class="{'mobile-hidden': column.mobileHidden}" v-for="column in tableColumns.filter((column) => column.displayed)">
                        <template v-if="column.name === 'chipId'"> {{ wallet.chipId }} </template>
                        <template v-if="column.name === 'publicId'"> {{ $filters.Chip(wallet.chipVisualId) }} </template>
                        <template v-if="column.name === 'currency'"> {{ getCorrespondingCurrency(wallet.currency).name }} ({{ getCorrespondingCurrency(wallet.currency).symbol }}) </template>
                        <template v-if="column.name === 'balance'"> {{ $filters.Money(wallet.balance) }} </template>
                        <template v-if="column.name === 'kycLevel'"> {{ wallet.kycLevel }} </template>
                        <template v-if="column.name === 'status'">
                            <span class="green label" v-if="!wallet.inactiveReason && wallet.active">
                                Active
                            </span>
                            <span class="red label" v-else>
                                <template v-if="wallet.inactiveReason === WalletInactiveReason.LOST"> Perdue </template>
                                <template v-else-if="wallet.inactiveReason === WalletInactiveReason.BROKEN"> Cassée </template>
                                <template v-else-if="wallet.inactiveReason === WalletInactiveReason.STOLEN"> Volée </template>
                                <template v-else> Désactivée </template>
                            </span>
                        </template>
                        <template v-if="column.name === 'creationDatetime'"> {{ $filters.Date(wallet.creationDatetimeOnServer) }} </template>
                        <template v-if="column.name === 'action'">
                            <div class="table-action">
                                <dropdown-button
                                    button-class="tertiary button"
                                    icon="fa-regular fa-ellipsis-vertical"
                                    title=""
                                    alignment="RIGHT"
                                    @clicked="chipDropdownClicked($event, wallet)"
                                    :actions="[{title: '', actions: chipDropdownActions}]"
                                ></dropdown-button>
                            </div>
                        </template>
                    </td>
                </tr>
            </template>
            <template v-slot:right>
                <div v-if="!selectedWallet" class="empty-right-panel">
                    <img src="../../assets/img/select-hint.svg" />
                    Cliquer sur un support pour <br/> le sélectionner
                </div>
                <div v-else class="selected-wallet">
                    <div class="close" @click="selectedWallet = null">
                        <i class="fa-regular fa-xmark"></i>
                        <span> Fermer </span>
                    </div>

                    <div class="header">
                        <div class="left">
                            <h2> {{ $filters.Chip(selectedWallet.chipVisualId) }} </h2>
                        </div>

                        <dropdown-button
                            button-class="grey button"
                            icon="fa-regular fa-ellipsis-vertical"
                            title=""
                            alignment="RIGHT"
                            @clicked="chipDropdownClicked($event, selectedWallet)"
                            :actions="[{title: '', actions: chipDropdownActions}]"
                        ></dropdown-button>
                    </div>

                    <div class="wallet-data">
                        <button class="small white button" @click="goToChipTransactions(selectedWallet.chipVisualId)">
                            <i class="fa-regular fa-arrow-up-right-from-square"></i>
                            Voir les transactions liées
                        </button>

                        <div class="properties-table">
                            <div class="row">
                                <span class="title"> Utilisateur lié </span>
                                <a :href="getCustomerUrl(selectedWalletCustomerChip.customerUid)" target="_blank" class="value" v-if="selectedWalletCustomerChip">
                                    Voir l'utilisateur
                                    <i class="fa-regular fa-arrow-up-right-from-square"></i>
                                </a>
                                <span class="value" v-else> Aucun </span>
                            </div>
                        </div>

                        <div class="see-all" v-if="!customerChipHistory[selectedWallet.uid]">
                            <button class="white button" :class="{loading: loadingCustomerChipHistory, disabled: loadingCustomerChipHistory}" @click="loadChipHistory(selectedWallet)">
                                Voir tout l'historique
                            </button>
                        </div>
                        <h3 v-else> Historique </h3>

                        <div class="properties-table" v-if="customerChipHistory[selectedWallet.uid]">
                            <div class="row" v-for="customerChip in customerChipHistory[selectedWallet.uid]">
                                <span class="title">
                                    {{ $filters.Date(customerChip.startingDatetime) }} -
                                    {{ customerChip.endingDatetime ? $filters.Date(customerChip.endingDatetime) : 'Maintenant' }}
                                </span>
                                <span class="value">
                                    <a :href="getCustomerUrl(customerChip.customerUid)" target="_blank" class="value">
                                        Voir l'utilisateur
                                        <i class="fa-regular fa-arrow-up-right-from-square"></i>
                                    </a>
                                </span>
                            </div>
                        </div>

                    </div>
                </div>
            </template>
        </filter-table-layout>

        <wallet-declare-state-form
            v-if="showUnusableChipConfirmationModal"
            :reason="showUnusableChipConfirmationModal.reason"
            :wallet-uid="showUnusableChipConfirmationModal.wallet.uid"
            @close="showUnusableChipConfirmationModal = null"
        ></wallet-declare-state-form>

        <wallet-network-transaction-form
            v-if="showEmptyChipConfirmationModal"
            :wallet="showEmptyChipConfirmationModal.wallet"
            :transaction-type="showEmptyChipConfirmationModal.type"
            @close="showEmptyChipConfirmationModal = null"
        ></wallet-network-transaction-form>

      <export-choice-modal
          v-if="showExportModal"
          :export-choices="[{
                id: 'raw',
                title: 'Balance des supports de paiement',
                description: `Exporte la liste des supports de paiement et leur balance au moment de l'export`,
                selected:true
            }]"
          :ask-export-zero-lines="false"
          @chose-export="processExport($event)"
          @close="showExportModal = false"
      ></export-choice-modal>
    </div>
</template>