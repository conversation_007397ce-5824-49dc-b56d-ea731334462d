import {Component, Prop, Vue, Watch} from "vue-facing-decorator";
import {
    DropdownComponent,
    DropdownValue,
    FormModalOrDrawerComponent,
    TablePagination, ToggleComponent
} from "@groupk/vue3-interface-sdk";
import ProductImageComponent
    from "../../../fronts/product-front/src/components/ProductImageComponent/ProductImageComponent.vue";
import {
    AppReservitApiOut, ProductApiOut,
    ReservitApi_configuration,
    ReservitProductAssociationMissingListApiOut,
    ProductRevisionNotDetailedApiOut,
    ReservitProductAssociationMissingWithTaxApiOut,
    ReservitApi_productId, ProductNotDetailedApiOut, UuidScopeProductProduct, ReservitProductAssociationApiIn
} from "@groupk/mastodon-core";
import {AutoWired, ScopedUuid} from "@groupk/horizon2-core";
import {BillingAccountRepository} from "../../repositories/BillingAccountRepository";
import {ProductRepository} from "../../repositories/ProductRepository";
import {ReservitAppRepository} from "../../repositories/ReservitAppRepository";
import {AppState} from "../../AppState";

@Component({
    components: {
        'form-modal-or-drawer': FormModalOrDrawerComponent,
        'product-image': ProductImageComponent,
        'dropdown': DropdownComponent,
        'toggle': ToggleComponent
    }
})
export default class ProductReservitBindingComponent extends Vue {
    @Prop({required: true}) reservitApp!: AppReservitApiOut;

    reservitConfiguration!: ReservitApi_configuration;
    reservitMissingProductAssociation!: ReservitProductAssociationMissingListApiOut;

    filteredProducts: ReservitProductAssociationMissingWithTaxApiOut[] = [];

    products: ProductApiOut[] = [];

    productBinding: Map<string, ReservitApi_productId> = new Map();
    vatBinding: Map<string, number> = new Map();

    lastKeyboardEvent: KeyboardEvent|null = null;

    missingOnly: boolean = true;

    loading: boolean = true;
    opened: boolean = false;
    saving: boolean = false;
    showPasteModal: boolean = false;
    pastedData: string = '';
    pasteImportError: string|null = null;
    search: string = '';
    error: string|null = null;

    pagination: TablePagination = {
        totalResults: 0,
        resultsPerPage: 150,
        currentPage: 1,
        estimateTotal: false
    }

    @AutoWired(ProductRepository) accessor productRepository!: ProductRepository;
    @AutoWired(BillingAccountRepository) accessor billingAccountRepository!: BillingAccountRepository;
    @AutoWired(ReservitAppRepository) accessor reservitAppRepository!: ReservitAppRepository;
    @AutoWired(AppState) accessor appState!: AppState;

    async mounted() {
        setTimeout(() => this.opened = true, 0);

        this.reservitConfiguration = (await this.reservitAppRepository.callContract('getConfiguration', {
            establishmentUid: this.appState.requireUrlEstablishmentUid(),
            appUid: this.reservitApp.uid
        }, undefined)).success();

        this.reservitMissingProductAssociation = (await this.reservitAppRepository.callContract('missingProductRevisionAssociationList', {
            establishmentUid: this.appState.requireUrlEstablishmentUid(),
            appUid: this.reservitApp.uid
        }, undefined)).success();

        this.filterRevisions();

        this.products = (await this.productRepository.callContract('list', {establishmentUid: this.appState.requireUrlEstablishmentUid()}, undefined)).success();

        window.addEventListener('keydown', this.handleKeyDown);
        window.addEventListener('keyup', this.handleKeyUp);

        this.loading = false;
    }

    unmounted() {
        window.removeEventListener('keydown', this.handleKeyDown);
        window.removeEventListener('keyup', this.handleKeyUp);
    }

    nextPage() {
        if(this.loading) return;
        this.pagination.currentPage++;
        this.filterRevisions();
    }

    previousPage() {
        if(this.loading) return;
        this.pagination.currentPage--;
        this.filterRevisions();
    }

    private searchTimeout: ReturnType<typeof setTimeout>|null = null;

    @Watch('search')
    onSearchChange() {
        if (this.searchTimeout) {
            clearTimeout(this.searchTimeout);
        }
        this.searchTimeout = setTimeout(() => {
            this.filterRevisions();
        }, 500);
    }

    filterRevisions() {
        const filtered = this.reservitMissingProductAssociation.list
            .filter(missing => missing.product.lastRevision.name.toLowerCase().includes(this.search.toLowerCase()))
            .sort((a, b) => a.product.lastRevision.name.localeCompare(b.product.lastRevision.name));
        const startIndex = (this.pagination.currentPage - 1) * this.pagination.resultsPerPage;
        const endIndex = startIndex + this.pagination.resultsPerPage;
        this.pagination.totalResults = filtered.length;
        this.filteredProducts = filtered.slice(startIndex, endIndex);
    }

    handleKeyDown(e: KeyboardEvent) {
        this.lastKeyboardEvent = e;
        if(e.ctrlKey && e.shiftKey && e.code === 'KeyV') {
            this.showPasteModal = true;
            e.preventDefault();
        }
    }

    handleKeyUp() {
        this.lastKeyboardEvent = null;
    }

    getReservitProductWithProduct(product: ProductNotDetailedApiOut): ReservitApi_productId|null {
        const reservitId = this.productBinding.get(product.uid);
        if(reservitId) return reservitId;
        return null;
    }

    getVatNumberWithProduct(product: ProductNotDetailedApiOut): number|null {
        const vatNumber = this.vatBinding.get(product.uid);
        if(vatNumber) return vatNumber;
        return null;
    }

    get reservitProductDropdownValues(): DropdownValue[] {
        return this.reservitConfiguration.products.map((product) => {
            return {
                name: (product.names.find((n) => n.lang === 'fr')?.name || product.names[0].name) + ' - ' + product.id,
                value: product.id
            }
        })
    }

    getTaxValuesForProduct(data: ReservitProductAssociationMissingWithTaxApiOut): DropdownValue[] {
        return data.billingTaxList.map((tax) => {
            return {
                name: tax.name + ' ' + (tax.percent / 1000) + '%',
                value: tax.percent
            }
        })
    }

    setProductBinding(productUid: ScopedUuid<UuidScopeProductProduct>, reservitId: ReservitApi_productId | null) {
        if (reservitId) {
            this.productBinding.set(productUid, reservitId);
        } else {
            this.productBinding.delete(productUid);
        }
    }

    setVatBinding(productUid: ScopedUuid<UuidScopeProductProduct>, vatNumber: number | null) {
        if (vatNumber) {
            this.vatBinding.set(productUid, vatNumber);
        } else {
            this.vatBinding.delete(productUid);
        }
    }

    async save() {
        this.saving = true;

        const tasks = Array.from(this.productBinding.entries()).map(([productUid, reservitId]) => {
            const vatNumber = this.vatBinding.get(productUid);
            if(!vatNumber) return;

            return this.reservitAppRepository.callContract('associateProductRevision', {
                establishmentUid: this.appState.requireUrlEstablishmentUid(),
                appUid: this.reservitApp.uid,
            }, new ReservitProductAssociationApiIn({
                productUid: productUid as ScopedUuid<UuidScopeProductProduct>,
                billingTaxPercent: vatNumber,
                reservitProductId: reservitId as ReservitApi_productId
            }))
        });
        await Promise.all(tasks);

        this.saving = false;
        this.close();
    }

    close() {
        this.opened = false;
        setTimeout(() => this.$emit('close'), 300);
    }

    processPastedData() {
        // Reset error state
        this.pasteImportError = null;

        if (!this.pastedData.trim()) {
            this.pasteImportError = "Aucune donnée n'a été collée";
            return;
        }

        try {
            // Split the pasted data into lines
            const lines = this.pastedData.trim().split('\n');
            let errorProducts: string[] = [];

            for (const line of lines) {
                // Skip empty lines
                if (!line.trim()) continue;

                // Parse the line - expecting format: ProductName, BillingAccountName
                const match = line.match(/([^,]+),\s*([^,]+),\s*(.+)/);
                if (!match) {
                    this.pasteImportError = `Format incorrect: ${line}. Format attendu: Nom produit, Taux TVA, ID produit Reservit`;
                    return;
                }

                const [, productName, vatNumberStr, reservitProductId] = match;

                // Find the product by name
                const data = this.reservitMissingProductAssociation.list.find(pr =>
                    pr.product.lastRevision.name.toLowerCase() === productName.trim().toLowerCase()
                );

                if (!data) {
                    // errorProducts.push(productName);
                    continue;
                }

                const vatNumber = parseInt(vatNumberStr.trim(), 10);
                if (isNaN(vatNumber)) {
                    this.pasteImportError = `Taux TVA invalide: ${vatNumberStr} pour le produit ${productName}`;
                    return;
                }

                // Verify that the VAT exists in the product's billing tax list
                const vatExists = data.billingTaxList.some(tax => (tax.percent / 1000) === vatNumber);
                if (!vatExists) {
                    this.pasteImportError = `Taux TVA ${vatNumber} non disponible pour le produit ${productName}`;
                    return;
                }

                // Verify that the Reservit product ID exists
                const reservitIdTrimmed = reservitProductId.trim();
                const reservitProductExists = this.reservitConfiguration.products.some(
                    product => product.id === reservitIdTrimmed
                );
                if (!reservitProductExists) {
                    this.pasteImportError = `ID produit Reservit ${reservitIdTrimmed} n'existe pas`;
                    return;
                }

                // Set both the VAT and product binding
                this.setVatBinding(data.product.uid, vatNumber * 1000);
                this.setProductBinding(data.product.uid, (reservitProductId.trim()) as ReservitApi_productId);
            }

            // If there were products not found, show an error
            if (errorProducts.length > 0) {
                this.pasteImportError = `Produits non trouvés: ${errorProducts.join(', ')}`;
            } else {
                // Close the paste modal if everything was successful
                this.$forceUpdate();
                this.pastedData = '';
                this.showPasteModal = false;
            }
        } catch (error) {
            this.pasteImportError = `Erreur lors du traitement des données: ${error}`;
        }
    }

    private productMap: Map<string, ProductApiOut> = new Map();

    getProductWithRevision(revision: ProductRevisionNotDetailedApiOut) {
        if (!this.productMap.size) {
            this.products.forEach(product => {
                this.productMap.set(product.uid, product);
            });
        }
        return this.productMap.get(revision.productUid) ?? null;
    }
}
