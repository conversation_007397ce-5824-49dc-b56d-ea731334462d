import {Component, Vue} from "vue-facing-decorator";

type IconCategory = {
	name: string,
	icon: string,
	icons: Icon[]
}

type Icon = {
	keywords: string[],
	url: string
}

@Component({
	emits: ['selected-icon']
})
export default class IconBankComponent extends Vue {
	iconSearch: string = '';

	categories: IconCategory[] = [{
		name: '<PERSON><PERSON><PERSON>',
		icon: 'fa-regular fa-beer-mug-empty',
		icons: [{
			keywords: [],
			url: 'https://v5.airtableusercontent.com/v3/u/32/32/*************/-9GVIBlknHH1lmUwWA9XWw/O_qkllDHAixxF8Bz60wu18t6Pg8jdoKUk1dwqifDfuxRIIYZksxc6g8c8kwqocobIm1MOlWsf-_qWggunx8kDRwZyxMHSyf8xC0IxsRW4LeCzoRmUrlm8PY7UE4dLQZI2iNwtwU83qg2PNB7TQ4UxA/aEVSrI7RYj5E8nYeDR32_SUVGUP8PneVudPLeTZQA8k'
		}, {
			keywords: [],
			url: 'https://cdn.worldvectorlogo.com/logos/heineken-1.svg'
		}, {
			keywords: [],
			url: 'https://upload.wikimedia.org/wikipedia/commons/6/64/Leffe_Logo.png'
		}, {
			keywords: [],
			url: 'https://upload.wikimedia.org/wikipedia/commons/thumb/a/a2/Logo_officiel_marque_1664.png/1200px-Logo_officiel_marque_1664.png'
		}]
	}, {
		name: 'Food',
		icon: 'fa-regular fa-fork-knife',
		icons: []
	}, {
		name: 'Alcool',
		icon: 'fa-regular fa-wine-glass-empty',
		icons: [{
			keywords: [],
			url: 'https://v5.airtableusercontent.com/v3/u/32/32/*************/OJQzxgngGfEsAj6nAsIUiw/eP_E28AnGVmTROpW-L7maoQBmONxyIHQtlQisVmmywNKfKtlqtlj_A5WuEhTcA8tIJ214zDHP5ZzOpEBK0YSC4EK2KpCSvsUFb4hAbam_56ZzukvIjDsJhO4WMkJ_BddqtwdrbhZdr_Oyr8_hLLdDQ/EPjR9v3PCZ_wJZBZU17u66GgsetGj62VQUuZe5B7uZc'
		}, {
			keywords: [],
			url: 'https://v5.airtableusercontent.com/v3/u/32/32/*************/39MbVEfdq4WZZKSisKaeGg/gIzHFy4MuYD5jkGzgqgPcLslAQt4I45cJcoYMbZz1AQXpZUxv6Mn0KOWiNW50xxJmLimwmhtNJ_zV0pX5pOcXjKWkVSEcoeGXmErVrgwKcSmp6tWDSUNQv7fGL6Jhle95jvZPRfIVRmpiH0-FO6-Gw/izp5ue4GhcSVYDLAKxR0vXtyslt6iA6H_f9gMjq0Qb4'
		}, {
			keywords: [],
			url: 'https://v5.airtableusercontent.com/v3/u/32/32/*************/s3uJ_yBreyHAA1PKiPNFRA/GKT9VuUjpB68vBBSDBTXZiJzFDJobBPx9szXirwj2F-f_DNm4ybwmb7ayUWycAbv8TVPZKxO2pcrla8841b-xlE1OyiLrtNQqZGK9110lkaRZkMLPH5enAb6gBS6XNESs_xPYe5xyTG5x9f9WtfweA/RrvFWNsfdtrM3dTeoDsTMArZ4octxo9yeD2KIdkg-gc'
		}]
	}];

	selectedCategory: string = 'all';

	get selectedCategoryIcons() {
		if(this.selectedCategory === 'all') {
			let icons: Icon[] = [];
			for(let category of this.categories) {
				icons = icons.concat(category.icons);
			}
			return icons;
		} else {
			const category = this.categories.find((category) => category.name === this.selectedCategory);
			return category?.icons ?? [];
		}
	}

	selectIcon(icon: string) {
		this.$emit('selected-icon', icon);
	}
}