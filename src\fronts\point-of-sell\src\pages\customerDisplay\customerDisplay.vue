<script lang="ts" src="./customerDisplay.ts" />

<style lang="sass" scoped>
@import './customerDisplay.scss'
</style>

<template>
    <div id="customer-display-page">
        <div class="loading" v-if="loading">
            <div class="loader"></div>
            Chargement...
        </div>
        <div class="waiting" v-else-if="!currentOrder">
            <img :src="getEstablishmentImageUrl()"/>
        </div>
        <div class="current-order" v-else>
            <div class="left" ref="leftPanel">
                <div class="purchase" v-for="purchase in currentOrder.purchases">
                    <div class="quantity"> {{ purchase.quantity }} </div>
                    <div class="data">
                        <span class="name"> {{ $filters.Length(purchase.name, 35) }} </span>
                        <span class="total">
                        <span class="price">{{ $filters.Money(purchase.unitPriceWithTax * purchase.quantity) }}</span>
                    </span>
                    </div>
                </div>
            </div>
            <div class="right">
                <span class="title"> Total </span>
                <span class="amount"> {{ $filters.Money(currentOrder.total) }} </span>
            </div>
        </div>

        <div class="banner">
            <span> weecop.fr </span>
        </div>
    </div>
</template>