import {Component, Prop, Vue} from "vue-facing-decorator";
import {AutoWired} from "@groupk/horizon2-core";
import {PrinterRepository} from "../../repositories/PrinterRepository";

export type PurchasePrintProgressModalComponentDataPerPrinter = {
    printers: Record<number, PurchasePrintProgressModalComponentData>
};

export interface PurchasePrintProgressModalComponentData {
    totalSteps: number,
    currentStep: number,
    currentStepName: string,
    error?: boolean
}

@Component({
    components: {}
})
export default class PurchasePrintProgressModalComponent extends Vue {
    data!: PurchasePrintProgressModalComponentDataPerPrinter;

    @AutoWired(PrinterRepository) accessor printerRepository!: PrinterRepository;

    beforeMount() {
        if(this.printerRepository.currentKitchenPrintingState) this.data = this.printerRepository.currentKitchenPrintingState;
        else this.data = {
            printers: {
                0: {
                    totalSteps: 2,
                    currentStep: 0,
                    currentStepName: '',
                }
            }
        };

        this.printerRepository.on('print-progress-update', this.printProgressUpdateListener);
    }

    unmounted() {
        this.printerRepository.off('print-progress-update', this.printProgressUpdateListener);
    }

    printProgressUpdateListener(data: PurchasePrintProgressModalComponentDataPerPrinter) {
        this.data = data;
        this.$forceUpdate();
    }

    close() {
        this.printerRepository.currentKitchenPrintingState = null;
        this.$emit('close');
    }
}