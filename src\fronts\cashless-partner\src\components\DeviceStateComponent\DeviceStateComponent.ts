import {Component, Prop, Vue} from "vue-facing-decorator";
import {FormModalOrDrawerComponent, FileInputComponent, InputPriceComponent} from "@groupk/vue3-interface-sdk";
import {CashlessDeviceStateApiOut} from "@groupk/mastodon-core";

@Component({
	components: {
		'form-modal-or-drawer': FormModalOrDrawerComponent,
		'file-input-component': FileInputComponent,
		'price-input': InputPriceComponent
	},
	emits: ['saved']
})
export default class DeviceStateComponent extends Vue {
	@Prop() deviceState!: CashlessDeviceStateApiOut;

	opened: boolean = false;

	beforeMount() {
		setTimeout(() => this.opened = true, 0);
	}

	close() {
		this.opened = false;
		setTimeout(() => this.$emit('close'), 300);
	}
}