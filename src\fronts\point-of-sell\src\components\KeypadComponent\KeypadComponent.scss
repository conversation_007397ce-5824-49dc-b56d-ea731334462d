.keypad-component {
    display: flex;
    flex-direction: column;
    border: 1px solid var(--keypad-border-color);
    border-radius: 8px;

    --keypad-border-color: black;

    .line {
        display: grid;
        grid-template-columns: 1fr 1fr 1fr;
        border-top: 1px solid var(--keypad-border-color);

        .key {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            border-left: 1px solid var(--keypad-border-color);
            font-weight: 500;
            cursor: pointer;

            &.full {
                grid-column: 1/4;
            }

            &:first-child {
                border: none;
            }

            &:hover {
                background: #f3f3f3;
            }
        }
    }
}