export enum ImageConversionStrategy {
	"lightness" = "lightness",
	"average" = "average",
	"luminosity" = "luminosity",
	"notWhite" = "notWhite",
}
export type ImageConverterOutputLoading = {width: number; height: number; pixels: Array<number>; colors: number};

export class ImageUtils {
	public static convertColor1DMatrixToGrayscale(pixelValues: Array<number>, nbColors: number = 3, strategy: ImageConversionStrategy = ImageConversionStrategy.lightness) {
		let result: Array<number> = [];

		if (nbColors === 1) return pixelValues;

		for (let i = 0; i < pixelValues.length; i += nbColors) {
			let grayScale = 0;
			if (nbColors === 3 || nbColors === 4) {
				let red = pixelValues[i]!;
				let green = pixelValues[i + 1]!;
				let blue = pixelValues[i + 2]!;
				if (strategy === ImageConversionStrategy.average) {
					grayScale = Math.round((red + green + blue) / 3);
				} else if (strategy === ImageConversionStrategy.lightness) {
					grayScale = Math.round((Math.max(red, green, blue) + Math.min(red, green, blue)) / 2);
				} else if (strategy === ImageConversionStrategy.luminosity) {
					grayScale = Math.round(0.21 * red + 0.72 * green + 0.07 * blue);
				} else if (strategy === ImageConversionStrategy.notWhite) {
					grayScale = red === 255 && green === 255 && blue === 255 ? 255 : 0;
				}
			} else {
				throw new Error("unsupported_color_format");
			}
			result.push(grayScale);
		}
		return result;
	}

	public static convertGrayscale1DToMono(pixelValues: Array<number>, threshold = 127) {
		let result: Array<number> = [];

		for (let i = 0; i < pixelValues.length; i++) {
			result.push(pixelValues[i]! >= threshold ? 0 : 1);
		}
		return result;
	}

	public static convert1DMatrixTo2DMatrix(values: Array<number>, width: number, height: number): number[][] {
		let result: Array<Array<number>> = [];
		if (width * height !== values.length) throw new Error("incompatible_dimensions_1d_to_2d");

		for (let y = 0; y < height; ++y) {
			let pos = y * width;
			let line: Array<number> = [];
			for (let x = 0; x < width; ++x) {
				line.push(values[pos + x]!);
			}
			result.push(line);
		}
		return result;
	}

	public static base64ToRawImage(
		base64: string,
		options: {targetWidth?: number; targetHeight?: number; fillColor?: string | null; imageSmoothing?: boolean} = {}
	): Promise<ImageConverterOutputLoading> {
		return new Promise<{width: number; height: number; pixels: Array<number>; colors: number}>((resolve, reject) => {
			let img = new Image();
			img.onerror = reject;
			img.onload = () => {
				let targetWidth = typeof options.targetWidth !== "undefined" ? options.targetWidth : img.width;
				let targetHeight = typeof options.targetHeight !== "undefined" ? options.targetHeight : img.height;
				let fillColor = typeof options.fillColor !== "undefined" ? options.fillColor : "white";
				let imageSmoothing = typeof options.imageSmoothing !== "undefined" ? options.imageSmoothing : false;

				const canvas = document.createElement("canvas");
				canvas.width = targetWidth;
				canvas.height = targetHeight;

				const ctx = <CanvasRenderingContext2D>canvas.getContext("2d");
				ctx.imageSmoothingEnabled = imageSmoothing;
				if (fillColor !== null) {
					ctx.beginPath();
					ctx.rect(0, 0, targetWidth, targetHeight);
					ctx.fillStyle = fillColor;
					ctx.fill();
				}

				ctx.drawImage(img, 0, 0, targetWidth, targetHeight);
				let pixelsBufferRaw = ctx.getImageData(0, 0, targetWidth, targetHeight);
				let pixelsBuffer: Array<number> = [];
				for (let i = 0; i < pixelsBufferRaw.data.length; ++i) {
					pixelsBuffer[i] = pixelsBufferRaw.data[i]!;
				}

				resolve({
					width: targetWidth,
					height: targetHeight,
					pixels: pixelsBuffer,
					colors: 4,
				});
			};
			img.src = base64;
		});
	}

	private static createBody(bitmapImage: Array<Array<number>>) {
		let result : number[] = [];
		let height = bitmapImage.length;
		let width = bitmapImage[0]!.length;
		let index = 0;
		let auxBinaryChar = ["0", "0", "0", "0", "0", "0", "0", "0"];
		for (let h = 0; h < height; h++) {
			for (let w = 0; w < width; w++) {
				auxBinaryChar[index] = bitmapImage[h]![w] === 1 ? "1" : "0";
				index++;
				if (index == 8 || w == width - 1) {
					result.push(parseInt(auxBinaryChar.join(""), 2));
					auxBinaryChar = ["0", "0", "0", "0", "0", "0", "0", "0"];
					index = 0;
				}
			}
			// result += "\n";
		}
		return result;
	}

	private static encodeBinaryToHex(code: string) {
		let zplHex = "";
		for (let i = 0; i < code.length; ++i) {
			if (code[i] === "\n") {
				// zplHex += ',';
			} else {
				zplHex += ("00" + Number(code.charCodeAt(i)).toString(16)).slice(-2).toUpperCase();
			}
		}
		return zplHex;
	}

	private static encodeBinaryStringToBinary(code: string) : number[] {
		let bytes : number[] = [];
		for (let i = 0; i < code.length; ++i) {
			if (code[i] === "\n") {
				// zplHex += ',';
			} else {
				bytes.push(code.charCodeAt(i));
			}
		}
		return bytes;
	}

	public static toRasterFormat(image: Array<Array<number>>, outputMode: "hex" | "binary") : number[] {
		let convertedImage = this.createBody(image);
		return convertedImage;
		// console.log(convertedImage)
		// if (outputMode === "hex") {
		// 	return this.encodeBinaryToHex(convertedImage);
		// } else {
		// 	return this.encodeBinaryStringToBinary(convertedImage);
		// }
	}

	public static rgb2cmyk(r: number, g: number, b: number, normalized: boolean = false) {
		let c: number;
		let m: number;
		let y: number;
		let k: number;

		if (r === 0 && g === 0 && b === 0) {
			c = 0;
			m = 0;
			y = 0;
			k = 1;
		} else {
			c = 1 - r / 255;
			m = 1 - g / 255;
			y = 1 - b / 255;
			k = 0;
		}

		c = (c - k) / (1 - k);
		m = (m - k) / (1 - k);
		y = (y - k) / (1 - k);

		if (!normalized) {
			c = Math.round(c * 10000) / 100;
			m = Math.round(m * 10000) / 100;
			y = Math.round(y * 10000) / 100;
			k = Math.round(k * 10000) / 100;
		}

		c = isNaN(c) ? 0 : c;
		m = isNaN(m) ? 0 : m;
		y = isNaN(y) ? 0 : y;
		k = isNaN(k) ? 0 : k;

		return {
			c: c,
			m: m,
			y: y,
			k: k,
		};
	}
}
