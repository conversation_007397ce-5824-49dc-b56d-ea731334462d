import {Component, Vue} from "vue-facing-decorator";
import RightModalComponent from "../RightModalComponent/RightModalComponent.vue";
import {
	CustomerApiIn,
	CustomerApiOut,
	CustomerCreationVerificationLevel,
	CustomerHttpCustomerContractSearchConfig
} from "@groupk/mastodon-core";
import {
	AutoWired,
	QueryFilterGroupClause,
	QueryOperator,
	TypedQuerySearch,
	Uuid,
} from "@groupk/horizon2-core";
import {CustomerRepository} from "../../../../../shared/repositories/CustomerRepository";
import {PosState} from "../../model/PosState";

@Component({
	components: {
		'right-modal': RightModalComponent
	},
	emits: ['selected-customer', 'close']
})
export default class CustomersListComponent extends Vue {
	customers: CustomerApiOut[] = [];
	searchValue: string = '';

	creatingCustomer: CustomerApiIn|null = null;

	selectedCustomer: CustomerApiOut|null = null;
	creating: boolean = false;
	loading: boolean = false;

	@AutoWired(CustomerRepository) accessor customerRepository!: CustomerRepository;
	@AutoWired(PosState) accessor posState!: PosState;

	mounted() {
		this.searchCustomers({});
	}

	searchTimeout: ReturnType<typeof setTimeout>|null = null;
	async search() {
		if(this.searchTimeout) {
			clearTimeout(this.searchTimeout);
			this.searchTimeout = null;
		}

		this.searchTimeout = setTimeout(async () => {
			await this.searchCustomers({
				filter: {
					group: QueryFilterGroupClause.OR,
					filters: [{
						name: 'firstname',
						value: this.searchValue,
						operator: QueryOperator.CONTAINS
					}, {
						name: 'lastname',
						value: this.searchValue,
						operator: QueryOperator.CONTAINS
					}, {
						name: 'email',
						value: this.searchValue,
						operator: QueryOperator.CONTAINS
					}]
				}
			});
		}, 500);
	}


	async searchCustomers(filters: TypedQuerySearch<typeof CustomerHttpCustomerContractSearchConfig>, cursor: {after?: Uuid, before?: Uuid}|null = null) {
		this.loading = true;

		if(cursor && cursor.after) filters.cursorAfter = cursor.after;
		if(cursor && cursor.before) filters.cursorBefore = cursor.before;
		this.customers = (await this.customerRepository.callContract('search', {establishmentUid: this.posState.establishmentUid}, filters)).success();
		this.loading = false;
	}

	get groupedCustomers(): Record<string, CustomerApiOut[]> {
		const sortedCustomers = this.customers.sort((c1, c2) => {
			const c1FullName = (c1.lastname ?? '?') + (c1.firstname ?? '?');
			const c2FullName = (c2.lastname ?? '?') + (c2.firstname ?? '?');
			return c1FullName.localeCompare(c2FullName);
		});

		const grouped: Record<string, CustomerApiOut[]> = {};
		for(let customer of sortedCustomers) {
			const letter = customer.lastname ? customer.lastname[0].toUpperCase() : '?';
			if(!grouped[letter]) {
				grouped[letter] = [];
			}
			grouped[letter].push(customer);
		}
		return grouped;
	}

	showCreateModal() {
		this.creatingCustomer = new CustomerApiIn({
			firstname: '',
			lastname: '',
			email: '',
			description: '',
			verificationLevel: CustomerCreationVerificationLevel.NORMAL,
			labels:	[],
			attributes: undefined
		});
	}

	async createCustomer() {
		if(!this.creatingCustomer) return;
		this.creating = true;
		const response = await this.customerRepository.callContract('create', {establishmentUid: this.posState.establishmentUid}, this.creatingCustomer);
		if(response.isSuccess()) {
			this.customers.push(response.success());
		}
		this.creating = false;
		this.creatingCustomer = null;
	}

	close() {
		if(this.selectedCustomer) {
			this.$emit('selected-customer', this.selectedCustomer);
		} else this.$emit('close');
	}
}