<script lang="ts" src="./StringDescriptorComponent.ts" />

<style lang="sass" scoped>
@import './StringDescriptorComponent.scss'
</style>

<template>
    <div class="string-descriptor-component">
        <div class="input-group">
            <textarea
                v-model="value"
                type="text"
                :maxlength="metadataDescriptorTemplate.maxLength || ''"
                :placeholder="metadataDescriptorTemplate.description ?? ''"
            ></textarea>
        </div>

        <button class="primary button" :class="{disabled: value.length < metadataDescriptorTemplate.minLength}" @click="validate()"> Valider </button>
    </div>
</template>