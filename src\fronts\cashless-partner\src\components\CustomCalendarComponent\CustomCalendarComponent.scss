
.custom-calendar-component {
	.month-navigation {
		display: flex;
		justify-content: space-between;
		margin-bottom: 20px;
		padding: 0 12px;

		div {
			flex-grow: 2;
			&.left {
				cursor: pointer;
				display: flex;
				align-items: center;
			}
			&.right {
				display: flex;
				justify-content: flex-end;
				align-items: center;
				cursor: pointer;
			}
		}

		.month {
			font-weight: 700;
			font-size: 12px;
		}

		img {
			height: 20px;
		}
	}

	.scroll-month {
		font-weight: 700;
		font-size: 12px;
		margin: 20px 0;
	}

	table {
		border-collapse: separate;
		border-spacing: 0 10px;
	}

	&.small {
		table {
			border-spacing: 0 5px;
		}
	}

	th {
		font-weight: 400;
		font-size: 12px;
		color: #bdbdbd;
		padding-bottom: 10px;
	}

	td {
		padding: 0;
		border: 0;

		&.disabled * {
			pointer-events: none;
		}

		$colors: (
			'default': #F2F2F2,
			'orange': rgba(242, 153, 75, 0.4),
			'green': rgba(109, 224, 169, 0.4),
			'blue': rgba(68, 134, 255, 0.4),
			'grey': rgba(130, 130, 130, 0.4),
			'red': rgba(235, 87, 87, 0.4),
		);

		@each $name, $color in $colors {
			&.in-range.#{$name} {
				background: $color;
			}

			&.in-range.#{$name}:first-child:not(.range-end),
			&.in-range.#{$name}.range-start {
				background: linear-gradient(270deg, $color 0%, $color 50%, transparent 50%, transparent 100%);
			}

			&.in-range.#{$name}:last-child:not(.range-start),
			&.in-range.#{$name}.range-end {
				background: linear-gradient(90deg, $color 0%, $color 50%, transparent 50%, transparent 100%);
			}

			&.in-range.#{$name}.range-start.range-end {
				background: none;
			}
		}

		&.in-range.range-start:last-child {
			background: none;
		}

		&.in-range.range-end:first-child {
			background: none;
		}
	}

	.day {
		display: flex;
		justify-content: center;
		align-items: center;
		cursor: pointer;

		.number {
			border: 1px solid var(--border-color);
			border-radius: 50%;
			font-weight: 700;
			font-size: 12px;
			width: 33px;
			height: 33px;
			display: flex;
			align-items: center;
			justify-content: center;
			position: relative;
			user-select: none;

			.profiles {
				position: absolute;
				top: -75%;
				left: 50%;
				transform: translate(-50%, 50%);
				display: flex;
				margin-left: 5px;

				.profile-thumbnail {
					margin-left: -10px;
					height: 20px;
					width: 20px;
					border-radius: 50%;
					border: 2px solid #ffffff;
					background-position: center;
					background-size: contain;

					&.text {
						display: flex;
						align-items: center;
						justify-content: center;
						background: #828282;
						color: white;
						font-size: 10px;
					}
				}
			}

			$colors: (
				'default': #F2F2F2,
				'orange': rgba(242, 153, 75),
				'green': rgb(109, 224, 169),
				'blue': rgb(68, 134, 255),
				'grey': rgba(130, 130, 130),
				'red': #eb5757,
			);

			&.morning {
				background: linear-gradient(0deg, rgba(109, 224, 169, 1) 0%, rgba(109, 224, 169, 1) 50%, rgba(255, 255, 255, 0) 50%, rgba(255, 255, 255, 0) 100%);
				border: 1px solid #6de0a9;
			}

			&.evening {
				background: linear-gradient(180deg, rgba(109, 224, 169, 1) 0%, rgba(109, 224, 169, 1) 50%, rgba(255, 255, 255, 0) 50%, rgba(255, 255, 255, 0) 100%);
				border: 1px solid #6de0a9;
			}

			&.all-day {
				background: #6de0a9;
				border: 1px solid #6de0a9;
			}

			&.conflict {
				background: #eb5757;
				border: 1px solid #eb5757;
			}

			&.fade {
				// Emulate opacity without being transparent
				filter: contrast(calc(1 / 7)) brightness(1.75);
				pointer-events: none;
			}

			&:hover {
				background: var(--secondary-hover-color);
			}

			&.hidden {
				opacity: 0;
				pointer-events: none;
			}

			@each $name, $color in $colors {
				&.#{$name} {
					background: $color;

					@if $name != 'default' {
						border: 1px solid $color;
					}
				}

				&.range-start.#{$name},
				&.range-end.#{$name} {
					@if $name != 'default' {
						background: $color;
						color: black;
						border: 1px solid $color;
					}
				}
			}

			&.range-start,
			&.range-end {
				color: white;
				background: #333333;
				border: 1px solid #333333;
			}

			&.selected {
				background: black !important;
				color: white !important;
				border: 1px solid black !important;
			}

			&.today {
				//box-shadow: 0 0 0 4px rgba(1, 55, 173, 0.73);
			}
		}
	}

	.load-more-button {
		margin-top: 20px;
		font-weight: bold;
		text-align: center;
	}
}
