import {Component, Prop, Vue} from "vue-facing-decorator";
import {CashkeeperConnection} from "@groupk/cashkeeper-protocol";
import {SocketNative, TcpSocketWrapper} from "@groupk/native-bridge";
import {AutoWired, BufferUtils} from "@groupk/horizon2-core";
import {PosProfileRepository} from "../../../../../../../shared/repositories/PosProfileRepository";
import {PaymentMethodSettingsCashKeeper} from "@groupk/mastodon-core";

@Component({
	emits: ['save', 'close', 'delete']
})
export default class CashKeeperSettingsFormComponent extends Vue {
	@Prop() settings!: PaymentMethodSettingsCashKeeper;
	@Prop() editing!: boolean;

	connectionTest: 'LOADING'|'ERROR'|'SUCCESS'|null = null;

	@AutoWired(PosProfileRepository) accessor posProfileRepository!: PosProfileRepository
	@AutoWired(SocketNative) accessor nativeSocket!: SocketNative

	async testConnection() {
		this.connectionTest = 'LOADING';

		try {
			const tcpWrapper = await this.nativeSocket.tcpConnectWrapper(this.settings.address, this.settings.officePort);

			const cashKeeperConnection = new CashkeeperConnection((data) => {
				if(!tcpWrapper) throw new Error('connection_have_been_closed');

				let payload: number[] = [];
				for (let i = 0; i < data.length; i++) {
					payload.push(data.charCodeAt(i));
				}
				tcpWrapper.send(payload);
			}, {officeMode: true});

			setTimeout(async () => {
				if(this.connectionTest === 'LOADING') {
					this.connectionTest = 'ERROR';
					await this.closeConnection(tcpWrapper, cashKeeperConnection);
				}
			}, 10000);

			tcpWrapper.on('data', async (bytes) => {
				if(!cashKeeperConnection) return;

				let data: string = String.fromCharCode(...(typeof bytes === 'string' ? BufferUtils.hex2Buffer(bytes) : bytes));

				try {
					await cashKeeperConnection.pushData(data);
				} catch(err) {
					this.connectionTest = 'ERROR';
					await this.closeConnection(tcpWrapper, cashKeeperConnection);
				}
			});

			tcpWrapper.on('closed', async ()=>{
				// this.connectionTest = 'ERROR';
				// await this.closeConnection(tcpWrapper, cashKeeperConnection);
			});

			cashKeeperConnection.on('ready', async (event)=>{
				this.connectionTest = 'SUCCESS';
				await this.closeConnection(tcpWrapper, cashKeeperConnection);
			});

			tcpWrapper.on('connected', () => {
				cashKeeperConnection.connect();
			});
		} catch(err) {
			this.connectionTest = 'ERROR';
		}
	}

	save() {
		this.$emit('save', this.settings);
	}

	async closeConnection(tcpWrapper: TcpSocketWrapper, cashKeeperConnection: CashkeeperConnection) {
		await cashKeeperConnection.disconnect();
		await tcpWrapper.close();
	}

	deleteSettings() {
		this.$emit('delete', this.settings);
	}

	close() {
		this.$emit('close');
	}
}