import {Component, Prop, Vue} from "vue-facing-decorator";
import {LinkApiIn, LinkApiOut} from "@groupk/mastodon-core";
import {AutoWired} from "@groupk/horizon2-core";
import {LinkRepository} from "../../repositories/LinkRepository";
import {AppState} from "../../../../../shared/AppState";

@Component({
	emits: ['close', 'updated']
})
export default class LinkTagsFormComponent extends Vue {
	@Prop({ required: true }) editingLink!: LinkApiOut;
	@Prop({ required: true }) existingTags!: string[];

	selectedTags: string[] = [];

	newTag: string = '';
	error: boolean = false;
	saving: boolean = false;

	@AutoWired(LinkRepository) accessor linkRepository!: LinkRepository;
	@AutoWired(AppState) accessor appState!: AppState;

	mounted() {
		this.selectedTags = [...this.editingLink.tags || []];
	}

	toggleTag(tag: string): void {
		if(this.selectedTags.includes(tag)) {
			this.selectedTags.splice(this.selectedTags.indexOf(tag), 1);
		} else {
			this.selectedTags.push(tag);
		}
	}

	close() {
		this.$emit('close');
	}

	async save() {
		if(!this.editingLink.tags) this.editingLink.tags = [];

		this.error = false;
		this.saving = true;

		const apiIn = new LinkApiIn();
		apiIn.targets = this.editingLink.targets;
		apiIn.tags = [];
		apiIn.comment = this.editingLink.comment;

		for(let selectedTag of this.selectedTags) {
			if(!apiIn.tags.includes(selectedTag)) apiIn.tags.push(selectedTag);
		}
		if(this.newTag && !apiIn.tags.includes(this.newTag)) apiIn.tags.push(this.newTag);

		const result = await this.linkRepository.callContract('update', {establishmentUid: this.appState.requireUrlEstablishmentUid(), linkId: this.editingLink.id}, apiIn);
		if(result.isSuccess()) {
			this.$emit('updated', result.success());
			this.close();
		} else {
			this.error = true;
		}

		this.saving = false;
	}
}