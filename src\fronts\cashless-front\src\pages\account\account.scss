#account-page {
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 30px;
    background: white;
    max-width: 500px;
    margin: auto;
    padding: 30px 20px 50px 20px;

    .form {
        display: flex;
        flex-direction: column;
        gap: 15px;
        width: 100%;

        .input {
            display: flex;
            flex-direction: column;
            gap: 10px;

            label {
                font-size: 18px;
            }

            .description {
                color: #575757;
            }

            .info {
                color: #2c7ffc;
                cursor: pointer;
            }

            input {
                all: unset;
                font-size: 18px;
                padding: 10px 15px;

                border: 1px solid black;
                border-radius: 8px;
                width: 100%;
                box-sizing: border-box;
            }
        }
    }

    .add-button {
        display: flex;
        align-items: center;
        justify-content: center;
        background: #2AB9D9;
        height: 50px;
        color: white;
        font-size: 16px;
        font-weight: bold;
        border-radius: 100px;
        cursor: pointer;

        &:hover {
            filter: brightness(0.96);
        }

        &.disabled {
            background: #F2F2F2;
            color: rgba(0, 0, 0, 0.47);
            font-weight: normal;
            pointer-events: none;
        }
    }

    .refund {
        display: flex;
        align-items: center;
        gap: 15px;
        padding: 15px;
        background: var(--secondary-hover-color);
        border-radius: 12px;

        .icon {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 42px;
            width: 42px;
            background: #D9D9D9;
            border-radius: 50%;
        }
    }

    .form-error.green {
        background: rgba(0, 128, 0, 0.1);
        color: green;
    }
}