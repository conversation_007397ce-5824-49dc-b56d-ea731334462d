<script lang="ts" src="./DeviceOnboardingScanComponent.ts" />

<style lang="sass" scoped>
@import './DeviceOnboardingScanComponent.scss'
</style>

<template>
    <div class="device-onboarding-scan-component">
        <form-modal-or-drawer
            title="Scan des terminaux en cours"
            subtitle="Retrouvez la liste des terminaux en demande de liaison sur votre réseau wifi"
            :state="opened"
            :fixed-buttons="true"
            @close="close()"
        >
            <template v-slot:content>
                <div v-if="updatingQuickLinks" class="loading-container">
                    <div class="loader"></div>
                </div>
                <div v-else-if="quickLinks.length === 0" class="empty-quick-links">
                    Aucun appareil trouvé
                </div>
                <div v-else class="container">
                    <span class="small-title"> Terminaux trouvés </span>
                    <div class="found-devices">
                        <div class="found-device" v-for="quickLink of quickLinks">
                            <div class="data">
                                <span class="id"> {{ quickLink.hardwareId }} </span>
                                <span class="name"> {{ quickLink.name }} - {{ quickLink.brand }} </span>
                            </div>

                            <button class="grey button" @click="importQuickLink(quickLink)">
                                <i class="fa-regular fa-circle-plus"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </template>

            <template v-slot:buttons>
                <div class="white button" @click="close()"> Fermer </div>
                <button class="button" @click="updateQuickLinkList()">
                    <i class="fa-regular fa-arrows-rotate"></i>
                    Actualiser
                </button>
            </template>

        </form-modal-or-drawer>
    </div>
</template>