<script lang="ts" src="./BadgingLocationFormComponent.ts">
</script>

<style lang="sass">
@use './BadgingLocationFormComponent.scss' as *
</style>

<template>
    <div class="badging-location-form-component">
        <form-modal-or-drawer
            :state="opened"
            :title="editingBadgingLocation ? 'Modifier le point de contrôle' : 'Ajouter un point de contrôle'"
            :subtitle="'Un point de contrôle permet d\'autoriser certains billets à accéder à un endroit spécifique'"
            @close="close()"
        >
            <template v-slot:content>
                <div class="input-group">
                    <label> Nom du point d'accès </label>

                    <div class="fluid input">
                        <input v-model="badgingLocation.name" class="fluid" placeholder="Nom du point d'accès" />
                    </div>
                </div>

                <div class="form-error" v-if="error">
                    <i class="fa-solid fa-exclamation-circle"></i>
                    <div class="details">
                        <span class="title"> Erreur </span>
                        <span class="description">{{ error }}</span>
                    </div>
                </div>
            </template>

            <template v-slot:buttons>
                <button type="button" class="white button" @click="close()"> Annuler </button>
                <button type="button" class="button" @click="create()">
                    <i v-if="!editingBadgingLocation" class="fa-regular fa-circle-plus"></i>
                    <i v-else class="fa-regular fa-pen-line fa-flip-horizontal"></i>
                    {{ editingBadgingLocation ? 'Valider' : 'Créer le point d\'accès' }}
                </button>
            </template>
        </form-modal-or-drawer>
    </div>
</template>