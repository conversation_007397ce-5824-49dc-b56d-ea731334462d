import type {Router, RouterRoute} from "@groupk/horizon2-front";

export type AnimatedRoute = RouterRoute & {
	animationIn?: string;
	animationOut?: string;
};

export type AnimationRouteChange = {
	name: string;
	classnameOldPageContainer?: string | null;
	classnameNewPageContainer?: string | null;
	oldPageContainerLevel?: "top" | "bottom";
	newPageContainerLevel?: "top" | "bottom";
	oldPageContainerDisplayAtStart?: "block" | "none";
	newPageContainerDisplayAtStart?: "block" | "none";
};

export class AnimationBetweenRouteChange {
	private router: Router;
	private oldPageContainer: HTMLElement;
	private newPageContainer: HTMLElement;
	private currentRoute: RouterRoute | AnimatedRoute | null = null;
	private endAnimationListenerExpectedCounter = 0;
	private endAnimationListenerCurrentCounter = 0;
	private endAnimationListener;

	private animationBaseValuesLink: Required<AnimationRouteChange> = {
		name: "",
		classnameOldPageContainer: null,
		classnameNewPageContainer: null,
		oldPageContainerLevel: "bottom",
		newPageContainerLevel: "top",
		oldPageContainerDisplayAtStart: "block",
		newPageContainerDisplayAtStart: "none",
	};
	private animationBaseValuesBrowser: Required<AnimationRouteChange> = {
		name: "",
		classnameOldPageContainer: null,
		classnameNewPageContainer: null,
		oldPageContainerLevel: "top",
		newPageContainerLevel: "bottom",
		oldPageContainerDisplayAtStart: "block",
		newPageContainerDisplayAtStart: "block",
	};

	private _onAnimationEnd: CallableFunction | null = null;

	constructor(router: Router) {
		this.router = router;
		this.oldPageContainer = document.getElementById("oldRouterContainer") as HTMLElement;
		this.newPageContainer = document.getElementById("mainRouterContainer") as HTMLElement;

		this.router.hookOnAsync("newPageLoaded", async (r) => {
			let previousRoute = this.currentRoute;
			this.currentRoute = r.newRoute;

			let animationNameToPlay: string | null = null;
			let routeToTakeForAnimation: RouterRoute | AnimatedRoute = r.newRoute;
			if (r.direction === "browser") routeToTakeForAnimation = previousRoute ?? r.newRoute;

			if ("animationIn" in routeToTakeForAnimation) {
				if (r.direction == "link") animationNameToPlay = routeToTakeForAnimation.animationIn ?? null;
				if (r.direction == "browser") animationNameToPlay = routeToTakeForAnimation.animationOut ?? null;

				if (animationNameToPlay === null && routeToTakeForAnimation.animationIn) {
					animationNameToPlay = routeToTakeForAnimation.animationIn + "Backwards";
				}
			}

			let animationToPlay = animationNameToPlay ? this.animationDictionary[animationNameToPlay] ?? null : null;
			// console.log('animation', animationToPlay, previousRoute);

			this.injectAnimationClean();
			if (animationToPlay && previousRoute !== null) {
				// console.log('play animation')
				this.injectAnimationStart(animationToPlay);

				await new Promise((resolve) => (this._onAnimationEnd = resolve));
				// start animation & resolve when done
				// console.log('play animation end')
			}
			this.injectAnimationEnd();
			return r;
		});

		this.endAnimationListener = () => {
			++this.endAnimationListenerCurrentCounter;
			if (this.endAnimationListenerCurrentCounter === this.endAnimationListenerExpectedCounter && this._onAnimationEnd) this._onAnimationEnd();
		};

		this.addAnimation({
			name: "slideFromLeft",
			classnameNewPageContainer: "animationSlideFromRightToLeft",
			classnameOldPageContainer: "animationSlideFromRightToLeft25Percent",
		});
	}

	private animationDictionary: {[name: string]: Required<AnimationRouteChange>} = {};

	addAnimation(animationLink: AnimationRouteChange, animationBrowser?: AnimationRouteChange) {
		let clonedAnimationLink: Required<AnimationRouteChange> = JSON.parse(JSON.stringify(animationLink));
		clonedAnimationLink = {...this.animationBaseValuesLink, ...clonedAnimationLink};

		let clonedAnimationBrowser: Required<AnimationRouteChange>;
		if (!animationBrowser) {
			clonedAnimationBrowser = {...this.animationBaseValuesBrowser};
			clonedAnimationBrowser.name = clonedAnimationLink.name + "Backwards";
			if (clonedAnimationLink.classnameNewPageContainer) clonedAnimationBrowser.classnameOldPageContainer = clonedAnimationLink.classnameNewPageContainer + "Backwards";
			if (clonedAnimationLink.classnameOldPageContainer) clonedAnimationBrowser.classnameNewPageContainer = clonedAnimationLink.classnameOldPageContainer + "Backwards";
		} else {
			clonedAnimationBrowser = {...this.animationBaseValuesLink, ...animationBrowser};
		}

		this.animationDictionary[clonedAnimationLink.name] = clonedAnimationLink;
		this.animationDictionary[clonedAnimationBrowser.name] = clonedAnimationBrowser;
	}

	private injectAnimationClean() {
		let newPageContainer = this.router.getContainerElement();
		// clean old animations & state
		this.oldPageContainer.classList.remove("pageOnTop");
		this.oldPageContainer.classList.remove("pageOnLower");
		newPageContainer.classList.remove("pageOnTop");
		newPageContainer.classList.remove("pageOnLower");
	}

	private injectAnimationStart(animationToPlay: AnimationRouteChange) {
		this.oldPageContainer.classList.add(animationToPlay.oldPageContainerLevel === "top" ? "pageOnTop" : "pageOnLower");
		this.newPageContainer.classList.add(animationToPlay.newPageContainerLevel === "top" ? "pageOnTop" : "pageOnLower");
		this.oldPageContainer.style.display = animationToPlay.oldPageContainerDisplayAtStart ?? 'block';
		this.newPageContainer.style.display = animationToPlay.newPageContainerDisplayAtStart ?? 'block';

		if (animationToPlay.classnameOldPageContainer) this.bindAnimationEndListener(this.oldPageContainer);
		if (animationToPlay.classnameNewPageContainer) this.bindAnimationEndListener(this.newPageContainer);

		setTimeout(() => {
			if (animationToPlay.classnameNewPageContainer) this.newPageContainer.classList.add(animationToPlay.classnameNewPageContainer);
			this.newPageContainer.style.display = "block";

			if (animationToPlay.classnameOldPageContainer) this.oldPageContainer.classList.add(animationToPlay.classnameOldPageContainer);
			this.oldPageContainer.style.display = "block";
		}, 0); // force to execute on next visual frame
	}

	private injectAnimationEnd() {
		this.unbindAnimationListener();
		this.cleanAnimationClassesOnElement(this.newPageContainer);
		this.cleanAnimationClassesOnElement(this.oldPageContainer);
		this.oldPageContainer.style.display = "none";
		this.endAnimationListenerExpectedCounter = 0;
		this.endAnimationListenerCurrentCounter = 0;
	}

	private bindAnimationEndListener(target: HTMLElement) {
		++this.endAnimationListenerExpectedCounter;
		target.addEventListener("animationend", this.endAnimationListener, false);
	}
	private unbindAnimationListener() {
		this.router.getContainerElement().removeEventListener("animationend", this.endAnimationListener, false);
		this.oldPageContainer.removeEventListener("animationend", this.endAnimationListener, false);
	}

	private cleanAnimationClassesOnElement(element: HTMLElement) {
		for (let i = 0; i < element.classList.length; ++i) {
			let clas = element.classList.item(i);
			if (clas && clas.startsWith("animation")) {
				element.classList.remove(clas);
				++i;
			}
		}
	}
}
