import {Component, Vue} from "vue-facing-decorator";
import {
	ContentHeaderComponent,
	LayoutContentWithRightPanelComponent,
	ModalCustomFilterComponent,
	ModalOrDrawerComponent,
	TableColumnsOrganizationComponent,
	ContentHeaderParameters,
	FormModalOrDrawerComponent,
	ForbiddenMessageComponent
} from "@groupk/vue3-interface-sdk";
import {
	CashlessPermissions,
	SimpleProductApiOut, SimpleProductCategoryApiIn,
	SimpleProductCategoryApiOut,
	ApplicationPermission,
	EstablishmentAccountPermissionModel,
	CashlessHttpSimpleProductContract, CashlessHttpSimpleProductCategoryContract
} from "@groupk/mastodon-core";
import {
	AutoWired, ScopedUuid, Uuid, UuidUtils, VisualScopedUuid,
} from "@groupk/horizon2-core";
import {TableColumnsRepository} from "../../../../../shared/repositories/TableColumnsRepository";
import {randomUUID, Router} from "@groupk/horizon2-front";
import {OptionBuilder} from "vue-facing-decorator/dist/optionBuilder";
import {
	Draggable, Sortable, SortableStopEvent,
} from "@shopify/draggable";
import {CategoriesRepository} from "../../../../../shared/repositories/CategoriesRepository";
import {PermissionBuilder, uuidScopeEstablishment, UuidScopeEstablishment} from "@groupk/mastodon-core";
import {ProductsRepository} from "../../../../../shared/repositories/ProductsRepository";
import SidebarStateListener from "../../../../../shared/utils/SidebarStateListener";
import CategoryFormComponent from "../../components/CategoryFormComponent/CategoryFormComponent.vue";
import ImportProductInCategoryComponent
	from "../../components/ImportProductInCategoryComponent/ImportProductInCategoryComponent.vue";
import {AppState} from "../../../../../shared/AppState";
import {ComponentUtils} from "../../../../../shared/utils/ComponentUtils";
import {CategoryFormComponentHasRequiredPermissions} from "../../components/CategoryFormComponent/CategoryFormComponent";

@Component({
	components: {
		'layout': LayoutContentWithRightPanelComponent,
		'content-header': ContentHeaderComponent,
		'modal-custom-filter': ModalCustomFilterComponent,
		'table-columns-organization': TableColumnsOrganizationComponent,
		'modal-or-drawer': ModalOrDrawerComponent,
		'category-form': CategoryFormComponent,
		'form-modal-or-drawer': FormModalOrDrawerComponent,
		'import-product-in-category': ImportProductInCategoryComponent,
		'forbidden-message': ForbiddenMessageComponent,
	}
})
export default class CategoriesView extends Vue {
	establishmentUid!: VisualScopedUuid<UuidScopeEstablishment>;

	categories: SimpleProductCategoryApiOut[] = [];
	products: SimpleProductApiOut[] = [];

	categorySearch: string = '';
	productSearch: string = '';

	selectedCategory: SimpleProductCategoryApiOut|null = null;
	forbidden: boolean = false;

	headerParameters: ContentHeaderParameters = {
		header: 'Catégories',
		subtitle: 'Catégorisez vos produits pour fluidifier le processus de vente',
		actions: [{
			type: 'SIMPLE_ACTION',
			id: 'new',
			name: 'Nouvelle catégorie',
			icon: 'fa-regular fa-circle-plus',
			callback: this.createCategory
		} as any],
		hideSearch: true,
		searchPlaceholder: 'Rechercher une catégorie'
	}

	refreshKey: string = randomUUID();

	draggable: Draggable | undefined = undefined;
	sortable: Sortable | undefined = undefined;

	editingCategory: SimpleProductCategoryApiOut|null = null;
	defaultParent: Uuid|null = null;
	showCreationModal: boolean = false;
	showImportProductsModal: SimpleProductCategoryApiOut|null = null;

	canWrite: boolean = false;
	loading: boolean = false;

	@AutoWired(ProductsRepository) accessor productsRepository!: ProductsRepository;
	@AutoWired(CategoriesRepository) accessor categoriesRepository!: CategoriesRepository;
	@AutoWired(TableColumnsRepository) accessor tableColumnsRepository!: TableColumnsRepository;
	@AutoWired(SidebarStateListener) accessor sidebarStateListener!: SidebarStateListener;
	@AutoWired(Router) accessor router!: Router;
	@AutoWired(AppState) accessor appState!: AppState;

	constructor(optionBuilder: OptionBuilder, vueInstance: any) {
		super(optionBuilder, vueInstance);
		let regexMatch = this.router.lastRouteRegexMatches;

		this.sidebarStateListener.setHiddenSidebar(false);

		if(window.innerWidth < 1400) this.sidebarStateListener.setMinimizedSidebar(true);
		else this.sidebarStateListener.setMinimizedSidebar(false);

		addEventListener("resize", this.resizeCallback);

		if (regexMatch && regexMatch[1]) {
			this.establishmentUid = UuidUtils.scopedToVisual<UuidScopeEstablishment>(regexMatch[1] as ScopedUuid<UuidScopeEstablishment>, uuidScopeEstablishment) ;

			this.loading = true;
		}

		this.canWrite = !!this.appState.hasPermission(PermissionBuilder(CashlessPermissions, 'SIMPLE-PRODUCT_UPDATE'))

		// Check permissions for form components and disable actions accordingly
		if (!ComponentUtils.hasPermissions(CategoryFormComponentHasRequiredPermissions)) {
			ComponentUtils.disableActions(this.headerParameters, ['new']);
		}
	}

	beforeUnmount() {
		removeEventListener("resize", this.resizeCallback);
	}

	resizeCallback(event: Event) {
		if(window.innerWidth < 1400) this.sidebarStateListener.setMinimizedSidebar(true);
		else this.sidebarStateListener.setMinimizedSidebar(false);
	}

	async mounted() {
		// Check for essential page functionality
		if(!ComponentUtils.hasPermissions((ownedPermissions: ApplicationPermission[]) => {
			return EstablishmentAccountPermissionModel.hasPermissionsForContracts(ownedPermissions, [
				CashlessHttpSimpleProductCategoryContract.list,
				CashlessHttpSimpleProductContract.list,
			])
		})) {
			this.forbidden = true;
			this.loading = false;
			return;
		}

		const response = await this.categoriesRepository.callContract('list', {establishmentUid: this.establishmentUid}, undefined);
		if(response.isSuccess()) {
			this.categories = response.success();
		} else {
			// Error
		}

		const productsResponse = await this.productsRepository.callContract('list', {establishmentUid: this.establishmentUid}, undefined);
		if(productsResponse.isSuccess()) {
			this.products = productsResponse.success();
		} else {
			// Error
		}

		this.loading = false;

		setTimeout(() => {
			this.initDraggable();
			this.initSwappable();
		}, 0)
	}

	createCategory() {
		this.showCreationModal = true;
	}

	getFilteredProducts() {
		return this.products.filter((product) => product.name.toLowerCase().includes(this.productSearch.toLowerCase()));
	}

	getTopLevelCategories() {
		return this.categories.filter((category) => category.parent === null);
	}

	getChildCategories(parentUid: string) {
		return this.categories
			.filter((category) => category.parent === parentUid)
			.filter((category) => category.name.toLowerCase().includes(this.categorySearch.toLowerCase()));
	}

	getCategoryProducts(category: SimpleProductCategoryApiOut) {
		return category.productIds.map((id) => this.getProductWithId(id));
	}

	getCategoryWithUid(uid: string) {
		const category = this.categories.find((category) => category.uid === uid);
		if(!category) throw new Error('missing_category');
		return category;
	}

	getProductWithId(id: number) {
		const product = this.products.find((product) => product.id === id);
		if(!product) throw new Error('missing_product');
		return product;
	}

	selectCategory(category: SimpleProductCategoryApiOut) {
		if(this.selectedCategory && this.selectedCategory.uid === category.uid) {
			this.selectedCategory = null;
			setTimeout(() => {
				this.initDraggable();
			}, 0)
			return;
		}
		this.selectedCategory = category;
	}

	initSwappable() {
		this.sortable = new Sortable(document.querySelectorAll('.swappable'), {
			draggable: '.product.copy',
		});

		let currentContainerDeleteZone: Element|null = null;
		this.sortable.on('drag:over:container', (e) => {
			const categoryUid = e.overContainer.dataset.uid;
			if(categoryUid) {
				if (currentContainerDeleteZone) currentContainerDeleteZone.classList.remove('displayed');
				currentContainerDeleteZone = null;

				const deleteZone = document.querySelectorAll(`[data-group=a${categoryUid}]`)[0];
				currentContainerDeleteZone = deleteZone;
				deleteZone.classList.add('displayed');
			}
		});

		this.sortable.on('sortable:stop', async (e: SortableStopEvent) => {
			this.sortable?.destroy();
			this.sortable = undefined;

			const oldCategory = this.getCategoryWithUid(e.oldContainer.dataset.uid ?? '');

			if(e.newContainer.classList.contains('delete-zone')) {
				// Dropzone is delete-zone
				oldCategory.productIds.splice(e.oldIndex, 1);
			} else {
				const newCategory = this.getCategoryWithUid(e.newContainer.dataset.uid ?? '');

				const productId = oldCategory.productIds.splice(e.oldIndex, 1)[0];
				if(!newCategory.productIds.includes(productId)) {
					if (e.newIndex >= newCategory.productIds.length) {
						newCategory.productIds.push(productId);
					} else {
						newCategory.productIds.splice(e.newIndex, 0, productId);
					}
				}

				try {
					this.saveCategory(newCategory);
				} catch(err) {}
			}

			try {
				this.saveCategory(oldCategory);
			} catch(err) {}

			if(currentContainerDeleteZone) {
				currentContainerDeleteZone.classList.remove('displayed');
				currentContainerDeleteZone = null;
			}

			this.refreshKey = randomUUID();

			this.$nextTick(() => {
				this.initSwappable();
				this.initDraggable();
			});
		});
	}

	initDraggable() {
		if (this.draggable) this.draggable.destroy();
		const dropzones = document.querySelectorAll('.drag-container');

		this.draggable = new Draggable(dropzones, {
			draggable: '.product.og',
		});

		let currentContainer: HTMLElement|null = null;
		this.draggable.on('drag:over:container', (e) => {
			currentContainer = e.overContainer;
			currentContainer.classList.add('hovered');
		});

		this.draggable.on('drag:out:container', (e) => {
			if(currentContainer) currentContainer.classList.remove('hovered');
			currentContainer = null;
		});

		this.draggable.on('drag:stop', async (e) => {
			if(!e.originalSource.dataset.id) return;
			const productId = parseInt(e.originalSource.dataset.id, 10);

			if(currentContainer) {
				const category = this.getCategoryWithUid(currentContainer.dataset.uid ?? '');
				if(!category.productIds.includes(productId)) {
					category.productIds.push(productId)
				}

				currentContainer.classList.remove('hovered');
				currentContainer = null;

				try {
					const categoryApiIn = new SimpleProductCategoryApiIn(category);
					await this.categoriesRepository.callContract('update', {establishmentUid: this.establishmentUid, categoryUid: category.uid}, categoryApiIn);
				} catch(err) {}
			}

			this.$nextTick(() => {
				this.initDraggable();
			});
		});
	}

	async toggleProductInCategory(category: SimpleProductCategoryApiOut, product: SimpleProductApiOut) {
		const index = category.productIds.indexOf(product.id);
		if(index !== -1) {
			category.productIds.splice(index, 1);
		} else {
			category.productIds.push(product.id);
		}

		this.saveCategory(category);
	}

	createdCategory(category: SimpleProductCategoryApiOut) {
		this.categories.push(category);
		this.showCreationModal = false;
		this.editingCategory = null;
	}

	updatedCategory(updatedCategory: SimpleProductCategoryApiOut) {
		const index = this.categories.findIndex((category) => category.uid === updatedCategory.uid);
		if(index !== -1) {
			this.categories.splice(index, 1, updatedCategory);
		} else this.categories.push(updatedCategory);

		this.showCreationModal = false;
		this.editingCategory = null;
	}

	deletedCategory(deletedCategory: SimpleProductCategoryApiOut) {
		const index = this.categories.findIndex((category) => category.uid === deletedCategory.uid);
		if(index !== -1) {
			this.categories.splice(index, 1);
		}

		this.showCreationModal = false;
		this.editingCategory = null;
	}

	async saveCategory(category: SimpleProductCategoryApiOut) {
		const categoryApiIn = new SimpleProductCategoryApiIn(category);
		await this.categoriesRepository.callContract('update', {establishmentUid: this.establishmentUid, categoryUid: category.uid}, categoryApiIn);
	}
}