import {OrderPaymentApiOut} from "@groupk/mastodon-core";
import {EventEmitter} from "@groupk/horizon2-core";

export interface PhysicalPaymentProtocol extends EventEmitter<{
	'totalPaidChanged': number,
	'totalRefundChanged': number,
	'stateChanged': string,
	'done': boolean
}> {
	config: PaymentProtocolConfig,
	initiatePayment: (payment: OrderPaymentApiOut) => void,
	cancel: () => void,
}

export type PaymentProtocolConfig = {
	displayLivePaidAmount: boolean,
	autoCloseOnSuccess: boolean,
	cancelable: boolean
}