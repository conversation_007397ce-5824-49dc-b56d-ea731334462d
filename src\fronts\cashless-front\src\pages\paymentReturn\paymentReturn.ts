import {Component, Vue} from "vue-facing-decorator";
import {
	PaymentStatus, PublicOnlineFundingApiOut, uuidScopeCashless_onlineFunding,
} from "@groupk/mastodon-core";
import {AutoWired, ScopedUuid, UuidUtils} from "@groupk/horizon2-core";
import {Router} from "@groupk/horizon2-front";
import {AppState} from "../../../../../shared/AppState";
import {CashlessCustomerRepository} from "../../../../../shared/repositories/CashlessCustomerRepository";
import {UuidScopeCashless_onlineFunding} from "@groupk/mastodon-core";
import {EstablishmentUrlBuilder} from "../../../../../shared/utils/EstablishmentUrlBuilder";

@Component({})
export default class PaymentReturnView extends Vue {
	funding!: PublicOnlineFundingApiOut;

	loading: boolean = true;

	@AutoWired(Router) private accessor router!: Router;
	@AutoWired(CashlessCustomerRepository) private accessor cashlessCustomerRepository!: CashlessCustomerRepository;
	@AutoWired(AppState) private accessor appState!: AppState;

	async mounted() {
		let regexMatch = this.router.lastRouteRegexMatches;
		if (regexMatch && regexMatch[1] && regexMatch[2]) {
			const fundingUid = UuidUtils.scopedToVisual(regexMatch[2] as ScopedUuid<UuidScopeCashless_onlineFunding>, uuidScopeCashless_onlineFunding);

			this.funding = (await this.cashlessCustomerRepository.callContract('getFunding', {
				establishmentUid: this.appState.requireUrlEstablishmentUid(),
				onlineFundingUid: fundingUid
			}, undefined)).success();
		}

		await this.checkStatus(this.funding);
	}

	async checkStatus(funding: PublicOnlineFundingApiOut|null = null) {
		if(!funding) {
			funding = (await this.cashlessCustomerRepository.callContract('getFunding', {
				establishmentUid: this.appState.requireUrlEstablishmentUid(),
				onlineFundingUid: this.funding.uid
			}, undefined)).success();
		}

		if(funding.onlinePayment.status === PaymentStatus.SUCCESS) {
			this.loading = false;
		} else if(funding.onlinePayment.status === PaymentStatus.ERROR) {
			this.loading = false;
		} else {
			setTimeout(() => {
				this.checkStatus();
			}, 3 * 1000);
		}
	}

	getIndexUrl() {
		return EstablishmentUrlBuilder.buildUrl('/');
	}
}