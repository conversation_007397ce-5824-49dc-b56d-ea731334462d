#transactions-statistics-page {
    height: 100%;
    display: flex;

    .layout {
        width: 100%;
    }

    .calendar-modal-dimmer {
        position: fixed;
        inset: 0;
        z-index: 998;
    }

    .calendar-modal {
        background: white;
        width: 300px;
        padding: 20px;
        right: 0;
    }

    .layout {
        width: 100%;
    }

    .right-panel{
        display: flex;
        flex-direction: column;
        gap: 20px;
        padding: 40px;

        @media (max-width: 900px) {
            padding: 20px;
        }
    }

    .small-button-group {
        display: flex;

        >:first-child {
            border-top-right-radius: 0;
            border-bottom-right-radius: 0;
            border-right: none;
        }

        >:last-child, >:last-child > .button {
            border-top-left-radius: 0;
            border-bottom-left-radius: 0;
        }
    }

    .data-actions {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        gap: 10px;

        .small.white.button {
            padding: 8px 15px;
        }
    }

    .print-area {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        gap: 10px;
    }

    .dropdown.small.white.button {
        padding: 0;

        &.opened:hover {
            background: white;
        }
    }

    .ca-chart {
        height: 150px !important;
    }

    .legend {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        gap: 10px;
        padding: 10px 23px;

        .item {
            display: flex;
            align-items: center;
            gap: 10px;

            .line {
                width: 15px;
                border-bottom: 2px solid #003dff;

                &.other {
                    border-bottom: 2px solid #bbc9fd;
                }

                &.red {
                    border-bottom: 2px solid var(--error-color);
                }
            }

            span {
                font-size: 12px;
                line-height: 18px;
            }
        }
    }

    .page-content {
        gap: 20px;
    }

    .context {
        display: flex;
        justify-content: flex-end;
        align-items: center;

        @media (max-width: 1100px) {
            flex-direction: column;
            align-items: flex-start;
            gap: 20px;

            .data-actions {
                flex-direction: column;
                align-items: flex-end;
                gap: 20px;
                width: 100%;
            }
        }
    }

    .day-mode {
        display: flex;
        justify-content: flex-end;
        margin-bottom: 40px;

    }

    .comparison {
        display: flex;
        justify-content: flex-end;
    }

    .button-group {
        border-radius: 8px;
        display: flex;
        border: 1px solid #E2E2E2;

        &.small {
            div {
                font-size: 12px;
                font-weight: 500;
                padding: 8px 15px;
            }
        }

        div {
            padding: 10px 15px;
            border-right: 1px solid #E2E2E2;
            cursor: pointer;
            font-size: 14px;
            user-select: none;
            flex-grow: 2;
            text-align: center;

            &:first-child {
                border-radius: 8px 0 0 8px;
            }

            &:last-child {
                border: none;
                border-radius: 0 8px 8px 0;
            }

            &:hover {
                background: var(--secondary-hover-color);
            }

            &.active {
                background: #F7F7F8;
                font-weight: 600;
            }
        }
    }

    .text-separator {
        font-size: 16px;
        font-weight: 600;
    }

    .key-numbers {
        display: grid;
        grid-template-columns: 1fr 1fr 1fr;
        gap: 20px;

        @media (max-width: 900px) {
            grid-template-columns: 1fr;
        }

        .key-number {
            display: flex;
            flex-direction: column;
            gap: 10px;
            padding: 25px;
            border: 1px solid #E2E2E2;
            border-radius: 8px;

            .type {
                font-size: 14px;
            }

            .value {
                font-size: 24px;
                font-weight: 700;
            }

            .indicator {
                display: flex;
                align-items: center;
                gap: 4px;
                color: #079455;
                font-weight: 600;
                font-size: 14px;

                span {
                    color: #545454;
                    font-size: 12px;
                    font-weight: 500;
                }
            }
        }
    }

    .frequenting-charts {
        display: grid;
        grid-template-columns: 1fr 1fr;
        grid-gap: 20px;
        width: 100%;

        @media (max-width: 900px) {
            grid-template-columns: 1fr;
        }

        .frequenting-chart {
            display: flex;
            flex-direction: column;
            gap: 20px;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid var(--border-color);

            h3 {
                margin: 0;
                font-size: 16px;
                font-weight: 600;
            }

            canvas {
                max-width: 100%;
                height: 250px !important;
            }
        }
    }

    .employee-charts {
        display: grid;
        grid-template-columns: 1fr 1fr;
        grid-gap: 20px;

        @media (max-width: 900px) {
            grid-template-columns: 1fr;
        }

        .dough-no-data {
            height: 160px;
            width: 160px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .chart-group {
            display: flex;
            align-items: center;
            gap: 20px;
            padding: 10px;

            .description {
                display: flex;
                flex-direction: column;
                gap: 10px;

                span {
                    font-size: 14px;
                }

                .title {
                    font-size: 16px;
                    font-weight: 700;
                }
            }

            .chart {
                height: 200px !important;
            }
        }
    }

    .total {
        font-weight: bold;
    }

    td.break-all {
        word-break: break-word;
    }

    .table-actions {
        display: flex;
        justify-content: flex-end;
    }

    .payment-methods {
        display: grid;
        grid-template-columns: 1fr 1fr 1fr;
        grid-gap: 10px;

        .payment-method {
            display: flex;
            align-items: center;
            gap: 10px;

            * {
                cursor: pointer;
            }

            input {
                width: auto;
            }

            label {
                flex-grow: 2;
            }
        }
    }

    .table-scroll-wrapper {
        overflow: auto;

        .no-break {
            white-space: nowrap;
        }

        &.no-margin {
            margin-right: -40px;
            padding-right: 40px;
        }
    }

    .sort-icon {
        font-size: 10px;
        margin-left: 2px;
    }
}