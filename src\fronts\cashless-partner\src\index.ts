import "./assets/css/global.scss";
import "./assets/css/vueInterface/layout.scss";
import "./assets/css/vueInterface/buttons.scss";
import "./assets/css/vueInterface/labels.scss";
import "./assets/css/vueInterface/TableComponent.scss";
import SidebarView from "./pages/sidebar/sidebar.vue";
import {createApp} from "vue";
import "@groupk/font-awesome-sdk/src/V6/css/pro-v4-font-face.min.css";
import "@groupk/font-awesome-sdk/src/V6/css/pro-v4-shims.min.css";
import "@groupk/font-awesome-sdk/src/V6/css/pro.min.css";
import "@groupk/vue3-interface-sdk/dist/style.css";
import {GlobalFilters} from "../../../shared/filters/GlobalFilters";
import TopBarView from "./pages/topbar/topbar.vue";
import {AnimatedRoute, AnimationBetweenRouteChange} from "../../../shared/routing/AnimatedRouter";
import {
	Router,
	RouterRoute,
	RouterStateConfigVersionModifier,
	VueRouteFactory,
	VueRouteOptions
} from "@groupk/horizon2-front";
import {GetInstance, ScopedUuid, setExecutionContext, SetInstance, UuidUtils} from "@groupk/horizon2-core";
import {VueAuthedRouteFactory} from "../../../shared/routing/AuthedVueJsRoute";
import {EstablishmentUrlBuilder} from "../../../shared/utils/EstablishmentUrlBuilder";
import {CashlessWorker} from "./CashlessWorker";
import {
	CashlessAppId,
	uuidScopeEstablishment,
	UuidScopeEstablishment
} from "@groupk/mastodon-core";
import {CrispManager} from "../../../shared/utils/CrispManager";
import {Configuration, MainConfig} from "../../../shared/MainConfig";
import {AuthStateModel} from "../../../shared/AuthStateModel";
import {AppState} from "../../../shared/AppState";
import assetsPlugin from "../../../shared/routing/assetsPlugin";

let mainConfig: MainConfig = GetInstance(MainConfig);

mainConfig.init().then(async function(configFromFile: Configuration) {
	if(configFromFile.develop) {
		setExecutionContext({
			debug: true
		});
	}

	const appState = GetInstance(AppState);
	appState.platformId = 'Cashless';

	const authStateModel = new AuthStateModel(configFromFile.mastodonApiEndpoint, false);
	try {
		await authStateModel.getState(true);
	} catch(err){}
	SetInstance(AuthStateModel, authStateModel);

	startApp(configFromFile);
});

function startApp(config: Configuration) {
	let router = new Router<AnimatedRoute | RouterRoute>({
		prefix: "/",
		registerGlobalInterceptor: true,
		container: "mainRouterContainer",
		oldContainer: "oldRouterContainer",
		disableRouteTriggerIfIdentical: true,
	});
	if (window.innerWidth < 900) {
		new AnimationBetweenRouteChange(router);
	}

	router.addHook(new RouterStateConfigVersionModifier(13));

	let uuidRegex = /([0-9a-f]{8}\-[0-9a-f]{4}\-[0-9a-f]{4}\-[0-9a-f]{4}\-[0-9a-f]{12})/;
	const vueRouteOptions: Partial<VueRouteOptions> = {
		filters: GlobalFilters,
		hookAppCreated(app) {
			app.use(assetsPlugin);
		},
	};

	router.addRoute({regex: new RegExp(/establishment\//.source + uuidRegex.source + /\/auth/.source), loader: () => import('./pages/auth/auth.vue').then((vue) => VueRouteFactory(vue.default, vueRouteOptions))});
	router.addRoute({regex: new RegExp(/establishment\//.source + uuidRegex.source + /\/devices/.source), loader: () => import('./pages/devices/devices.vue').then((vue) => VueAuthedRouteFactory(vue.default, vueRouteOptions))});
	router.addRoute({regex: new RegExp(/establishment\//.source + uuidRegex.source + /\/$/.source), loader: () => import('./pages/index/index.vue').then((vue) => VueAuthedRouteFactory(vue.default, vueRouteOptions))});
	router.addRoute({regex: new RegExp(/establishment\//.source + uuidRegex.source + /\/categories/.source), loader: () => import('./pages/categories/categories.vue').then((vue) => VueAuthedRouteFactory(vue.default, vueRouteOptions))});
	router.addRoute({regex: new RegExp(/establishment\//.source + uuidRegex.source + /\/products/.source), loader: () => import('./pages/products/products.vue').then((vue) => VueAuthedRouteFactory(vue.default, vueRouteOptions))});
	router.addRoute({regex: new RegExp(/establishment\//.source + uuidRegex.source + /\/wallets/.source), loader: () => import('./pages/wallets/wallets.vue').then((vue) => VueAuthedRouteFactory(vue.default, vueRouteOptions))});
	router.addRoute({regex: new RegExp(/establishment\//.source + uuidRegex.source + /\/kiosk-fundings/.source), loader: () => import('./pages/kioskFunding/kioskFunding.vue').then((vue) => VueAuthedRouteFactory(vue.default, vueRouteOptions))});
	router.addRoute({regex: new RegExp(/establishment\//.source + uuidRegex.source + /\/transactions-statistics/.source), loader: () => import('./pages/transactionsStatistics/transactionsStatistics.vue').then((vue) => VueAuthedRouteFactory(vue.default, vueRouteOptions))});
	router.addRoute({regex: new RegExp(/establishment\//.source + uuidRegex.source + /\/transactions/.source), loader: () => import('./pages/transactions/transactions.vue').then((vue) => VueAuthedRouteFactory(vue.default, vueRouteOptions))});
	router.addRoute({regex: new RegExp(/establishment\//.source + uuidRegex.source + /\/customers/.source), loader: () => import('./pages/customers/customers.vue').then((vue) => VueAuthedRouteFactory(vue.default, vueRouteOptions))});
	router.addRoute({regex: new RegExp(/establishment\//.source + uuidRegex.source + /\/profiles/.source), loader: () => import('./pages/profiles/profiles.vue').then((vue) => VueAuthedRouteFactory(vue.default, vueRouteOptions))});
	router.addRoute({regex: new RegExp(/establishment\//.source + uuidRegex.source + /\/profile\//.source + uuidRegex.source), loader: () => import('./pages/profile/profile.vue').then((vue) => VueAuthedRouteFactory(vue.default, vueRouteOptions))});
	router.addRoute({regex: new RegExp(/establishment\//.source + uuidRegex.source + /\/settings/.source), loader: () => import('./pages/settings/settings.vue').then((vue) => VueAuthedRouteFactory(vue.default, vueRouteOptions))});
	router.addRoute({regex: new RegExp(/establishment\//.source + uuidRegex.source + /\/sample-report/.source), loader: () => import('./pages/sampleReport/sampleReport.vue').then((vue) => VueAuthedRouteFactory(vue.default, vueRouteOptions))});
	router.addRoute({regex: new RegExp(/establishment\//.source + uuidRegex.source + /\/pricing/.source), loader: () => import('./pages/pricing/pricing.vue').then((vue) => VueAuthedRouteFactory(vue.default, vueRouteOptions))});
	router.addRoute({regex: new RegExp(/establishment\//.source + uuidRegex.source + /\/ask-refund/.source), loader: () => import('./pages/askRefund/askRefund.vue').then((vue) => VueRouteFactory(vue.default, vueRouteOptions))});
	router.addRoute({regex: new RegExp(/establishment\//.source + uuidRegex.source + /\/refund-requests/.source), loader: () => import('./pages/refundRequests/refundRequests.vue').then((vue) => VueAuthedRouteFactory(vue.default, vueRouteOptions))});
	router.addRoute({regex: new RegExp(/establishment\//.source + uuidRegex.source + /\/refund-batches/.source), loader: () => import('./pages/refundBatches/refundBatches.vue').then((vue) => VueAuthedRouteFactory(vue.default, vueRouteOptions))});
	router.addRoute({regex: new RegExp(/establishment\//.source + uuidRegex.source + /\/package-tracking/.source), loader: () => import('./pages/packageTracking/packageTracking.vue').then((vue) => VueAuthedRouteFactory(vue.default, vueRouteOptions))});
	router.addRoute({regex: new RegExp(/establishment\//.source + uuidRegex.source + /\/.*/.source), loader: () => import('./pages/notFound/notFound.vue').then((vue) => VueRouteFactory(vue.default, vueRouteOptions))});
	router.addRoute({regex: new RegExp(/pricing/.source), loader: () => import('./pages/pricing/pricing.vue').then((vue) => VueAuthedRouteFactory(vue.default, vueRouteOptions))});
	router.addRoute({regex: new RegExp(/.*/.source), loader: () => import('./pages/casRedirect/casRedirect.vue').then((vue) => VueRouteFactory(vue.default, vueRouteOptions))});

	SetInstance(Router, router);

	if(mainConfig.configuration.gtagId) {
		const appState = GetInstance(AppState);
		// appState.googleAnalyticsTrackers.push(new GoogleAnalyticsTracker(mainConfig.configuration.gtagId));
	}

	router.updateCurrentPageFromCurrentLocation().then(async () => {
		CrispManager.initiate();

		const sidebar = createApp(SidebarView);
		sidebar.mount("#sidebar");

		const topBar = createApp(TopBarView);
		topBar.mount("#top-bar");

		const router = GetInstance(Router);

		let regexMatch = router.lastRouteRegexMatches;
		if (!regexMatch || !regexMatch[1]) {
			if(!window.location.pathname.startsWith('/not-found')) {
				await router.changePage('not-found');
				await router.updateCurrentPageFromCurrentLocation()
			}
			return;
		}

		const cashlessWorker = new CashlessWorker(UuidUtils.scopedToVisual(regexMatch[1] as ScopedUuid<UuidScopeEstablishment>, uuidScopeEstablishment));
		SetInstance(CashlessWorker, cashlessWorker);
		cashlessWorker.start();

		const isEnabled = await EstablishmentUrlBuilder.isPlatformEnabled(CashlessAppId);
		if(isEnabled.establishmentUid && !isEnabled.enabled) {
			if(!window.location.pathname.includes('/pricing')) {
				await router.changePage('pricing');
				await router.updateCurrentPageFromCurrentLocation();
			}
			return;
		}
	});
}
