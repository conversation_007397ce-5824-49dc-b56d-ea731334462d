.order-list-component {
    display: flex;
    height: 100%;
    width: 100%;
    user-select: none;

    .left {
        display: flex;
        flex-direction: column;
        gap: 20px;
        flex-grow: 2;
        height: 100%;
        padding: 20px;
        box-sizing: border-box;
        overflow: auto;

        .head {
            display: flex;
            align-items: flex-start;
            justify-content: space-between;

            .white.button {
                border: none;
            }

            h2 {
                margin: 10px 0 0 0;
                font-size: 20px;
                font-weight: 600;
            }

            .buttons {
                margin: 0;
            }

            .button-group {
                display: flex;

                button {
                    all: unset;
                    display: flex;
                    align-items: center;
                    gap: 10px;
                    background: white;
                    padding: 12px;

                    &.selected {
                        background: var(--primary-color);
                        color: var(--primary-text-color);
                    }

                    &:first-child {
                        border-radius: 8px 0 0 8px;
                    }

                    &:last-child {
                        border-radius: 0 8px 8px 0;
                    }
                }
            }
        }

        .filter.input-group {
            .dropdown-component {
                background: white;
                font-size: 16px;
                padding: 2px;
            }
        }

        .pages {
            display: flex;
            justify-content: flex-end;
            align-items: center;
            gap: 5px;

            span {
                font-size: 16px;
                margin-right: 10px;
            }

            .button {
                i {
                    font-size: 16px;
                }
            }

            .button:hover {
                background: var(--secondary-hover-color);
            }

            .button.no-pointer-event {
                pointer-events: none;
            }
        }

        &::-webkit-scrollbar {
            display: none;
        }

        table {
            background: white;
            border: none;

            .empty-row {
                text-align: center;
            }

            tr {
                position: relative;

                td {
                    padding: 15px;
                }
            }

            * {
                font-size: 16px;
            }
        }
    }

    .form-error {
        .red.button {
            flex-shrink: 0;
        }
    }

    .right {
        flex-shrink: 0;
        width: var(--sidebar-width);
        background: white;
    }


    .button-group {
        display: flex;

        button {
            all: unset;
            display: flex;
            align-items: center;
            gap: 10px;
            background: white;
            padding: 12px;

            &.selected {
                background: var(--primary-color);
                color: var(--primary-text-color);
            }

            &:first-child {
                border-radius: 8px 0 0 8px;
            }

            &:last-child {
                border-radius: 0 8px 8px 0;
            }
        }
    }

    .tables-area {
        display: flex;
        flex-direction: column;
        gap: 20px;
        background: white;
        border-radius: 12px;
        padding: 20px;

        .tables-group {
            display: flex;
            flex-direction: column;
            gap: 10px;

            .tables {
                display: grid;
                grid-template-columns: 1fr 1fr 1fr 1fr 1fr;
                grid-gap: 10px;

                .table {
                    display: flex;
                    flex-direction: column;
                    gap: 15px;
                    align-items: center;
                    justify-content: center;
                    background: #F2F4F7;
                    padding: 10px;
                    border-radius: 8px;
                    aspect-ratio: 1/1;

                    .name {
                        font-weight: 500;
                        padding: 6px 12px;
                        background: white;
                        border-radius: 8px;
                    }

                    &.blue {
                        background: rgba(0, 102, 255, 0.1);

                        .name {
                            background: #06F;
                            color: white;
                        }
                    }

                    .guests {
                        display: flex;
                        align-items: center;
                        gap: 5px;

                        i {
                            font-size: 18px;
                        }

                        .infos {
                            display: flex;
                            flex-direction: column;
                            align-items: center;

                            .number {
                            }
                        }
                    }
                }
            }
        }
    }

    .legend-container {
        display: flex;
        justify-content: flex-end;
        gap: 20px;
        background: white;

        .legend {
            display: flex;
            align-items: center;
            gap: 10px;

            .color {
                height: 8px;
                width: 8px;
                border-radius: 50%;
                background: #F2F4F7;

                &.blue {
                    background: #06F;
                }
            }
        }
    }
}