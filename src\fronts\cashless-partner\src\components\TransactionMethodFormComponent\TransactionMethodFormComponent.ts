import {Component, Prop, Vue} from "vue-facing-decorator";
import {DropdownComponent, DropdownValue, FormModalOrDrawerComponent} from "@groupk/vue3-interface-sdk";
import {AutoWired, VisualScopedUuid} from "@groupk/horizon2-core";
import {
	TransactionApiOut,
	ApplicationPermission,
	EstablishmentAccountPermissionModel,
	PaymentHttpMethodContract,
	CashlessHttpTransactionContract,
} from "@groupk/mastodon-core";
import {TransactionsRepository} from "../../../../../shared/repositories/TransactionsRepository";
import {PaymentMethodsRepository} from "../../../../../shared/repositories/PaymentMethodsRepository";
import {PaymentMethodApiOut} from "@groupk/mastodon-core";
import {TransactionFixApiIn} from "@groupk/mastodon-core";
import {UuidScopePayment_method} from "@groupk/mastodon-core";
import {AppState} from "../../../../../shared/AppState";

export function TransactionMethodFormComponentHasRequiredPermissions(ownedPermissions: ApplicationPermission[]): boolean {
	return EstablishmentAccountPermissionModel.hasPermissionsForContracts(ownedPermissions, [
		PaymentHttpMethodContract.list,
		CashlessHttpTransactionContract.fix,
	]);
}

@Component({
	components: {
		'form-modal-or-drawer': FormModalOrDrawerComponent,
		'dropdown': DropdownComponent
	},
	emits: ['close']
})
export default class TransactionMethodFormComponent extends Vue {
	@Prop() transaction!: TransactionApiOut;

	paymentMethods: PaymentMethodApiOut[] = [];
	selectedPaymentMethod: VisualScopedUuid<UuidScopePayment_method>|null = null;

	opened: boolean = false;
	loading: boolean = true;
	error: string|null = null;

	@AutoWired(PaymentMethodsRepository) accessor paymentMethodsRepository!: PaymentMethodsRepository;
	@AutoWired(TransactionsRepository) accessor transactionsRepository!: TransactionsRepository;
	@AutoWired(AppState) accessor appState!: AppState;

	async mounted() {
		setTimeout(() => this.opened = true, 0);

		this.selectedPaymentMethod = this.transaction.paymentMethod;

		this.paymentMethods = (await this.paymentMethodsRepository.callContract(
			'list',
			{establishmentUid: this.appState.requireUrlEstablishmentUid()},
			undefined)
		).success();

		this.loading = false;
	}

	get paymentMethodsDropdownValues(): DropdownValue[] {
		return this.paymentMethods.map((paymentMethod) => {
			return {
				name: paymentMethod.name,
				value: paymentMethod.uid
			}
		});
	}

	close() {
		this.opened = false;
		setTimeout(() => this.$emit('close'), 300);
	}

	async validate() {
		this.error = null;

		if(!this.selectedPaymentMethod) {
			this.error = 'Veuillez sélectionner un moyen de paiement';
			return;
		}

		this.loading = true;

		const response = await this.transactionsRepository.callContract(
			'fix',
			{establishmentUid: this.appState.requireUrlEstablishmentUid(), transactionUid: this.transaction.uid},
			new TransactionFixApiIn({
				paymentMethod: this.selectedPaymentMethod
			})
		);

		if (response.isSuccess()) {
			this.$emit('updated', response.success())
            this.close();
        } else {
            const error = response.error();
			if(error && 'error' in error) {
				if(error.error === 'tx_creation_weird_state') {
					this.error = 'tx_creation_weird_state';
				}
				if(error.error === 'unknown_profile') {
					this.error = 'unknown_profile';
				}
			} else {
				this.error = 'Une erreur inconnue est survenue';
			}
        }

		this.loading = false;
	}
}