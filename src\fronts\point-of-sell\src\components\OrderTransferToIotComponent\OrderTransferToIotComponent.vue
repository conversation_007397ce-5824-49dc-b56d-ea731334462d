<script lang="ts" src="./OrderTransferToIotComponent.ts" />

<style lang="sass" scoped>
@import './OrderTransferToIotComponent.scss'
</style>

<template>
    <div class="right-modal-dimmer"></div>
    <div class="order-transfer-to-iot-component">
        <right-modal
            :bottom-button-text="'Annuler'"
            :error="error"
            @bottom-button-clicked="close()"
        >
            <div class="container">
                <div class="head">
                    <span class="title"> Transférer la commande </span>
                    <span class="description"> Liste des terminaux disponibles pour un transfert </span>
                </div>

                <div class="empty" v-if="posDevices.length === 0">
                    Aucun appareil
                </div>

                <div class="iot-establishment-devices">
                    <div class="device" v-for="device in posDevices">
                        <div class="favorite-button" @click="toggleFavorite(device.uid)">
                            <i class="fa-regular fa-star" v-if="!favorites.includes(device.uid)"></i>
                            <i class="fa-solid fa-star" v-else></i>
                        </div>

                        <div class="left">
                            <div class="name"> {{ posState.requireDeviceWithUid(device.deviceUid).brand }} </div>
                            <div class="id"> {{ posState.requireDeviceWithUid(device.deviceUid).brand }} </div>
                        </div>

                        <button class="grey button" :class="{loading: creatingTransfer === device.uid, disabled: creatingTransfer}" @click="transferToIot(device.uid)"> Envoyer </button>
                    </div>
                </div>
            </div>
        </right-modal>
    </div>
</template>