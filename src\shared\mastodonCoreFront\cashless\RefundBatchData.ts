import {RefundBatchApiIn} from "@groupk/mastodon-core";
import {ScopedUuid} from "@groupk/horizon2-core";
import {UuidScopeCashless_refund} from "@groupk/mastodon-core";
import {SepaSettingsApi} from "@groupk/mastodon-core";

export class RefundBatchData {
    refundUidList: ScopedUuid<UuidScopeCashless_refund>[] | undefined;
    sepaConfig: SepaSettingsData= new SepaSettingsData();

    constructor() {
    }

    toApiIn() {
        return new RefundBatchApiIn({
            refundUidList: this.refundUidList,
            sepaConfig: this.sepaConfig.toApiIn()
        }
    )
    }
}

export class SepaSettingsData {
    orgId: string = "";
    orgName: string = "";
    orgIban: string = "";
    orgBic: string = "";
    batchBooking: boolean | null = null;

    constructor() {
    }

    toApiIn() {
        return new SepaSettingsApi({
                orgId: this.orgId,
                orgName: this.orgName,
                orgIban: this.orgIban,
                orgBic: this.orgBic,
                batchBooking: this.batchBooking
            }
        )
    }
}