#refund-batches-page {
    box-sizing: border-box;
    height: 100%;
    .link {
        color: #2c7ffc;
        cursor: pointer;
        text-decoration: underline;
    }

    .right-loading {
        height: 100%;
    }

    .page-content {
        .ticket-data {
            display: flex;
            align-items: center;
            gap: 15px;

            .ticket-icon {
                display: flex;
                justify-content: center;
                align-items: center;
                height: 32px;
                width: 32px;
                background: var(--secondary-hover-color);
                border-radius: 4px;
            }

            .ticket-name {
                display: flex;
                flex-direction: column;
                gap: 4px;
                font-weight: 500;

                .ticket-description {
                    font-size: 13px;
                    color: #494949;
                    font-weight: 400;
                }
            }
        }
    }

    .selected-batch {
        padding: 40px;

        @media (max-width: 900px) {
            padding: 20px;
        }

        .content {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .ticket-data {
            display: flex;
            align-items: center;
            gap: 15px;

            .ticket-icon {
                flex-shrink: 0;
                display: flex;
                align-items: center;
                justify-content: center;
                background: var(--secondary-hover-color);
                font-size: 12px;
                font-weight: 600;
                border-radius: 50%;
                height: 30px;
                width: 30px;
                text-transform: uppercase;
            }

            .ticket-name {
                display: flex;
                flex-direction: column;
                gap: 4px;
                font-weight: 500;
                text-transform: capitalize;

                .ticket-description {
                    font-size: 13px;
                    color: #494949;
                    font-weight: 400;
                    text-transform: lowercase;
                }
            }
        }

        .block {
            display: flex;
            flex-direction: column;
            gap: 10px;

            h3 {
                margin: 0;
                font-size: 16px;
            }

            .table-scroll-wrapper {
                overflow: auto;
            }
        }
    }

    .empty {
        text-align: center;
        font-style: italic;
    }
}