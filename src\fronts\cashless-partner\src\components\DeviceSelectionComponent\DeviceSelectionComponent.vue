<script lang="ts" src="./DeviceSelectionComponent.ts">
</script>

<style lang="sass">
@use './DeviceSelectionComponent.scss' as *
</style>

<template>
    <div class="device-selection-component">
        <form-modal-or-drawer
            :state="opened"
            :title="'Choisir un appareil'"
            :subtitle="'Sélectionnez un ou plusieurs appareils'"
            @close="close()"
        >
            <template v-slot:content>
                <div>
                    <input v-model="search" placeholder="Rechercher...">
                </div>

                <div class="table-scroll-wrapper">
                    <table class="data-table">
                        <thead>
                        <tr>
                            <td> Appareils </td>
                        </tr>
                        </thead>
                        <tbody>
                        <tr v-if="devices.length === 0">
                            <td class="empty" colspan="100%"> Aucun appareil dans cet établissement </td>
                        </tr>
                        <tr v-else-if="filteredEstablishmentDevices.length === 0">
                            <td class="empty" colspan="100%"> Aucun résultat... </td>
                        </tr>
                        <tr v-for="device of filteredEstablishmentDevices">
                            <td class="ticket-data" @click="toggleEstablishmentDevice(device)">
                                <div class="checkbox" :class="{selected: selectedEstablishmentDevices.includes(device.uid), disabled: isLinkedWithProfile(device) || device.establishmentAccountUid === null}">
                                    <i class="fa-regular fa-check"></i>
                                </div>

                                <div class="iot-data">
                                    <div class="iot-icon">
                                        <i v-if="device.brand.toLowerCase() === 'apple'" class="fa-brands fa-apple" />
                                        <i v-if="device.brand.toLowerCase() === 'chrome'" class="fa-brands fa-chrome" />
                                        <i v-else class="fa-regular fa-tablet" />
                                    </div>
                                    <div class="infos">
                                        <span class="name"> {{ device.brand }} - {{ device.model }} </span>
                                        <span class="email"> {{ device.hardwareId }} </span>
                                    </div>
                                </div>

                                <div class="red label" v-if="isLinkedWithProfile(device)">
                                    Déjà lié
                                </div>
                                <div class="red label" v-if="device.establishmentAccountUid === null">
                                    No account
                                </div>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </template>

            <template v-slot:buttons>
                <button type="button" :class="{disabled: selectedEstablishmentDevices.length === 0}" class="button" @click="select()">
                    <i class="fa-regular fa-circle-plus"></i>
                    Sélectionner {{ selectedEstablishmentDevices.length }} appareil{{ selectedEstablishmentDevices.length > 1 ? 's' : '' }}
                </button>
            </template>
        </form-modal-or-drawer>
    </div>
</template>