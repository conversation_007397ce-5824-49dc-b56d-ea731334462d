import {Component, Prop, Vue, Watch} from "vue-facing-decorator";
import {PosState} from "../../model/PosState";
import {PosProfile} from "../../model/PosProfile";
import {LocalOrder, LocalOrderTransfer} from "../../model/LocalOrder";
import {AutoWired, UuidUtils, VisualScopedUuid} from "@groupk/horizon2-core";
import {AppBus} from "../../config/AppBus";
import {LocalOrderRepository} from "../../repositories/LocalOrderRepository";
import {OrderPaymentStatus, UuidScopeProductOrder} from "@groupk/mastodon-core";
import MobileOrderCardComponent from "../MobileOrderCardComponent/MobileOrderCardComponent.vue";
import OrderSidebarComponent from "../OrderSidebarComponent/OrderSidebarComponent.vue";
import MobileBottomCartComponent from "../MobileBottomCartComponent/MobileBottomCartComponent.vue";
import UnlockTransferModalComponent from "../UnlockTransferModalComponent/UnlockTransferModalComponent.vue";
import OrderTransferToIotComponent from "../OrderTransferToIotComponent/OrderTransferToIotComponent.vue";
import {LocalOrderTransferRepository} from "../../repositories/LocalOrderTransferRepository";
import {AuthStateModel} from "../../../../../shared/AuthStateModel";
import {MainConfig} from "../../../../../shared/MainConfig";

@Component({
	components: {
		'mobile-bottom-cart': MobileBottomCartComponent,
		'order-sidebar': OrderSidebarComponent,
		'mobile-order-card': MobileOrderCardComponent,
		'unlock-transfer-modal': UnlockTransferModalComponent,
		'order-transfer-to-iot': OrderTransferToIotComponent
	}
})
export default class MobileOrderListComponent extends Vue {
	@Prop() posState!: PosState;
	@Prop() posProfile!: PosProfile;

	animateMobileCart: boolean = false;
	showMobileCart: boolean = false;

	orderTransfers: LocalOrderTransfer[] = [];
	orders: LocalOrder[] = [];
	hasValidToken: boolean = true;

	unlockTransfer: LocalOrderTransfer|null = null;

	chunkedOrders: LocalOrder[][] = [];
	quantityPerPage: number = 20;
	page: number = 1;
	sort: 'DATE'|'NOT_PAYED'|'ALL' = 'DATE';

	@AutoWired(AppBus) accessor appBus!: AppBus;
	@AutoWired(AuthStateModel) accessor authCenter!: AuthStateModel;
	@AutoWired(LocalOrderRepository) accessor localOrderRepository!: LocalOrderRepository;
	@AutoWired(LocalOrderTransferRepository) accessor localOrderTransferRepository!: LocalOrderTransferRepository;
	@AutoWired(MainConfig) accessor config!: MainConfig;

	@Watch('posState', {deep: true})
	posStateWatch() {
		this.sortAndChunkOrders();
	}

	async beforeMount() {
		this.appBus.on('orderSaved', this.updateOrder);
		this.appBus.on('orderTransferSaved', this.updateOrderTransfer);

		if(!this.authCenter.getStateSync()) this.hasValidToken = false;

		this.orderTransfers = await this.localOrderTransferRepository.findAllNotDone();
		this.orders = await this.localOrderRepository.findAll();
		this.sortAndChunkOrders();
	}

	unmounted() {
		this.appBus.off('orderSaved', this.updateOrder);
		this.appBus.off('orderTransferSaved', this.updateOrderTransfer);
	}

	async updateOrder(localOrder: LocalOrder) {
		const index = this.orders.findIndex((ramLocalOrder) => ramLocalOrder.uid === localOrder.uid);
		if(index !== -1) {
			this.orders.splice(index, 1, localOrder);
		} else {
			this.orders.push(localOrder);
		}
		this.$forceUpdate();
		this.sortAndChunkOrders();
	}

	async updateOrderTransfer(localOrderTransfer: LocalOrderTransfer) {
		const index = this.orderTransfers.findIndex((ramLocalOrderTransfer) => ramLocalOrderTransfer.uid === localOrderTransfer.uid);
		if(index !== -1) {
			if(localOrderTransfer.canceled || localOrderTransfer.fetchedFromTargetDatetime !== null) {
				this.orderTransfers.splice(index, 1);
			} else {
				this.orderTransfers.splice(index, 1, localOrderTransfer);
			}
		} else {
			this.orderTransfers.push(localOrderTransfer);
		}
		this.$forceUpdate();
	}

	sortAndChunkOrders() {
		const sortedOrders = [...this.orders]
			.filter((order) => {
				if(order.transferred) return false;
				return this.sort !== 'ALL' ? order.order.sellerEstablishmentAccountUid === this.posState.currentEstablishmentAccountUid : true;
			})
			.sort((orderA, orderB) => {
				if(this.sort === 'DATE') return orderA.order.creationDatetime < orderB.order.creationDatetime ? 1 : -1;
				else {
					const aHavePendingPayment = orderA.order.payments.find((payment) => payment.status === OrderPaymentStatus.PENDING) !== undefined;
					const bHavePendingPayment = orderB.order.payments.find((payment) => payment.status === OrderPaymentStatus.PENDING) !== undefined;
					const aTotals = this.posState.orderExecutorModel.getOrderTotals(orderA.order);
					const bTotals = this.posState.orderExecutorModel.getOrderTotals(orderB.order);
					if(aHavePendingPayment) return -1;
					if(bHavePendingPayment) return 1;
					if(aTotals.leftToPay !== 0 && bTotals.leftToPay !== 0) return 0;
					else if(aTotals.leftToPay !== 0) return -1;
					else if(bTotals.leftToPay !== 0) return 1;
					else return 0;
				}
			});

		const chunked: LocalOrder[][] = [];
		for (let i = 0; i < sortedOrders.length; i += this.quantityPerPage) {
			chunked.push(sortedOrders.slice(i, i + this.quantityPerPage));
		}
		this.chunkedOrders = chunked;
	}

	async selectOrder(orderUid: VisualScopedUuid<UuidScopeProductOrder>) {
		this.posState.currentOrder = await this.localOrderRepository.findOne(orderUid);
		this.toggleMobileCart();
	}

	get notSyncOrders() {
		return this.orders.filter((localOrder) => !localOrder.synced);
	}

	pagesNumber() {
		return this.chunkedOrders.length;
	}

	totalOrders() {
		return this.chunkedOrders.reduce((aggregator, chunk) => aggregator + chunk.length, 0);
	}

	get getPurchasesQuantity(){
		if(!this.posState.currentOrder) return 0;
		let quantity = 0;
		for(let purchase of this.posState.currentOrder.order.purchases) {
			for(let item of purchase.items) {
				quantity += item.quantity;
			}
		}
		return quantity;
	}

	toggleMobileCart() {
		this.animateMobileCart = true;
		setTimeout(() => {
			this.showMobileCart = true;
		}, 150)
	}

	closeMobileCart() {
		this.showMobileCart = false;
		this.animateMobileCart = false;
	}

	unlockedTransfer(updatedTransfer: LocalOrderTransfer) {
		const index = this.orderTransfers.findIndex((transfer) => transfer.uid === updatedTransfer.uid);
		if(index !== -1) this.orderTransfers.splice(index, 1);
	}

	goToCas() {
		window.location.href = this.config.configuration.casFrontUrl + 'establishment/' + UuidUtils.visualToUuid(this.posState.establishmentUid) + '/login?platform=PointOfSell';
	}
}