<script lang="ts" src="./transactionsStatistics.ts">
</script>

<style scoped lang="sass">
@use './transactionsStatistics.scss' as *
</style>

<template>
    <div id="transactions-statistics-page" class="page">
        <layout :drawer-opened="selectedProfile||selectedProduct">
            <template v-slot:content>
                <content-header
                    :parameters="headerParameters"
                    @selected-tab="currentView = $event.id"
                ></content-header>

                <div class="loading-container" v-if="loading">
                    <div class="loader"></div>
                </div>
                <div v-else class="page-content">
                    <div class="context">

                        <div class="data-actions">
                            <div class="small-button-group">
                                <dropdown
                                    class="dropdown small white button"
                                    placeholder="Ce mois"
                                    :values="periodsDropdownValues"
                                    :default-selected="selectedDropdownPeriod"
                                    @update="selectedDropdownValue($event)"
                                ></dropdown>

                                <div class="action-group">
                                    <button class="small white button" @click="toggleCalendarModal()">
                                        <i class="fa-regular fa-calendar"></i>
                                        <template v-if="selectedRange.start"> {{ $filters.Date(selectedRange.start.toISOString()) }} </template>
                                        <template v-if="selectedRange.end"> - {{ $filters.Date(selectedRange.end.toISOString())  }} </template>
                                    </button>

                                    <div v-if="showCalendarModal" class="calendar-modal-dimmer" @click="toggleCalendarModal()"></div>
                                    <div v-if="showCalendarModal" class="calendar-modal actions-dropdown floating">
                                        <custom-calendar
                                            :default-selection="[DateUtils.cloneRange(selectedRange)]"
                                            @selected-period="selectedPeriod($event)"
                                        ></custom-calendar>
                                        <button class="black button" @click="applyCalendarRange()">
                                            Appliquer
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <dropdown
                                class="dropdown small white button"
                                placeholder="Mensuel"
                                :values="groupByDropdownValues"
                                :default-selected="selectedDropdownGroupBy"
                                @update="selectedGroupByDropdownValue($event)"
                            ></dropdown>
                        </div>
                    </div>

                    <template v-if="currentView === 'GLOBAL'">

                        <!--                        <div class="comparison">-->
                        <!--                            <div class="button-group">-->
                        <!--                                <div class="active"> Comparer avec le mois dernier </div>-->
                        <!--                                <div> Comparer avec le même mois N-1 </div>-->
                        <!--                            </div>-->
                        <!--                        </div>-->

                        <span class="text-separator"> CA journalier </span>

                        <line-chart class="ca-chart" :data="getRefillChartData()"></line-chart>

                        <div class="legend">
                            <div class="item">
                                <div class="line"></div>
                                <span> Rechargements  </span>
                            </div>
                            <div class="item">
                                <div class="line other"></div>
                                <span> Dépenses </span>
                            </div>
                        </div>

                        <div class="key-numbers">
                            <div class="key-number">
                                <span class="type"> Total Rechargements </span>
                                <span class="value"> {{ $filters.Money(getCATotals().refills) }} </span>
                                <!--                                <span class="indicator">-->
                                <!--                                    <i class="fa-solid fa-arrow-up"></i>-->
                                <!--                                    15%-->
                                <!--                                    <span> période précédente </span>-->
                                <!--                                </span>-->
                            </div>
                            <div class="key-number">
                                <span class="type"> Total Dépenses </span>
                                <span class="value">{{ $filters.Money(getCATotals().sells) }} </span>
                                <!--                                <span class="indicator">-->
                                <!--                                    <i class="fa-solid fa-arrow-up"></i>-->
                                <!--                                    30%-->
                                <!--                                     <span> période précédente </span>-->
                                <!--                                </span>-->
                            </div>
<!--                            <div class="key-number">-->
<!--                                <span class="type"> Panier moyen </span>-->
<!--                                <span class="value" v-if="getCATotals().sellTransactions > 0"> {{ $filters.Money(getCATotals().sells / getCATotals().sellTransactions) }} </span>-->
<!--                                <span class="value" v-else> {{ $filters.Money(0) }} </span>-->
<!--                                &lt;!&ndash;                                <span class="indicator">&ndash;&gt;-->
<!--                                &lt;!&ndash;                                    <i class="fa-solid fa-arrow-up"></i>&ndash;&gt;-->
<!--                                &lt;!&ndash;                                    15%&ndash;&gt;-->
<!--                                &lt;!&ndash;                                    <span> période précédente </span>&ndash;&gt;-->
<!--                                &lt;!&ndash;                                </span>&ndash;&gt;-->
<!--                            </div>-->
                        </div>
                    </template>

                    <template v-else-if="currentView === 'PROFILES'">
                        <div class="employee-charts">
                            <div class="chart-group">
                                <doughnut-chart v-if="profileTotal.refills > 0" class="chart" :data="getProfileRefillChartData()" :data-format="'MONEY'"></doughnut-chart>
                                <div class="dough-no-data" v-else>
                                    Aucune donnée
                                </div>

                                <div class="description">
                                    <span class="title"> Rechargements par point de vente </span>
                                    <span> Comparaison des points de vente avec le plus de rechargements </span>
                                </div>
                            </div>

                            <div class="chart-group">
                                <doughnut-chart v-if="profileTotal.sells > 0" class="chart" :data="getProfileSellsChartData()" :data-format="'MONEY'"></doughnut-chart>
                                <div class="dough-no-data" v-else>
                                    Aucune donnée
                                </div>

                                <div class="description">
                                    <span class="title"> Ventes par point de vente </span>
                                    <span> Comparaison des points de vente avec le plus de ventes </span>
                                </div>
                            </div>
                        </div>

                        <div class="table-scroll-wrapper">
                            <table class="data-table">
                                <thead>
                                <tr>
                                    <td class="no-break" @click="sortProfiles('name')">
                                        Point de vente
                                        <i v-if="profileSort && profileSort.key === 'name' && profileSort.order === 'ASC'" class="sort-icon fa-solid fa-sort-up"></i>
                                        <i v-else-if="profileSort && profileSort.key === 'name' && profileSort.order === 'DESC'" class="sort-icon fa-solid fa-sort-down"></i>
                                        <i v-else class="sort-icon fa-solid fa-sort"></i>
                                    </td>
                                    <td class="no-break" @click="sortProfiles('refills')">
                                        Rechargements
                                        <i v-if="profileSort && profileSort.key === 'refills' && profileSort.order === 'ASC'" class="sort-icon fa-solid fa-sort-up"></i>
                                        <i v-else-if="profileSort && profileSort.key === 'refills' && profileSort.order === 'DESC'" class="sort-icon fa-solid fa-sort-down"></i>
                                        <i v-else class="sort-icon fa-solid fa-sort"></i>
                                    </td>
                                    <td class="no-break" @click="sortProfiles('sells')">
                                        Dépenses
                                        <i v-if="profileSort && profileSort.key === 'sells' && profileSort.order === 'ASC'" class="sort-icon fa-solid fa-sort-up"></i>
                                        <i v-else-if="profileSort && profileSort.key === 'sells' && profileSort.order === 'DESC'" class="sort-icon fa-solid fa-sort-down"></i>
                                        <i v-else class="sort-icon fa-solid fa-sort"></i>
                                    </td>
                                    <td class="no-break"> Détail </td>
                                </tr>
                                </thead>
                                <tbody>
                                <tr v-for="profile of sortedProfiles">
                                    <td class="no-break" > {{ profile.name }}</td>
                                    <td> {{ $filters.Money(aggregateProfileTotal[profile.uid] ? aggregateProfileTotal[profile.uid].totalWithTaxes.refills : 0) }} </td>
                                    <td> {{ $filters.Money(aggregateProfileTotal[profile.uid] ? aggregateProfileTotal[profile.uid].totalWithTaxes.sells : 0) }} </td>
                                    <td>
                                        <button class="small white button no-break" @click="selectedProfile = profile; selectedProduct = null; search = ''">
                                            Voir le détail
                                        </button>
                                    </td>
                                </tr>
                                <tr class="total">
                                    <td> Total </td>
                                    <td> {{ $filters.Money(profileTotal.refills) }} </td>
                                    <td> {{ $filters.Money(profileTotal.sells) }} </td>
                                    <td></td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                    </template>

                    <template v-else-if="currentView === 'PRODUCTS'">
                        <div class="table-scroll-wrapper">
                            <table class="data-table">
                                <thead>
                                <tr>
                                    <td class="no-break" @click="sortProduct('name')">
                                        Produit
                                        <i v-if="productSort && productSort.key === 'name' && productSort.order === 'ASC'" class="sort-icon fa-solid fa-sort-up"></i>
                                        <i v-else-if="productSort && productSort.key === 'name' && productSort.order === 'DESC'" class="sort-icon fa-solid fa-sort-down"></i>
                                        <i v-else class="sort-icon fa-solid fa-sort"></i>
                                    </td>
                                    <td class="no-break" @click="sortProduct('refills')">
                                        Rechargements
                                        <i v-if="productSort && productSort.key === 'refills' && productSort.order === 'ASC'" class="sort-icon fa-solid fa-sort-up"></i>
                                        <i v-else-if="productSort && productSort.key === 'refills' && productSort.order === 'DESC'" class="sort-icon fa-solid fa-sort-down"></i>
                                        <i v-else class="sort-icon fa-solid fa-sort"></i>
                                    </td>
                                    <td class="no-break" @click="sortProduct('sells')">
                                        Dépenses
                                        <i v-if="productSort && productSort.key === 'sells' && productSort.order === 'ASC'" class="sort-icon fa-solid fa-sort-up"></i>
                                        <i v-else-if="productSort && productSort.key === 'sells' && productSort.order === 'DESC'" class="sort-icon fa-solid fa-sort-down"></i>
                                        <i v-else class="sort-icon fa-solid fa-sort"></i>
                                    </td>
                                    <td class="no-break" @click="sortProduct('quantity')">
                                        Quantité
                                        <i v-if="productSort && productSort.key === 'quantity' && productSort.order === 'ASC'" class="sort-icon fa-solid fa-sort-up"></i>
                                        <i v-else-if="productSort && productSort.key === 'quantity' && productSort.order === 'DESC'" class="sort-icon fa-solid fa-sort-down"></i>
                                        <i v-else class="sort-icon fa-solid fa-sort"></i>
                                    </td>
                                    <td> Détail </td>
                                </tr>
                                </thead>
                                <tbody>
                                <tr v-for="product of sortedProducts">
                                    <td class="no-break"> {{ product.name }}</td>
                                    <td> {{ $filters.Money(aggregateProductTotal[product.id] ? aggregateProductTotal[product.id].totalWithTaxes.refills : 0) }} </td>
                                    <td> {{ $filters.Money(aggregateProductTotal[product.id] ? aggregateProductTotal[product.id].totalWithTaxes.sells : 0) }} </td>
                                    <td> {{ aggregateProductTotal[product.id] ? aggregateProductTotal[product.id].quantity : 0 }} </td>
                                    <td>
                                        <button class="small white button" @click="selectedProduct = product; selectedProfile = null; search = ''">
                                            Voir le détail
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="no-break"> Montant libre </td>
                                    <td> {{ $filters.Money(aggregateProductTotal['none'] ? aggregateProductTotal['none'].totalWithTaxes.refills : 0) }} </td>
                                    <td> {{ $filters.Money(aggregateProductTotal['none'] ? aggregateProductTotal['none'].totalWithTaxes.sells : 0) }} </td>
                                    <td> {{ aggregateProductTotal['none'] ? aggregateProductTotal['none'].quantity : 0 }} </td>
                                    <td>
                                        <button class="small white button no-break" @click="selectedProduct = 'none'; selectedProfile = null; search = ''">
                                            Voir le détail
                                        </button>
                                    </td>
                                </tr>
                                <tr class="total">
                                    <td> Total </td>
                                    <td> {{ $filters.Money(productTotal.refills) }} </td>
                                    <td> {{ $filters.Money(productTotal.sells) }} </td>
                                    <td> {{ productTotal.quantity }} </td>
                                    <td></td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                    </template>

                    <template v-else-if="currentView === 'KIOSKS'">
                        <div class="table-scroll-wrapper">
                            <table class="data-table">
                                <thead>
                                <tr>
                                    <td> Borne </td>
                                    <td> Total Paiements </td>
                                    <td> En cours de synchro </td>
                                    <td> Détail </td>
                                </tr>
                                </thead>
                                <tbody>

                                <tr class="total">
                                    <td> Total </td>
                                    <td> {{ $filters.Money(0) }} </td>
                                    <td> {{ $filters.Money(0) }} </td>
                                    <td></td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                    </template>
                </div>
            </template>

            <template v-slot:right>
                <div class="right-panel">

                    <template v-if="selectedKiosk">
                        <div class="close" @click="selectedProfile = null">
                            <i class="fa-regular fa-xmark"></i>
                            <span> Fermer </span>
                        </div>

                        <div class="header">
                            <div class="left">
                                <h2> {{ selectedKiosk.name }} </h2>
                                <span>
                                    Ventes depuis
                                    {{ selectedKiosk.name }}
                                    sur la période
                                </span>
                            </div>
                        </div>

                        <div class="table-scroll-wrapper">
                            <div class="input-group">
                                <input type="text" v-model="search" placeholder="Rechercher un moyen de paiement" />
                            </div>

                            <table class="data-table">
                                <thead>
                                <tr>
                                    <td> Méthode </td>
                                    <td> Paiements </td>
                                    <td> En sync.. </td>
                                </tr>
                                </thead>
                                <tbody>
                                <tr v-for="method of searchInArray(paymentMethods)">
                                    <td class="break-all"> {{ method.name }}</td>
<!--                                    <td> {{ $filters.Money(aggregateProfileTotal[selectedProfile.uid] ? aggregateProfileTotal[selectedProfile.uid].perPaymentMethod[method.uid].refills : 0) }} </td>-->
<!--                                    <td> {{ $filters.Money(aggregateProfileTotal[selectedProfile.uid] ? aggregateProfileTotal[selectedProfile.uid].perPaymentMethod[method.uid].sells : 0) }} </td>-->
                                </tr>
                                <tr>
                                    <td> Aucune </td>
<!--                                    <td> {{ $filters.Money(aggregateProfileTotal[selectedProfile.uid] ? aggregateProfileTotal[selectedProfile.uid].perPaymentMethod['none'].refills : 0) }} </td>-->
<!--                                    <td> {{ $filters.Money(aggregateProfileTotal[selectedProfile.uid] ? aggregateProfileTotal[selectedProfile.uid].perPaymentMethod['none'].sells : 0) }} </td>-->
                                </tr>
                                </tbody>
                            </table>
                        </div>

                    </template>

                    <template v-if="selectedProfile">
                        <div class="close" @click="selectedProfile = null">
                            <i class="fa-regular fa-xmark"></i>
                            <span> Fermer </span>
                        </div>

                        <div class="header">
                            <div class="left">
                                <h2> {{ selectedProfile.name }} </h2>
                                <span>
                                    Ventes depuis
                                     {{ selectedProfile.name }}
                                    sur la période
                                </span>
                            </div>
                        </div>

                        <div class="chart-area">
                            <line-chart :key="selectedProfile.uid" v-if="selectedProfile" class="ca-chart" :data="getSelectedProfileChartData()"></line-chart>
                        </div>

                        <div class="button-group">
                            <div :class="{active: profileDetailView === 'PAYMENT_METHODS'}" @click="profileDetailView = 'PAYMENT_METHODS'; search = ''"> Par moyen de paiement</div>
                            <div :class="{active: profileDetailView === 'PRODUCTS'}" @click="profileDetailView = 'PRODUCTS'; search = ''"> Par produit </div>
                        </div>

                        <div class="input-group">
                            <input type="text" v-model="search" :placeholder="`Rechercher un ${profileDetailView === 'PAYMENT_METHODS' ? 'moyen de paiement' : 'produit'}`" />
                        </div>

                        <template v-if="profileDetailView === 'PAYMENT_METHODS'">
                            <div class="table-scroll-wrapper">
                                <table class="data-table">
                                    <thead>
                                    <tr>
                                        <td @click="sortProfilePaymentMethods('name')">
                                            Méthode
                                            <i v-if="profilePaymentMethodsSort && profilePaymentMethodsSort.key === 'name' && profilePaymentMethodsSort.order === 'ASC'" class="sort-icon fa-solid fa-sort-up"></i>
                                            <i v-else-if="profilePaymentMethodsSort && profilePaymentMethodsSort.key === 'name' && profilePaymentMethodsSort.order === 'DESC'" class="sort-icon fa-solid fa-sort-down"></i>
                                            <i v-else class="sort-icon fa-solid fa-sort"></i>
                                        </td>
                                        <td @click="sortProfilePaymentMethods('refills')">
                                            Recharg..
                                            <i v-if="profilePaymentMethodsSort && profilePaymentMethodsSort.key === 'refills' && profilePaymentMethodsSort.order === 'ASC'" class="sort-icon fa-solid fa-sort-up"></i>
                                            <i v-else-if="profilePaymentMethodsSort && profilePaymentMethodsSort.key === 'refills' && profilePaymentMethodsSort.order === 'DESC'" class="sort-icon fa-solid fa-sort-down"></i>
                                            <i v-else class="sort-icon fa-solid fa-sort"></i>
                                        </td>
                                        <td @click="sortProfilePaymentMethods('sells')">
                                            Dépenses
                                            <i v-if="profilePaymentMethodsSort && profilePaymentMethodsSort.key === 'sells' && profilePaymentMethodsSort.order === 'ASC'" class="sort-icon fa-solid fa-sort-up"></i>
                                            <i v-else-if="profilePaymentMethodsSort && profilePaymentMethodsSort.key === 'sells' && profilePaymentMethodsSort.order === 'DESC'" class="sort-icon fa-solid fa-sort-down"></i>
                                            <i v-else class="sort-icon fa-solid fa-sort"></i>
                                        </td>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <tr v-for="method of searchInArray(sortedProfilePaymentMethods)">
                                        <td class="break-all"> {{ method.name }}</td>
                                        <td> {{ $filters.Money(aggregateProfileTotal[selectedProfile.uid] ? aggregateProfileTotal[selectedProfile.uid].perPaymentMethod[method.uid].refills : 0) }} </td>
                                        <td> {{ $filters.Money(aggregateProfileTotal[selectedProfile.uid] ? aggregateProfileTotal[selectedProfile.uid].perPaymentMethod[method.uid].sells : 0) }} </td>
                                    </tr>
                                    <tr>
                                        <td> Aucune </td>
                                        <td> {{ $filters.Money(aggregateProfileTotal[selectedProfile.uid] ? aggregateProfileTotal[selectedProfile.uid].perPaymentMethod['none'].refills : 0) }} </td>
                                        <td> {{ $filters.Money(aggregateProfileTotal[selectedProfile.uid] ? aggregateProfileTotal[selectedProfile.uid].perPaymentMethod['none'].sells : 0) }} </td>
                                    </tr>
                                    </tbody>
                                </table>
                            </div>
                        </template>

                        <template v-if="profileDetailView === 'PRODUCTS'">
                            <div class="table-scroll-wrapper">
                                <table class="data-table">
                                    <thead>
                                    <tr>
                                        <td class="no-break" @click="sortProfileProduct('name')">
                                            Produit
                                            <i v-if="profileProductSort && profileProductSort.key === 'name' && profileProductSort.order === 'ASC'" class="sort-icon fa-solid fa-sort-up"></i>
                                            <i v-else-if="profileProductSort && profileProductSort.key === 'name' && profileProductSort.order === 'DESC'" class="sort-icon fa-solid fa-sort-down"></i>
                                            <i v-else class="sort-icon fa-solid fa-sort"></i>
                                        </td>
                                        <td class="no-break" @click="sortProfileProduct('refills')">
                                            Recharg..
                                            <i v-if="profileProductSort && profileProductSort.key === 'refills' && profileProductSort.order === 'ASC'" class="sort-icon fa-solid fa-sort-up"></i>
                                            <i v-else-if="profileProductSort && profileProductSort.key === 'refills' && profileProductSort.order === 'DESC'" class="sort-icon fa-solid fa-sort-down"></i>
                                            <i v-else class="sort-icon fa-solid fa-sort"></i>
                                        </td>
                                        <td class="no-break" @click="sortProfileProduct('sells')">
                                            Dépenses
                                            <i v-if="profileProductSort && profileProductSort.key === 'sells' && profileProductSort.order === 'ASC'" class="sort-icon fa-solid fa-sort-up"></i>
                                            <i v-else-if="profileProductSort && profileProductSort.key === 'sells' && profileProductSort.order === 'DESC'" class="sort-icon fa-solid fa-sort-down"></i>
                                            <i v-else class="sort-icon fa-solid fa-sort"></i>
                                        </td>
                                        <td class="no-break">
                                            Quantité
                                        </td>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <tr v-for="product of searchInArray(sortedProfileProducts)">
                                        <td class="break-all"> {{ product.name }}</td>
                                        <td> {{ $filters.Money(aggregateProfileTotal[selectedProfile.uid] ? aggregateProfileTotal[selectedProfile.uid].perProduct[product.id].refills : 0) }} </td>
                                        <td> {{ $filters.Money(aggregateProfileTotal[selectedProfile.uid] ? aggregateProfileTotal[selectedProfile.uid].perProduct[product.id].sells : 0) }} </td>
                                        <td> {{ aggregateProfileTotal[selectedProfile.uid] ? aggregateProfileTotal[selectedProfile.uid].perProduct[product.id].quantity : 0 }} </td>
                                    </tr>
                                    <tr>
                                        <td>
                                           Montant libre
                                        </td>
                                        <td> {{ $filters.Money(aggregateProfileTotal[selectedProfile.uid] ? aggregateProfileTotal[selectedProfile.uid].perProduct['none'].refills : 0) }} </td>
                                        <td> {{ $filters.Money(aggregateProfileTotal[selectedProfile.uid] ? aggregateProfileTotal[selectedProfile.uid].perProduct['none'].sells : 0) }} </td>
                                        <td> {{ aggregateProfileTotal[selectedProfile.uid] ? aggregateProfileTotal[selectedProfile.uid].perProduct['none'].quantity : 0 }} </td>
                                    </tr>
                                    </tbody>
                                </table>
                            </div>
                        </template>
                    </template>

                    <template v-if="selectedProduct">
                        <div class="close" @click="selectedProduct = null">
                            <i class="fa-regular fa-xmark"></i>
                            <span> Fermer </span>
                        </div>

                        <div class="header">
                            <div class="left">
                                <h2> {{ selectedProduct === 'none' ? 'Montant libre' : selectedProduct.name }} </h2>
                                <span>
                                    Ventes de
                                    {{ selectedProduct === 'none' ? 'Montant libre' : selectedProduct.name }}
                                    sur la période
                                </span>
                            </div>
                        </div>

                        <div class="chart-area">
                            <line-chart :key="selectedProduct === 'none' ? 'none' : selectedProduct.id" v-if="selectedProduct" class="ca-chart" :data="getSelectedProductChartData()"></line-chart>
                        </div>

                        <div class="input-group">
                            <input type="text" v-model="search" placeholder="Rechercher un point de vente" />
                        </div>

                        <div class="table-scroll-wrapper no-margin">
                            <table class="data-table">
                                <thead>
                                <tr>
                                    <td class="no-break" @click="sortProductProfile('name')">
                                        Point de vente
                                        <i v-if="productProfileSort && productProfileSort.key === 'name' && productProfileSort.order === 'ASC'" class="sort-icon fa-solid fa-sort-up"></i>
                                        <i v-else-if="productProfileSort && productProfileSort.key === 'name' && productProfileSort.order === 'DESC'" class="sort-icon fa-solid fa-sort-down"></i>
                                        <i v-else class="sort-icon fa-solid fa-sort"></i>
                                    </td>
                                    <td class="no-break" @click="sortProductProfile('refills')">
                                        Recharg..
                                        <i v-if="productProfileSort && productProfileSort.key === 'refills' && productProfileSort.order === 'ASC'" class="sort-icon fa-solid fa-sort-up"></i>
                                        <i v-else-if="productProfileSort && productProfileSort.key === 'refills' && productProfileSort.order === 'DESC'" class="sort-icon fa-solid fa-sort-down"></i>
                                        <i v-else class="sort-icon fa-solid fa-sort"></i>
                                    </td>
                                    <td class="no-break" @click="sortProductProfile('sells')">
                                        Dépenses
                                        <i v-if="productProfileSort && productProfileSort.key === 'sells' && productProfileSort.order === 'ASC'" class="sort-icon fa-solid fa-sort-up"></i>
                                        <i v-else-if="productProfileSort && productProfileSort.key === 'sells' && productProfileSort.order === 'DESC'" class="sort-icon fa-solid fa-sort-down"></i>
                                        <i v-else class="sort-icon fa-solid fa-sort"></i>
                                    </td>
                                    <td class="no-break" @click="sortProductProfile('quantity')">
                                        Quantité
                                        <i v-if="productProfileSort && productProfileSort.key === 'quantity' && productProfileSort.order === 'ASC'" class="sort-icon fa-solid fa-sort-up"></i>
                                        <i v-else-if="productProfileSort && productProfileSort.key === 'quantity' && productProfileSort.order === 'DESC'" class="sort-icon fa-solid fa-sort-down"></i>
                                        <i v-else class="sort-icon fa-solid fa-sort"></i>
                                    </td>
                                </tr>
                                </thead>
                                <tbody>
                                <tr v-for="profile of searchInArray(sortedProductProfiles)">
                                    <td class="break-all"> {{ profile.name }}</td>
                                    <td> {{ $filters.Money(aggregateProductTotal[selectedProduct === 'none' ? 'none' : selectedProduct.id] ? aggregateProductTotal[selectedProduct === 'none' ? 'none' : selectedProduct.id].perProfile[profile.uid].refills : 0) }} </td>
                                    <td> {{ $filters.Money(aggregateProductTotal[selectedProduct === 'none' ? 'none' : selectedProduct.id] ? aggregateProductTotal[selectedProduct === 'none' ? 'none' : selectedProduct.id].perProfile[profile.uid].sells : 0) }} </td>
                                    <td> {{ aggregateProductTotal[selectedProduct === 'none' ? 'none' : selectedProduct.id] ? aggregateProductTotal[selectedProduct === 'none' ? 'none' : selectedProduct.id].perProfile[profile.uid].quantity : 0 }} </td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                    </template>
                </div>
            </template>
        </layout>

        <form-modal-or-drawer
            :title="'Imprimer le rapport de transactions'"
            :subtitle="'Paramétrez le rapport avant de le générer'"
            :state="showReportOptionsModal"
            @close="showReportOptionsModal = false"
        >
            <template v-slot:content>
                <div class="payment-methods">
                    <div v-for="paymentMethod of paymentMethods" class="payment-method">
                        <input :id="paymentMethod.uid" type="checkbox" @change="toggleReportPaymentMethod(paymentMethod.uid)" :checked="!reportExcludedPaymentMethods.includes(paymentMethod.uid)" >
                        <label :for="paymentMethod.uid"> {{ paymentMethod.name }} </label>
                    </div>
                </div>
            </template>
            <template v-slot:buttons>
                <button class="white button" @click="!printingReport ? showReportOptionsModal = false : ()=>{}"> Annuler </button>
                <button class="button" :class="{loading: printingReport, disabled: printingReport}" @click="printReport()">
                    <i class="fa-regular fa-print"></i>
                    Imprimer le rapport
                </button>
            </template>
        </form-modal-or-drawer>

        <export-choice-modal
            v-if="showExportModal"
            :export-choices="[{
                id: 'perProfile',
                title: 'Totaux par produit par point de vente',
                description: 'Exporte le total des ventes agrégé par produit et par point de vente'
            }, {
                id: 'perCategory',
                title: 'Totaux par catégorie',
                description: 'Exporte le total des ventes agrégé par catégorie'
            }]"
            @chose-export="processExport($event)"
            @close="showExportModal = false"
        ></export-choice-modal>

        <form-modal-or-drawer
            :title="'Imprimer le rapport de ventes par catégorie'"
            :subtitle="'Paramétrez le rapport avant de le générer'"
            :state="showExportPerCetagoriesModal"
            @close="showExportPerCetagoriesModal = false"
        >
            <template v-slot:content>
                <div class="payment-methods">
                    <div v-for="category of categories" class="payment-method">
                        <input :id="category.uid" type="checkbox" @change="toggleCategory(category.uid)" :checked="!exportCategories.includes(category.uid)" >
                        <label :for="category.uid"> {{ category.name }} </label>
                    </div>
                </div>
            </template>
            <template v-slot:buttons>
                <button class="white button" @click="!exportingPerCategories ? showExportPerCetagoriesModal = false : ()=>{}"> Annuler </button>
                <button class="button" :class="{loading: printingReport, disabled: printingReport}" @click="exportCategoriesStatistics()">
                    <i class="fa-regular fa-arrow-down-to-line"></i>
                    Exporter les statistiques
                </button>
            </template>
        </form-modal-or-drawer>

    </div>
</template>