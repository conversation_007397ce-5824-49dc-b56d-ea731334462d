import {Component, Prop, Vue} from "vue-facing-decorator";
import {
	allowedPaymentProtocolsPerPaymentMethod,
	PaymentMethodApiOut,
	PaymentProtocol, UuidScopePayment_method
} from "@groupk/mastodon-core";
import {AutoWired, UuidUtils, VisualScopedUuid} from "@groupk/horizon2-core";
import {PaymentMethodRepository} from "../../../../../shared/repositories/PaymentMethodRepository";
import {PosState} from "../../model/PosState";
import {DropdownComponent, FormModalOrDrawerComponent} from "@groupk/vue3-interface-sdk";
import CaisseApSettingsFormComponent
	from "./PaymentMethodTypeForms/CaisseApSettingsFormComponent/CaisseApSettingsFormComponent.vue";
import CashKeeperSettingsFormComponent
	from "./PaymentMethodTypeForms/CashKeeperSettingsFormComponent/CashKeeperSettingsFormComponent.vue";
import {PosProfileRepository} from "../../../../../shared/repositories/PosProfileRepository";
import {ProtocolFilter} from "../../../../../shared/filters/PaymentMethodFilter";
import {PosProfile} from "../../model/PosProfile";
import {
	PaymentMethodSettingsCashKeeper,
	PaymentMethodSettings_type,
	PaymentMethodSettingsCaisseAp,
	PosProfilePaymentMethodSettings,
	PosProfileApiOut
} from "@groupk/mastodon-core";
import {CashkeeperConnection} from "@groupk/cashkeeper-protocol";

@Component({
	components: {
		'form-modal': FormModalOrDrawerComponent,
		'dropdown': DropdownComponent,
		'caisse-app-settings-form': CaisseApSettingsFormComponent,
		'cash-keeper-settings': CashKeeperSettingsFormComponent
	}
})
export default class PaymentMethodSettingsComponent extends Vue {
	@Prop() posState!: PosState;
	@Prop() mainProfile!: PosProfileApiOut;

	PaymentProtocol = PaymentProtocol;

	editing: {
		editIndex: number|null
		method: PaymentMethodApiOut,
		settings: PaymentMethodSettings_type|null
	}|null = null;

	@AutoWired(PaymentMethodRepository) accessor paymentMethodRepository!: PaymentMethodRepository;
	@AutoWired(PosProfileRepository) accessor posProfileRepository!: PosProfileRepository;

	updateProtocol(protocol: PaymentProtocol) {
		if(!this.editing) return;
		if(protocol === PaymentProtocol.PHYSICAL_CASHKEEPER) {
			this.editing.settings = new PaymentMethodSettingsCashKeeper({
				name: 'CashKeeper',
				securitySeed: CashkeeperConnection.DEFAULT_SECURITY_SEED,
				address: '',
				masterPort: CashkeeperConnection.DEFAULT_MASTER_PORT,
				officePort: CashkeeperConnection.DEFAULT_OFFICE_PORT,
			});
		} else if(protocol === PaymentProtocol.PHYSICAL_CAISEAP) {
			this.editing.settings = new PaymentMethodSettingsCaisseAp({
				name: 'TPE',
				port: 0,
				ip: ''
			});
		} else {
			this.editing.settings = null;
		}
		this.$forceUpdate();
	}

	addSettings(method: PaymentMethodApiOut){
		this.editing = {
			editIndex: null,
			method: method,
			settings: new PaymentMethodSettingsCaisseAp({
				name: 'TPE',
				port: 0,
				ip: ''
			})
		}
	}

	getAuthorizedProtocolsDropdownValues() {
		if(!this.editing) return [];
		return allowedPaymentProtocolsPerPaymentMethod[this.editing.method.type].map((protocol) => {
			return {
				name: ProtocolFilter(protocol),
				value: protocol
			}
		});
	}

	editDevice(paymentMethod: PaymentMethodApiOut, settings: PaymentMethodSettings_type, index: number) {
		this.editing = {
			editIndex: index,
			method: paymentMethod,
			settings: settings
		}
	}

	saveSettings(settings: PaymentMethodSettings_type) {
		if(!this.editing) return;
		if(!this.mainProfile.paymentMethodConfigs) this.mainProfile.paymentMethodConfigs = [];
		const config = new PosProfilePaymentMethodSettings({
			paymentMethodUid: UuidUtils.visualToScoped<UuidScopePayment_method>(this.editing.method.uid),
			settings: settings
		});
		if(this.editing.editIndex !== null) {
			this.mainProfile.paymentMethodConfigs.splice(this.editing.editIndex, 1, config);
		} else {
			this.mainProfile.paymentMethodConfigs.push(config);
		}

		if(this.mainProfile.uid) this.posProfileRepository.callContract('update', {establishmentUid: this.posState.establishmentUid, posProfileUid: this.mainProfile.uid}, PosProfile.apiOutToApiIn(this.mainProfile));

		this.editing = null;
	}

	deleteSettings() {
		if(!this.editing || this.editing.editIndex === null) return;
		if(!this.mainProfile.paymentMethodConfigs) this.mainProfile.paymentMethodConfigs = [];
		this.mainProfile.paymentMethodConfigs.splice(this.editing.editIndex, 1);
		if(this.mainProfile.uid) this.posProfileRepository.callContract('update', {establishmentUid: this.posState.establishmentUid, posProfileUid: this.mainProfile.uid}, PosProfile.apiOutToApiIn(this.mainProfile));
		this.editing = null;
	}

	close() {
		this.editing = null;
	}

	getSettingsForPaymentMethod(paymentMethodUid : VisualScopedUuid<UuidScopePayment_method>): {settings: PaymentMethodSettings_type, index: number}[] {
		const settings: {settings: PaymentMethodSettings_type, index: number}[] = [];
		let index: number = 0;
		for(let config of this.mainProfile.paymentMethodConfigs ?? []) {
			if(config.paymentMethodUid === UuidUtils.visualToScoped(paymentMethodUid)) settings.push({settings: config.settings, index: index});
			index++;
		}
		return settings;
	}
}