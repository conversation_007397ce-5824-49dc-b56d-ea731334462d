import {Component, Prop, Vue} from "vue-facing-decorator";
import {MetadataDescriptorTemplateBoolApiOut} from "@groupk/mastodon-core";

@Component({
    emits: ['validated']
})
export default class BoolDescriptorComponent extends Vue {
    @Prop({required: true}) metadataDescriptorTemplate!: MetadataDescriptorTemplateBoolApiOut;
    @Prop({default: null}) defaultValue!: boolean|null;

    value: boolean = false;

    beforeMount() {
        if(this.defaultValue) this.value = this.defaultValue;
    }

    validate() {
        this.$emit('validate', this.value);
    }
}