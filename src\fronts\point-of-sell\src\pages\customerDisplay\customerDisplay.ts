import {Component, Ref, Vue} from "vue-facing-decorator";
import {AutoWired, buildHttpRoutePathWithArgs, ScopedUuid} from "@groupk/horizon2-core";
import {DisplaysNative, NativeHelper, NativeInterface} from "@groupk/native-bridge";
import {
    EstablishmentApiOut,
    MastodonHttpImagesContract,
    UuidScopeEstablishment
} from "@groupk/mastodon-core";
import {EstablishmentRepository} from "../../../../../shared/repositories/EstablishmentRepository";
import {Router} from "@groupk/horizon2-front";
import {MainConfig} from "../../../../../shared/MainConfig";
import {ErrorHandler} from "../../class/ErrorHandler";

export interface CustomerDisplayPurchase {
    name: string,
    quantity: number,
    unitPriceWithTax: number
}

export interface CustomerDisplayOrder {
    purchases: CustomerDisplayPurchase[];
    total: number
}

export type CustomerDisplayMessage = CustomerDisplayOrder|null;

@Component({})
export default class customerDisplay extends Vue {
    establishment: EstablishmentApiOut|null = null;
    currentOrder: CustomerDisplayOrder|null = null;

    loading: boolean = true;

    @Ref() leftPanel!: HTMLDivElement;

    @AutoWired(EstablishmentRepository) accessor establishmentRepository!: EstablishmentRepository;
    @AutoWired(DisplaysNative) accessor displaysNative!: DisplaysNative;
    @AutoWired(MainConfig) accessor mainConfig!: MainConfig;
    @AutoWired(ErrorHandler) accessor errorHandler!: ErrorHandler;
    @AutoWired(Router) accessor router!: Router;

    async mounted() {
        let regexMatch = this.router.lastRouteRegexMatches;

        const preLoaderDiv = document.getElementById('pre-loader');
        if(preLoaderDiv) preLoaderDiv.remove();

        if (regexMatch && regexMatch[1]) {
            try {
                this.establishment = (await this.establishmentRepository.callContract('get', {establishmentUid: regexMatch[1] as ScopedUuid<UuidScopeEstablishment>}, undefined)).success()
            } catch(err) {}
        }

        this.displaysNative.listenMessages(NativeInterface.instance.getContext().screenId).catch((err) => {
            this.errorHandler.logEverywhere({
                title: 'Cannot listen display messages',
                description: '',
                error: err
            })
        });

        this.displaysNative.on('message', (payload: {srcScreenId: string, data: unknown}): void => {
            const typedData = payload.data as CustomerDisplayMessage;

            if(typedData === null) this.currentOrder = null;
            if(typedData && typeof typedData === 'object' && 'purchases' in typedData) {
                this.currentOrder = typedData;

                setTimeout(() => {
                    this.leftPanel.scrollTo(0, this.leftPanel.scrollHeight);
                }, 0);
            }
        });

        this.loading = false;
    }

    unmounted() {
        this.displaysNative.disable(NativeHelper.getInstance().getContext().screenId);
    }

    getEstablishmentImageUrl() {
        if(!this.establishment || !this.establishment.mainImageUpload) return '';
        return this.mainConfig.configuration.mastodonApiEndpoint + buildHttpRoutePathWithArgs(MastodonHttpImagesContract.getPublicImage, {
            establishmentUid: this.establishment.uid,
            imageId: this.establishment.mainImageUpload.uid,
            options: {},
            dimensions: {width: 128},
            extension: 'png',
            resizeType: 'contain',
            revision: this.establishment.mainImageUpload.lastUpdateDatetime
        });
    }

}