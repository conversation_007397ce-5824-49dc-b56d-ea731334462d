<script lang="ts" src="./StatisticsComponent.ts">
</script>

<style lang="sass" scoped>
@import './StatisticsComponent.scss'
</style>

<template>
    <div id="statistics-component">
        <div class="button-group">
            <button class="button" :class="{active: currentView === 'EMPLOYEES'}" @click="currentView = 'EMPLOYEES'"> Employés </button>
            <button class="button" :class="{active: currentView === 'PRODUCTS'}" @click="currentView = 'PRODUCTS'"> Ventes Produits </button>
            <button class="button" :class="{active: currentView === 'STOCK'}" @click="currentView = 'STOCK'"> Stock Produits </button>
        </div>

        <template v-if="currentView === 'EMPLOYEES'">
            <div class="key-numbers">
                <div class="key-number">
                    <span class="type"> CA Total </span>
                    <span class="value"> {{ $filters.Money(ordersStatistics.totals.withTaxes) }} </span>
                </div>
                <div class="key-number">
                    <span class="type"> Nombre de commandes </span>
                    <span class="value"> {{ orders.length }} </span>
                </div>
                <div class="key-number">
                    <span class="type"> Panier moyen </span>
                    <span class="value" v-if="orders.length > 0"> {{ $filters.Money(ordersStatistics.totals.withTaxes / orders.length) }} </span>
                    <span class="value" v-else> {{ $filters.Money(0) }} </span>
                </div>
            </div>

            <div class="table-scroll-wrapper">
                <table class="data-table">
                    <thead>
                    <tr>
                        <td> Vendeur </td>
                        <td v-for="method of posState.paymentMethods"> {{ method.name }} </td>
                        <td> Impayés </td>
                        <td> Total </td>
                    </tr>
                    </thead>
                    <tbody>
                    <tr v-for="(data, sellerUid) in ordersStatistics.perSeller">
                        <td> {{ posState.getEstablishmentAccountWithUid(sellerUid).firstname }} </td>
                        <td v-for="method of posState.paymentMethods">
                            {{ $filters.Money(data.perPaymentMethod[method.uid] ? data.perPaymentMethod[method.uid].success : 0) }}
                        </td>
                        <td> {{ $filters.Money(data.leftToPay) }} </td>
                        <td> {{ $filters.Money(data.withTaxes) }} </td>
                    </tr>
                    <tr>
                        <td> Total </td>
                        <td v-for="method of posState.paymentMethods">
                            {{ $filters.Money(ordersStatistics.perPaymentMethod[method.uid] ? ordersStatistics.perPaymentMethod[method.uid].success : 0) }}
                        </td>
                        <td> {{ $filters.Money(ordersStatistics.totals.leftToPay) }} </td>
                        <td> {{ $filters.Money(ordersStatistics.totals.withTaxes) }} </td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </template>

        <template v-else-if="currentView === 'PRODUCTS'">
            <table class="data-table">
                <thead>
                <tr>
                    <td> Produit </td>
                    <td> Unités </td>
                    <td> CA </td>
                </tr>
                </thead>
                <tbody>
                <tr v-for="(data, index) in productStatistics">
                    <td> <b> {{ index + 1 }}. </b> {{ data.name }}</td>
                    <td> {{ data.quantity }} </td>
                    <td> {{ $filters.Money(data.totalPriceWithTaxes) }} </td>
                </tr>
                </tbody>
            </table>
        </template>

        <template v-else-if="currentView === 'STOCK'">

            <div class="stock-params">
                <div class="input">
                    <input v-model="stockSearch" placeholder="Rechercher un produit..." type="text" />
                </div>
                <button class="small white button" @click="toggleAllFavorite()">
                    <i class="fa-regular fa-star"></i>
                    Tout mettre en favori
                </button>
            </div>

            <table class="data-table">
                <thead>
                <tr>
                    <td> Produit </td>
                    <td> Stock </td>
                    <td class="hover-able">
                        Favori
                        <i class="fa-regular fa-circle-info"></i>
                        <div class="popup">
                            Affiche le stock dans la caisse
                        </div>
                    </td>
                </tr>
                </thead>
                <tbody>
                <template :key="refreshKey" v-for="product in filteredProducts">
                    <stock-line :product="product" :pos-state="posState"></stock-line>
                </template>
                </tbody>
            </table>
        </template>
    </div>
</template>