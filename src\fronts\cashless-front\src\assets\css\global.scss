
:root {
  --primary-hover-color: rgba(42, 185, 217, 0.1) !important;
  --primary-hover-text-color: white !important;
  --primary-color: #2AB9D9 !important;
  --primary-text-color: white !important;
  --primary-button-hover-color: #1ea8c7 !important;
}

html, body {
  height: 100%;
  margin: 0;
  padding: 0;
  overflow: hidden;
}

*:not(i, text) {
  font-family: 'Inter', sans-serif !important;
}

.page {
  overflow: hidden;
  height: 100%;
  width: 100%;
}

#mainRouterContainer {
  width: 100%;
  height: 100%;
}

// Computer
@media (min-width: 900px) {
  body, #mainRouterContainer {
    display: flex;
  }
}

// Mobile & Tablet
@media (max-width: 900px) {
  #mainRouterContainer .page {
    padding-top: 60px;
  }
}

.layout-main-content-right-panel {
  .close {
    display: none;
    margin-bottom: 20px;
    cursor: pointer;
    user-select: none;
  }

  @media screen and (max-width: 1400px) {
    .close {
      display: flex;
      gap: 10px;
      align-items: center;
    }
  }
}

.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 20px;
}


.button {
  all: unset;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  padding: 10px 15px;
  height: 42px;
  background: #EAF8FC;
  border-radius: 50px;
  box-sizing: border-box;
  cursor: pointer;

  &:hover {
    filter: brightness(0.96);
  }

  font-family: Inter, sans-serif;
  font-size: 13px;
  font-weight: 600;

  i {
    font-size: 16px;
  }

  &.icon {
    justify-content: center;
    height:  42px;
    width: 42px;
    padding: 0;
    border-radius: 50%;
  }

  &.grey {
    background: #F2F2F2;
  }
}