import {Component, Prop, Vue} from "vue-facing-decorator";
import {
	DropdownComponent, FormModalOrDrawerComponent,
} from "@groupk/vue3-interface-sdk";
import {AutoWired} from "@groupk/horizon2-core";
import {CustomerRepository} from "../../../../../shared/repositories/CustomerRepository";
import {CashlessEstablishmentRepository} from "../../../../../shared/repositories/CashlessEstablishmentRepository";
import {
	ApplicationPermission,
	CashlessEstablishmentApi, CashlessHttpEstablishmentContract, CashlessHttpProfileContract,
	EstablishmentAccountPermissionModel, PaymentHttpMethodContract
} from "@groupk/mastodon-core";
import {CashlessEstablishmentData} from "../../../../../shared/mastodonCoreFront/cashless/CashlessEstablishmentData";
import {AppState} from "../../../../../shared/AppState";

export function CashlessEstablishmentFormComponentHasRequiredPermissions(ownedPermissions: ApplicationPermission[]): boolean {
	return EstablishmentAccountPermissionModel.hasPermissionsForContracts(ownedPermissions, [
		CashlessHttpEstablishmentContract.update,
	]);
}

@Component({
	components: {
		dropdown: DropdownComponent,
		'form-modal-or-drawer': FormModalOrDrawerComponent,
	},
	emits: ['close', 'chose-export']
})
export default class CashlessEstablishmentFormComponent extends Vue {
	@Prop({required: true}) editingCashlessEstablishment!: CashlessEstablishmentApi;

	cashlessEstablishment!: CashlessEstablishmentData;

	error: string|null = null;
	opened: boolean = false;
	saving: boolean = false;

	@AutoWired(CustomerRepository) accessor customerRepository!: CustomerRepository;
	@AutoWired(CashlessEstablishmentRepository) accessor cashlessEstablishmentRepository!: CashlessEstablishmentRepository;
	@AutoWired(AppState) accessor appState!: AppState;

	beforeMount() {
		this.cashlessEstablishment = new CashlessEstablishmentData(this.editingCashlessEstablishment);
	}

	mounted() {
		setTimeout(() => this.opened = true, 0);
	}

	close() {
		this.opened = false;
		setTimeout(() => this.$emit('close'), 300);
	}

	addAdminChip() {
		if(!this.cashlessEstablishment.adminPublicChipIds) this.cashlessEstablishment.adminPublicChipIds = [];
		this.cashlessEstablishment.adminPublicChipIds.push('');
	}

	setAdminPublicChip(event: Event, index: number) {
		if(!this.cashlessEstablishment.adminPublicChipIds) return;
		if(event.target && event.target instanceof HTMLInputElement){
			this.cashlessEstablishment.adminPublicChipIds[index] = event.target.value.toUpperCase();
		}
	}

	async saveCashlessEstablishment() {
		this.saving = true;

		const response = await this.cashlessEstablishmentRepository.callContract('update', {establishmentUid: this.appState.requireUrlEstablishmentUid()}, this.cashlessEstablishment.toApi())
		if(response.isSuccess()) {
			this.$emit('updated', response.success());
			this.close();
		} else {
			const error = response.error();
			this.error = error && 'error' in error ? (error?.error ?? 'Une erreur inconnue est survenue') : 'Une erreur inconnue est survenue';
		}

		this.saving = false;

	}
}