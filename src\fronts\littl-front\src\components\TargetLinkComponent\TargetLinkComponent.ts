import {Component, Prop, Vue} from 'vue-facing-decorator';
import {
	ToggleComponent
} from '@groupk/vue3-interface-sdk';
import {LinkTarget} from '@groupk/mastodon-core';
import {CleaveDirective} from "../../../../../shared/directives/CleaveDirective";

@Component({
	components: {
		'toggle': ToggleComponent,
	},
	directives: {
		cleave: CleaveDirective
	},
	emits: ['enter'],
})
export default class TargetLinkComponent extends Vue {
	@Prop() target!: LinkTarget;

	linkTargetDefinition = LinkTarget.__entityDefinition;

	enter(){
		this.$emit('enter');
	}
}
