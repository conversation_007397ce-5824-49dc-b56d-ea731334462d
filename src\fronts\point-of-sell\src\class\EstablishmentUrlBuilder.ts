import {GetInstance, ScopedUuid} from "@groupk/horizon2-core";
import {Router} from "@groupk/horizon2-front";
import {UuidScopeEstablishment} from '@groupk/mastodon-core';

export class EstablishmentUrlBuilder {
	public static buildUrl(path: string) {
		const router = GetInstance(Router);
		let regexMatch = router.lastRouteRegexMatches;
		if (regexMatch && regexMatch[1]) {
			const establishmentUid = regexMatch[1] as ScopedUuid<UuidScopeEstablishment>;

			return '/establishment/' + establishmentUid + path
		}
		return path;
	}
}
