<script lang="ts" src="./ProfileCategoriesComponent.ts">
</script>

<style lang="sass">
@use './ProfileCategoriesComponent.scss' as *
</style>

<template>
    <div class="profile-categories-component">
        <div class="empty" v-if="allowedCategories.filter((data) => data.categories.length > 0).length === 0">
            Aucune catégorie dans ce point de vente

            <button class="grey button" @click="showCategoryModal = true">
                <i class="fa-regular fa-plus"></i>
                Créer une catégorie
            </button>
        </div>

        <template v-else>
            <div class="parent-category" v-for="data of allowedCategories">
                <template v-if="data.categories.length > 0">
                    <div v-if="allowedCategories.filter((data) => data.categories.length > 0).length > 1"> {{ data.parent.name }}</div>
                    <div class="categories-group">
                        <sortable
                            :elements="data.categories"
                            container-class="categories"
                            draggable-class="category"
                            handle-class="handle"
                            @sorted="updateProfileCategories(false)"
                        >
                            <div
                                class="category"
                                v-for="category of data.categories"
                                :class="{active: selectedCategory && selectedCategory.uid === category.uid}"
                                @click="selectCategory(category)"
                            >
                                <div class="handle">
                                    <i class="fa-regular fa-grip-dots-vertical"></i>
                                </div>

                                <div class="left">
                                    <span class="name"> {{ category.name }} </span>
                                    <span class="items"> {{ category.productIds.length }} produits </span>
                                </div>

                                <div class="edit" @click.stop="editingCategory = category; showCategoryModal = true">
                                    <i class="fa-regular fa-cog"></i>
                                </div>
                            </div>
                        </sortable>

                        <div class="new-category" @click="showAddCategoryToParentModal=true">
                            <i class="fa-regular fa-circle-plus"></i>
                        </div>

                        <category-form
                            v-if="showAddCategoryToParentModal"
                            :parent-categories="parentCategories"
                            :parent-category="data.parent.uid"
                            :editing-category="editingCategory"
                            @created="createdOrUpdatedCategory($event)"
                            @updated="createdOrUpdatedCategory($event)"
                            @deleted="deletedCategory($event)"
                            @close="showAddCategoryToParentModal = false; editingCategory = null"
                        ></category-form>

                        <div class="categories-group-right">
                            <div class="grey button" @click="showImportProductsModal = true">
                                <i class="fa-regular fa-arrow-up-to-line"></i>
                                <div class="column">
                                    Importer des produits
                                </div>
                            </div>
                        </div>
                    </div>
                </template>
            </div>

            <div class="products" v-if="selectedCategory">
                <sortable
                    :elements="selectedCategory.productIds"
                    container-class="category-products"
                    draggable-class="product.copy"
                    :delete-when-drag-outside="true"
                    @sorted="updateCategory();"
                >
                    <div class="empty-category-text" v-if="selectedCategory.productIds.length === 0">
                        Aucun produit dans la catégorie
                    </div>
                    <template v-for="productId in selectedCategory.productIds">
                        <div class="product copy">
                            <div class="top">
                                {{ $filters.Length(requireProductWithUid(productId).name, 40) }}
                            </div>
                            <div class="bottom">
                                {{ $filters.Money(requireProductWithUid(productId).prices[0]) }}
                            </div>
                        </div>
                    </template>
                </sortable>
            </div>

            <button class="primary button mobile-button" @click="clickedManageProducts()">
                <i class="fa-regular fa-cog"></i>
                Gérer les produits
            </button>

            <div class="device-emulator-dimmer" v-if="showDeviceEmulator">
                <div class="device-emulator">
                    <div class="notch"></div>

                    <div class="emulated-products" v-if="selectedCategory">
                        <sortable
                            :elements="selectedCategory.productIds"
                            container-class="category-products"
                            draggable-class="product.copy"
                            :delete-when-drag-outside="true"
                        >
                            <template v-for="productId in selectedCategory.productIds">
                                <div class="product copy">
                                    <div class="top">
                                        {{ $filters.Length(requireProductWithUid(productId).name, 40) }}
                                    </div>
                                    <div class="bottom">
                                        {{ $filters.Money(requireProductWithUid(productId).prices[0]) }}
                                    </div>
                                </div>
                            </template>
                        </sortable>
                    </div>
                </div>
            </div>
        </template>

        <category-form
            v-if="showCategoryModal"
            :parent-categories="parentCategories"
            :parent-category="editingCategory ? null : requireDebitCategory().uid"
            :editing-category="editingCategory"
            :display-parent-category="false"
            @created="createdOrUpdatedCategory($event)"
            @updated="createdOrUpdatedCategory($event)"
            @deleted="deletedCategory($event)"
            @close="showCategoryModal = false; editingCategory = null"
        ></category-form>

        <import-product-in-category
            ref="importProduct"
            v-if="showImportProductsModal"
            :products="products"
            :category="selectedCategory"
            @close="showImportProductsModal = false"
        ></import-product-in-category>
    </div>
</template>