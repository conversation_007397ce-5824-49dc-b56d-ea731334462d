<script lang="ts" src="./ItemActionsComponent.ts"/>

<style lang="sass" scoped>
@import './ItemActionsComponent.scss'
</style>

<template>
    <div class="item-actions-component" :class="{opened}" @click="close()">
        <div class="actions">
            <div class="action-group" v-for="action of actions">
                <div class="action" @click.stop="clickedAction(action)">
                    <i :class="action.icon"></i>
                </div>
                <span class="text"> {{ action.text }} </span>
            </div>
        </div>

    </div>
</template>