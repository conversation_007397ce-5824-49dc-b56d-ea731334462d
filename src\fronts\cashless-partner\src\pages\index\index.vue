<script lang="ts" src="./index.ts">
</script>

<style scoped lang="sass">
@use './index.scss' as *
</style>

<template>
  <div id="index-page" class="page">
    <forbidden-message v-if="forbidden"></forbidden-message>
    <layout v-else :drawer-opened="false">
      <template v-slot:content>
        <content-header
            :parameters="headerParameters"
        ></content-header>

        <div class="main-content">
          <div class="loading-container" v-if="loading">
            <div class="loader"></div>
          </div>

          <template v-else>
            <div class="steps">
              <div class="step step-1" :class="{done: products.length > 0}">

                <div class="check" v-if="products.length">
                  <i class="fa-regular fa-check"></i>
                </div>

                <div class="step-number"> 1</div>
                <div class="name"> Créez vos produits</div>
                <div class="description">
                  Créez les produits que vous allez mettre en vente via notre système Cashless
                </div>

                <div></div>

                <a :href="buildUrl('/products')" class="white button"> Créer les produits </a>
              </div>

              <div class="step step-2" :class="{done: profiles.length > 0, disabled: products.length === 0}">

                <div class="check" v-if="profiles.length > 0">
                  <i class="fa-regular fa-check"></i>
                </div>

                <div class="step-number"> 2</div>
                <div class="name"> Ajoutez vos points de vente</div>
                <div class="description">
                  Ajoutez des points de vente afin de lier vos produits à un point de vente physique
                </div>

                <div></div>

                <a :href="buildUrl('/profiles')" class="white button" :class="{disabled: products.length === 0}">
                  Ajouter un point de vente </a>
              </div>
              <div class="step step-3" :class="{disabled: products.length === 0||profiles.length === 0}">

                <div class="step-number"> 3</div>
                <div class="name"> Associez vos terminaux</div>
                <div class="description">
                  Définissez quel terminal, doit être affecté à quel point de vente.
                </div>

                <div></div>

                <a :href="buildUrl('/devices')" class="white button" :class="{disabled: products.length === 0||profiles.length === 0}">
                  Configurez vos terminaux </a>
              </div>
            </div>

            <div class="asks">
              <h2> Questions fréquentes </h2>

              <div class="icon input-group">
                <i class="fa-regular fa-search"></i>
                <input v-model="faqSearch" type="text" placeholder="Chercher une question"/>
              </div>

              <div class="ask" v-for="ask in filteredFaq" @click="ask.opened = !ask.opened">
                <div class="icon">
                  <i class="fa-regular fa-circle-question"></i>
                </div>

                <div class="content">
                  <div class="title"> {{ ask.question }}</div>
                  <div v-if="ask.opened" class="response" @click.stop="">
                    {{ ask.response }}
                  </div>
                </div>

                <i :class="{flipped: ask.opened}" class="fa-regular fa-chevron-down"></i>
              </div>
            </div>
          </template>
        </div>
      </template>
      <template v-slot:right>
      </template>
    </layout>
  </div>
</template>