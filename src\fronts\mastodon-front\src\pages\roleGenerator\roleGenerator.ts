import {Component, Vue} from "vue-facing-decorator";
import {ApplicationFrontPermissionContract} from "@groupk/mastodon-core";
import {AutoWired} from "@groupk/horizon2-core";
import {DropdownComponent} from "@groupk/vue3-interface-sdk";
import PermissionGroupFormComponent from "../../components/PermissionGroupFormComponent/PermissionGroupFormComponent.vue";
import {ApplicationFrontBackPermission} from "@groupk/mastodon-core";
import {ApplicationPermission} from "@groupk/mastodon-core";
import SidebarStateListener from "../../../../../shared/utils/SidebarStateListener";

@Component({
	components: {
		'dropdown': DropdownComponent,
		'permission-group-form': PermissionGroupFormComponent
	}
})
export default class roleGenerator extends Vue {
	applicationFrontContracts: ApplicationFrontPermissionContract[] = [];

	closedContractGroup: string[] = [];
	closedContractRoles: string[] = [];

	editingGroup: {
		id: string;
		backPermissions: ApplicationFrontBackPermission[];
	}|{
		id: string;
		frontPermissions: string[];
		backPermissions: ApplicationPermission[];
	}|null = null;

	parseError: string|null = null;

	@AutoWired(SidebarStateListener) accessor sidebarStateListener!: SidebarStateListener;

	mounted() {
		this.sidebarStateListener.setHiddenSidebar(false);
		this.sidebarStateListener.setMinimizedSidebar(false);
	}

	pastedJson(event: any) {
		setTimeout(()=>{
			this.parseContractsJson(event.target.value);
		}, 0)
	}

	parseContractsJson(rawContracts: string) {
		this.parseError = null;
		try {
			this.applicationFrontContracts = JSON.parse(JSON.parse(rawContracts));
			this.closedContractGroup = [];
			this.closedContractRoles = [];
			for(let contract of this.applicationFrontContracts) {
				this.closedContractGroup.push(contract.applicationId);
				this.closedContractRoles.push(contract.applicationId);
			}
		} catch(err) {
			this.parseError = 'Impossible de parser le string donné'
		}
	}

	addContract() {
		this.applicationFrontContracts.push({
			applicationId: '',
			roles: [],
			permissions: []
		})
	}

	addGroup(contract: ApplicationFrontPermissionContract) {
		contract.permissions.push({
			id: '',
			backPermissions: []
		})
	}

	addRole(contract: ApplicationFrontPermissionContract) {
		contract.roles.push({
			id: '',
			backPermissions: [],
			frontPermissions: [],
		})
	}

	editedPermission(data: {
		permissions: ApplicationFrontBackPermission[]
		frontPermissions: string[]
	}) {
		if(!this.editingGroup) return;
		this.editingGroup.backPermissions = data.permissions;
		if('frontPermissions' in this.editingGroup) this.editingGroup.frontPermissions = data.frontPermissions;
		this.editingGroup = null;
	}

	exportContracts() {
		console.log(JSON.stringify(JSON.stringify(this.applicationFrontContracts)));
	}
}