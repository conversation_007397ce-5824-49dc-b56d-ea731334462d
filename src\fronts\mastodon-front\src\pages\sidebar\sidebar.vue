<script lang="ts" src="./sidebar.ts" />

<style scoped lang="sass">
@import './sidebar.scss'
</style>

<template>
    <sidebar
        :opened="opened"
        :minimized="minimized"
        :hidden="hidden"
        :header="header"
        :menus="menus"
        :bottom="bottom"
        :selected-navigation="selectedNavigation"
        v-on:navigation-clicked="navigationClicked($event)"
        @close="opened = false"
    >
        <template v-slot:default>
            <establishment-switch></establishment-switch>
        </template>
    </sidebar>
</template>
