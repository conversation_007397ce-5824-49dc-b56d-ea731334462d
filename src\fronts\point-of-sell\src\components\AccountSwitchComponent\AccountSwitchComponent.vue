<script lang="ts" src="./AccountSwitchComponent.ts">
</script>

<style lang="sass" scoped>
@import './AccountSwitchComponent.scss'
</style>

<template>
    <div class="account-switch-component">
        <div class="left">
            <div
                class="account"
                v-for="establishmentAccountUid in posState.pointOfSale.establishmentAccountsUid"
                @click="changeAccount(establishmentAccountUid)"
            >
                {{ posState.getEstablishmentAccountWithUid(establishmentAccountUid).firstname  }}
                <i class="fa-solid fa-arrow-right"></i>
            </div>
        </div>
        <div class="right">

        </div>
    </div>
</template>