#metadata-page {
    .status {
        display: flex;
        align-items: center;
        justify-content: center;

        .square {
            width: 20px;
            height: 20px;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: #F2F2F2;
            border: 1px solid #E2E2E2;

            &.green {
                background-color: rgba(13, 184, 27, 0.2);
                border-color: #0db71b;
                color: #0db71b;
            }

            i {
                font-size: 12px;
            }
        }
    }

    .selected-metadata {
        display: flex;
        flex-direction: column;
        position: relative;
        gap: 20px;
        padding: 40px;

        @media (max-width: 900px) {
            padding: 20px;
        }

        .close {
            display: none;
            margin-bottom: 20px;
            cursor: pointer;
            user-select: none;

            @media screen and (max-width: 1500px) {
                display: flex;
                gap: 10px;
                align-items: center;
            }
        }

        .header {
            display: flex;
            align-items: center;
            justify-content: space-between;


            .left {
                display: flex;
                flex-direction: column;
                gap: 5px;

                h2 {
                    margin: 0;
                    font-size: 22px;
                    font-weight: bold;
                }

                span {
                    font-size: 16px;
                    color: #575757;
                }
            }
        }

        .metadata-details {
            h3 {
                margin: 20px 0 15px 0;
                font-size: 16px;
                font-weight: 600;
                color: black;

                &:first-child {
                    margin-top: 0;
                }
            }
        }
    }
}