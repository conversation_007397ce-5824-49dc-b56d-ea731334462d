<script lang="ts" src='./LimitUsageComponent.ts'>
</script>

<style lang="sass" scoped>
@use './LimitUsageComponent.scss' as *
</style>

<template>
    <div class="limit-usage-component">
        <div class="header">
            <span class="title"> Limite d’utilisation </span>
            <span class="subtitle"> Votre plan d’utilisation actuel vous offre un nombre limité de liens </span>
        </div>

        <div class="quota" v-if="!loading">
            <span>
                <b> {{ linksCount }}/{{ littlEstablishment.maxLinks }} </b>
                Liens
            </span>
            <div class="bar">
                <div class="progress links" :class="{red: linksCount === littlEstablishment.maxLinks}" :style="`width: ${(linksCount/littlEstablishment.maxLinks)*100}%`"></div>
            </div>
        </div>
        <!--                <div class="quota">-->
        <!--                    <span>-->
        <!--                        <b> 2/3 </b>-->
        <!--                        Qr codes-->
        <!--                    </span>-->
        <!--                    <div class="bar">-->
        <!--                        <div class="progress qr"></div>-->
        <!--                    </div>-->
        <!--                </div>-->

        <a :href="upgradeUrl" class="small white button">
            <i class="fa-regular fa-up"></i>
            Modifier mon offre
        </a>
    </div>
</template>