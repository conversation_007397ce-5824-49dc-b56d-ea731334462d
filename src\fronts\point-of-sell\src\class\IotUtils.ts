import {EnumUtils, GetInstance, ScopedUuid, VisualScopedUuid} from "@groupk/horizon2-core";
import {
	DeviceKnownBrand,
	EstablishmentDeviceApiOut, EstablishmentDeviceAppV2ApiOut,
	EstablishmentDeviceGenericServiceApi,
	EstablishmentDeviceKnownService,
	EstablishmentDeviceV2ApiOut, UuidScopeIot_deviceApp,
} from "@groupk/mastodon-core";
import {randomUUID} from "@groupk/horizon2-front";
import {EstablishmentDeviceRepository} from "../../../../shared/repositories/EstablishmentDeviceRepository";
import {UuidScopeEstablishment} from "@groupk/mastodon-core";
import {NativeInterface, SystemInfoNative} from "@groupk/native-bridge";
import {EstablishmentDeviceCreationNewApiIn} from "@groupk/mastodon-core";
import {PosState} from "../model/PosState";
import {AuthStateModel} from "../../../../shared/AuthStateModel";
import {LS_DEVICE_APP_UID_KEY} from "../pages/iotOnboarding/iotOnboarding";

export class IotUtils {
	static async getCurrentDeviceSerialAndBrand(): Promise<EstablishmentDeviceCreationNewApiIn> {
		if(!NativeInterface.instance.available()) {
			return new EstablishmentDeviceCreationNewApiIn({
				brand: DeviceKnownBrand.DESKTOP_CHROME,
				hardwareId: null,
				softwareId: IotUtils.getWebBrowserSerial(),
				model: null,
				dumb: false,
				comment: null,
				localIpList: undefined,
				serviceList: [new EstablishmentDeviceGenericServiceApi({
					name: EstablishmentDeviceKnownService.POINT_OF_SALE,
					version: '1'
				})],
				batteryLevel: undefined,
				ssidName: undefined
			});
		} else {
			const systemInfo = GetInstance(SystemInfoNative);

			let serial = await systemInfo.getDeviceID();
			if(!serial) serial = await systemInfo.getDeviceAppId();

			let brand = DeviceKnownBrand.UNKNOWN;
			const deviceInfos = await systemInfo.getDeviceInfo();
			for(const knownBrand of EnumUtils.values(DeviceKnownBrand)) {
				if(deviceInfos.deviceBrand.toUpperCase() === knownBrand) {
					brand = knownBrand;
					break;
				}
			}

			return new EstablishmentDeviceCreationNewApiIn({
				brand: brand,
				hardwareId: serial,
				softwareId: IotUtils.getWebBrowserSerial(),
				model: null,
				dumb: false,
				comment: null,
				localIpList: undefined,
				serviceList: [new EstablishmentDeviceGenericServiceApi({
					name: EstablishmentDeviceKnownService.POINT_OF_SALE,
					version: '1'
				})],
				batteryLevel: undefined,
				ssidName: undefined
			});
		}
	}

	static getWebBrowserSerial(): string {
		const lsKey = 'web-browser-serial';
		let serial = localStorage.getItem(lsKey);
		if(!serial) {
			serial = randomUUID();
			localStorage.setItem(lsKey, serial);
		}
		return serial;
	}

	static doesDeviceMatchExactly(against: EstablishmentDeviceV2ApiOut, device: EstablishmentDeviceCreationNewApiIn){
		return against.hardwareId === device.hardwareId &&
			against.softwareId === device.softwareId &&
			against.brand === device.brand;
	}

	static doesDeviceMatchPartially(against: EstablishmentDeviceV2ApiOut, device: EstablishmentDeviceCreationNewApiIn){
		return (against.hardwareId !== null && (against.hardwareId === device.hardwareId)) ||
			(against.softwareId !== null && (against.softwareId === device.softwareId));
	}

	static findMatchingDevice(registeredDevices: EstablishmentDeviceV2ApiOut[], device: EstablishmentDeviceCreationNewApiIn): EstablishmentDeviceV2ApiOut|null{
		for(const registeredDevice of registeredDevices) {
			if(this.doesDeviceMatchExactly(registeredDevice, device)) {
				return registeredDevice;
			}
		}
		for(const registeredDevice of registeredDevices) {
			if(this.doesDeviceMatchPartially(registeredDevice, device)) {
				return registeredDevice;
			}
		}
		return null;
	}

	static async getCurrentIotDevice(establishmentUid: VisualScopedUuid<UuidScopeEstablishment>): Promise<EstablishmentDeviceAppV2ApiOut|null> {
		const establishmentDeviceRepository = GetInstance(EstablishmentDeviceRepository);

		const storedDeviceAppUid = localStorage.getItem(LS_DEVICE_APP_UID_KEY);
		if(!storedDeviceAppUid) {
			return null;
		}

		const response = await establishmentDeviceRepository.callContract('getOneApp', {establishmentUid: establishmentUid, deviceAppUid: storedDeviceAppUid as ScopedUuid<UuidScopeIot_deviceApp>}, undefined);

		if(response.isSuccess()) {
			return response.success().item;
		} if(response.isNetworkError()) {
			throw new Error('no_internet');
		} else {
			if(response.responseCode === 403) {
				const authCenter = GetInstance(AuthStateModel);
				if(!authCenter.getStateSync()) window.location.href = '/auth';
				throw new Error('not_connected');
			} else {
				throw new Error('cannot_get_devices');
			}
		}
	}
}