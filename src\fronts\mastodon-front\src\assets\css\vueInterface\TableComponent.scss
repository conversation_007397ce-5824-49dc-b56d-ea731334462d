.table-container {
  overflow-x: auto;
  border-radius: 8px;
  border: 1px solid #E2E2E2;

  .products-table {
    width: 100%;
    border-spacing:0; /* Removes the cell spacing via CSS */


    thead {
      td {
        padding: 15px 20px;
        border-bottom: 1px solid #E2E2E2;

        font-size: 14px;
        font-style: normal;
        font-weight: 700;
        line-height: normal;
        cursor: pointer;

        .flex-container {
          display: flex;
          gap: 5px;
          align-items: center;
        }
      }
    }

    tbody {
      tr {
        td {
          padding: 20px 20px;
          border-bottom: 1px solid #E2E2E2;
          cursor: pointer;
          white-space: nowrap;

          .flex-container {
            display: flex;
            align-items: center;
            gap: 10px;

            .product-image {
              height: 32px;
              width: 32px;
              background: #D9D9D9;
              border-radius: 4px;
            }
          }
        }

        &:last-child {
          td {
            border: none;
          }
        }
      }
    }
  }
}