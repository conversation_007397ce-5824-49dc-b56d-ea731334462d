.negative-space {
    margin: 0 -10px;
}

.default-slot {
    display: flex;
    flex-direction: column;
    gap: 20px;

    .platform-logo {
        height: 45px;
        width: auto;
    }
}

.reseller {
    display: flex;
    padding: 10px;
    align-items: center;
    gap: 10px;
    border-radius: var(--radius-md, 8px);
    background: #F2F2F2;
    cursor: pointer;
    margin-top: 20px;

    &.hidden {
        display: none;
    }

    img {
        width: 44px;
        height: 44px;
        flex-shrink: 0;
    }

    .data {
        display: flex;
        flex-direction: column;
        gap: 4px;

        .name {
            font-size: 14px;
            font-weight: 500;
        }

        .info {
            font-size: 12px;
        }
    }
}

.advanced {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 5px;
    padding: 10px;
    border-radius: 8px;
    background: #fdc17a;
    margin-top: 10px;
    color: black;
    font-size: 14px;
}