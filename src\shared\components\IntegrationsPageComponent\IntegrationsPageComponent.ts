import {Component, Vue} from "vue-facing-decorator";
import {
    ContentHeaderComponent,
    ContentHeaderParameters,
    FormModalOrDrawerComponent,
    LayoutContentWithRightPanelComponent
} from "@groupk/vue3-interface-sdk";
import {AutoWired} from "@groupk/horizon2-core";
import {AppApiOut_type, AppType, PaymentProviderStripeApiIn} from "@groupk/mastodon-core";
import StripeAppSelectorComponent from "../StripeAppSelectorComponent/StripeAppSelectorComponent.vue";
import AppIntegrationListComponent
    from "../AppIntegrationListComponent/AppIntegrationListComponent.vue";
import {PaymentProviderStripeRepository} from "../../repositories/PaymentProviderStripeRepository";
import SidebarStateListener from "../../utils/SidebarStateListener";
import {AppState} from "../../AppState";
import {AppRepository} from "../../repositories/AppRepository";
import CustomIntegrationFormComponent
    from "../CustomIntegrationFormComponent/CustomIntegrationFormComponent.vue";
import ReservitIntegrationComponent from "../ReservitIntegrationComponent/ReservitIntegrationComponent.vue";
import ProductReservitBindingComponent from "../ProductReservitBindingComponent/ProductReservitBindingComponent.vue";
import {PlatformState} from "../../../fronts/ticketing-partner/src/PlatformState";

@Component({
    components: {
        'content-header': ContentHeaderComponent,
        'layout': LayoutContentWithRightPanelComponent,
        'custom-integration-form': CustomIntegrationFormComponent,
        'stripe-app-selector': StripeAppSelectorComponent,
        'form-modal-or-drawer': FormModalOrDrawerComponent,
        'app-integration-list': AppIntegrationListComponent,
        'product-reservit-binding': ProductReservitBindingComponent,
        'reservit-integration': ReservitIntegrationComponent
    }
})
export default class IntegrationsPageComponent extends Vue {
    apps: AppApiOut_type[] = [];

    AppType = AppType;

    showIntegrationListModal: AppType|null = null;
    editingCustomIntegration: AppApiOut_type|null = null;
    showCustomIntegrationFormModal: boolean = false;
    showStripeModal: boolean = false;
    showReservitModal: boolean = false;
    showReservitConfigurationModal: boolean = false;
    showStripeProcessorModal: boolean = false;
    creatingStripeProcessorModal: boolean = false;

    @AutoWired(PaymentProviderStripeRepository) private accessor paymentProviderStripeRepository!: PaymentProviderStripeRepository;
    @AutoWired(AppRepository) private accessor appRepository!: AppRepository;
    @AutoWired(AppState) private accessor appState!: AppState;
    @AutoWired(PlatformState) private accessor platformState!: PlatformState;
    @AutoWired(SidebarStateListener) private accessor sidebarStateListener!: SidebarStateListener;

    headerParameters: ContentHeaderParameters = {
        header: 'Intégrations',
        subtitle: 'Activez des intégrations pour relier Billetterie à des applications externes',
        actions: [],
        hideSearch: true,
        searchPlaceholder: 'Rechercher une réduction',
    }

    async mounted() {
        this.sidebarStateListener.setHiddenSidebar(false);
        this.apps = (await this.appRepository.callContract('list', {establishmentUid: this.appState.requireUrlEstablishmentUid()}, undefined)).success().list;

        const url = new URL(window.location.href);
        if(url.searchParams.get('from') === 'stripe_account_binding') {
            if(this.getAppsForType(AppType.STRIPE).length > 0) {
                this.showStripeProcessorModal = true;
            }
            url.searchParams.delete('from');
            history.pushState({}, '', url.toString());
        }
    }

    async selectedStripeApp() {
        this.creatingStripeProcessorModal = true;

        const stripeApp = this.getAppsForType(AppType.STRIPE)[this.getAppsForType(AppType.STRIPE).length - 1];

        const result = await this.paymentProviderStripeRepository.callContract('create', {establishmentUid: this.appState.requireUrlEstablishmentUid()}, new PaymentProviderStripeApiIn({
            appUid: stripeApp.uid
        }));

        if(result.isSuccess()) {

        }

        this.showStripeProcessorModal = false;
        this.creatingStripeProcessorModal = false;
    }

    async selectedSpecificStripeApp(stripeApp: AppApiOut_type) {
        this.creatingStripeProcessorModal = true;

        const result = await this.paymentProviderStripeRepository.callContract('create', {establishmentUid: this.appState.requireUrlEstablishmentUid()}, new PaymentProviderStripeApiIn({
            appUid: stripeApp.uid
        }));

        if(result.isSuccess()) {
            this.platformState.requirePaymentProviders().push(result.success());
        }

        this.creatingStripeProcessorModal = false;
    }

    getAppsForType(type: AppType) {
        return this.apps.filter((app) => app.type === type);
    }
}