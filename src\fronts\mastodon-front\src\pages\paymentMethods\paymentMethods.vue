<script lang="ts" src="./paymentMethods.ts">
</script>

<style lang="sass">
@import './paymentMethods.scss'
</style>

<template>
    <div id="payment-methods-page">
        <filter-table-layout
            :header-parameters="headerParameters"
            :allowed-filters="{filters: [], sorts: []}"
            :table-columns="tableColumns"
            :filters="{}"
            :drawer-opened="selectedPaymentMethod !== null"
            @changed-column-preferences="saveColumnPreferences($event)"
        >
            <template v-slot:table-data>
                <div class="table-dimmer" v-if="loading">
                    <div class="loader-container">
                        <div class="loader"></div>
                    </div>
                </div>
                <tr class="table-no-data" v-else-if="paymentMethods.length === 0">
                    <td colspan="100%">Aucune donnée</td>
                </tr>
                <tr v-else v-for="paymentMethod in paymentMethods" :class="{selected: selectedPaymentMethod && paymentMethod.uid === selectedPaymentMethod.uid}" @dblclick="toggleSelectedMethod(paymentMethod, $event)">
                    <td :class="{'mobile-hidden': column.mobileHidden}" v-for="column in tableColumns.filter((column) => column.displayed)">
                        <template v-if="column.name === 'uid'"> {{ paymentMethod.uid }} </template>
                        <template v-if="column.name === 'name'"> {{ paymentMethod.name }} </template>
                        <template v-if="column.name === 'type'"> {{ $filters.PaymentMethodType(paymentMethod.type) }} </template>
                        <template v-if="column.name === 'creationDatetime'"> {{ $filters.Date(paymentMethod.creationDatetime) }} </template>
                    </td>
                </tr>
            </template>
            <template v-slot:right>
<!--                <div v-if="!selectedWallet" class="empty-right-panel">-->
<!--                    <img src="../../assets/img/select-hint.svg" />-->
<!--                    Cliquer sur un support pour <br/> le sélectionner-->
<!--                </div>-->
<!--                <div v-else class="selected-product">-->
<!--                    <div class="close">-->
<!--                        <i class="fa-regular fa-xmark"></i>-->
<!--                        <span> Fermer </span>-->
<!--                    </div>-->

<!--                    <div class="header">-->
<!--                        <div class="left">-->
<!--                            <h2> {{ selectedWallet.publicId }} </h2>-->
<!--                        </div>-->
<!--                    </div>-->
<!--                </div>-->
            </template>
        </filter-table-layout>
    </div>

    <payment-method-form
        v-if="showCreationModal"
        :editing-payment-method="editingMethod"
        @close="showCreationModal = false; editingMethod = null"
        @updated="updatedMethod($event)"
    ></payment-method-form>
</template>