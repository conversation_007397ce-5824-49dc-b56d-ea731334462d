<script lang="ts" src="./PaymentMethodFormComponent.ts">
</script>

<style lang="sass">
@import './PaymentMethodFormComponent.scss'
</style>

<template>
    <div class="discount-template-form-component">
        <form-modal-or-drawer
            :state="opened"
            :title="editingPaymentMethod ? 'Modifier la méthode de paiement' : 'Ajouter une méthode de paiement'"
            :subtitle="editingPaymentMethod ? 'Modifier la méthode de paiement' : 'Ajouter une méthode de paiement'"
            @close="close()"
        >
            <template v-slot:content>
                <div class="input-group">
                    <label> Nom </label>

                    <div class="fluid input">
                        <input v-model="name" class="fluid" placeholder="Nom" />
                    </div>
                </div>

                <div class="input-group" v-if="!editingPaymentMethod">
                    <label> Type </label>

                    <div class="fluid input">
                        <dropdown
                            :values="getTypeDropdownValues()"
                            :searchable="true"
                            :default-selected="type"
                            placeholder="Type"
                            @update="type = $event"
                        ></dropdown>
                    </div>
                </div>

                <div class="form-error" v-if="error">
                    <i class="fa-solid fa-exclamation-circle"></i>
                    <div class="details">
                        <span class="title"> Erreur </span>
                        <span class="description">{{ error }}</span>
                    </div>
                </div>
            </template>

            <template v-slot:buttons>
                <button type="button" class="white button" @click="close()"> Annuler </button>
                <button type="button" class="button" :class="{loading: loading, disabled: loading}" @click="create()">
                    <i v-if="!editingPaymentMethod" class="fa-regular fa-circle-plus"></i>
                    <i v-else class="fa-regular fa-circle-plus" ></i>
                    {{ editingPaymentMethod ? 'Modifier la réduction' : 'Créer la réduction' }}
                </button>
            </template>
        </form-modal-or-drawer>
    </div>
</template>