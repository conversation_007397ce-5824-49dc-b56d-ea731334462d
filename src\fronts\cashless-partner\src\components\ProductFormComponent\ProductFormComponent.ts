import {Component, Prop, Vue} from "vue-facing-decorator";
import {FormModalOrDrawerComponent, FileInputComponent, InputPriceComponent} from "@groupk/vue3-interface-sdk";
import {
	SimpleProductApiOut,
	ApplicationPermission,
	EstablishmentAccountPermissionModel,
	CashlessHttpSimpleProductContract
} from "@groupk/mastodon-core";
import {AutoWired, VisualScopedUuid} from "@groupk/horizon2-core";
import {ProductsRepository} from "../../../../../shared/repositories/ProductsRepository";
import {UuidScopeEstablishment} from "@groupk/mastodon-core";
import IconBankComponent from "../IconBankComponent/IconBankComponent.vue";
import {SimpleProductData} from "../../../../../shared/mastodonCoreFront/cashless/SimpleProductData";

export function ProductFormComponentHasRequiredPermissions(ownedPermissions: ApplicationPermission[]): boolean {
	return EstablishmentAccountPermissionModel.hasPermissionsForContracts(ownedPermissions, [
		CashlessHttpSimpleProductContract.create,
		CashlessHttpSimpleProductContract.update,
	]);
}

@Component({
	components: {
		'form-modal-or-drawer': FormModalOrDrawerComponent,
		'file-input-component': FileInputComponent,
		'price-input': InputPriceComponent,
		'icon-bank': IconBankComponent
	},
	emits: ['saved']
})
export default class ProductFormComponent extends Vue {
	@Prop() establishmentUid!: VisualScopedUuid<UuidScopeEstablishment>;
	@Prop({default: null}) editingProduct!: SimpleProductApiOut|null;
	@Prop({default: 0}) nextProductId!: number;

	product: SimpleProductData = new SimpleProductData();

	showIconModal: boolean = false;
	saving: boolean = false;
	error: string|null = null;

	@AutoWired(ProductsRepository) accessor productsRepository!: ProductsRepository

	beforeMount() {
		this.product = new SimpleProductData(this.editingProduct);
		if(!this.editingProduct) this.product.id = this.nextProductId
	}

	selectIcon(icon: string) {
		this.product.img = icon;
		this.showIconModal = false;
	}

	async save() {
		this.saving = true;
		this.error = null;

		try {
			const apiIn = this.product.toApiIn();

			if(!this.editingProduct) {
				const response = await this.productsRepository.callContract('create', {establishmentUid: this.establishmentUid}, apiIn);
				if(response.isSuccess()) {
					const product = response.success();

					this.$emit('saved', product);
				} else {
					const error = response.error();
					if(error && 'error' in error) {
						this.error = error.error_details;
					} else {
						this.error = 'Erreur inconnue';
					}
				}
			} else {
				const response = await this.productsRepository.callContract('update', {establishmentUid: this.establishmentUid, productId: this.editingProduct.id}, apiIn);
				if(response.isSuccess()) {
					const product = response.success();

					this.$emit('saved', product);
				} else {
					const error = response.error();
					if(error && 'error' in error) {
						this.error = error.error_details;
					} else {
						this.error = 'Erreur inconnue';
					}
				}
			}

		} catch(err) {
			// form errors
		}

		this.saving = false;
	}
}