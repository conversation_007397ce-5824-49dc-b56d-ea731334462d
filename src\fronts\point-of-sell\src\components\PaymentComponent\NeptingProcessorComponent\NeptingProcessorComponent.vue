<script lang="ts" src="./NeptingProcessorComponent.ts">
</script>

<style lang="scss">
@use './NeptingProcessorComponent.scss' as *;
</style>

<template>
    <template v-if="state !== 'NOTHING' ">
        <div class="nepting-processor-component"
             :class="{error: result?.status === PaymentMethodDataStatus.ERROR}">
            <div class="buttons" v-if="options.showOfflineTransactions">
                <div @click="forceNeptingSync()"
                     class="stuckTransactionsBar description label" v-if="offlineState && !offlineState.offline && offlineState.stuckTransactions > 0"
                     :class="{major: isNeptingOfflineStateReallyOld()}"
                >
                    <i class="fa-solid fa-circle-exclamation" v-if="isNeptingOfflineStateReallyOld()"></i>&nbsp;
                    <template v-if="offlineState.stuckTransactions > 1" >
                        {{ offlineState.stuckTransactions }} paiements CB non synchronisés
                    </template>
                    <template v-else>
                        {{ offlineState.stuckTransactions }} paiement CB non synchronisé
                    </template>
                    <i class="fa-solid fa-arrows-rotate" style="float: right"></i>
                </div>
            </div>

            <template v-if="internalError !== undefined">
                <div class="top">
                    <span class="title red"> Erreur interne </span>
                    <i class="big-result-icon red fa-solid fa-circle-xmark"></i>
                    <span class="subtitle">
						{{JSON.stringify(internalError)}}
					</span>
                </div>
                <div class="buttons">
                    <div class="big white button" @click="closeEvent()">
                        Quitter
                    </div>
                </div>
            </template>

            <div class="receipt-page" v-else-if="state === 'RESPONSE_BUTTONS'">
                <div class="option" v-if="bestPrinter !== null" @click="printReceipt()">
                    <i class="fa-solid fa-print"></i>
                    Imprimer
                </div>
                <div class="option" v-if="qrCodeImgBase64 !== undefined" @click="state = 'RESPONSE_QR'">
                    <i class="fa-solid fa-qrcode"></i>
                    QR Code
                </div>
                <div class="option" @click="state = 'RESPONSE_RAW'">
                    <i class="fa-solid fa-eye"></i>
                    Afficher
                </div>
                <button class="big grey button" @click="state = 'RESPONSE' ">
                    Retour
                </button>
            </div>

            <div class="receipt-page"  v-else-if="state === 'RESPONSE_QR'">
                <div class="qr-code-container" v-if="qrCodeImgBase64">
                    <img :src="qrCodeImgBase64" alt="Reçu de paiement"/>
                </div>

                <button class="big blue button" v-if="result?.status === PaymentMethodDataStatus.SUCCESS" @click="closeEvent()">
                    Continuer vers Cashless
                </button>
                <button class="big grey button" @click="state = 'RESPONSE_BUTTONS'">
                    Retour
                </button>
            </div>

            <div class="receipt-page" v-else-if="state === 'RESPONSE_RAW'">
				<pre class="raw-receipt">
					{{result?.ticket}}
				</pre>
                <button class="big blue button" v-if="result?.status === PaymentMethodDataStatus.SUCCESS" @click="closeEvent()">
                    Continuer vers Cashless
                </button>
                <button class="big grey button" @click="state = 'RESPONSE_BUTTONS'">
                    Retour
                </button>
            </div>

            <template v-else-if="result === null">
<!--                <img src="../../../assets/imgs/svg/contactless-card-logo.svg" />-->
                <div class="header">
                    <span class="title"> En attente ... </span>
                    <span class="subtitle"> Vous allez être redirigé sur l'app de paiement </span>
                </div>
                <div class="spinner"></div>
            </template>

            <template v-else-if="result">
                <template v-if="result.status === PaymentMethodDataStatus.SUCCESS">
                    <div class="top">
                        <span class="title" v-if="result.type === 'PAYMENT'"> {{getPaymentTypeName()}} réussi </span>
                        <span class="title" v-else-if="result.type === 'LOGIN'"> Connexion réussi </span>
                        <i class="big-result-icon fa-solid fa-circle-check"></i>
                        <span class="subtitle" v-if="result?.errorDetails">
							{{result.errorDetails}}
						</span>
                    </div>
                    <div class="buttons">
                        <div class="description label" v-if="result?.signatureRequired && result?.ticket">
                            La signature du reçu de paiement par le client est requise pour ce paiement
                        </div>
                        <div class="big green button" @click="closeEvent()">
                            Continuer
                        </div>
                        <div class="big grey button" @click="state = 'RESPONSE_BUTTONS'" v-if="result?.ticket">
                            Reçu de {{getPaymentTypeName()}}
                        </div>
                    </div>
                </template>
                <template v-else-if="result?.status === PaymentMethodDataStatus.REFUSED || result?.status === PaymentMethodDataStatus.ERROR || result?.status === PaymentMethodDataStatus.ABORTED">
                    <div class="top">
                        <span class="title red" v-if="result.type === 'PAYMENT'"> {{getPaymentTypeName()}} refusé </span>
                        <span class="title red" v-else-if="result.type === 'LOGIN'"> Connexion refusé </span>
                        <i class="big-result-icon red fa-solid fa-circle-xmark"></i>
                        <span class="subtitle">
							<template v-if="result.type === 'LOGIN'">
								La monétique est indisponible
								<br/>
							</template>
							{{result.errorDetails}}
						</span>
                    </div>
                    <div class="buttons">
                        <div class="big white button" @click="closeEvent()">
                            Quitter
                        </div>
                        <div class="big red button" @click="state = 'RESPONSE_BUTTONS'" v-if="result?.ticket">
                            Reçu de {{getPaymentTypeName()}}
                        </div>
                    </div>
                </template>
                <template v-else="">
                    <div class="top">
                        <span class="title red"> Etat non géré: {{result.status}} </span>
                        <i class="big-result-icon red fa-solid fa-circle-xmark"></i>
                    </div>
                    <div class="buttons">
                        <div class="big white button" @click="closeEvent()">
                            Quitter
                        </div>
                    </div>
                </template>
            </template>
        </div>
    </template>
</template>