import {Component, Prop, Vue} from "vue-facing-decorator";
import {FormModalOrDrawerComponent} from "@groupk/vue3-interface-sdk";
import {
    CategoryApiIn,
    CategoryApiOut,
    PointOfSaleApiOut,
    ProductApiOut,
    uuidScopeProductCategory
} from "@groupk/mastodon-core";
import {AutoWired, UuidUtils, VisualScopedUuid} from "@groupk/horizon2-core";
import {PointOfSaleRepository} from "../../../../../shared/repositories/PointOfSaleRepository";
import {AppState} from "../../../../../shared/AppState";
import {UuidScopeProduct_pointOfSale} from "@groupk/mastodon-core";
import {CategoryRepository} from "../../../../../shared/repositories/CategoryRepository";
import {UuidScopeProductCategory} from "@groupk/mastodon-core";
import {CategoryProductConfigApi} from "@groupk/mastodon-core";

@Component({
    components: {
        'form-modal-or-drawer': FormModalOrDrawerComponent,
    },
    emits: ['close']
})
export default class QuickProductIntoPosComponent extends Vue {
    @Prop({required: true}) productToAdd!: ProductApiOut;

    pointOfSales: PointOfSaleApiOut[] = [];
    categories: CategoryApiOut[] = [];

    selectedPointOfSales: VisualScopedUuid<UuidScopeProduct_pointOfSale>[] = [];
    selectedCategories: CategoryApiOut[] = [];

    opened: boolean = false;
    saving: boolean = false;
    loading: boolean = true;

    @AutoWired(PointOfSaleRepository) accessor pointOfSaleRepository!: PointOfSaleRepository;
    @AutoWired(CategoryRepository) accessor categoryRepository!: CategoryRepository;
    @AutoWired(AppState) accessor appState!: AppState;

    async mounted() {
        setTimeout(() => this.opened = true, 0);

        this.categories = (await this.categoryRepository.callContract('list', {establishmentUid: this.appState.requireUrlEstablishmentUid()}, undefined)).success();

        const pointOfSales = (await this.pointOfSaleRepository.callContract('list', {establishmentUid: this.appState.requireUrlEstablishmentUid()}, undefined)).success()
        this.pointOfSales = pointOfSales.filter((pointOfSale) => pointOfSale.categoriesUid.length > 0);

        if(this.pointOfSales.length === 0 || this.pointOfSales.every((pointOfSale) => pointOfSale.categoriesUid.length === 0)) this.close();

        this.loading = false;
    }

    togglePointOfSale(pointOfSale: PointOfSaleApiOut) {
        const index = this.selectedPointOfSales.findIndex(uid => uid === pointOfSale.uid);
        if (index === -1) {
            this.selectedPointOfSales.push(pointOfSale.uid);
        } else {
            this.selectedPointOfSales.splice(index, 1);
        }
    }

    toggleCategory(categoryUid: VisualScopedUuid<UuidScopeProductCategory>) {
        const index = this.selectedCategories.findIndex(category => category.uid === categoryUid);
        if (index === -1) {
            this.selectedCategories.push(this.requireCategoryWithUid(categoryUid));
        } else {
            this.selectedCategories.splice(index, 1);
        }
    }

    isCategoryToggled(categoryUid: VisualScopedUuid<UuidScopeProductCategory>): boolean {
        return this.selectedCategories.some(category => category.uid === categoryUid);
    }

    requireCategoryWithUid(categoryUid: VisualScopedUuid<UuidScopeProductCategory>) {
        const category = this.categories.find(category => category.uid === categoryUid);
        if(!category) throw new Error('Category not found');
        return category;
    }

    async save() {
        this.saving = true;

        const promises: Promise<any>[] = [];
        for(const category of this.selectedCategories) {
            category.productsConfig.push(new CategoryProductConfigApi({
                productUid: this.productToAdd.uid,
                color: null
            }));

            const apiIn = new CategoryApiIn({
                uid: category.uid ?? UuidUtils.randomVisualScopedUUID<UuidScopeProductCategory>(uuidScopeProductCategory),
                name: category.name,
                position: 0,
                productsConfig: category.productsConfig
            });

            promises.push(this.categoryRepository.callContract('update', {establishmentUid: this.appState.requireUrlEstablishmentUid(), categoryUid: category.uid}, apiIn));
        }
        await Promise.all(promises);

        this.close();

        this.saving = false;
    }

    close() {
        this.opened = false;
        setTimeout(() => this.$emit('close'), 300);
    }
}