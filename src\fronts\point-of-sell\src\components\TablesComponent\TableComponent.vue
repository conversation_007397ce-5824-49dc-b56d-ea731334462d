<script lang="ts" src="./TableComponent.ts"/>

<style lang="sass" scoped>
@import './TableComponent.scss'
</style>

<template>
    <div class="tables-dimmer" v-if="opened" @click="opened = false;"></div>

    <div class="table"
         :class="{blue: filteredOrders.length > 0, opened: filteredOrders.length > 1 && opened}"
         :data-testid="table.uid"
         @click="filteredOrders.length > 1 ? opened = true : createOrderForTable(table)"
         @mousedown="handleLongPressStart(table, $event)"
         @touchstart="handleLongPressStart(table, $event)"
         @mouseup="handleLongPressEnd()"
         @touchend="handleLongPressEnd()"
         @mouseleave="handleLongPressEnd()"
    >
        <span class="name">
            {{ table.name }}
        </span>

        <span
            v-if="filteredOrders.length > 0"
            class="orders-count"
            @click=""
            :class="{orange: filteredOrders.length === 1 && filteredOrders[0].cashStatementSessionUid !== posState.currentCashStatementSession.uid}"
        >
            {{ filteredOrders.length }}
        </span>

        <div class="guests">
            <i class="fa-regular fa-user"></i>
            <div class="infos">
                <span class="number"> {{ filteredOrders[0]?.diningExtra?.guestCount ?? 0 }}/{{ table.headCount }} </span>
            </div>
        </div>

        <span class="hour" v-if="filteredOrders.length === 0"> --:-- </span>
        <span class="hour" v-else> {{ getTimeSinceDate(filteredOrders[0].creationDatetime) }} </span>
        <div ref="tableOrders" class="orders" v-if="filteredOrders.length > 1">
            <div
                class="order"
                v-for="order in filteredOrders"
                @click.stop="selectOrder(order); opened = false;"
                @mousedown.stop="handleLongPressStart(table, $event)"
                @touchstart.stop="handleLongPressStart(table, $event)"
                @mouseup.stop="handleLongPressEnd()"
                @touchend.stop="handleLongPressEnd()"
                @mouseleave.stop="handleLongPressEnd()"
            >
                <div v-if="order.cashStatementSessionUid !== posState.currentCashStatementSession.uid" class="orange label">
                    Distant
                </div>

                <div class="head">
                    <span class="time"> {{ $filters.Hour(order.creationDatetime) }} </span>
                    <span class="account"> {{ order.sellerEstablishmentAccountUid ? posState.getEstablishmentAccountWithUid(order.sellerEstablishmentAccountUid).firstname : 'En ligne' }} </span>
                </div>

                {{ $filters.Money(getOrderTotals(order).purchases.withTaxesBeforeDiscount) }}
            </div>
        </div>
    </div>
</template>