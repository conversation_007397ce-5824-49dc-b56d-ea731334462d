.header {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
}

.available-methods {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 14px;

    .method {
        display: flex;
        align-items: center;
        gap: 10px;
        padding: 10px;
        border-radius: 6px;
        border: 1px solid #D8D8D8;
        background: white;

        &.action {
            cursor: pointer;
        }
    }
}

.enable-credit-debit {
    display: flex;
    gap: 5px;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    padding: 5px;
    cursor: pointer;

    i {
        margin-top: 2px;
    }
}

.title-group {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;

    .disable {
        cursor: pointer;
        text-decoration: underline;
    }
}

.toggle-input {
    display: flex;
    align-items: center;
    gap: 15px;

    .infos {
        display: flex;
        flex-direction: column;
        gap: 4px;
        flex-grow: 2;

        .title {
            font-size: 15px;
            font-weight: 500;
        }

        .subtitle {
            font-size: 14px;
        }
    }
}

.toggles {
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin-top: 20px;
}