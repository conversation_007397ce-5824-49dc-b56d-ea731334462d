<script lang="ts" src="./ProfileSettingsComponent.ts">
</script>

<style lang="sass">
@use './ProfileSettingsComponent.scss' as *
</style>

<template>
    <div class="profile-settings-component">

        <div class="settings-line">
            <div class="left">
                <div class="infos">
                    <span class="title"> Paramètres généraux </span>
                    <span class="subtitle">

                    </span>
                </div>
            </div>
            <div class="right">
                <div class="global-settings">
                    <div class="form">
                        <div class="input-group">
                            <label for="title"> Nom </label>
                            <input v-model="profile.name" type="text" placeholder="Nom" />
                        </div>

                        <div class="two-inputs" v-if="appState.advancedInterfaces">
                            <div class="input-group" :class="{disabled: !appState.advancedInterfaces}">
                                <label for="title"> ID de la puce de crédit </label>
                                <div class="ui input">
                                    <input v-model="profile.creditChipId" type="number" />
                                </div>
                            </div>

                            <div class="input-group" :class="{disabled: !appState.advancedInterfaces}">
                                <label for="title"> ID de la puce de débit </label>
                                <input v-model="profile.debitChipId" type="number" />
                            </div>
                        </div>

                        <div class="two-inputs" v-if="appState.advancedInterfaces" style="align-items: flex-end">
                            <div class="input-group">
                                <label for="title"> Suppression des transactions (temps en secondes) </label>
                                <div class="ui input">
                                    <input v-model="profile.txHistoryDelay" type="number" />
                                </div>
                            </div>

                            <div class="input-group">
                                <label for="title"> Mot de passe admin </label>
                                <div class="ui input">
                                    <input v-model="profile.adminPassword" type="text" />
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="toggles">
                        <div class="toggle-input">
                            <toggle :default-toggled-value="profile.allowBalanceReading" @toggled="profile.allowBalanceReading = $event"></toggle>
                            <div class="infos">
                                <span class="title"> Autoriser la lecture de solde </span>
                            </div>

                            <hoverable-info
                                :data="{
                                title: 'Autoriser la lecture de solde',
                                description: `
                                  Permet au terminal de lire le solde d'une carte sans faire d'opération.<br/>
                                  <br/>
                                  <b>Utilisation :</b> une personne vient vous voir car elle ne se souvient plus de son solde.
                                `,
                                image: null
                            }"
                                :alignment="'LEFT'"
                            ></hoverable-info>
                        </div>

                        <div class="toggle-input">
                            <toggle :default-toggled-value="profile.resetBalanceOptionOnBalanceReading" @toggled="profile.resetBalanceOptionOnBalanceReading = $event"></toggle>
                            <div class="infos">
                                <span class="title"> Bouton de réinitialisation de solde après lecture de solde  </span>
                            </div>

                            <hoverable-info
                                :data="{
                                title: 'Bouton de réinitialisation de solde après lecture de solde',
                                description: `
                                  Permet de lire le solde, puis cliquer sur le bouton et rescanner le bracelet pour le vider.<br/>
                                  <br/>
                                  <b>Information :</b> cela évite de lire le solde, puis d'aller dans terminal, entrer le montant à vider, et vider le bracelet.
                                `,
                                image: null
                            }"
                                :alignment="'LEFT'"
                            ></hoverable-info>
                        </div>

                        <div class="toggle-input">
                            <toggle :default-toggled-value="profile.allowDeviceHistoricReading" @toggled="profile.allowDeviceHistoricReading = $event"></toggle>
                            <div class="infos">
                                <span class="title"> Autoriser la lecture de l'historique </span>
                            </div>

                            <hoverable-info
                                :data="{
                                title: 'Autoriser la lecture de l\'historique',
                                description: `
                                  Permet de consulter le solde du terminal.<br/>
                                  <br/>
                                  <b>Information :</b> vous ne verrez pas l'historique des autres terminaux. Uniquement du terminal en question.
                                `,
                                image: null
                            }"
                                :alignment="'LEFT'"
                            ></hoverable-info>
                        </div>

<!--                        <div class="toggle-input">-->
<!--                            <toggle :default-toggled-value="profile.allowTerminalProduct" @toggled="profile.allowTerminalProduct = $event"></toggle>-->
<!--                            <div class="infos">-->
<!--                                <span class="title"> Activer la visualisation des produits </span>-->
<!--                            </div>-->

<!--                            <hoverable-info-->
<!--                                :data="{-->
<!--                                title: 'Activer la visualisation des produits',-->
<!--                                description: `-->
<!--                                  Vendez en sélectionnant des produits.<br/>-->
<!--                                  <br/>-->
<!--                                  <b>Information :</b> vous devez créer une catégorie avec des produits à l'intérieur et les associer à ce point de vente.-->
<!--                                `,-->
<!--                                image: null-->
<!--                            }"-->
<!--                                :alignment="'LEFT'"-->
<!--                            ></hoverable-info>-->
<!--                        </div>-->

                        <div class="toggle-input">
                            <toggle :default-toggled-value="profile.demoMode" @toggled="profile.demoMode = $event"></toggle>
                            <div class="infos">
                                <span class="title"> Mode démo </span>
                                <span class="subtitle"> Permet d'exclure des statistiques les transactions. Les transactions sont quand même remontées. </span>
                            </div>
                        </div>

                        <div class="toggle-input" v-if="appState.advancedInterfaces">
                            <toggle :default-toggled-value="profile.allowDevicePosManaged" @toggled="profile.allowDevicePosManaged = $event"></toggle>
                            <div class="infos">
                                <span class="title"> allowDevicePosManaged </span>
                            </div>
                        </div>

                        <div class="toggle-input" v-if="appState.advancedInterfaces">
                            <toggle :default-toggled-value="profile.allowDeviceKiosk" @toggled="profile.allowDeviceKiosk = $event"></toggle>
                            <div class="infos">
                                <span class="title"> allowDeviceKiosk </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="settings-line">
            <div class="left">
                <div class="infos">
                    <span class="title"> Créditer un montant libre </span>
                    <span class="subtitle"> Autoriser le crédit d'un montant libre sur les supports </span>
                </div>

                <hoverable-info
                    :data="{
                        title: 'Permet de créditer manuellement',
                        description: `Il sera possible de saisir un montant libre pour créditer un utilisateur.`,
                        image: '/img/credit-keyboard.png'
                    }"
                    :alignment="'TOP'"
                ></hoverable-info>
            </div>
            <div class="right">
                <div class="terminal-keypad-credit-settings">
                    <div class="form">

                        <div class="toggle-input">
                            <toggle :default-toggled-value="profile.terminalKeypadCredit !== null" @toggled="!profile.terminalKeypadCredit ? addCreditKeypad() : profile.terminalKeypadCredit = null"></toggle>
                            <div class="infos">
                                <span class="title"> {{ profile.terminalKeypadCredit ? 'Activé' : 'Désactivé' }} </span>
                            </div>
                        </div>

                        <template v-if="profile.terminalKeypadCredit">
                            <div class="toggle-input">
                                <toggle :default-toggled-value="profile.terminalKeypadCredit.requirePaymentMethod" @toggled="profile.terminalKeypadCredit.requirePaymentMethod = $event"></toggle>
                                <div class="infos">
                                    <span class="title"> Demander le moyen de paiement lors du crédit </span>
                                </div>
                            </div>

                            <div class="input-group" v-if="profile.terminalKeypadCredit.requirePaymentMethod">
                                <label> Moyens de paiement autorisés </label>
                                <select-multiple-dropdown
                                    placeholder="Moyens de paiement autorisés"
                                    :searchable="true"
                                    :values="paymentMethodsDropdownValues"
                                    :default-selected="profile.terminalKeypadCredit.paymentMethods ?? []"
                                    @update="profile.terminalKeypadCredit.paymentMethods = $event;"
                                ></select-multiple-dropdown>
                            </div>

                            <div class="toggle-input" v-if="appState.advancedInterfaces">
                                <toggle :default-toggled-value="profile.terminalKeypadCredit.autoDecimals" @toggled="profile.terminalKeypadCredit.autoDecimals = $event"></toggle>
                                <div class="infos">
                                    <span class="title"> Décimales automatiques </span>
                                </div>
                            </div>

                            <div class="toggle-input" v-if="appState.advancedInterfaces">
                                <toggle :default-toggled-value="profile.terminalKeypadCredit.requireAdminAuth" @toggled="profile.terminalKeypadCredit.requireAdminAuth = $event"></toggle>
                                <div class="infos">
                                    <span class="title"> Requiert admin </span>
                                </div>
                            </div>
                        </template>
                    </div>
                </div>
            </div>
        </div>

        <div class="settings-line">
            <div class="left">
                <div class="infos">
                    <span class="title"> Débiter un montant libre </span>
                    <span class="subtitle"> Autoriser le débit d'un montant libre (non lié à un produit) </span>
                </div>

                <hoverable-info
                    :data="{
                        title: 'Permet de débiter manuellement',
                        description: `Il sera possible de saisir un montant libre pour débiter un utilisateur.`,
                        image: '/img/debit-keyboard.png'
                    }"
                    :alignment="'TOP'"
                ></hoverable-info>
            </div>
            <div class="right">
                <div class="terminal-keypad-credit-settings">
                    <div class="form">
                        <div class="toggle-input">
                            <toggle :default-toggled-value="profile.terminalKeypadDebit !== null" @toggled="!profile.terminalKeypadDebit ? addDebitKeypad() : profile.terminalKeypadDebit = null"></toggle>
                            <div class="infos">
                                <span class="title"> {{ profile.terminalKeypadDebit ? 'Activé' : 'Désactivé' }} </span>
                            </div>
                        </div>

                        <template v-if="profile.terminalKeypadDebit">
                            <div class="toggle-input">
                                <toggle :default-toggled-value="profile.terminalKeypadDebit.requirePaymentMethod" @toggled="profile.terminalKeypadDebit.requirePaymentMethod = $event"></toggle>
                                <div class="infos">
                                    <span class="title"> Demander le moyen de paiement lors du débit </span>
                                </div>
                            </div>

                            <div class="input-group" v-if="profile.terminalKeypadDebit.requirePaymentMethod">
                                <label> Moyens de paiement autorisés </label>
                                <select-multiple-dropdown
                                    placeholder="Moyens de paiement autorisés"
                                    :searchable="true"
                                    :open-from-top="true"
                                    :values="paymentMethodsDropdownValues"
                                    :default-selected="profile.terminalKeypadDebit.paymentMethods ?? []"
                                    @update="profile.terminalKeypadDebit.paymentMethods = $event;"
                                ></select-multiple-dropdown>
                            </div>

                            <div class="toggle-input" v-if="appState.advancedInterfaces">
                                <toggle :default-toggled-value="profile.terminalKeypadDebit.autoDecimals" @toggled="profile.terminalKeypadDebit.autoDecimals = $event"></toggle>
                                <div class="infos">
                                    <span class="title"> Décimales automatiques </span>
                                </div>
                            </div>

                            <div class="toggle-input" v-if="appState.advancedInterfaces">
                                <toggle :default-toggled-value="profile.terminalKeypadDebit.requireAdminAuth" @toggled="profile.terminalKeypadDebit.requireAdminAuth = $event"></toggle>
                                <div class="infos">
                                    <span class="title"> Requiert admin </span>
                                </div>
                            </div>
                        </template>
                    </div>
                </div>
            </div>
        </div>

        <div class="settings-line" v-if="appState.advancedInterfaces">
            <div class="left">
                <div class="infos">
                    <span class="title"> Autoriser le pilotage par une caisse enregistreuse </span>
                    <span class="subtitle">
                        Autorise le terminal à être réveillé par une caisse enregistreuse
                        lors d'un paiement avec le moyen de paiement 'Cashless'
                    </span>
                </div>
            </div>
            <div class="right">
                <div class="white button" v-if="!profile.pos" @click="addPos()"> Activer </div>

                <div class="terminal-pos-settings" v-else>
                    <div class="form">
                        <div class="two-inputs">
                            <div class="input-group">
                                <label for="title"> Port d'écoute </label>
                                <div class="ui input">
                                    <input v-model="profile.pos.listeningPort" type="number" />
                                </div>
                            </div>
                        </div>

                        <div class="two-inputs">
                            <div
                                class="boolean-input"
                                :class="{selected: profile.pos.allowReturn}"
                                @click="profile.pos.allowReturn = !profile.pos.allowReturn"
                            >
                                <i class="fa-regular" :class="profile.pos.allowReturn ? 'fa-square-check' : 'fa-square'"></i>
                                <div class="right">
                                    <span class="title"> allowReturn </span>
                                </div>
                            </div>
                        </div>

                        <div class="buttons">
                            <div class="white button" @click="profile.terminalKeypadDebit = null">
                                <i class="fa-regular fa-ban"></i>
                                Désactiver le débit d'un montant libre
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="settings-line" v-if="appState.advancedInterfaces">
            <div class="left">
                <div class="infos">
                    <span class="title"> Puces admin </span>
                    <span class="subtitle">
                        Une puce admin permet de passer d'un point de vente à l'autre sur un terminal sans avoir besoin d'internet.
                    </span>
                </div>
            </div>
            <div class="right">
                <div class="global-settings">
                    <div class="form">
                        <div class="two-inputs">
                            <div class="input-group" v-for="(chipId, index) in profile.adminPublicChipIds">
                                <label for="title"> Numéro de puce </label>
                                <div class="action-input">
                                    <input maxlength="8" class="white" :value="profile.adminPublicChipIds[index]" @input="setAdminPublicChip($event, index)"  type="text" />
                                    <i @click="profile.adminPublicChipIds.splice(index, 1)" class="fa-regular fa-trash-alt"></i>
                                </div>
                            </div>
                        </div>

                        <div>
                            <button class="small transparent button" @click="profile.adminPublicChipIds.push('')">
                                <i class="fa-regular fa-plus"></i>
                                Ajouter une puce
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="settings-line" v-if="appState.advancedInterfaces">
            <div class="left">
                <div class="infos">
                    <span class="title"> Mode kiosk </span>
                    <span class="subtitle">
                        Passer le point de vente en mode kiosk
                    </span>
                </div>
            </div>
            <div class="right">
                <div class="white button" v-if="!profile.kiosk" @click="addKiosk()"> Activer </div>

                <div class="terminal-pos-settings" v-else>
                    <div class="form">
                        <div class="input-group">
                            <label for="title"> Montant fiat par défaut </label>
                            <input class="white" v-model="profile.kiosk.defaultFiatAmount" type="number" />
                        </div>

                        <div class="two-inputs">
                            <div class="input-group">
                                <label for="title"> Fiat MIN </label>
                                <div class="ui input">
                                    <input class="white" v-model="profile.kiosk.fiatMin" type="number" />
                                </div>
                            </div>

                            <div class="input-group">
                                <label for="title"> Fiat MAX </label>
                                <input class="white" v-model="profile.kiosk.fiatMax" type="number" />
                            </div>
                        </div>

                        <div class="input-group">
                            <label for="title"> Méthode de paiement </label>
                            <dropdown
                                class="white"
                                placeholder="Méthode de paiement"
                                :values="paymentMethodsDropdownValues"
                                :default-selected="profile.kiosk.paymentMethodUid"
                                @update="profile.kiosk.paymentMethodUid = $event"
                            ></dropdown>
                        </div>

                        <div class="buttons">
                            <div class="white button" @click="profile.kiosk = null; profile.allowDeviceKiosk = false">
                                <i class="fa-regular fa-ban"></i>
                                Désactiver le débit d'un montant libre
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="settings-line" v-if="appState.advancedInterfaces">
            <div class="left">
                <div class="infos">
                    <span class="title"> Mode "Machine à laver" </span>
                    <span class="subtitle">
                        Passer le point de vente en mode "Machine à laver"
                    </span>
                </div>
            </div>
            <div class="right">
                <div class="white button" v-if="!profile.kioskProduct" @click="addKioskProduct()"> Activer </div>

                <div class="terminal-pos-settings kiosk-product" v-else>

                    <div class="products-cards" v-if="profile.kioskProduct.products.length > 0">
                        <div class="product-card" v-for="(product, index) in profile.kioskProduct.products" :key="index">
                            <div class="card-header">
                                <div class="product-name">
                                    <i class="fa-regular fa-box"></i>
                                    <span class="name">{{ product.productId ? requireProductWithId(product.productId).name : 'Pas de produit' }}</span>
                                </div>
                                <div class="card-actions">
                                    <button class="small red button" @click="removeProductFromKiosk(index)" title="Supprimer le produit">
                                        <i class="fa-regular fa-trash-alt"></i>
                                    </button>
                                </div>
                            </div>

                            <div class="card-content">
                                <div class="properties-table">
                                    <div class="row">
                                        <span class="title">Code</span>
                                        <span class="value">
                                            <span class="label white">{{ product.code }}</span>
                                        </span>
                                    </div>
                                    <div class="row" v-if="product.iotDeviceUid">
                                        <span class="title">Appareil IoT</span>
                                        <span class="value">
                                            <span class="label green">{{ product.iotDeviceUid }}</span>
                                        </span>
                                    </div>
                                    <div class="row" v-if="product.unavailablePeriodSeconds">
                                        <span class="title">Période d'indisponibilité</span>
                                        <span class="value">
                                            <span class="label orange">{{ product.unavailablePeriodSeconds }}s</span>
                                        </span>
                                    </div>
                                    <div class="row" v-if="product.iotDeviceConfig">
                                        <span class="title">Configuration IoT</span>
                                        <span class="value">
                                            <span class="label">Configuré</span>
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="no-products" v-else>
                        <div class="empty-state">
                            <i class="fa-regular fa-box-open"></i>
                            <span class="message">Aucun produit configuré</span>
                            <span class="submessage">Ajoutez un produit pour commencer à utiliser le mode "Machine à laver"</span>
                        </div>
                    </div>

                    <button class="grey button" @click="showProductToKioskModal()">
                        <i class="fa-regular fa-circle-plus"></i>
                        Ajouter un produit
                    </button>

                    <div class="buttons">
                        <div class="white button" @click="profile.kioskProduct = null; profile.allowKioskProduct = false">
                            <i class="fa-regular fa-ban"></i>
                            Désactiver le mode "Machine à laver"
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="settings-line" v-if="appState.advancedInterfaces">
            <div class="left">
                <div class="infos">
                    <span class="title"> Payment method list </span>
                    <span class="subtitle">
                        ProfileTerminalProduct.paymentMethodList
                    </span>
                </div>
            </div>
            <div class="right">
                <div class="global-settings">

                    <div class="table-scroll-wrapper">
                        <table class="data-table" v-if="profile.terminalProduct && profile.terminalProduct.paymentMethodList && profile.terminalProduct.paymentMethodList.length > 0">
                            <thead>
                            <tr>
                                <td> Payment method uid </td>
                                <td> Virtual chip uid</td>
                                <td style="width: 1px"></td>
                            </tr>
                            </thead>
                            <tbody>
                            <tr v-for="data of profile.terminalProduct.paymentMethodList">
                                <td> {{ data.paymentMethodUid }} </td>
                                <td> {{ data.virtualChipUid }} </td>
                                <td>
                                    <div class="buttons">
                                        <button class="grey button" @click="editPaymentMethod(data)">
                                            <i class="fa-regular fa-pen-line"></i>
                                        </button>
                                        <button class="red button" @click="deletePaymentMethod(data)">
                                            <i class="fa-regular fa-trash-alt"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>

                    <div class="form">
                        <div>
                            <button class="small transparent button" @click="toggleShowPaymentMethodModal()">
                                <i class="fa-regular fa-plus"></i>
                                Ajouter un moyen de paiement
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="settings-line" v-if="appState.advancedInterfaces">
            <div class="left">
                <div class="infos">
                    <span class="title"> Payment method configs </span>
                    <span class="subtitle">
                        Profile.paymentMethodConfigs
                    </span>
                </div>
            </div>
            <div class="right">
                <div class="global-settings">
                    <div class="form">
                        <div class="table-scroll-wrapper">
                            <table class="data-table" v-if="profile.paymentMethodConfigs && profile.paymentMethodConfigs.length > 0">
                                <thead>
                                <tr>
                                    <td> Payment method uid </td>
                                    <td> Settings type </td>
                                    <td> Settings name </td>
                                    <td style="width: 1px"></td>
                                </tr>
                                </thead>
                                <tbody>
                                <tr v-for="data of profile.paymentMethodConfigs">
                                    <td> {{ data.paymentMethodUid }} </td>
                                    <td> {{ data.settings?.protocol }} </td>
                                    <td> {{ data.settings?.name }} </td>
                                    <td>
                                        <div class="buttons">
                                            <button class="grey button" @click="editPaymentMethodConfig(data)">
                                                <i class="fa-regular fa-pen-line"></i>
                                            </button>
                                            <button class="red button" @click="deletePaymentMethodConfig(data)">
                                                <i class="fa-regular fa-trash-alt"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                        </div>

                        <div>
                            <button class="small transparent button" @click="toggleShowPaymentMethodConfigModal()">
                                <i class="fa-regular fa-plus"></i>
                                Ajouter un config de moyen de paiement
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <form-modal-or-drawer
            :state="showPaymentMethodModal"
            :title="editingPaymentMethod ? 'Modifier le moyen de paiement' : 'Créer une moyen de paiement'"
            subtitle="-"
            @close="showPaymentMethodModal = null"
        >
            <template v-slot:content>
                <template v-if="showPaymentMethodModal">
                    <div class="input-group">
                        <label> paymentMethodUid (visual) </label>
                        <dropdown
                            placeholder="Méthode de paiement"
                            :values="paymentMethodsDropdownValues"
                            @update="showPaymentMethodModal.paymentMethodUid = $event"
                        ></dropdown>
                    </div>

                    <div class="input-group">
                        <label> virtualChipUid (visual) </label>
                        <input type="text" v-model="showPaymentMethodModal.virtualChipUid" />
                    </div>

                    <div class="form-error" v-if="paymentMethodModalError">
                        <i class="fa-regular fa-circle-exclamation"></i>
                        <div class="details">
                            {{ paymentMethodModalError }}
                        </div>
                    </div>
                </template>

            </template>

            <template v-slot:buttons>
                <button class="white button" @click="showPaymentMethodModal = null"> Annuler </button>
                <button class="button" @click="validatePaymentMethodModal()"> Valider </button>
            </template>
        </form-modal-or-drawer>

        <form-modal-or-drawer
            :state="showPaymentMethodConfigModal"
            :title="editingPaymentMethodConfig ? 'Modifier le config de moyen de paiement' : 'Créer un config de moyen de paiement'"
            subtitle="-"
            @close="showPaymentMethodConfigModal = null"
        >
            <template v-slot:content>
                <template v-if="showPaymentMethodConfigModal">
                    <div class="input-group">
                        <label> paymentMethodUid (scoped) </label>
                        <dropdown
                            placeholder="Méthode de paiement"
                            :values="paymentMethodsScopedDropdownValues"
                            @update="showPaymentMethodConfigModal.paymentMethodUid = $event"
                        ></dropdown>
                    </div>

                    <div class="input-group">
                        <label> Settings Type </label>
                        <dropdown
                            placeholder="Settings Type"
                            :values="settingsTypeDropdownValues"
                            :default-selected="showPaymentMethodConfigModal.selectedSettingsType"
                            @update="showPaymentMethodConfigModal.selectedSettingsType = $event"
                        ></dropdown>
                    </div>

                    <div class="input-group">
                        <label> Name </label>
                        <input type="text" v-model="showPaymentMethodConfigModal.settings.name" />
                    </div>

                    <!-- CaisseAp specific fields -->
                    <template v-if="showPaymentMethodConfigModal.selectedSettingsType === 'PHYSICAL_CAISEAP'">
                        <div class="input-group">
                            <label> IP Address </label>
                            <input type="text" v-model="showPaymentMethodConfigModal.settings.ip" />
                        </div>
                        <div class="input-group">
                            <label> Port </label>
                            <input type="number" v-model="showPaymentMethodConfigModal.settings.port" />
                        </div>
                    </template>

                    <!-- CashKeeper specific fields -->
                    <template v-if="showPaymentMethodConfigModal.selectedSettingsType === 'PHYSICAL_CASHKEEPER'">
                        <div class="input-group">
                            <label> Security Seed </label>
                            <input type="number" v-model="showPaymentMethodConfigModal.settings.securitySeed" />
                        </div>
                        <div class="input-group">
                            <label> Address </label>
                            <input type="text" v-model="showPaymentMethodConfigModal.settings.address" />
                        </div>
                        <div class="input-group">
                            <label> Master Port </label>
                            <input type="number" v-model="showPaymentMethodConfigModal.settings.masterPort" />
                        </div>
                        <div class="input-group">
                            <label> Office Port </label>
                            <input type="number" v-model="showPaymentMethodConfigModal.settings.officePort" />
                        </div>
                    </template>

                    <!-- NeptingApp2App specific fields -->
                    <template v-if="showPaymentMethodConfigModal.selectedSettingsType === 'PHYSICAL_NEPTING_APP2APP'">
                        <div class="input-group">
                            <label> Webservice URL </label>
                            <input type="text" v-model="showPaymentMethodConfigModal.settings.webserviceUrl" />
                        </div>
                        <div class="input-group">
                            <label> Merchant Code </label>
                            <input type="text" v-model="showPaymentMethodConfigModal.settings.merchantCode" />
                        </div>
                        <div class="input-group">
                            <label> Store ID </label>
                            <input type="text" v-model="showPaymentMethodConfigModal.settings.storeId" />
                        </div>
                        <div class="input-group">
                            <label> Terminal Type </label>
                            <input type="number" v-model="showPaymentMethodConfigModal.settings.terminalType" />
                        </div>
                        <div class="input-group">
                            <label> Login Interval </label>
                            <input type="number" v-model="showPaymentMethodConfigModal.settings.loginInterval" />
                        </div>
                    </template>

                    <div class="form-error" v-if="paymentMethodConfigModalError">
                        <i class="fa-regular fa-circle-exclamation"></i>
                        <div class="details">
                            {{ paymentMethodConfigModalError }}
                        </div>
                    </div>
                </template>

            </template>

            <template v-slot:buttons>
                <button class="white button" @click="showPaymentMethodConfigModal = null"> Annuler </button>
                <button class="button" @click="validatePaymentMethodConfigModal()"> Valider </button>
            </template>
        </form-modal-or-drawer>

        <form-modal-or-drawer
            :state="showProductKioskModal"
            :title="editingPaymentMethod ? 'Ajouter un produit dans la Machine à laver' : 'Modifier le produit dans la Machine à laver'"
            subtitle="-"
            @close="showProductKioskModal = null"
        >
            <template v-slot:content>
                <template v-if="showProductKioskModal">
                    <div class="input-group">
                        <label for="title"> Product ID </label>
                        <dropdown
                            placeholder="Produit"
                            :values="productsDropdownValues"
                            @update="showProductKioskModal.productId = $event"
                        ></dropdown>
                    </div>

                    <div class="input-group">
                        <label for="title"> Code </label>
                        <input class="white" v-model="showProductKioskModal.code" type="number" />
                    </div>

                    <div class="input-group">
                        <label for="title"> Iot device uid </label>
                        <string-or-null-input
                            :value="showProductKioskModal.iotDeviceUid"
                            @change="showProductKioskModal.iotDeviceUid = $event"
                        ></string-or-null-input>
                    </div>

                    <div class="input-group">
                        <label for="title"> unavailablePeriodSeconds </label>
                        <input class="white" v-model="showProductKioskModal.unavailablePeriodSeconds" type="number" />
                    </div>

                    <button class="grey button" v-if="!showProductKioskModal.iotDeviceConfig" @click="initializeIotDeviceConfig(showProductKioskModal)">
                        <i class="fa-regular fa-plus"></i>
                        Ajouter une configuration iot
                    </button>

                    <template v-else-if="showProductKioskModal.iotDeviceConfig && showProductKioskModal.iotDeviceConfig instanceof IotDeviceConfigShellySwitch">
                        <div class="input-group">
                            <label for="title"> number </label>
                            <input class="white" v-model="showProductKioskModal.iotDeviceConfig.number" type="number" placeholder="Pin number" />
                        </div>

                        <div class="input-group">
                            <label for="title"> on </label>
                            <input type="checkbox" v-model="showProductKioskModal.iotDeviceConfig.on" />
                        </div>

                        <div class="input-group">
                            <label for="title"> toggle </label>
                            <input type="number" v-model="showProductKioskModal.iotDeviceConfig.toggle" />
                        </div>

                        <button class="grey button" @click="showProductKioskModal.iotDeviceConfig = undefined">
                            <i class="fa-regular fa-minus"></i>
                            Retirer la configuration iot
                        </button>
                    </template>

                    <div class="form-error" v-if="productKioskModalError">
                        <i class="fa-regular fa-circle-exclamation"></i>
                        <div class="details">
                            {{ productKioskModalError }}
                        </div>
                    </div>
                </template>
            </template>

            <template v-slot:buttons>
                <button class="white button" @click="showProductKioskModal = null"> Annuler </button>
                <button v-if="showProductKioskModal" class="button" @click="addProductToKiosk(showProductKioskModal)"> Valider </button>
            </template>
        </form-modal-or-drawer>
    </div>
</template>
