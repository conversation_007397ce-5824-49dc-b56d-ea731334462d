
td {
    padding: 15px !important;
}

.name >div {
    display: flex;
    gap: 10px;
    align-items: center;

    .error {
        i {
            color: var(--error-color);
        }
    }

    .warning {
        i {
            color: #F49025;
        }
    }
}

.transferring-hover {
    position: absolute;
    inset: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    background: rgba(255, 255, 255, 0.9);
    font-size: 18px;
    font-weight: 600;
    color: black;
    border-bottom: 1px solid var(--border-color);

    .dropdown-button-component {
        position: absolute;
        right: 15px;
    }
}

.reservit-logo {
    width: 48px;
    height: 48px;
}

.modal-dimmer {
    position: fixed;
    z-index: 50;
    inset: 0;
    background: rgba(0, 0, 0, 0.16);
    backdrop-filter: blur(10px);
    display: flex;
    align-items: center;
    justify-content: center;

    .modal {
        background: white;
        padding: 40px;
        border-radius: 8px;
        max-width: 600px;
        margin: 20px;
        max-height: 80vh;
        overflow: auto;

        h3 {
            margin: 0 0 20px 0;
        }
    }
}