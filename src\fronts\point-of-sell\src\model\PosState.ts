import {
	BillingRegionApiOut,
	CashStatementApiOut,
	CashStatementSessionApiOut,
	CategoryApiOut,
	ContextApiOut,
	DiningAreaApiOut,
	DiscountTemplateApiOut,
	EstablishmentAccountApiOut,
	ExecutionFlowApiOut,
	MetadataDescriptorApiOut,
	OrderDiscountTemplateModel,
	OrderExecutorModel,
	OrderPurchaseStatusApiOut,
	PaymentMethodApiOut,
	PointOfSaleApiOut,
	PointOfSaleConfigForIotApiOut,
	PosProfileApiOut,
	ProductApiOut,
	ProductEstablishmentApiOut,
	PurchaseApiOut,
	StockSimpleApiOut,
	UuidScopeEstablishment,
	UuidScopeEstablishmentAccount,
	UuidScopeMetadata_descriptor,
	UuidScopePayment_method,
	UuidScopeProduct_diningTable,
	UuidScopeProduct_printingLocation,
	UuidScopeProductCategory,
	UuidScopeProductProduct,
	UuidScopeProduct_stockSimple,
	VirtualStockWatcher,
	WorkClockApiOut,
	OrderExecutorPurchaseStatusActionHandler,
	OrderPurchaseStatusAction,
	DiningTableApiOut,
	OrderApiOut,
	UuidScopeIot_deviceApp,
	EstablishmentDeviceAppV2ApiOut,
	EstablishmentDeviceV2ApiOut,
	UuidScopeIot_device,
	AppReservitApiOut,
	ReservitApi_configuration
} from "@groupk/mastodon-core";
import {randomUUID} from "@groupk/horizon2-front";
import {CountryEnum, GetInstance, ScopedUuid, SetInstance, VisualScopedUuid} from "@groupk/horizon2-core";
import {LocalOrder} from "./LocalOrder";
import {DisplaysNative, NativeHelper} from "@groupk/native-bridge";
import {
	CustomerDisplayMessage,
	CustomerDisplayOrder,
	CustomerDisplayPurchase
} from "../pages/customerDisplay/customerDisplay";
import {LengthFilter} from "../../../../shared/filters/StringFilter";
import {AppBus} from "../config/AppBus";
import {reactive} from "vue";

export class PosState {
	establishmentUid!: VisualScopedUuid<UuidScopeEstablishment>;
	toCountry: CountryEnum = CountryEnum.FRA;
	private _products: { [productUid: VisualScopedUuid<UuidScopeProductProduct>]: ProductApiOut } = {};
	private _billingRegions: BillingRegionApiOut[] = [];
	private _purchaseStatuses: OrderPurchaseStatusApiOut[] = [];
	private _paymentMethods: PaymentMethodApiOut[] = [];
	private _stocks: StockSimpleApiOut[] = [];
	private _contexts: ContextApiOut[] = [];
	private _discountTemplates: DiscountTemplateApiOut[] = [];
	categories: CategoryApiOut[] = [];
	lastWorkClocks: WorkClockApiOut[] = [];
	posProfiles: PosProfileApiOut[] = [];
	metadataDescriptors: MetadataDescriptorApiOut[] = [];
	pointOfSale!: PointOfSaleApiOut;
	pointOfSaleConfiguration!: PointOfSaleConfigForIotApiOut

	establishmentAccounts: EstablishmentAccountApiOut[] = [];
	iotDeviceApps: EstablishmentDeviceAppV2ApiOut[] = [];
	iotDevices: EstablishmentDeviceV2ApiOut[] = [];

	currentCashStatement!: CashStatementApiOut;
	currentCashStatementSession!: CashStatementSessionApiOut;
	iotDevice!: EstablishmentDeviceAppV2ApiOut;

	currentEstablishmentAccountUid!: VisualScopedUuid<UuidScopeEstablishmentAccount>;

	currentPage: string = 'sell';
	currentOrderByEstablishmentAccount: Record<VisualScopedUuid<UuidScopeEstablishmentAccount>, LocalOrder> = reactive({});
	showCashStatementModal: boolean = false;
	showQuickActions: boolean = false;
	showCashKeeperActions: boolean = false;
	showTicketPrintingModal: boolean = false;
	displayedReceipt: string|null = null;
	showTransferOrderMenu: LocalOrder|null = null;
	showReservitTransferOrderMenu: LocalOrder|null = null;
	showDebugInformations: LocalOrder|OrderApiOut|null = null;
	linkCustomerToOrder: LocalOrder|null = null;
	forceProfileSwitch: boolean = false;

	productEstablishment: ProductEstablishmentApiOut|null = null;

	virtualStockHandler!: VirtualStockWatcher;
	orderExecutorModel!: OrderExecutorModel;
	orderDiscountTemplateModel!: OrderDiscountTemplateModel;
	reservit: {app: AppReservitApiOut, configuration: ReservitApi_configuration|null}|null = null;

	orderGroupBy: 'PRODUCT'|'STEP' = 'PRODUCT';

	diningAreas: DiningAreaApiOut[] = [];

	haveSendPrinterInfos: Record<string, boolean> = {};

	columns: number = 6;

	// constructor() {
	// 	const appBus = GetInstance(AppBus);
	// 	appBus.on('orderSaved', (localOrder) => {
	// 		const currentOrder = this.currentOrder;
	// 		if(this.currentOrder && localOrder.error && currentOrder && localOrder.uid === currentOrder.uid && currentOrder !== localOrder) {
	// 			this.currentOrder.error = localOrder.error;
	// 			this.currentOrder.synced = localOrder.synced;
	//
	// 			// this.currentOrder.lastLocalUpdateDate=localOrder.lastLocalUpdateDate;
	// 		}
	// 	});
	// }

	constructor() {
		const appBus = GetInstance(AppBus);
		appBus.on('orderSaved', (localOrder) => {
			const currentOrder = this.currentOrder;
			if(currentOrder && localOrder.uid === currentOrder.uid && currentOrder !== localOrder) this.currentOrder = localOrder;
		});
	}

	isDiningEnabled() {
		return this.diningAreas.length > 0;
	}

	registerOrderExecutorModel(purchaseStatuses: OrderPurchaseStatusApiOut[], billingRegions: BillingRegionApiOut[], executionFlow: ExecutionFlowApiOut, pointOfSale: PointOfSaleApiOut) {
		this._purchaseStatuses = purchaseStatuses;
		this._billingRegions = billingRegions;
		this.pointOfSale = pointOfSale;

		this.orderExecutorModel = new OrderExecutorModel({
			randomUUID: randomUUID,
			purchaseStatuses: purchaseStatuses,
			billingRegions: billingRegions,
			executionFlow: executionFlow,
			pointOfSaleUid: pointOfSale.uid,
			actionsHandler: [new class implements OrderExecutorPurchaseStatusActionHandler<undefined> {
				canHandle(action : OrderPurchaseStatusAction): boolean {
					return action === OrderPurchaseStatusAction.DISPATCH
				}

				handlePurchaseStatusUpdate() {}
			}]
		});

		this.virtualStockHandler = new VirtualStockWatcher();
		this.virtualStockHandler.bindOrderExecutorModel(this.orderExecutorModel);
		this.virtualStockHandler.on('stockSimpleChanged', (updatedStock) => {
			const index = this._stocks.findIndex((stock) => updatedStock.uid === stock.uid);
			if(index !== -1) {
				this._stocks.splice(index, 1, updatedStock);
			}
		});

		this.orderDiscountTemplateModel = new OrderDiscountTemplateModel();

		SetInstance(OrderExecutorModel, this.orderExecutorModel);
	}

	get products(): ProductApiOut[] {
		return Object.values(this._products);
	}

	set products(value: ProductApiOut[]) {
		for(let product of value) {
			this._products[product.uid] = product
		}
		this.orderExecutorModel.registerProducts(value);
	}

	get paymentMethods(): PaymentMethodApiOut[] {
		return this._paymentMethods;
	}

	set paymentMethods(paymentMethods: PaymentMethodApiOut[]) {
		this._paymentMethods = paymentMethods;
		this.orderExecutorModel.registerPaymentMethods(this._paymentMethods);
	}

	get stocks(): StockSimpleApiOut[] {
		return this._stocks;
	}

	set stocks(stocks: StockSimpleApiOut[]) {
		this._stocks = stocks;
		for(const stock of stocks) this.virtualStockHandler.registerStockSimple(stock);
	}

	get billingRegions(): BillingRegionApiOut[] {
		return this._billingRegions;
	}

	get contexts(): ContextApiOut[] {
		return this._contexts;
	}

	set contexts(contexts: ContextApiOut[]) {
		this._contexts = contexts;
		this.orderDiscountTemplateModel.registerContext(this._contexts);
	}

	get discountTemplates(): DiscountTemplateApiOut[] {
		return this._discountTemplates;
	}

	set discountTemplates(value: DiscountTemplateApiOut[]) {
		this._discountTemplates = value;
		this.orderDiscountTemplateModel.registerDiscountTemplates(this._discountTemplates);
	}

	get purchaseStatuses(): OrderPurchaseStatusApiOut[] {
		return this._purchaseStatuses;
	}

	getProductWithUid(productUid: VisualScopedUuid<UuidScopeProductProduct>) {
		const product = this._products[productUid];
		if(!product) throw new Error('no_product_matching');
		return product;
	}

	getStockWithUid(stockUid: VisualScopedUuid<UuidScopeProduct_stockSimple>) {
		const stock = this._stocks.find((stock) => stock.uid === stockUid);
		if(!stock) throw new Error('no_stock_matching');
		return stock;
	}

	getMethodWithUid(methodUid: VisualScopedUuid<UuidScopePayment_method>) {
		const method = this.paymentMethods.find((method) => method.uid === methodUid);
		if(!method) throw new Error('no_method_matching');
		return method;
	}

	getEstablishmentAccountWithUid(accountUid: VisualScopedUuid<UuidScopeEstablishmentAccount>) {
		const account = this.establishmentAccounts.find((account) => account.uid === accountUid);
		if(!account) throw new Error('no_account_matching');
		return account;
	}

	get currentOrder(): LocalOrder|null {
		return this.currentOrderByEstablishmentAccount[this.currentEstablishmentAccountUid] ?? null;
	}

	set currentOrder(localOrder: LocalOrder|null) {
		if(localOrder === null) delete this.currentOrderByEstablishmentAccount[this.currentEstablishmentAccountUid];
		else this.currentOrderByEstablishmentAccount[this.currentEstablishmentAccountUid] = localOrder;
		this.notifyCustomerScreen();
	}

	getPurchaseDiscount(purchase: PurchaseApiOut) {
		if(!this.currentOrder) throw new Error('no_current_order');
		for(let discount of this.currentOrder.order.discounts) {
			if(discount.rootPurchaseUids.includes(purchase.uid) && discount.cancelDatetime === null) return discount;
		}
		return null;
	}

	getPurchaseTotals(purchase: PurchaseApiOut) {
		if(!this.currentOrder) throw new Error('no_current_order');
		const totals = this.orderExecutorModel.getPurchasePrice(this.currentOrder.order, purchase);
		if(!('withTaxes' in totals)) throw new Error('invalid_pos_rounding');
		return totals;
	}

	async notifyCustomerScreen() {
		if(NativeHelper.instance.isModuleAvailable(DisplaysNative)) {
			const displaysNative = GetInstance(DisplaysNative);
			const list = (await displaysNative.status()).list;

			if(!this.currentOrder) {
				for(let display of list) {
					displaysNative.sendMessage(display.id, null satisfies CustomerDisplayMessage).catch();
				}
			} else {
				const displayOrder: CustomerDisplayOrder = {
					purchases: [],
					total: this.orderExecutorModel.getOrderTotals(this.currentOrder.order).purchases.withTaxesAfterDiscount
				};

				for(let purchase of this.currentOrder.order.purchases) {
					const quantity = this.getPurchaseQuantity(purchase);
					if(quantity > 0) {
						displayOrder.purchases.push({
							name: LengthFilter(this.orderExecutorModel.requireProductRevision(purchase.productRevisionUid).name, 35),
							quantity: quantity,
							unitPriceWithTax: this.orderExecutorModel.requireProductRevision(purchase.productRevisionUid).prices[0].withTaxes
						} satisfies CustomerDisplayPurchase)
					}
				}

				for(let display of list) {
					displaysNative.sendMessage(display.id, displayOrder satisfies CustomerDisplayMessage).catch();
				}
			}
		}
	}

	getPurchaseQuantity(purchase: PurchaseApiOut) {
		return purchase.items.reduce((quantity, item) => {
			const isItemCanceled = this.orderExecutorModel.isPurchaseItemCanceled(item);
			return quantity + (isItemCanceled ? 0 : item.quantity);
		}, 0);
	}

	isClockedIn(): boolean {
		const accountLastWorkClock = this.lastWorkClocks.find((workClock) => workClock.establishmentAccountUid === this.currentEstablishmentAccountUid) ?? null;
		if(accountLastWorkClock && accountLastWorkClock.clockIn) return true;
		return false;
	}

	getFirstLevelProfile(): PosProfileApiOut|null {
		let minMatchingKeys: number|null = null;
		let firstLevelProfile: PosProfileApiOut|null = null;

		for(let profile of this.posProfiles) {
			let matchingKeys: number = 0;
			if(profile.iotDeviceUid !== null) matchingKeys++;
			if(profile.establishmentAccountUid !== null) matchingKeys++;

			if(minMatchingKeys === null || matchingKeys < minMatchingKeys) {
				minMatchingKeys = matchingKeys;
				firstLevelProfile = profile;
			}
		}
		return firstLevelProfile;
	}

	getEstablishmentAccountProfile(establishmentAccountUid: VisualScopedUuid<UuidScopeEstablishmentAccount>) {
		return this.posProfiles.find((profile) => profile.establishmentAccountUid === establishmentAccountUid) ?? null;
	}

	requireCategoryWithUid(categoryUid: VisualScopedUuid<UuidScopeProductCategory>) {
		const category = this.categories.find((category) => category.uid === categoryUid);
		if(!category) throw new Error('no_matching_category');
		return category;
	}

	requirePaymentMethodWithUid(paymentMethodUid: VisualScopedUuid<UuidScopePayment_method>) {
		const paymentMethod = this._paymentMethods.find((paymentMethod) => paymentMethod.uid === paymentMethodUid);
		if(!paymentMethod) throw new Error('no_matching_payment_method');
		return paymentMethod;
	}

	requireMetadataDescriptorWithUid(metadataDescriptorUid: VisualScopedUuid<UuidScopeMetadata_descriptor>) {
		const metadataDescriptor = this.metadataDescriptors.find((metadataDescriptor) => metadataDescriptor.uid === metadataDescriptorUid);
		if(!metadataDescriptor) throw new Error('no_matching_metadata_descriptor');
		return metadataDescriptor;
	}

	requireTableWithUid(tableUid: VisualScopedUuid<UuidScopeProduct_diningTable>) {
		for(const area of this.diningAreas) {
			for(const table of area.tables) {
				if(table.uid === tableUid) return table;
			}
		}
		return new DiningTableApiOut({
			uid: tableUid,
			name: 'Table inconnue',
			headCount: 0,
			creationDatetime: new Date().toISOString(),
			iconUploadUid: undefined
		});
	}

	requirePrintingLocationWithUid(printingLocationUid: VisualScopedUuid<UuidScopeProduct_printingLocation>) {
		const printingLocation = this.pointOfSaleConfiguration.printingLocationList.find((printingLocation) => printingLocation.uid === printingLocationUid);
		if(!printingLocation) throw new Error('no_matching_printing_location');
		return printingLocation;
	}

	requireDeviceAppWithUid(deviceUid: ScopedUuid<UuidScopeIot_deviceApp>): EstablishmentDeviceAppV2ApiOut {
		const iotDevice = this.iotDeviceApps.find((iotDevice) => iotDevice.uid === deviceUid);
		if(!iotDevice) throw new Error('no_matching_iot_device');
		return iotDevice;
	}

	requireDeviceWithUid(deviceUid: ScopedUuid<UuidScopeIot_device>): EstablishmentDeviceV2ApiOut {
		const iotDevice = this.iotDevices.find((iotDevice) => iotDevice.uid === deviceUid);
		if(!iotDevice) throw new Error('no_matching_iot_device');
		return iotDevice;
	}
}