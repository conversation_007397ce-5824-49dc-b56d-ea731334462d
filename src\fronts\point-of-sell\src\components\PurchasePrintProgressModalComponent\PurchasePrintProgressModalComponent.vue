<script lang="ts" src="./PurchasePrintProgressModalComponent.ts"/>

<style lang="sass" scoped>
@import './PurchasePrintProgressModalComponent.scss'
</style>

<template>
    <div class="purchase-print-progress-modal-component">
        <div class="modal-dimmer">
            <div class="modal" @click.stop>
                <div class="head">
                    <span class="title"> Impression en cours </span>
                </div>

                <div class="progress-group" :class="{error: printerData.error}" v-for="(printerData, key) of data.printers">
                    <span class="current-step">
                        <span class="printer-name"> Impr. {{ key }} </span>
                        <span class="step"> {{ printerData.currentStepName }} </span>
                    </span>
                    <div class="progress-bar">
                        <div class="progress" :style="{width: (printerData.currentStep / printerData.totalSteps) * 100 + '%'}"></div>
                    </div>

                    <button class="white button" v-if="printerData.error" @click="printerRepository.retryFailedPrinting()">
                        Rééssayer
                    </button>
                </div>

                <button class="big grey button" @click="close()">
                    Fermer
                </button>
            </div>
        </div>
    </div>
</template>