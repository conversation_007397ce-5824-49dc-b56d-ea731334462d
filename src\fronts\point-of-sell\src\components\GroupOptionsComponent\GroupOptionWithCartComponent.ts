import {Component, Prop, Vue} from "vue-facing-decorator";
import {
	OrderExecutorModelPurchaseAddDescriptor,
	OrderExecutorModelPurchaseGroupItemAddDescriptor,
	ProductApiOut,
	ProductGroupApiOut,
	ProductGroupItemApiOut,
	ProductType,
	UuidScopeProductProduct,
	UuidScopeProductProductGroup,
	UuidScopeProductProductGroupItem
} from "@groupk/mastodon-core";
import GroupOptionsComponent from "./GroupOptionsComponent.vue";
import {PosState} from "../../model/PosState";
import {VisualScopedUuid} from "@groupk/horizon2-core";
import {PosProfile} from "../../model/PosProfile";

export interface StackingContext {
	product: ProductApiOut,
	parentGroupUid: VisualScopedUuid<UuidScopeProductProductGroup>|null,
	item?: ProductGroupItemApiOut
	descriptor: OrderExecutorModelPurchaseGroupItemAddDescriptor|OrderExecutorModelPurchaseAddDescriptor,
	editing?: boolean
}

@Component({
	components: {
		'group-option': GroupOptionsComponent,
	},
	emits: ['cancel', 'add-to-order']
})
export default class GroupOptionWithCartComponent extends Vue {
	@Prop() posState!: PosState;
	@Prop() posProfile!: PosProfile;
	@Prop() product!: ProductApiOut;

	initiate: boolean = true;
	displayCart: boolean = false;

	descriptor!: OrderExecutorModelPurchaseAddDescriptor;

	stackingContext: StackingContext[] = [];

	mounted() {
		let dining: {
			step: number,
		}|undefined = undefined;

		// if it is a customizable product we add only first item to current step
		// Else if it is a menu, see comment in GroupOptionsComponent
		if(this.product.lastRevision.type === null) {
			dining = {
				step: this.posState.currentOrder?.currentStep ?? 0
			}
		}

		this.descriptor = {
			productUid: this.product.uid,
			quantity: 1,
			groups: this.product.lastRevision.groups.map((group) => {
				return {
					groupUid: group.uid,
					items: []
				}
			}),
			dining: dining
		};
		this.stackingContext.push({
			product: this.product,
			parentGroupUid: null,
			descriptor: this.descriptor,
		});
		this.initiate = false;
	}

	addToContext(data: {item: ProductGroupItemApiOut, product: ProductApiOut, group: ProductGroupApiOut, dining: {
			step: number,
		}|undefined;
	}) {
		const descriptor: OrderExecutorModelPurchaseGroupItemAddDescriptor = {
			itemUid: data.item.uid,
			productUid: data.product.uid,
			quantity: 1,
			groups: data.product.lastRevision.groups.map((group) => {
				return {
					groupUid: group.uid,
					items: []
				}
			}),
			dining: data.dining
		}

		const currentDescriptor = this.stackingContext[this.stackingContext.length - 1].descriptor;
		for(let descriptorGroup of (currentDescriptor.groups??[])) {
			if(descriptorGroup.groupUid === data.group.uid) {
				descriptorGroup.items.push(descriptor);
			}
		}

		this.stackingContext.push({
			product: data.product,
			item: data.item,
			parentGroupUid: data.group.uid,
			descriptor: descriptor
		});
	}

	cancelGroup() {
		const descriptorToDelete = this.stackingContext[this.stackingContext.length - 1].descriptor;
		if(!('itemUid' in descriptorToDelete)) {
			this.$emit('cancel');
		} else {
			const deletedStackingContext = this.stackingContext.pop();
			if(!deletedStackingContext) throw new Error('no_popped_context');

			if(!deletedStackingContext.editing) {
				const currentDescriptor = this.stackingContext[this.stackingContext.length - 1].descriptor;
				if (!currentDescriptor.groups) throw new Error('invalid_group_state');
				for (let group of currentDescriptor.groups) {
					if (group.groupUid === deletedStackingContext.parentGroupUid) {
						group.items.pop();
					}
				}
			}
		}
	}

	configuredGroup(descriptor: OrderExecutorModelPurchaseGroupItemAddDescriptor|OrderExecutorModelPurchaseAddDescriptor) {
		if(!('itemUid' in descriptor)) {
			// Product can be added to cart since root group has been fully configured
			console.log(descriptor);
			this.$emit('add-to-order', descriptor);
		} else {
			// Sub product have been configured, remove it from stack
			this.stackingContext.pop();
		}
	}

	getCurrentGroups() {
		const currentDescriptor = this.stackingContext[this.stackingContext.length - 1].descriptor;
		if(!currentDescriptor.groups) throw new Error('invalid_group_state');
		return currentDescriptor.groups;
	}

	getGroupWithUid(groupUid: VisualScopedUuid<UuidScopeProductProductGroup>) {
		const group = this.stackingContext[this.stackingContext.length - 1].product.lastRevision.groups.find((group) => group.uid === groupUid);
		if(!group) throw new Error('missing_group');
		return group;
	}

	getItemRecursiveItems(item: OrderExecutorModelPurchaseGroupItemAddDescriptor, depth: number = 1): { name: string, quantity: number, depth: number }[] {
		const items: { name: string, quantity: number, depth: number }[] = [{
			name: this.posState.getProductWithUid(item.productUid).lastRevision.name,
			quantity: item.quantity,
			depth: depth
		}];

		if(!item.groups) return items;

		for(let group of item.groups) {
			for(let subItem of group.items) {
				items.push(...this.getItemRecursiveItems(subItem, depth + 1));
			}
		}

		return items;
	}

	getTotalPrice() {
		let price = 0;
		const product = this.posState.getProductWithUid(this.descriptor.productUid);
		price += product.lastRevision.prices[0].withTaxes;

		if(this.descriptor.groups) {
			for (let group of this.descriptor.groups) {
				for (let item of group.items) {
					price += this.getItemDescriptorExtraCharges(item, product);
				}
			}
		}
		return price;
	}

	getItemDescriptorExtraCharges(descriptor: OrderExecutorModelPurchaseGroupItemAddDescriptor, parentProduct: ProductApiOut) {
		let extraCharges = 0;
		const product = this.posState.getProductWithUid(descriptor.productUid);
		const item = this.findItemWithUid(descriptor.itemUid, parentProduct);
		if(item.prices !== null) {
			extraCharges += item.prices[0].withTaxes * descriptor.quantity;
		} else {
			extraCharges += product.lastRevision.prices[0].withTaxes * descriptor.quantity;
		}

		if(descriptor.groups) {
			for(let group of descriptor.groups) {
				for(let item of group.items) {
					extraCharges += this.getItemDescriptorExtraCharges(item, product);
				}
			}
		}

		return extraCharges;
	}

	editItem(descriptor: OrderExecutorModelPurchaseGroupItemAddDescriptor, parentGroupUid: VisualScopedUuid<UuidScopeProductProductGroup>) {
		const product = this.posState.getProductWithUid(descriptor.productUid);
		this.stackingContext.push({
			descriptor: descriptor,
			product: product,
			parentGroupUid: parentGroupUid,
			editing: true
		});
		this.displayCart = false;
	}

	cancelItem(descriptor: OrderExecutorModelPurchaseGroupItemAddDescriptor, parentGroup: {
		groupUid: VisualScopedUuid<UuidScopeProductProductGroup>,
		items: OrderExecutorModelPurchaseGroupItemAddDescriptor[],
	}) {
		let index = 0;
		for(let itemDescriptor of parentGroup.items) {
			if(itemDescriptor === descriptor) {
				if(descriptor.quantity > 1) descriptor.quantity--;
				else parentGroup.items.splice(index, 1);
				return;
			}
			index++;
		}
	}

	findItemWithUid(itemUid: VisualScopedUuid<UuidScopeProductProductGroupItem>, parentProduct: ProductApiOut): ProductGroupItemApiOut {
		for(const group of parentProduct.lastRevision.groups) {
			for(const item of group.items) {
				if(item.uid === itemUid) return item;
			}
		}
		throw new Error('missing_item');
	}
};