import {dehydrate, hydrate} from "@groupk/horizon2-core";
import {CashStatementApiOut, CashStatementSessionApiOut} from "@groupk/mastodon-core";

export class LocalCashStatementRepository {
	lsKey = 'cash-statement-session';

	getSession(){
		const rawSession = localStorage.getItem(this.lsKey);
		if(rawSession) {
			try {
				return hydrate(CashStatementSessionApiOut, JSON.parse(rawSession));
			} catch (err) {
				return null;
			}
		} else {
			return null;
		}
	}

	saveSession(session: CashStatementSessionApiOut) {
		localStorage.setItem(this.lsKey, JSON.stringify(dehydrate(session)));
	}

	isSessionActive(session: CashStatementSessionApiOut, cashStatement: CashStatementApiOut) {
		const currentSession = cashStatement.sessions.find((openedSession) => openedSession.uid === session.uid);
		if(currentSession && currentSession.closingDatetime === null) {
			return true;
		}
		return false;
	}
}