.device-onboarding-scan-component {
    .empty-quick-links {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        gap: 20px;
        padding: 20px;
        border-radius: 8px;
    }

    .container {
        display: flex;
        flex-direction: column;
        gap: 6px;

        .small-title {
            font-size: 14px;
            font-weight: 500;
        }

        .found-devices {
            display: flex;
            flex-direction: column;
            gap: 10px;

            .found-device {
                display: flex;
                align-items: center;
                gap: 20px;
                padding: 15px 15px;
                border-radius: 8px;
                background: var(--secondary-hover-color);

                .data {
                    flex-grow: 2;
                    display: flex;
                    flex-direction: column;
                    gap: 4px;

                    .id {
                        font-weight: 500;
                    }

                    .name {
                        font-size: 14px;
                    }
                }
            }
        }
    }
}