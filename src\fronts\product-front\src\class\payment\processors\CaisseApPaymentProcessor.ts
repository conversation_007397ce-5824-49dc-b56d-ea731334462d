import {OrderPaymentApiOut} from "@groupk/mastodon-core";
import {AutoWired, Buffer<PERSON>tils, EventEmitter, Uuid, UuidUtils} from "@groupk/horizon2-core";
import {PhysicalPaymentProtocol} from "./PhysicalPaymentProtocol";
import {
	ActionStatus,
	createPaymentRequest,
	decodePaymentResult,
	PaymentRequest
} from "@groupk/caisse-protocol";
import {PaymentMethodSettingsCaisseAp} from "@groupk/mastodon-core";
import {CommunicationKnownTagContainer} from "@groupk/caisse-protocol";
import {CONCERT_DESCRIPTORS} from "@groupk/caisse-protocol";
import {SocketNative} from "@groupk/native-bridge";
import {DecodeException} from "@groupk/caisse-protocol";

export interface CashlessProtocolCaissePrivateFieldRequest {
	forceSync?: boolean|undefined;
	expectedChip?: number|undefined;
	identifyDelay?: number|undefined;
	internalTxId?: Uuid|undefined;
}
// export interface CashlessProtocolCaissePrivateFieldResponse {
// 	tx?: ScopedUuid<UuidScopeCashlessTransaction>;
// 	chip?: number;
// 	balance?: number;
// }

export class CaisseApPaymentProcessor extends EventEmitter<{
	'totalPaidChanged': number,
	'totalRefundChanged': number,
	'done': boolean
}> implements PhysicalPaymentProtocol {
	settings: PaymentMethodSettingsCaisseAp;

	config = {
		displayLivePaidAmount: false,
		autoCloseOnSuccess: false,
		cancelable: false
	};

	@AutoWired(SocketNative) accessor nativeSocket!: SocketNative;

	constructor(settings: PaymentMethodSettingsCaisseAp) {
		super();
		this.settings = settings;
	}

	async initiatePayment(payment: OrderPaymentApiOut) {
		const tcpWrapper = await this.nativeSocket.tcpConnectWrapper(this.settings.ip, this.settings.port);

		const caisseApPayment: PaymentRequest = {
			currency: '978',
			amount: payment.amount,
			// delayWaitPayment: 60,
			manualTags: [
				new CommunicationKnownTagContainer(CONCERT_DESCRIPTORS.CF, JSON.stringify({
					internalTxId: UuidUtils.visualToUuid(payment.uid),
				} satisfies CashlessProtocolCaissePrivateFieldRequest))
			]
		}

		const timeout = setTimeout(async () => {
			try {
				tcpWrapper.close();
			} catch(err) {
				console.log(err);
			}
		}, (caisseApPayment.delayWaitPayment ?? 60)*1000 + 30000);

		tcpWrapper.on('closed', ()=> {
			clearTimeout(timeout);
			this.cancel();
		})

		tcpWrapper.on('connected', ()=>{
			tcpWrapper.on('data', (bytes) => {
				let response: string = String.fromCharCode(...(typeof bytes === 'string' ? BufferUtils.hex2Buffer(bytes) : bytes));

				try {
					const result = decodePaymentResult(response, {
						decodeUnknownTags: true
					});
					alert(JSON.stringify(result));
					if(result.status === ActionStatus.OperationDone) {
						this.emit('done', true);
					} else {
						this.emit('done', false);
					}
				} catch(err) {
					console.log(response)
					console.log(err);
					console.log(JSON.stringify(err));
					if(err instanceof DecodeException ){
						console.log(err.partialBundle)
						console.log(err.cause)
					}
					this.emit('done', false);
				}

				clearTimeout(timeout);
				tcpWrapper.close();
			});

			const data = createPaymentRequest(caisseApPayment);
			let payload: number[] = [];
			for (let i = 0; i < data.length; i++) {
				payload.push(data.charCodeAt(i));
			}
			tcpWrapper.send(payload);
		})
	}

	cancel() {
		this.emit('done', false);
	}
}
