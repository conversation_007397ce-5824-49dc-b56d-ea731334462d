<script lang="ts" src="./UnlockTransferModalComponent.ts" />

<style lang="sass" scoped>
@import './UnlockTransferModalComponent.scss'
</style>

<template>
    <div class="unlock-transfer-modal-component">
        <div class="modal">
            <div class="head">
                <span class="title"> Ren<PERSON>z le code de déblocage </span>

                <div class="close" @click="close()">
                    <i class="fa-regular fa-xmark"></i>
                </div>
            </div>


            <keypad :simple-mode="true" @clicked-key="clickedSecurityCodeKey($event)">
                <template v-slot:display>
                    <div class="code" :class="{red: invalidCode}">
                        <div class="character"> {{ unlockCode[0] }} </div>
                        <div class="character"> {{ unlockCode[1] }} </div>
                        <div class="character"> {{ unlockCode[2] }} </div>
                        <div class="character"> {{ unlockCode[3] }} </div>
                    </div>
                </template>
            </keypad>

            <button class="primary button" @click="tryUnlockTransfer()"> Valider </button>
        </div>
    </div>
</template>