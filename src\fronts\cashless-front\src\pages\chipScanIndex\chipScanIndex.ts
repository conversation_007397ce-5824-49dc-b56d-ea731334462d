import {Component, Vue} from "vue-facing-decorator";
import {CashlessEstablishmentModel, PublicCashlessEstablishmentApiOut} from "@groupk/mastodon-core";
import {CashlessEstablishmentRepository} from "../../../../../shared/repositories/CashlessEstablishmentRepository";
import {AutoWired, UuidUtils} from "@groupk/horizon2-core";

@Component({

})
export default class chipScanIndexView extends Vue {
    publicEstablishment!: PublicCashlessEstablishmentApiOut;
    publicChipId!: string;

    error: string | null = null;
    redirecting: boolean = false;
    areRefundsAvailable: boolean = false;
    areOnlineFundingsAvailable: boolean = false;

    @AutoWired(CashlessEstablishmentRepository) accessor cashlessEstablishmentRepository!: CashlessEstablishmentRepository;

    async mounted() {
        const urlParams = new URLSearchParams(window.location.search);

        if (urlParams.has('e') && urlParams.has('c')) {
            this.publicChipId = urlParams.get('c')!;
            const publicEstablishmentId = CashlessEstablishmentModel.decodePublicRoutingId(urlParams.get('e')!);

            const response = await this.cashlessEstablishmentRepository.callContract('getPublicWithPublicRouting', {establishmentPublicRoutingId: publicEstablishmentId}, undefined);

            if (!response.isSuccess()) {
                this.error = 'Impossible de charger la page'
                return;
            }

            this.publicEstablishment = response.success();

            if (this.publicEstablishment.websiteUrl) {
                this.redirecting = true;
                window.location.href = this.publicEstablishment.websiteUrl;
                return;
            }

            if (this.publicEstablishment.refunds) {
                const startDate = new Date(this.publicEstablishment.refunds.startingDatetime);
                const endDate = new Date(this.publicEstablishment.refunds.endingDatetime);
                const now = new Date();

                if (now.getTime() >= startDate.getTime() && now.getTime() < endDate.getTime()) this.areRefundsAvailable = true;
            }

            if (this.publicEstablishment.onlineFunding) {
                const startDate = this.publicEstablishment.onlineFunding.startingDatetime ? new Date(this.publicEstablishment.onlineFunding.startingDatetime) : null;
                const endDate = this.publicEstablishment.onlineFunding.endingDatetime ? new Date(this.publicEstablishment.onlineFunding.endingDatetime) : null;
                const now = new Date();

                if ((!startDate || now.getTime() >= startDate.getTime()) && (!endDate || now.getTime() < endDate.getTime())) this.areOnlineFundingsAvailable = true;
            }
        }
    }

    getRefundPageUrl() {
        return window.location.origin + `/establishment/${UuidUtils.visualToScoped(this.publicEstablishment.uid)}/account?chipId=${this.publicChipId}`;
    }

    getRefillPageUrl() {
        return window.location.origin + `/establishment/${UuidUtils.visualToScoped(this.publicEstablishment.uid)}/refill?chipId=${this.publicChipId}`;
    }
}