import {Component, Prop, Vue} from "vue-facing-decorator";
import {CleaveDirective} from "../../directives/CleaveDirective";
import {DropdownComponent, FormModalOrDrawerComponent, InputDateTimeComponent} from "@groupk/vue3-interface-sdk";
import GeocodingInputComponent from "../../../fronts/ticketing-partner/src/components/GeocodingInputComponent/GeocodingInputComponent";
import UploadInputComponent from "../UploadInputComponent/UploadInputComponent";
import {AutoWired, EnumUtils} from "@groupk/horizon2-core";
import {AppCustomApiIn, AppEvent} from "@groupk/mastodon-core";
import {
    AppEventDestinationWebhookMethod
} from "@groupk/mastodon-core";
import {CustomAppRepository} from "../../repositories/CustomAppRepository";
import {AppState} from "../../AppState";
import {AppCustomApiOut, AppType} from "@groupk/mastodon-core";
import {AppEventDestinationData} from "../../mastodonCoreFront/AppEventDestinationData";

@Component({
    directives: {
        cleave: CleaveDirective,
    },
    components: {
        'date-time-input': InputDateTimeComponent,
        'geocoding-input': GeocodingInputComponent,
        'form-modal-or-drawer': FormModalOrDrawerComponent,
        'upload-input': UploadInputComponent,
        'dropdown': DropdownComponent
    },
    emits: ['close', 'created', 'updated']
})
export default class CustomIntegrationFormComponent extends Vue {
    @Prop({default: null}) editingApp!: AppCustomApiOut|null;

    appName: string = '';
    eventDestinations: AppEventDestinationData[] = [];

    opened: boolean = false;
    loading: boolean = false;
    error: string|null = null;

    @AutoWired(CustomAppRepository) private accessor customAppRepository!: CustomAppRepository;
    @AutoWired(AppState) private accessor appState!: AppState;

    async mounted() {
        setTimeout(() => this.opened = true, 0);

        if(this.editingApp) {
            this.appName = this.editingApp.name;
            this.eventDestinations = this.editingApp.eventDestinationList.map((eventDestination) => new AppEventDestinationData(eventDestination));
        }
    }

    getAppEventDropdownValues() {
        return EnumUtils.values(AppEvent).map((value) => {
            return {
                name: value,
                value: value
            }
        })
    }

    getWebhookMethodDropdownValues() {
        return EnumUtils.values(AppEventDestinationWebhookMethod).map((value) => {
            return {
                name: value,
                value: value
            }
        })
    }

    addEventDestination() {
        this.eventDestinations.push(new AppEventDestinationData());
    }

    async save() {
        let app: AppCustomApiOut|null = this.editingApp;
        if(!app) {
            app = (await this.customAppRepository.callContract('create', {establishmentUid: this.appState.requireUrlEstablishmentUid()}, new AppCustomApiIn({
                name: this.appName,
                type: AppType.CUSTOM
            }))).success().item as AppCustomApiOut;
        }

        for(const eventDestination of this.eventDestinations) {
            if(eventDestination.uid) {
                this.customAppRepository.callContract('updateEventDestination', {
                    eventDestinationUid: eventDestination.uid,
                    establishmentUid: this.appState.requireUrlEstablishmentUid(),
                    appUid: app.uid
                }, eventDestination.toApi()).catch((err) => {
                    console.log(err);
                });
            } else {
                this.customAppRepository.callContract('createEventDestination', {
                    establishmentUid: this.appState.requireUrlEstablishmentUid(),
                    appUid: app.uid
                }, eventDestination.toApi()).catch((err) => {
                    console.log(err);
                });
            }
        }

        this.close();
    }

    close() {
        this.opened = false;
        setTimeout(() => this.$emit('close'), 300);
    }
}