# FilterTableLayout Component Documentation

## Component Overview

The `FilterTableLayoutComponent` is a comprehensive layout component from the `@groupk/vue3-interface-sdk` package that provides a complete table interface with advanced filtering, sorting, pagination, and side drawer functionality. It serves as the primary layout component for data management pages across all frontend applications in the mastodon-fronts monorepo.

### Purpose and Primary Use Cases

- **Data Management Pages**: Primary component for listing and managing entities (transactions, orders, products, etc.)
- **Administrative Interfaces**: Provides consistent UI/UX across all admin panels
- **Master-Detail Views**: Built-in side drawer for detailed entity views
- **Advanced Filtering**: Sophisticated filtering system with saved filters support
- **Export Functionality**: Integrated export capabilities with multiple formats

### Component Hierarchy and Relationships

```
FilterTableLayoutComponent
├── ContentHeaderComponent (header with actions and search)
├── CustomFilterComponent (advanced filtering interface)
├── TableColumnsOrganizationComponent (column management)
├── TableComponent (data display)
└── RightDrawerComponent (detail view panel)
```

### Key Features and Capabilities

- **Responsive Design**: Mobile-optimized with column hiding
- **Real-time Updates**: WebSocket integration for live data updates
- **Column Management**: User-customizable column display and ordering
- **Pagination**: Server-side pagination with configurable page sizes
- **Sorting**: Multi-column sorting with visual indicators
- **Search**: Full-text search with debounced input
- **Saved Filters**: User-defined filter presets
- **Export Integration**: Built-in export functionality
- **Keyboard Navigation**: Accessibility-focused keyboard shortcuts

## API Reference

### Props

| Prop | Type | Required | Default | Description |
|------|------|----------|---------|-------------|
| `allowedFilters` | `TypedQuerySearchConfig` | ✅ | - | Configuration object defining available filters and their types |
| `appliedFilters` | `TypedQuerySearch<AllowedFilters>` | ✅ | - | Currently applied filters object |
| `headerParameters` | `ContentHeaderParameters` | ✅ | - | Header configuration including title, subtitle, and actions |
| `tableColumns` | `TableColumn[]` | ✅ | - | Column definitions with display settings |
| `filters` | `TypedQuerySearch<AllowedFilters>` | ✅ | - | Current filter state |
| `filterParameters` | `{[filterName: string]: FilterParameters}` | ❌ | `{}` | Filter UI configuration and validation rules |
| `drawerOpened` | `boolean` | ❌ | `false` | Controls the right drawer visibility |
| `pagination` | `TablePagination \| null` | ❌ | `null` | Pagination configuration and state |
| `savedFilters` | `SavedFilter[]` | ❌ | `[]` | Array of user-saved filter presets |
| `displayOnMidScreen` | `boolean` | ❌ | `false` | Layout optimization for medium screens |
| `large` | `boolean` | ❌ | `false` | Enable large layout mode |
| `hideFirstLevelFilters` | `boolean` | ❌ | `false` | Hide basic filter controls |
| `sortedRow` | `{name: string, direction: 'asc' \| 'desc'} \| null` | ❌ | `null` | Current sort configuration |
| `showFilters` | `boolean` | ❌ | `true` | Show/hide filter interface |
| `showSavedFilters` | `boolean` | ❌ | `true` | Show/hide saved filters dropdown |
| `showColumnsOrganization` | `boolean` | ❌ | `true` | Show/hide column organization controls |
| `alternateLines` | `boolean` | ❌ | `true` | Enable alternating row colors |

### Events

| Event | Payload Type | Description |
|-------|--------------|-------------|
| `filters-changed` | `TypedQuerySearch<AllowedFilters>` | Emitted when filters are modified |
| `search` | `string` | Emitted when search input changes |
| `next` | `void` | Emitted when next page is requested |
| `previous` | `void` | Emitted when previous page is requested |
| `sorted` | `{name: string, direction: 'asc' \| 'desc'}` | Emitted when column sorting changes |
| `changed-column-preferences` | `TableColumn[]` | Emitted when column display preferences change |
| `save-filter` | `string` | Emitted when user saves a filter with given name |
| `select-filter` | `SavedFilter` | Emitted when user selects a saved filter |
| `delete-filter` | `number` | Emitted when user deletes a saved filter by index |
| `tab-selected` | `ContentHeaderParametersTab` | Emitted when header tab is selected |

### Slots

| Slot Name | Description | Scope |
|-----------|-------------|-------|
| `table-data` | **Required**. Table rows content | - |
| `right` | Right drawer content | - |
| `actions-left` | Additional actions on the left side of header | - |
| `actions-right` | Additional actions on the right side of header | - |
| `filters-top` | Custom filters above default filter bar | - |
| `filters-bottom` | Custom filters below default filter bar | - |

### Methods

| Method | Parameters | Return Type | Description |
|--------|------------|-------------|-------------|
| `resetFilters()` | - | `void` | Clear all applied filters |
| `applyFilters(filters)` | `TypedQuerySearch<AllowedFilters>` | `Promise<void>` | Apply new filter set |
| `sortRow(name)` | `string` | `void` | Sort by column name |
| `search(value)` | `string` | `void` | Perform search |
| `previousPage()` | - | `void` | Navigate to previous page |
| `nextPage()` | - | `void` | Navigate to next page |
| `selectFilter(filter)` | `SavedFilter` | `void` | Apply saved filter |
| `saveFilter(name)` | `string` | `void` | Save current filters with name |
| `deleteFilter(index)` | `number` | `void` | Delete saved filter by index |

## Usage Examples

### Basic Usage Example

```vue
<template>
  <div class="my-page">
    <filter-table-layout
      :header-parameters="headerParameters"
      :allowed-filters="allowedFilters"
      :table-columns="tableColumns"
      :filters="filters"
      :drawer-opened="selectedItem !== null"
      @filters-changed="handleFiltersChanged"
      @changed-column-preferences="saveColumnPreferences"
    >
      <template v-slot:table-data>
        <tr v-if="loading" class="table-dimmer">
          <td colspan="100%">
            <div class="loader-container">
              <div class="loader"></div>
            </div>
          </td>
        </tr>
        <tr v-else-if="items.length === 0" class="table-no-data">
          <td colspan="100%">No data available</td>
        </tr>
        <tr v-else v-for="item in items" 
            :key="item.uid"
            :class="{selected: selectedItem?.uid === item.uid}"
            @click="selectItem(item)">
          <td v-for="column in visibleColumns" 
              :class="{'mobile-hidden': column.mobileHidden}">
            <template v-if="column.name === 'name'">
              {{ item.name }}
            </template>
            <template v-if="column.name === 'status'">
              <span class="label" :class="getStatusClass(item.status)">
                {{ item.status }}
              </span>
            </template>
          </td>
        </tr>
      </template>
      
      <template v-slot:right>
        <div v-if="!selectedItem" class="empty-right-panel">
          <img :src="selectHintImage" />
          Click an item to select it
        </div>
        <div v-else class="selected-item-details">
          <div class="close" @click="selectedItem = null">
            <i class="fa-regular fa-xmark"></i>
            <span>Close</span>
          </div>
          <!-- Item details here -->
        </div>
      </template>
    </filter-table-layout>
  </div>
</template>

<script lang="ts">
import { Component, Vue } from "vue-facing-decorator";
import { 
  FilterTableLayoutComponent, 
  ContentHeaderParameters, 
  TableColumn 
} from "@groupk/vue3-interface-sdk";

@Component({
  components: {
    'filter-table-layout': FilterTableLayoutComponent
  }
})
export default class MyPage extends Vue {
  items = [];
  selectedItem = null;
  loading = false;
  
  headerParameters: ContentHeaderParameters = {
    header: 'My Items',
    subtitle: 'Manage your items',
    actions: [{
      type: 'SIMPLE_ACTION',
      name: 'Create Item',
      icon: 'fa-regular fa-plus',
      callback: this.createItem
    }]
  };
  
  tableColumns: TableColumn[] = [
    {
      title: 'Name',
      name: 'name',
      displayed: true,
      mobileHidden: false
    },
    {
      title: 'Status',
      name: 'status',
      displayed: true,
      mobileHidden: true
    }
  ];
  
  allowedFilters = {
    filters: [],
    sorts: []
  };
  
  filters = {};
  
  get visibleColumns() {
    return this.tableColumns.filter(col => col.displayed);
  }
  
  handleFiltersChanged(newFilters) {
    this.filters = newFilters;
    this.loadItems();
  }
  
  saveColumnPreferences(columns: TableColumn[]) {
    this.tableColumns = columns;
    // Save to localStorage or API
  }
}
</script>
```

### Advanced Configuration with Pagination and Saved Filters

```vue
<template>
  <filter-table-layout
    :header-parameters="headerParameters"
    :allowed-filters="allowedFilters"
    :table-columns="tableColumns"
    :filters="filters"
    :applied-filters="appliedFilters"
    :drawer-opened="selectedItem !== null"
    :pagination="pagination"
    :saved-filters="savedFilters"
    :filter-parameters="filterParameters"
    @filters-changed="searchItems"
    @next="nextPage"
    @previous="previousPage"
    @sorted="handleSort"
    @save-filter="saveFilter"
    @select-filter="selectFilter"
    @delete-filter="deleteFilter"
  >
    <template v-slot:actions-left>
      <div v-if="newItemsCount > 0" 
           class="new-data cursor-pointer" 
           @click="refreshItems">
        <div class="label">
          Show {{ newItemsCount }} new items
        </div>
      </div>
    </template>
    
    <template v-slot:table-data>
      <!-- Table content with advanced features -->
      <tr v-for="item in items" 
          :key="item.uid"
          :class="{selected: selectedItem?.uid === item.uid}"
          @click="selectItem(item)">
        <td v-for="column in visibleColumns" 
            :class="{'mobile-hidden': column.mobileHidden}">
          <template v-if="column.name === 'actions'">
            <dropdown-button
              button-class="tertiary button"
              icon="fa-regular fa-ellipsis-vertical"
              alignment="RIGHT"
              @clicked="handleActionClick($event, item)"
              :actions="getItemActions(item)"
            />
          </template>
          <!-- Other column templates -->
        </td>
      </tr>
    </template>
  </filter-table-layout>
</template>

<script lang="ts">
export default class AdvancedPage extends Vue {
  pagination: TablePagination = {
    totalResults: 0,
    resultsPerPage: 25,
    currentPage: 1,
    estimateTotal: false
  };
  
  savedFilters: SavedFilter[] = [];
  
  filterParameters: { [key: string]: FilterParameters } = {
    'status': {
      translation: 'Status',
      type: 'DROPDOWN',
      dropdownValues: [
        { value: 'active', label: 'Active' },
        { value: 'inactive', label: 'Inactive' }
      ]
    },
    'createdDate': {
      translation: 'Creation Date',
      type: 'DATE_RANGE'
    },
    'amount': {
      translation: 'Amount',
      type: 'DECIMAL',
      validation: (value) => !isNaN(parseFloat(value))
    }
  };
  
  async searchItems(newFilters) {
    this.loading = true;
    try {
      const result = await this.itemsRepository.search({
        filters: newFilters,
        pagination: this.pagination
      });
      this.items = result.items;
      this.pagination.totalResults = result.total;
    } finally {
      this.loading = false;
    }
  }
  
  nextPage() {
    if (this.pagination.currentPage * this.pagination.resultsPerPage < this.pagination.totalResults) {
      this.pagination.currentPage++;
      this.searchItems(this.filters);
    }
  }
  
  handleSort(sortConfig) {
    this.appliedFilters.sorts = [sortConfig];
    this.searchItems(this.appliedFilters);
  }
}
</script>
```

### Master-Detail Pattern with Complex Right Drawer

```vue
<template>
  <filter-table-layout
    :drawer-opened="selectedTransaction !== null"
    :header-parameters="headerParameters"
    :table-columns="tableColumns"
    :filters="filters"
    :allowed-filters="allowedFilters"
  >
    <template v-slot:right>
      <div v-if="!selectedTransaction" class="empty-right-panel">
        <img :src="selectHintIcon" />
        Click on a transaction to select it
      </div>
      <div v-else class="selected-transaction">
        <div class="close" @click="selectedTransaction = null">
          <i class="fa-regular fa-xmark"></i>
          <span>Close</span>
        </div>
        
        <div class="header">
          <div class="left">
            <h2>{{ formatChip(selectedTransaction.chipVisualId) }}</h2>
          </div>
          <dropdown-button
            button-class="grey button"
            icon="fa-regular fa-ellipsis-vertical"
            alignment="RIGHT"
            @clicked="handleTransactionAction"
            :actions="transactionActions"
          />
        </div>
        
        <div class="transaction-details">
          <div class="labels">
            <div v-if="selectedTransaction.network" class="label">Network</div>
            <div v-if="selectedTransaction.refund" class="label">Refunded</div>
          </div>
          
          <h3>General Information</h3>
          <div class="properties-table">
            <div class="row">
              <span class="title">ID</span>
              <span class="value">{{ selectedTransaction.uid }}</span>
            </div>
            <div class="row">
              <span class="title">Amount</span>
              <span class="value">{{ formatMoney(selectedTransaction.signedAmount) }}</span>
            </div>
            <div class="row">
              <span class="title">Status</span>
              <span class="value">
                <span class="label" :class="getStatusClass(selectedTransaction.status)">
                  {{ formatStatus(selectedTransaction.status) }}
                </span>
              </span>
            </div>
          </div>
        </div>
      </div>
    </template>
  </filter-table-layout>
</template>
```

## Implementation Details

### Internal Component Structure

The FilterTableLayoutComponent is structured as follows:

1. **Header Section**: Contains title, subtitle, search bar, and action buttons
2. **Filter Bar**: Advanced filtering interface with saved filters
3. **Table Section**: Data display with sorting and column management
4. **Pagination**: Navigation controls when pagination is enabled
5. **Right Drawer**: Slide-out panel for detailed views

### Key Computed Properties and Methods

- `visibleColumns`: Filters table columns based on `displayed` property
- `hasAppliedFilters`: Determines if any filters are currently active
- `canNavigateNext/Previous`: Pagination navigation state
- `filtersCount`: Counts active filters for display

### State Management Approach

The component uses a reactive approach where:
- Filter state is managed through props and events
- Column preferences are persisted via events
- Pagination state is externally managed
- Drawer state is controlled by boolean prop

### Performance Considerations

- **Virtual Scrolling**: Not implemented - consider for large datasets
- **Debounced Search**: Search input is debounced to prevent excessive API calls
- **Lazy Loading**: Supports server-side pagination for large datasets
- **Column Virtualization**: Mobile columns are hidden via CSS classes

## Styling Guide

### Available CSS Classes and Their Purposes

| Class | Purpose |
|-------|---------|
| `.table-dimmer` | Loading overlay for table |
| `.table-no-data` | Empty state styling |
| `.selected` | Selected row highlighting |
| `.mobile-hidden` | Hide columns on mobile devices |
| `.label` | Status badge styling |
| `.properties-table` | Key-value pair display |
| `.empty-right-panel` | Empty drawer state |

### Theming and Customization Options

The component inherits styles from the `@groupk/vue3-interface-sdk` package. Customization is achieved through:

1. **CSS Custom Properties**: Override SDK variables
2. **Scoped Styles**: Component-specific styling
3. **Class Modifiers**: Conditional class application

```scss
// Custom theming example
.filter-table-layout {
  --primary-color: #007bff;
  --selected-row-bg: #e3f2fd;
  --mobile-breakpoint: 768px;
}

.selected {
  background-color: var(--selected-row-bg);
  border-left: 3px solid var(--primary-color);
}

.mobile-hidden {
  @media (max-width: 768px) {
    display: none;
  }
}
```

### Responsive Behavior Documentation

- **Desktop**: Full column display with drawer on right
- **Tablet**: Reduced columns, drawer overlay
- **Mobile**: Minimal columns, full-screen drawer

### Common Styling Patterns

```scss
// Loading state
.table-dimmer {
  position: relative;
  
  .dimmer {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

// Status labels
.label {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  
  &.green { background: #d4edda; color: #155724; }
  &.red { background: #f8d7da; color: #721c24; }
  &.grey { background: #e2e3e5; color: #383d41; }
}
```

## Integration Guide

### How to integrate with other components

```typescript
// Repository integration
@AutoWired(MyRepository) accessor repository!: MyRepository;
@AutoWired(TableColumnsRepository) accessor tableColumnsRepository!: TableColumnsRepository;

async searchItems(filters: TypedQuerySearch<AllowedFilters>) {
  this.loading = true;
  try {
    const result = await this.repository.search(filters);
    this.items = result.items;
    this.appliedFilters = filters;
  } finally {
    this.loading = false;
  }
}
```

### Data Flow Patterns

1. **Filter Changes**: Component → Event → Parent → API → Component
2. **Column Preferences**: Component → Event → Parent → Storage
3. **Pagination**: Component → Event → Parent → API → Component
4. **Selection**: Component → Parent State → Drawer Content

### Common Gotchas and Troubleshooting

#### Issue: Filters not working
**Cause**: `allowedFilters` configuration mismatch
**Solution**: Ensure filter names in `allowedFilters` match those used in `filterParameters`

#### Issue: Pagination not updating
**Cause**: Pagination object not reactive
**Solution**: Create new pagination object instead of mutating existing one

```typescript
// Wrong
this.pagination.currentPage++;

// Correct
this.pagination = {
  ...this.pagination,
  currentPage: this.pagination.currentPage + 1
};
```

#### Issue: Mobile columns still showing
**Cause**: CSS not properly applied
**Solution**: Ensure `mobileHidden` property is set and CSS media query is present

#### Issue: Drawer not opening
**Cause**: `drawerOpened` prop not reactive
**Solution**: Ensure the controlling variable is reactive

```typescript
// In component data
selectedItem: MyItem | null = null;

// In template
:drawer-opened="selectedItem !== null"
```

### Best Practices for Implementation

1. **Always handle loading states** in the table-data slot
2. **Implement empty states** for better UX
3. **Use consistent column naming** across similar pages
4. **Save column preferences** to improve user experience
5. **Implement proper error handling** for API failures
6. **Use TypeScript** for better type safety
7. **Follow mobile-first** responsive design principles

## TypeScript Support

### Interface Definitions

```typescript
interface ContentHeaderParameters {
  header: string;
  subtitle?: string;
  actions?: HeaderAction[];
  hideSearch?: boolean;
  searchPlaceholder?: string;
  tabs?: ContentHeaderParametersTab[];
}

interface TableColumn {
  title: string;
  name: string;
  displayed: boolean;
  mobileHidden: boolean;
}

interface TablePagination {
  totalResults: number;
  resultsPerPage: number;
  currentPage: number;
  estimateTotal: boolean;
}

interface FilterParameters {
  translation: string;
  type: 'DROPDOWN' | 'DATE_RANGE' | 'DECIMAL' | 'UNKNOWN';
  dropdownValues?: Array<{value: string, label: string}>;
  validation?: (value: unknown) => boolean;
  transformFunction?: (value: unknown) => unknown;
}

interface SavedFilter {
  name: string;
  filters: TypedQuerySearch<any>;
  creationDate: Date;
}
```

### Type Usage Examples

```typescript
// Strongly typed filter configuration
const allowedFilters: TypedQuerySearchConfig = {
  filters: {
    status: { type: 'string', operators: ['eq', 'in'] },
    amount: { type: 'number', operators: ['gte', 'lte'] },
    createdDate: { type: 'date', operators: ['gte', 'lte'] }
  },
  sorts: ['createdDate', 'amount', 'status']
};

// Type-safe filter parameters
const filterParameters: { [key: string]: FilterParameters } = {
  status: {
    translation: 'Status',
    type: 'DROPDOWN',
    dropdownValues: [
      { value: 'active', label: 'Active' },
      { value: 'inactive', label: 'Inactive' }
    ],
    validation: (value): value is string => typeof value === 'string'
  }
};

// Generic component with type constraints
export default class MyFilterPage<T extends { uid: string }> extends Vue {
  items: T[] = [];
  selectedItem: T | null = null;
  
  selectItem(item: T) {
    this.selectedItem = item;
  }
}
```

### Generic Type Parameters

The FilterTableLayoutComponent is generic and can be strongly typed:

```typescript
class FilterTableLayoutComponent<AllowedFilters extends TypedQuerySearchConfig> extends Vue {
  allowedFilters!: AllowedFilters;
  filters!: TypedQuerySearch<AllowedFilters>;
  appliedFilters!: TypedQuerySearch<AllowedFilters>;
}
```

### Type-Safe Integration Patterns

```typescript
// Repository with typed search
interface MyRepository {
  search(query: TypedQuerySearch<MySearchConfig>): Promise<SearchResult<MyItem>>;
}

// Component with full type safety
@Component({
  components: { 'filter-table-layout': FilterTableLayoutComponent }
})
export default class TypedPage extends Vue {
  private repository = this.container.get(MyRepository);
  
  items: MyItem[] = [];
  filters: TypedQuerySearch<MySearchConfig> = {};
  
  async handleFiltersChanged(newFilters: TypedQuerySearch<MySearchConfig>) {
    const result = await this.repository.search(newFilters);
    this.items = result.items;
  }
}
```

## Performance Optimization

### Recommended Patterns

1. **Pagination**: Always implement server-side pagination for large datasets
2. **Debouncing**: Use built-in search debouncing
3. **Virtual Scrolling**: Consider for very large datasets (not built-in)
4. **Lazy Loading**: Load drawer content only when needed
5. **Memoization**: Cache computed column configurations

### Memory Management

```typescript
// Proper cleanup in component lifecycle
beforeUnmount() {
  // Clean up any subscriptions or timers
  this.websocketSubscription?.unsubscribe();
}
```

## Migration Guide

### From Previous Versions

If upgrading from older versions:

1. **Check prop names**: Some props may have been renamed
2. **Update event handlers**: Event payload formats may have changed
3. **Review CSS classes**: Styling classes may have been updated
4. **Update TypeScript types**: Import latest type definitions

### Breaking Changes

- Filter configuration format may have changed
- Event names standardized (e.g., `filters-changed` vs `filtersChanged`)
- Some CSS classes renamed for consistency

This comprehensive documentation covers all aspects of the FilterTableLayoutComponent based on actual usage patterns found throughout the mastodon-fronts codebase. The component serves as the backbone for data management interfaces across all frontend applications in the system.