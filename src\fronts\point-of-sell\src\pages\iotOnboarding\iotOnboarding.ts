import {Component, Vue} from "vue-facing-decorator";
import {EstablishmentDeviceRepository} from "../../../../../shared/repositories/EstablishmentDeviceRepository";
import {AutoWired, ScopedUuid, UuidUtils, VisualScopedUuid} from "@groupk/horizon2-core";
import {
    EstablishmentDeviceAppCreateApiIn,
    EstablishmentDeviceKnownService,
    uuidScopeEstablishment,
    UuidScopeEstablishment, UuidScopeIot_deviceApp
} from "@groupk/mastodon-core";
import {Router} from "@groupk/horizon2-front";
import {IotUtils} from "../../class/IotUtils";
import {EstablishmentUrlBuilder} from "../../class/EstablishmentUrlBuilder";
import {translateResponseError} from "../../../../../shared/RepositoryExtensions";
import {IotEstablishmentDeviceV2HttpContract, EstablishmentDeviceV2ApiOut, EstablishmentDeviceAppV2ApiOut} from "@groupk/mastodon-core";
import {EstablishmentDevicePointOfSaleSettingsApi} from "@groupk/mastodon-core";

export const LS_DEVICE_APP_UID_KEY = 'iotv2-device-app-uid';

@Component({})
export default class iotOnboarding extends Vue {
    establishmentUid!: VisualScopedUuid<UuidScopeEstablishment>;

    establishmentDevices: EstablishmentDeviceV2ApiOut[] = [];
    establishmentDeviceApps: EstablishmentDeviceAppV2ApiOut[] = [];

    error: string|null = null;

    creating: boolean = false;
    loading: boolean = true;

    @AutoWired(EstablishmentDeviceRepository) accessor establishmentDeviceRepository!: EstablishmentDeviceRepository;
    @AutoWired(Router) accessor router!: Router;

    async mounted() {
        let regexMatch = this.router.lastRouteRegexMatches;

        const preLoaderDiv = document.getElementById('pre-loader');
        if(preLoaderDiv) preLoaderDiv.remove();

        if (regexMatch && regexMatch[1]) {
            this.establishmentUid = UuidUtils.scopedToVisual<UuidScopeEstablishment>(regexMatch[1] as ScopedUuid<UuidScopeEstablishment>, uuidScopeEstablishment);
        }

        const response = await this.establishmentDeviceRepository.callContract('list', {establishmentUid: this.establishmentUid}, undefined);
        if(!response.isSuccess()) {
            this.error = translateResponseError<typeof IotEstablishmentDeviceV2HttpContract, 'list'>(response, {});
            this.loading = false;
            return;
        }

        this.establishmentDevices = response.success().list;
        this.establishmentDeviceApps = response.success().appList;

        let devicePosApp: EstablishmentDeviceAppV2ApiOut|null = null;

        const storedDeviceAppUid = localStorage.getItem(LS_DEVICE_APP_UID_KEY);
        if(storedDeviceAppUid) {
            devicePosApp = this.requireEstablishmentDeviceAppWithUid(storedDeviceAppUid as ScopedUuid<UuidScopeIot_deviceApp>);
        } else {
            // Retro compatibility to update old POS localStorage
            const deviceInfos = await IotUtils.getCurrentDeviceSerialAndBrand();

            const iotDevice = IotUtils.findMatchingDevice(this.establishmentDevices, deviceInfos);
            devicePosApp = this.establishmentDeviceApps.find((app) => app.deviceUid === iotDevice?.uid && app.settings.type === EstablishmentDeviceKnownService.POINT_OF_SALE) ?? null;
            if(devicePosApp) localStorage.setItem(LS_DEVICE_APP_UID_KEY, devicePosApp.uid);
        }

        if(devicePosApp) window.location.href = EstablishmentUrlBuilder.buildUrl('/cash-statement')

        this.loading = false;
    }

    get posServiceEnabledDevices() {
        return this.establishmentDevices.filter((device) => {
            for(let establishmentDeviceApp of this.establishmentDeviceApps) {
                if(establishmentDeviceApp.deviceUid === device.uid && establishmentDeviceApp.settings.type === EstablishmentDeviceKnownService.POINT_OF_SALE) {
                    return true;
                }
            }
        });
    }

    async createEstablishmentDevice() {
        this.creating = true;

        const deviceInfos = await IotUtils.getCurrentDeviceSerialAndBrand();
        const response = await this.establishmentDeviceRepository.callContract(
            'create',
            {establishmentUid: this.establishmentUid},
            new EstablishmentDeviceAppCreateApiIn({
                name: '',
                hardwareId: deviceInfos.hardwareId,
                softwareId: deviceInfos.softwareId,
                brand: deviceInfos.brand,
                dumb: false,
                model: deviceInfos.model ?? '',
                comment: null,
                batteryLevel: deviceInfos.batteryLevel,
                ssidName: deviceInfos.ssidName,
                localIpList: deviceInfos.localIpList,
                settings: new EstablishmentDevicePointOfSaleSettingsApi({
                    type: EstablishmentDeviceKnownService.POINT_OF_SALE
                }),
                version: '1'
            })
        );

        if(response.isSuccess()) {
            const deviceApp = response.success().appList[0];
            localStorage.setItem(LS_DEVICE_APP_UID_KEY, deviceApp.uid);
            window.location.href = EstablishmentUrlBuilder.buildUrl('/cash-statement');
        } else {
            this.error = translateResponseError<typeof IotEstablishmentDeviceV2HttpContract, 'create'>(response, {})
            this.creating = false;
        }
    }

    requireEstablishmentDeviceAppWithUid(uid: ScopedUuid<UuidScopeIot_deviceApp>) {
        const deviceApp = this.establishmentDeviceApps.find((app) => app.uid === uid);
        if(!deviceApp) throw new Error('missing_establishment_device_app');
        return deviceApp;
    }
}