<script lang="ts" src="./CategoryFormComponent.ts">
</script>

<style lang="sass" scoped>
@use './CategoryFormComponent.scss' as *
</style>

<template>
    <form-modal-or-drawer
        :state="true"
        :title="editingCategory ? 'Modifier la catégorie' : 'Créer une catégorie'"
        :subtitle="'La catégorie pourra être associé a un terminal de vente'"
    >
        <template v-slot:content>
            <div class="input-group" v-if="!parentCategory && displayParentCategory">
                <label for="title"> Catégorie parent </label>
                <dropdown
                    placeholder="Catégorie parent"
                    :default-selected="category.parent"
                    :values="getParentDropdownValues()"
                    @update="category.parent = $event"
                ></dropdown>
            </div>

            <div class="input-group">
                <label for="title"> Nom </label>
                <div class="ui input">
                    <input v-model="category.name" type="text" placeholder="Nom" />
                </div>
            </div>

<!--            <div class="two-inputs">-->
<!--                <div class="input-group">-->
<!--                    <label for="title"> Couleur </label>-->
<!--                    <div class="ui input color">-->
<!--                        <input v-model="category.color" type="color" placeholder="Couleur" />-->
<!--                    </div>-->
<!--                </div>-->
<!--            </div>-->


            <div class="form-error" v-if="error">
                <i class="fa-solid fa-exclamation-circle"></i>
                <div class="details">
                    <span class="title"> Erreur </span>
                    <span class="description">{{ error }}</span>
                </div>
            </div>
        </template>

        <template v-slot:buttons>
            <button class="red button" :class="{loading: deleting, disabled: deleting}" @click="deleteCategory()" v-if="editingCategory">
                <i class="fa-regular fa-trash-alt"></i>
                Supprimer
            </button>
            <button class="button" :class="{loading: saving, disabled: saving}" @click="save()">
                <i class="fa-regular fa-check"></i>
                Sauvegarder
            </button>
        </template>
    </form-modal-or-drawer>
</template>