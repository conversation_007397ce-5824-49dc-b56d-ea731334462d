<script lang="ts" src="./SellComponent.ts">
</script>

<style lang="sass" scoped>
@import './SellComponent.scss'
</style>

<template>
    <div class="sell-component">
        <div class="left">
            <form @submit.prevent="submitSearch()" class="search-input" v-if="posProfile.displayProductsSearch">
                <input ref="searchInput" type="text" v-model="searchValue" placeholder="Rechercher un produit" @input="search()" />
                <i class="fa-regular fa-magnifying-glass"></i>
            </form>

            <div class="categories" v-if="posState.pointOfSale.categoriesUid.length">
                <div
                    class="category"
                    :data-selector="`${posState.requireCategoryWithUid(categoryUid).name} ${posState.requireCategoryWithUid(categoryUid).uid}`"
                    :data-testid="categoryUid"
                    :class="{active: selectedCategory && selectedCategory.uid === posState.requireCategoryWithUid(categoryUid).uid}"
                    v-for="categoryUid in posState.pointOfSale.categoriesUid" @click="selectCategory(posState.requireCategoryWithUid(categoryUid))"
                >
                    <span class="name"> {{ posState.requireCategoryWithUid(categoryUid).name }} </span>
                    <span class="items"> {{ posState.requireCategoryWithUid(categoryUid).productsConfig.length }} produits </span>
                </div>
            </div>

            <div class="too-much" v-if="products.length > 100">
                Cette catégorie contient plus de 100 produits, affinez votre recherche pour afficher des produits.
            </div>
            <div class="too-much" v-else-if="searchValue !== '' && products.length === 0">
                Aucun résultats pour la recherche "{{ searchValue }}".
            </div>
            <div class="too-much" v-else-if="products.length === 0">
                Cette catégorie est vide.
            </div>
            <div class="products" :class="[{images: posProfile.displayProductImage}, 'column-' + posState.columns]" v-else>
                <div class="product" :data-selector="`${product.lastRevision.name} ${product.uid}`" :data-testid="product.uid" v-for="product in products" @click="addProductToOrder(product)">
                    <div class="left-stock" v-if="favoriteStockProductUids.includes(product.uid)">
                        <span> reste </span>
                        {{ posState.virtualStockHandler.estimateRemainingStock(product.lastRevision) ?? '999+' }}
                    </div>
                    <div class="quantity" v-if="getProductQuantityInOrder(product) > 0"> {{ getProductQuantityInOrder(product) }} </div>
                    <div class="top" :class="{dark: isDark(product.uid)}" :style="getProductColorInSelectedCategory(product.uid) ? `background-color: ${getProductColorInSelectedCategory(product.uid)}`: ''">
                        <div v-if="posProfile.displayProductImage" class="image" :style="product.mainImageUpload ? `background-image: url(${getProductImageUrl(product)})` : ''"></div>
                        <span> {{ $filters.Length(product.lastRevision.name, 40) }} </span>
                    </div>
                    <div class="bottom">
                        {{ $filters.Money(posState.orderExecutorModel.getProductPrice(product, posState.toCountry).withTaxes) }}
                    </div>
                </div>
            </div>

            <div class="group-dimmer" v-if="addingProductWithGroups"></div>
            <div class="group-container" v-if="addingProductWithGroups">
                <group-option
                    :pos-state="posState"
                    :pos-profile="posProfile"
                    :product="addingProductWithGroups"
                    @add-to-order="addDescriptorToOrder($event)"
                    @cancel="addingProductWithGroups = null"
                ></group-option>
            </div>

            <product-required-metadata-list
                v-if="addingProductWithRequiredMetadata"
                :product="addingProductWithRequiredMetadata.product"
                :pos-state="posState"
                @done="addingProductWithRequiredMetadata.descriptor ? addDescriptorToOrder(addingProductWithRequiredMetadata.descriptor, $event) : addProductToOrder(addingProductWithRequiredMetadata.product, $event)"
                @cancel="addingProductWithRequiredMetadata = null"
            ></product-required-metadata-list>

            <mobile-bottom-cart
                v-if="getPurchasesQuantity > 0"
                :opened="animateMobileCart"
                :pos-state="posState"
                @click="toggleMobileCart()"
            ></mobile-bottom-cart>
        </div>
        <div class="right" :class="{opened: showMobileCart && posState.currentOrder}">
            <order-sidebar :pos-state="posState" :pos-profile="posProfile" @close="closeMobileCart()"></order-sidebar>
        </div>

<!--        <metadata-descriptor-form-->
<!--            :metadata-descriptor="posState.metadataDescriptors[0]"-->
<!--        ></metadata-descriptor-form>-->

<!--        <div class="floating-step-selector" v-if="posState.currentOrder && posState.orderGroupBy !== 'STEP'">-->
<!--            <span> Choisir une étape </span>-->
<!--            <div-->
<!--                class="step"-->
<!--                :class="{selected: round.name === posState.currentOrder.currentRound}"-->
<!--                v-for="round of posState.currentOrder.purchasesPerRound"-->
<!--                @click="posState.currentOrder.currentRound = round.name"-->
<!--            >-->
<!--                {{ round.name }}-->
<!--            </div>-->
<!--            <div class="step" @click="posState.currentOrder.addRound()">-->
<!--                <i class="fa-regular fa-plus"></i>-->
<!--            </div>-->
<!--        </div>-->
    </div>
</template>