import {Component, Prop, Vue} from "vue-facing-decorator";
import {PosState} from "../../model/PosState";
import {OptionBuilder} from "vue-facing-decorator/dist/optionBuilder";
import {AutoWired, UuidUtils, VisualScopedUuid} from "@groupk/horizon2-core";
import {CashStatementRepository} from "../../../../../shared/repositories/CashStatementRepository";
import {ErrorHandler} from "../../class/ErrorHandler";
import {LocalOrderRepository} from "../../repositories/LocalOrderRepository";
import {LocalOrder, LocalOrderTransfer} from "../../model/LocalOrder";
import {OrderRepository} from "../../../../../shared/repositories/OrderRepository";
import {LocalCashStatementRepository} from "../../repositories/LocalCashStatementRepository";
import {
	CashStatementApiOut,
	CashStatementSessionApiOut,
	OrderExecutorModel,
	UuidScopeEstablishmentAccount
} from "@groupk/mastodon-core";
import {OrderReceiptRender} from "../../class/OrderReceiptRender";
import {PrinterRepository} from "../../repositories/PrinterRepository";
import {LocalWorkClockRepository} from "../../repositories/LocalWorkClockRepository";
import {LocalWorkClock} from "../../model/LocalWorkClock";
import {LocalOrderTransferRepository} from "../../repositories/LocalOrderTransferRepository";
import {AuthStateModel} from "../../../../../shared/AuthStateModel";
import {MainConfig} from "../../../../../shared/MainConfig";
import {UuidScopePayment_method} from "@groupk/mastodon-core";
import {
	OrderApiOut,
	OrderPaymentApiOut,
	OrderPaymentStatus
} from "@groupk/mastodon-core";
import {Constants} from "@groupk/mastodon-core";
import {Toast} from "../ToastManagerComponent/ToastManagerComponent";
import PrintSettingsComponent from "../PrintSettingsComponent/PrintSettingsComponent.vue";

export interface OrderListResumePerPaymentMethod {
	[methodUid: VisualScopedUuid<UuidScopePayment_method>]: {
		pending: number,
		error: number,
		success: number,
		canceled: number
	};
}

export interface OrderListResume {
	totals: {
		leftToPay: number,
		withTaxes: number,
		withoutTaxes: number,
		canceled: number
	},
	perTaxes: {
		[taxesName: string]: {
			withTaxes: number,
			withoutTaxes: number,
			collected: number,
		}
	}
	perPaymentMethod: OrderListResumePerPaymentMethod,
	perSeller: {
		[sellerUid: VisualScopedUuid<UuidScopeEstablishmentAccount>]: {
			leftToPay: number,
			withTaxes: number,
			withoutTaxes: number,
			canceled: number,
			perPaymentMethod: OrderListResumePerPaymentMethod,
		}
	},
}

@Component({
	components: {
		'print-settings': PrintSettingsComponent
	}
})
export default class ClosingCashStatementComponent extends Vue {
	@Prop() posState!: PosState;

	orders: LocalOrder[] = [];
	workClocks: LocalWorkClock[] = [];
	pendingLocalOrderTransfers: LocalOrderTransfer[] = [];
	currentCashStatement!: CashStatementApiOut;
	currentCashStatementSession!: CashStatementSessionApiOut;

	state: 'ORDER_ERROR'|'ERROR'|'NETWORK'|'AUTH'|'UNPAID'|'UNSYNC'|'UNSYNC_WORK_CLOCK'|'TRANSFER_PENDING'|'READY'|'CLOSED'|'LOADING' = 'LOADING';
	error: string|null = null;
	showCanceledPayments: boolean = false;
	showPrintSettings: boolean = false;
	closing: boolean = false;
	loadingOrders: boolean = false;

	noPrinterToast: Toast = {
		title: 'Aucune imprimante sélectionnée',
		description: 'Veuillez sélectionner une imprimante dans les paramètres afin d’imprimer',
		color: 'red',
		closable: true,
		duration: 4000,
		action: [{
			name: 'Afficher',
			icon: 'fa-regular fa-receipt',
			callback: () => {}
		}, {
			name: 'Paramètres',
			icon: 'fa-regular fa-cog',
			callback: this.showPrintSettingsModal
		}]
	}

	@AutoWired(OrderExecutorModel) accessor orderExecutorModel !: OrderExecutorModel;
	@AutoWired(LocalOrderRepository) accessor localOrderRepository!: LocalOrderRepository;
	@AutoWired(LocalOrderTransferRepository) accessor localOrderTransferRepository!: LocalOrderTransferRepository;
	@AutoWired(LocalWorkClockRepository) accessor localWorkClockRepository!: LocalWorkClockRepository;
	@AutoWired(OrderRepository) accessor orderRepository!: OrderRepository;
	@AutoWired(CashStatementRepository) accessor cashStatementRepository!: CashStatementRepository;
	@AutoWired(LocalCashStatementRepository) accessor localCashStatementRepository!: LocalCashStatementRepository;
	@AutoWired(PrinterRepository) accessor printerRepository!: PrinterRepository;
	@AutoWired(ErrorHandler) accessor errorHandler!: ErrorHandler;
	@AutoWired(AuthStateModel) accessor authCenter!: AuthStateModel;
	@AutoWired(MainConfig) accessor config!: MainConfig;

	constructor(optionBuilder: OptionBuilder, vueInstance: any) {
		super(optionBuilder, vueInstance);
	}

	async mounted() {
		await this.computeState();
	}

	unmounted() {
		if(this.state === 'CLOSED') {
			// Disable clicks on all page since cash statement is close to avoid errors
			// since page reload can take a while. Useful when user close modal by clicking on dimmer.
			window.document.body.style.pointerEvents = 'none';
			this.reload();
		}
	}

	get ordersStatistics() {
		return this.compute(this.orders.map((localOrder) => localOrder.order));
	}

	async computeState() {
		if(!this.authCenter.getStateSync()) {
			this.state = 'AUTH';
			return;
		}

		this.orders = (await this.localOrderRepository.findAll()).filter((order) => !order.transferred && !order.order.transferredTo);
		this.workClocks = await this.localWorkClockRepository.findAll();
		this.pendingLocalOrderTransfers = await this.localOrderTransferRepository.findAllNotDone();

		const response = await this.cashStatementRepository.callContract('currentCashStatement', {establishmentUid: this.posState.establishmentUid}, undefined);
		if(response.isSuccess()) {
			const cashStatement = response.success();
			if(cashStatement === null) {
				this.errorHandler.logToSentry({
					title: 'No current cash statement but POS is opened',
					description: 'User accessed "closing cash statement component" but no cash statement is opened, pos should have been blocked'
				});
				return;
			} else {
				this.currentCashStatement = cashStatement;
			}
		} else if(response.isNetworkError()) {
			this.state = 'NETWORK';
		} else if(response.isServerError() || response.isError()) {
			this.state = 'ERROR';
		}

		const session = this.localCashStatementRepository.getSession();
		if(!session || !this.localCashStatementRepository.isSessionActive(session, this.currentCashStatement)) {
			this.errorHandler.logToSentry({
				title: 'No current cash statement session but POS is opened',
				description: 'User accessed "closing cash statement component" but no cash statement session is opened, pos should have been blocked'
			});
			return;
		} else {
			this.currentCashStatementSession = session;
		}

		if(this.isErrorOrder()) {
			this.state = 'ORDER_ERROR';
		} else if(this.isUnpaidOrder()) {
			this.state = 'UNPAID';
		} else if(this.isUnSynchronisedOrder() || this.currentCashStatement === null) {
			this.state = 'UNSYNC';
		} else {
			this.state = 'READY';
		}

		if(this.pendingLocalOrderTransfers.length > 0) {
			this.state = 'TRANSFER_PENDING';
		}

		if(this.isUnSynchronisedWorkClock()) {
			this.state = 'UNSYNC_WORK_CLOCK';
		}
	}

	async manualOrdersReload() {
		this.loadingOrders = true;
		const promises: Promise<any>[] = [
			new Promise((resolve) => setTimeout(() => resolve(null), 1500)),
			this.computeState()
		];
		await Promise.all(promises);
		this.loadingOrders = false;
	}

	isErrorOrder() {
		return this.orders.find((order) => order.error !== null) !== undefined;
	}

	isUnpaidOrder() {
		return this.orders.find((order) => {
			return this.posState.orderExecutorModel.getOrderTotals(order.order).leftToPay > 0;
		}) !== undefined;
	}

	isUnSynchronisedOrder() {
		return this.orders.find((order) => order.synced !== true) !== undefined;
	}

	isUnSynchronisedWorkClock() {
		return this.workClocks.find((workClock) => workClock.synced !== true) !== undefined;
	}

	goToUnpaidOrders() {
		this.posState.showCashStatementModal = false;
		this.posState.currentPage = 'orders';
	}

	async closeCashStatement() {
		if(!this.currentCashStatement) return

		this.closing = true;
		const result = await this.cashStatementRepository.callContract('closeSession', {
			establishmentUid: this.posState.establishmentUid,
			cashStatementUid: this.currentCashStatement.uid,
			cashStatementSessionUid: this.currentCashStatementSession.uid
		}, undefined);

		if(result.isSuccess()) {
			try {
				this.currentCashStatementSession.closingDatetime = result.success().session.closingDatetime;
				await this.localOrderRepository.clear();
				await this.localWorkClockRepository.clear();
			} catch(err) {
				this.errorHandler.logToSentry({
					title: 'Could not clear indexeddb',
					description: 'Indexeddb failed for unknown reason',
					error: err as Error
				});
			}

			this.state = 'CLOSED';
		} else if(result.isError()) {
			const error = result.error();
			if(error && 'error' in error) {
				this.error = error.error;
				this.errorHandler.logToSentry({
					title: 'Cannot close cash statement',
					description: error.error
				});
			} else {
				this.error = 'Empty error';
				this.errorHandler.logToSentry({
					title: 'Cannot close cash statement',
					description: 'Empty error'
				});
			}
		} else if(result.isServerError()) {
			this.error = 'Une erreur inconnue est survenue (500)'
			this.errorHandler.logToSentry({
				title: 'Cannot close cash statement',
				description: 'Server 500 error'
			});
		} else if(result.isNetworkError()) {
			this.error = 'Pas de connexion internet'
		}
		this.closing = false;
	}

	printCashStatement() {
		const orderReceiptRender = new OrderReceiptRender();
		const parts = orderReceiptRender.renderCashStatement(this.orders.map((localOrder) => localOrder.order), this.currentCashStatementSession);


		if(Array.isArray(this.noPrinterToast.action)) this.noPrinterToast.action[0].callback = async () => {
			this.posState.displayedReceipt = await this.printerRepository.printToBase64(parts);
		}

		this.printerRepository.printToSelectedPrinter(parts, this.noPrinterToast).catch(() => {});
	}

	reload() {
		// In .vue we cant directly access window object
		window.location.reload();
	}

	goToCas() {
		window.location.href = this.config.configuration.casFrontUrl + 'establishment/' + UuidUtils.visualToUuid(this.posState.establishmentUid) + '/login?platform=PointOfSell2';
	}

	compute(orders: OrderApiOut[]): OrderListResume {
		const resume: OrderListResume = {
			totals: {
				leftToPay: 0,
				withTaxes: 0,
				withoutTaxes: 0,
				canceled: 0
			},
			perTaxes: {},
			perPaymentMethod: {},
			perSeller: {},
		};

		for (const order of orders) {
			const orderPrice = this.posState.orderExecutorModel.getOrderTotals(order);
			resume.totals.withTaxes += orderPrice.purchases.withTaxesAfterDiscount;
			resume.totals.withoutTaxes += orderPrice.purchases.withoutTaxesAfterDiscount;
			resume.totals.leftToPay += orderPrice.leftToPay;

			for (let tax of orderPrice.purchases.taxDetails) {
				const key = tax.descriptor.name + ' ' + (tax.descriptor.percent / Constants.PERCENT_MULTIPLIER * 100 + '%');
				if (!resume.perTaxes[key]) {
					resume.perTaxes[key] = {
						withTaxes: 0,
						withoutTaxes: 0,
						collected: 0,
					};
				}

				resume.perTaxes[key]!.withTaxes += tax.amount + tax.baseAmount;
				resume.perTaxes[key]!.withoutTaxes += tax.baseAmount;
				resume.perTaxes[key]!.collected += tax.amount;
			}

			for (const payment of order.payments) {
				if(payment.amount < 0 && payment.status === OrderPaymentStatus.SUCCESS) {
					resume.totals.canceled += payment.amount;
				}
			}

			this.fillPerPaymentMethod(order.payments, resume.perPaymentMethod);

			if (order.sellerEstablishmentAccountUid !== null) {
				if (typeof resume.perSeller[order.sellerEstablishmentAccountUid] === 'undefined') {
					resume.perSeller[order.sellerEstablishmentAccountUid] = {
						leftToPay: 0,
						withTaxes: 0,
						withoutTaxes: 0,
						canceled: 0,
						perPaymentMethod: {},
					};
				}

				resume.perSeller[order.sellerEstablishmentAccountUid]!.leftToPay += orderPrice.leftToPay;
				resume.perSeller[order.sellerEstablishmentAccountUid]!.withoutTaxes += orderPrice.purchases.withoutTaxesAfterDiscount;
				resume.perSeller[order.sellerEstablishmentAccountUid]!.withTaxes += orderPrice.purchases.withTaxesAfterDiscount;

				for (const payment of order.payments) {
					if(payment.amount < 0 && payment.status === OrderPaymentStatus.SUCCESS) {
						resume.perSeller[order.sellerEstablishmentAccountUid]!.canceled += payment.amount;
					}
				}

				this.fillPerPaymentMethod(order.payments, resume.perSeller[order.sellerEstablishmentAccountUid]!.perPaymentMethod);
			}
		}

		return resume;
	}

	fillPerPaymentMethod(payments: OrderPaymentApiOut[], container: OrderListResumePerPaymentMethod) {
		for (const payment of payments) {
			if (typeof container[payment.method] === 'undefined') {
				container[payment.method] = {
					pending: 0,
					error: 0,
					success: 0,
					canceled: 0
				};
			}

			switch (payment.status) {
				case OrderPaymentStatus.ERROR:
					container[payment.method]!.error += payment.amount;
					break;
				case OrderPaymentStatus.SUCCESS:
					container[payment.method]!.success += payment.amount;
					if(payment.amount < 0) container[payment.method]!.canceled += payment.amount;
					break;
				case OrderPaymentStatus.PENDING:
					container[payment.method]!.pending += payment.amount;
					break;
			}
		}
	}

	showPrintSettingsModal() {
		this.showPrintSettings = true;
	}
}
