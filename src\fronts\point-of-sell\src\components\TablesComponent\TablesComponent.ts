import {Component, Prop, Ref, Vue} from "vue-facing-decorator";
import OrderSidebarComponent from "../OrderSidebarComponent/OrderSidebarComponent.vue";
import {PosState} from "../../model/PosState";
import {PosProfile} from "../../model/PosProfile";
import {LocalOrder, LocalOrderTransfer} from "../../model/LocalOrder";
import {
    AutoWired, GetInstance,
    QueryFilterGroupClause,
    QueryOperator, UuidUtils,
    VisualScopedUuid
} from "@groupk/horizon2-core";
import {AppBus} from "../../config/AppBus";
import {LocalOrderRepository} from "../../repositories/LocalOrderRepository";
import {
    DiningAreaApiOut,
    DiningTableApiOut,
    OrderApiOut,
    OrderTransferApiOut,
    UuidScopeProduct_diningTable,
} from "@groupk/mastodon-core";
import MobileBottomCartComponent from "../MobileBottomCartComponent/MobileBottomCartComponent.vue";
import {AuthStateModel} from "../../../../../shared/AuthStateModel";
import {MainConfig} from "../../../../../shared/MainConfig";
import {LocalOrderTransferRepository} from "../../repositories/LocalOrderTransferRepository";
import {OrderRepository} from "../../../../../shared/repositories/OrderRepository";
import {UuidScopeProductCashStatement} from "@groupk/mastodon-core";
import {ErrorHandler} from "../../class/ErrorHandler";
import {CashStatementRepository} from "../../../../../shared/repositories/CashStatementRepository";
import {ProductUtils} from "../../class/ProductUtils";
import RightModalComponent from "../RightModalComponent/RightModalComponent.vue";
import MobileOrderCardComponent from "../MobileOrderCardComponent/MobileOrderCardComponent.vue";
import TableComponent from "./TableComponent.vue";
import {UuidScopeProduct_diningArea} from "@groupk/mastodon-core";

@Component({
    components: {
        'order-sidebar': OrderSidebarComponent,
        'mobile-bottom-cart': MobileBottomCartComponent,
        'mobile-order-card': MobileOrderCardComponent,
        'right-modal': RightModalComponent,
        'table-view': TableComponent
    }
})
export default class TablesComponent extends Vue {
    @Prop() posState!: PosState;
    @Prop() posProfile!: PosProfile;

    orders: LocalOrder[] = [];
    serverOrders: OrderApiOut[] = [];
    orderTransfers: LocalOrderTransfer[] = [];
    serverOrderTransfers: OrderTransferApiOut[] = [];
    hasValidToken: boolean = true;

    page: number = 1;
    sort: 'DATE'|'NOT_PAYED'|'ALL' = 'DATE';

    animateMobileCart: boolean = false;
    showMobileCart: boolean = false;
    selectedArea: DiningAreaApiOut|null = null;
    showModalForTable: DiningTableApiOut|null = null

    tablesByHeadCountByArea: Record<VisualScopedUuid<UuidScopeProduct_diningArea>, Record<number, DiningTableApiOut[]>> = {};

    isOffline: boolean = false;
    loading: boolean = true;
    loadingCashStatement: boolean = false;
    showNetworkModal: boolean = false;

    mapOrders: Record<VisualScopedUuid<UuidScopeProduct_diningTable>, {
        localOrders: LocalOrder[],
        serverOrders: OrderApiOut[]
    }> = {};

    @Ref() tableOrders!: HTMLDivElement[];
    @Ref() contentContainer!: HTMLDivElement;

    @AutoWired(AppBus) accessor appBus!: AppBus;
    @AutoWired(AuthStateModel) accessor authCenter!: AuthStateModel;
    @AutoWired(OrderRepository) accessor orderRepository!: OrderRepository;
    @AutoWired(LocalOrderRepository) accessor localOrderRepository!: LocalOrderRepository;
    @AutoWired(CashStatementRepository) accessor cashStatementRepository!: CashStatementRepository;
    @AutoWired(LocalOrderTransferRepository) accessor localOrderTransferRepository!: LocalOrderTransferRepository;
    @AutoWired(MainConfig) accessor config!: MainConfig;

    async beforeMount() {
        this.appBus.on('orderSaved', this.updateOrder);
        this.appBus.on('orderTransferSaved', this.updateOrderTransfer);

        if(!this.authCenter.getStateSync()) this.hasValidToken = false;

        if(this.posState.diningAreas.length > 0) this.selectedArea = this.posState.diningAreas[0];

        await this.loadOrders();
    }

    unmounted() {
        this.appBus.off('orderSaved', this.updateOrder);
        this.appBus.off('orderTransferSaved', this.updateOrderTransfer);
    }

    updateOrder(localOrder: LocalOrder) {
        //TODO : quand on switch de table il faut update la map, pour virer la table d'avant et ajouter la table d'après.
        let table = localOrder.diningExtra?.tableUid;
        if(!table) return;

        // Secu mais normalement déjà fait à l'init
        if(!this.mapOrders[table]) this.mapOrders[table] = {
            localOrders: [],
            serverOrders: []
        };

        if(localOrder.transferred) {

            const index = this.mapOrders[table].localOrders.findIndex((order) => order.uid === localOrder.uid);
            if(index !== -1) {
                this.mapOrders[table].localOrders.splice(index, 1);
                // this.mapOrders[table].serverOrders.push(localOrder.order);
                if(!this.mapOrders[table].serverOrders.find(order => order.uid === localOrder.order.uid)) {
                    this.mapOrders[table].serverOrders.push(localOrder.order);
                }
            }
        }else{
            const index = this.mapOrders[table].localOrders.findIndex((order) => order.uid === localOrder.uid);
            if(index !== -1) {
                this.mapOrders[table].localOrders.splice(index, 1, localOrder);
            } else {
                // this.mapOrders[table].localOrders.push(localOrder);
                if(!this.mapOrders[table].localOrders.find(order => order.uid === localOrder.uid)) {
                    this.mapOrders[table].localOrders.push(localOrder);
                }
            }
        }


    }

    initTables(){
        this.mapOrders={};
        for(const area of this.posState.diningAreas) {
            this.tablesByHeadCountByArea[area.uid] = {};
            for(const table of area.tables) {
                if(!this.tablesByHeadCountByArea[area.uid][table.headCount]) this.tablesByHeadCountByArea[area.uid][table.headCount] = [];
                this.tablesByHeadCountByArea[area.uid][table.headCount].push(table);
                this.mapOrders[table.uid] = {
                    localOrders: [],
                    serverOrders: []
                };
            }
        }
    }

    updateOrderTransfer(localOrderTransfer: LocalOrderTransfer) {
        const index = this.orderTransfers.findIndex((ramLocalOrderTransfer) => ramLocalOrderTransfer.uid === localOrderTransfer.uid);
        if(index !== -1) {
            if(localOrderTransfer.canceled || localOrderTransfer.fetchedFromTargetDatetime !== null) {
                this.orderTransfers.splice(index, 1);
            } else {
                this.orderTransfers.splice(index, 1, localOrderTransfer);
            }
            this.$forceUpdate();
        } else {
            this.orderTransfers.push(localOrderTransfer);
        }
    }


    async loadOrders() {
        this.loading = true;

        this.initTables();

        this.isOffline = !navigator.onLine;

        this.orderTransfers = await this.localOrderTransferRepository.findAllNotDone();
        this.orders = await this.localOrderRepository.findAll();

        if(!this.isOffline) {
            const openedSessions = this.posState.currentCashStatement.sessions
                .filter((session) => session.closingDatetime === null)
                .map((session) => session.uid);

            const response = (await this.orderRepository.callContract('search', {establishmentUid: this.posState.establishmentUid}, {
                filter: {
                    group: QueryFilterGroupClause.OR,
                    filters: openedSessions.map((sessionUid) => {
                        return {
                            name: 'cashStatementSessionUid',
                            operator: QueryOperator.EQUAL,
                            value: sessionUid as VisualScopedUuid<UuidScopeProductCashStatement>
                        } as any
                    })
                }
            }));

            const transfersResponse = (await this.orderRepository.callContract('listPendingTransfers', {establishmentUid: this.posState.establishmentUid}, undefined));

            if(!response.isSuccess() || !transfersResponse.isSuccess()) {
                if(response.isNetworkError() || transfersResponse.isNetworkError()) {
                    this.isOffline = true;
                    this.loading = false;
                }
            }

            this.serverOrders = response.success();
            this.serverOrderTransfers = transfersResponse.success().list;

            await ProductUtils.getMissingProductRevisions(this.serverOrders);
        }

        for(let order of this.serverOrders){
            let table = order.diningExtra?.tableUid;
            if(!table) continue;

            if(!this.mapOrders[table]) this.mapOrders[table] = {
                localOrders: [],
                serverOrders: []
            };
            this.mapOrders[table].serverOrders.push(order);
        }

        for(let order of this.orders){
            let table = order.diningExtra?.tableUid;
            if(!table) continue;

            if(!this.mapOrders[table]) this.mapOrders[table] = {
                localOrders: [],
                serverOrders: []
            };
            this.mapOrders[table].localOrders.push(order);
        }

        setTimeout(() => {
            this.updateTableOrdersPosition();
        }, 0);

        this.loading = false;
    }

    async loadCurrentCashStatement() {
        this.loadingCashStatement = true;
        try {
            const cashStatement = (await this.cashStatementRepository.callContract('currentCashStatement', {establishmentUid: this.posState.establishmentUid }, undefined)).success();
            if(cashStatement === null) {
                window.location.href = '/establishment/' + UuidUtils.visualToScoped(this.posState.establishmentUid) + '/cash-statement';
                return;
            }
            this.posState.currentCashStatement = cashStatement;
        } catch(err) {
            GetInstance(ErrorHandler).logEverywhere({
                title: 'Erreur',
                description: 'Impossible de charger la liste des caisses',
                error: err as Error
            })
        }
        this.loadingCashStatement = false;
    }

    updateTableOrdersPosition() {
        if(!this.tableOrders) return;
        const containerRect = this.contentContainer.getBoundingClientRect();

        for(const ordersContainer of this.tableOrders) {
            const rect = ordersContainer.getBoundingClientRect();
            if((rect.x + rect.width) > (containerRect.x + containerRect.width)) {
                ordersContainer.classList.add('align-right');
            }
            if(rect.x < containerRect.x) {
                ordersContainer.classList.add('align-left');
            }
            if((rect.y + rect.height) > (containerRect.y + containerRect.height)) {
                ordersContainer.classList.add('align-bottom');
            }
        }
    }

    get getPurchasesQuantity(){
        if(!this.posState.currentOrder) return 0;
        let quantity = 0;
        for(let purchase of this.posState.currentOrder.order.purchases) {
            for(let item of purchase.items) {
                quantity += item.quantity;
            }
        }
        return quantity;
    }

    toggleMobileCart() {
        this.animateMobileCart = true;
        setTimeout(() => {
            this.showMobileCart = true;
        }, 150)
    }

    closeMobileCart() {
        this.showMobileCart = false;
        this.animateMobileCart = false;
    }
}
