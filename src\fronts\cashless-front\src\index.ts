import "@groupk/vue3-interface-sdk/dist/style.css";
import "./assets/css/global.scss";
import "@groupk/font-awesome-sdk/src/V6/css/pro-v4-font-face.min.css";
import "@groupk/font-awesome-sdk/src/V6/css/pro-v4-shims.min.css";
import "@groupk/font-awesome-sdk/src/V6/css/pro.min.css";
import {
	Router,
	RouterRoute,
	RouterStateConfigVersionModifier,
	VueRouteFactory,
	VueRouteOptions
} from "@groupk/horizon2-front";
import {GetInstance, setExecutionContext, SetInstance} from "@groupk/horizon2-core";
import {AnimatedRoute, AnimationBetweenRouteChange} from "../../../shared/routing/AnimatedRouter";
import {GlobalFilters} from "../../../shared/filters/GlobalFilters";
import {Configuration, MainConfig} from "../../../shared/MainConfig";
import {AuthStateModel} from "../../../shared/AuthStateModel";
import {VueSpecificAppRouteFactory} from "./SpecificAppVueJsRoute";
import {ErrorHandler} from "../../point-of-sell/src/class/ErrorHandler";
import assetsPlugin from "../../../shared/routing/assetsPlugin";

let mainConfig: MainConfig = GetInstance(MainConfig);

mainConfig.init().then(async function(configFromFile: Configuration) {
	if(configFromFile.develop) {
		setExecutionContext({
			debug: true
		});
	}

	const authStateModel = new AuthStateModel(configFromFile.mastodonApiEndpoint, false);
	try {
		await authStateModel.getState(true);
	} catch(err){}

	SetInstance(AuthStateModel, authStateModel);

	startApp(configFromFile);
});

function startApp(config: Configuration) {
	let router = new Router<AnimatedRoute | RouterRoute>({
		prefix: "/",
		registerGlobalInterceptor: true,
		container: "mainRouterContainer",
		oldContainer: "oldRouterContainer",
		disableRouteTriggerIfIdentical: true,
	});
	if (window.innerWidth < 900) {
		new AnimationBetweenRouteChange(router);
	}

	router.addHook(new RouterStateConfigVersionModifier(13));

	const sentryConfig = {
		dsn: 'https://<EMAIL>/****************'
	};
	const errorHandler = new ErrorHandler(sentryConfig);
	SetInstance(ErrorHandler, errorHandler);

	let uuidRegex = /([0-9a-f]{8}\-[0-9a-f]{4}\-[0-9a-f]{4}\-[0-9a-f]{4}\-[0-9a-f]{12})/;
	const vueRouteOptions: Partial<VueRouteOptions> = {
		filters: GlobalFilters,
		hookAppCreated(app) {
			app.use(assetsPlugin);
		},
	};

	router.addRoute({regex: new RegExp(/establishment\//.source + uuidRegex.source + /\/auth/.source), loader: () => import('./pages/auth/auth.vue').then((vue) => VueRouteFactory(vue.default, vueRouteOptions))});
	router.addRoute({regex: new RegExp(/establishment\//.source + uuidRegex.source + /\/$/.source), loader: () => import('./pages/index/index.vue').then((vue) => VueSpecificAppRouteFactory(vue.default, vueRouteOptions))});
	router.addRoute({regex: new RegExp(/establishment\//.source + uuidRegex.source + /\/no-account-index/.source), loader: () => import('./pages/noAccountIndex/noAccountIndex.vue').then((vue) => VueSpecificAppRouteFactory(vue.default, vueRouteOptions))});
	router.addRoute({regex: new RegExp(/establishment\//.source + uuidRegex.source + /\/refill/.source), animationIn: "slideFromLeft", loader: () => import('./pages/refill/refill.vue').then((vue) => VueSpecificAppRouteFactory(vue.default, vueRouteOptions))});
	router.addRoute({regex: new RegExp(/establishment\//.source + uuidRegex.source + /\/add-chip/.source), animationIn: "slideFromLeft", loader: () => import('./pages/addChip/addChip.vue').then((vue) => VueSpecificAppRouteFactory(vue.default, vueRouteOptions))});
	router.addRoute({regex: new RegExp(/establishment\//.source + uuidRegex.source + /\/account/.source), animationIn: "slideFromLeft", loader: () => import('./pages/account/account.vue').then((vue) => VueSpecificAppRouteFactory(vue.default, vueRouteOptions))});
	router.addRoute({regex: new RegExp(/establishment\//.source + uuidRegex.source + /\/payment-return\//.source + uuidRegex.source), animationIn: "slideFromLeft", loader: () => import('./pages/paymentReturn/paymentReturn.vue').then((vue) => VueSpecificAppRouteFactory(vue.default, vueRouteOptions))});
	router.addRoute({regex: new RegExp(/chip-scan/.source), animationIn: "slideFromLeft", loader: () => import('./pages/chipScanIndex/chipScanIndex.vue').then((vue) => VueRouteFactory(vue.default, vueRouteOptions))});
	router.addRoute({regex: new RegExp(/.*/.source), loader: () => import('./pages/casRedirect/casRedirect.vue').then((vue) => VueRouteFactory(vue.default, vueRouteOptions))});

	SetInstance(Router, router);

	if(config.develop) {
		setExecutionContext({
			debug: true
		});
	}
	router.updateCurrentPageFromCurrentLocation();
}
