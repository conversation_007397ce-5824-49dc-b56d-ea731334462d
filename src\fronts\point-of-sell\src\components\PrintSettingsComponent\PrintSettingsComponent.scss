.print-settings-component {
    display: flex;
    flex-direction: column;
    gap: 10px;

    .add-network-printer {
        display: flex;
        justify-content: center;
    }

    .two-inputs {
        margin-bottom: 10px;

        .boolean-input {
            box-sizing: border-box;
            border: none;
            background: #F2F4F7;
            color: black;

            .title {
                font-size: 15px;
                font-weight: 600;
            }

            .description {
                font-size: 14px;
            }

            &.selected {
                background: var(--primary-color);
                color: var(--primary-text-color);

                .title {
                    font-weight: 700;
                }
            }
        }
    }


    .no-device {
        padding: 15px 25px;
        text-align: center;
    }

    .separator {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 20px;
        font-size: 15px;
        font-weight: 600;
        background: #F2F4F7;
        width: 100%;
        margin: 0 -20px;

        .tertiary.button {
            padding: 0;
            font-weight: normal;
        }
    }

    .printer-device {
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 10px;
        padding: 15px 25px;
        border-radius: 8px;
        background: #F2F4F7;

        .left {
            display: flex;
            align-items: center;
            gap: 10px
        }

        &.selected {
            color: white;
            background: #06F;
        }

        i {
            &.red {
                color: #E12727;
            }

            &.green {
                color: #43BF57;
            }
        }
    }

    .printer-settings-modal {
        display: flex;
        flex-direction: column;
        gap: 20px;

        h3 {
            margin: 0;
        }
    }

    .tcp-test-connection {
        display: flex;
        justify-content: flex-end;
    }

    .vertical-buttons {
        display: flex;
        gap: 10px;
        justify-content: flex-end;
        flex-wrap: wrap;

        @media (max-width: 900px) {
            display: flex;
            flex-direction: column;
            gap: 10px;
            flex-grow: 2;

            .button {
                height: 30px;
            }
        }
    }
}