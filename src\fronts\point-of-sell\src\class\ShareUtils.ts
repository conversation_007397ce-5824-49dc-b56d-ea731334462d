import {GetInstance} from "@groupk/horizon2-core";
import {AppBus} from "../config/AppBus";
import {SystemInfoNative_request_share_compatibleType} from "@groupk/native-bridge";

export default class ShareUtils {
	static base64ToBlob(base64: string, mimeType: string) {
		const byteString = atob(base64.split(',')[1]); // Decode Base64
		const arrayBuffer = new ArrayBuffer(byteString.length);
		const intArray = new Uint8Array(arrayBuffer);

		// Populate the Uint8Array with byte data
		for (let i = 0; i < byteString.length; i++) {
			intArray[i] = byteString.charCodeAt(i);
		}

		// Create a Blob from the ArrayBuffer
		return new Blob([intArray], { type: mimeType });
	}

	static async share(base64: string, mime: string) {
		const blob = this.base64ToBlob(base64, mime);
		const file = new File([blob], 'recu.png', { type: 'image/png' });

		try {
			await navigator.share(<SystemInfoNative_request_share_compatibleType> {
				title: 'Votre reçu',
				text: '',
				files: [file],
			})
		} catch(err){
			console.log(err);
			const appBus = GetInstance(AppBus);
			appBus.emit('displayToast', {
				title: 'Envoi impossible',
				description: 'Cet appareil ne supporte pas le partage',
				color: 'red',
				duration: 3000,
				closable: true
			});
		}
	}
}