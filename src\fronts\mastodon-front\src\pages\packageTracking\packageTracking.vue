<script lang="ts" src="./packageTracking.ts" />

<style lang="sass" scoped>
@import './packageTracking.scss'
</style>

<template>
    <div id="package-tracking-page" class="page">
        <filter-table-layout
            :header-parameters="headerParameters"
            :allowed-filters="allowedFilters"
            :table-columns="tableColumns"
            :filters="filters"
            :drawer-opened="selectedPackage !== null"
            @changed-column-preferences="saveColumnPreferences($event)"
        >
            <template v-slot:table-data>
                <tr class="table-dimmer" :class="{relative: packages.length === 0}" v-if="loading">
                    <td colspan="100%">
                        <div class="dimmer">
                            <div class="loader-container">
                                <div class="loader"></div>
                            </div>
                        </div>
                    </td>
                </tr>
                <tr class="table-no-data" v-else-if="packages.length === 0">
                    <td colspan="100%">Aucun colis</td>
                </tr>
                <tr v-for="postalPackage in packages" :class="{selected: selectedPackage && postalPackage.uid === postalPackage.uid}">
                    <td :class="{'mobile-hidden': column.mobileHidden, grow: column.name === 'name'}" v-for="column in tableColumns.filter((column) => column.displayed)" @click="selectedPackage = postalPackage">
                        <template v-if="column.name === 'trackingId'"> {{ postalPackage.trackingId ?? '-' }} </template>
                        <template v-if="column.name === 'provider'"> {{ postalPackage.transportProvider }} </template>
                        <template v-if="column.name === 'comment'"> {{ postalPackage.comment || '-' }} </template>
                    </td>
                </tr>
            </template>
            <template v-slot:right>
                <div v-if="!selectedPackage" class="empty-right-panel">
                    <img src="../../assets/img/select-hint.svg" />
                    Cliquer sur un colis pour <br/> le sélectionner
                </div>
                <div v-else class="selected-product">
                    <div class="close" @click="selectedPackage = null">
                        <i class="fa-regular fa-xmark"></i>
                        <span> Fermer </span>
                    </div>

                    <div class="header">
                        <div class="left">
                            <h2> {{ selectedPackage.transportProvider }} </h2>
                        </div>
                    </div>

                </div>
            </template>
        </filter-table-layout>

        <postal-package-form
            v-if="showPackageFormModal"
            @close="showPackageFormModal = false"
        ></postal-package-form>
    </div>
</template>