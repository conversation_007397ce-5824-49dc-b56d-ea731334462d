import {Component, Vue} from "vue-facing-decorator";
import {
    ContentHeaderComponent,
    ContentHeaderParameters, DropdownButtonAction, DropdownButtonComponent,
    LayoutContentWithRightPanelComponent
} from "@groupk/vue3-interface-sdk";
import {AutoWired, sinkNever, UuidUtils, ScopedUuid, hydrate} from "@groupk/horizon2-core";
import SidebarStateListener from "../../../../../shared/utils/SidebarStateListener";
import {
    CashlessHttpChipRefundContract,
    RefundApiOut,
    RefundStatus,
    UuidScopeCashless_refund,
    RefundBatchApiIn, CashStatementSessionApiOut
} from "@groupk/mastodon-core";
import {RefundRepository} from "../../../../../shared/repositories/RefundRepository";
import {AppBus} from "../../config/AppBus";
import ToastManagerComponent from "../../components/ToastManagerComponent/ToastManagerComponent.vue";
import {AppState} from "../../../../../shared/AppState";
import {translateResponseError} from "../../../../../shared/RepositoryExtensions";
import {Router} from "@groupk/horizon2-front";
import {EstablishmentUrlBuilder} from "../../../../../shared/utils/EstablishmentUrlBuilder";
import SEPARefundFormComponent from "../../components/SEPARefundFormComponent/SEPARefundFormComponent.vue";
import {RefundPlannedError} from "@groupk/mastodon-core";

@Component({
    components: {
        'content-header': ContentHeaderComponent,
        'layout': LayoutContentWithRightPanelComponent,
        'toast-manager': ToastManagerComponent,
        'dropdown-button': DropdownButtonComponent,
        'sepa-refund-form':SEPARefundFormComponent
    }
})
export default class refundRequests extends Vue {
    headerParameters: ContentHeaderParameters = {
        header: 'Demandes de remboursement',
        subtitle: 'Liste des demandes de remboursement des supports Cashless',
        actions: [{
            type: 'SIMPLE_ACTION',
            name: 'Demande de remboursement',
            icon: 'fa-regular fa-circle-plus',
            callback: this.newSepaRefund
        }, {
            type: 'SIMPLE_ACTION',
            name: 'Voir les remboursement effectués',
            icon: 'fa-regular fa-arrow-up-right-from-square',
            callback: this.goToRefundBatches
        }],
        hideSearch: true,
        searchPlaceholder: ''
    }

    showSEPARefundModal: boolean = false;
    refunds: RefundApiOut[] = [];
    selectedRefund: RefundApiOut | null = null;
    errorMode: boolean = false;

    preprocessing: boolean = false;
    loading: boolean = true;

    @AutoWired(RefundRepository) accessor refundRepository!: RefundRepository;
    @AutoWired(AppState) accessor appState!: AppState;
    @AutoWired(SidebarStateListener) accessor sidebarStateListener!: SidebarStateListener;
    @AutoWired(AppBus) accessor appBus!: AppBus;
    @AutoWired(Router) accessor router!: Router;

    beforeMount() {
        this.sidebarStateListener.setMinimizedSidebar(false);
        this.sidebarStateListener.setHiddenSidebar(false);
    }

    async mounted() {
        this.refunds = (await this.refundRepository.callContract('list', {establishmentUid: this.appState.requireUrlEstablishmentUid()}, undefined)).success();
        this.loading = false;
    }


    toggleSelectedRefund(refund: RefundApiOut) {
        if (this.selectedRefund && this.selectedRefund.uid === refund.uid) {
            this.selectedRefund = null;
        } else {
            this.selectedRefund = refund;
        }
    }

    newSepaRefund() {
        this.showSEPARefundModal = true;
    }

    getDuplicatedRefunds() {
        const pendingRefunds = this.refunds.filter(refund => refund.status === RefundStatus.PENDING);
        const chipIdCounts = pendingRefunds.reduce((acc, refund) => {
            acc[refund.chipVisualId] = (acc[refund.chipVisualId] || 0) + 1;
            return acc;
        }, {} as Record<string, number>);

        return pendingRefunds.filter(refund =>
            chipIdCounts[refund.chipVisualId] > 1
        ).sort((a, b) => a.chipVisualId.localeCompare(b.chipVisualId));
    }

    async dropdownClicked(refund: RefundApiOut, action: DropdownButtonAction) {
        if (action.id === 'cancel') {
            const response = await this.refundRepository.callContract('cancel', {
                establishmentUid: this.appState.requireUrlEstablishmentUid(),
                refundUid: refund.uid
            }, undefined);

            if (response.isSuccess()) {
                refund.status = RefundStatus.ERROR;
                this.appBus.emit('emit-toast', {
                    title: 'Remboursement annulé',
                    description: 'Le remboursement à bien été annulé',
                    duration: 3000,
                    type: 'SUCCESS',
                    closable: true
                });
            } else {
                const error = translateResponseError<typeof CashlessHttpChipRefundContract, 'cancel'>(response, {});
                this.appBus.emit('emit-toast', {
                    title: 'Echec de l\'annulation',
                    description: 'Le remboursement n\'a pas pu être annulé (' + error + ')',
                    duration: 3000,
                    type: 'SUCCESS',
                    closable: true
                });
            }
        }
    }

    formatIban(iban: string): string {
        if (!iban) return '';
        const first4 = iban.slice(0, 4);
        const last4 = iban.slice(-4);
        const middleLength = 4;
        return `${first4}${'.'.repeat(middleLength)}${last4}`;
    }

    translateRefundStatus(refundStatus: RefundStatus|RefundPlannedError|null) {
        if(refundStatus === null) return '-';

        if (refundStatus === RefundStatus.PENDING) {
            return 'En attente';
        } else if (refundStatus === RefundStatus.SUCCESS) {
            return 'Remboursé';
        } else if (refundStatus === RefundStatus.ERROR) {
            return 'Erreur/Annulé';
        }else if (refundStatus === RefundPlannedError.PENDING_TRANSACTIONS) {
            return 'Ignorée';
        } else {
            sinkNever(refundStatus)
        }
    }

    goToRefundBatches() {
        this.router.changePage(EstablishmentUrlBuilder.buildUrl('/refund-batches'));
    }
}