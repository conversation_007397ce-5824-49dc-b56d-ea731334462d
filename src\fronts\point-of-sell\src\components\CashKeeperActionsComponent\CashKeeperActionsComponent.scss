.empty-cash-keeper-component {
    .modal {
        h3 {
            margin: 0;
        }

        h4 {
            margin: 0;
            font-weight: 500;
        }

        display: flex;
        flex-direction: column;
        gap: 20px;
    }

    .cash-keepers {
        display: grid;
        grid-template-columns: 1fr 1fr 1fr;
        grid-gap: 10px;

        button {
            height: 100%;
            box-sizing: border-box;
            font-weight: bold;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;
            padding: 20px;

            &:not(.active) {
                background: var(--secondary-hover-color);
                color: black;
            }

            .small {
                font-weight: normal;
            }
        }
    }

    .denominations, .amounts {
        display: grid;
        grid-template-columns: repeat(6, 1fr);
        text-align: center;
    }

    .amounts {
        font-weight: 600;
        text-align: center;
    }

    .actions {
        display: grid;
        grid-template-columns: 1fr 1fr 1fr;
        grid-gap: 10px;

        button {
            height: 100%;
            box-sizing: border-box;
            font-weight: bold;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            gap: 15px;
            background: var(--secondary-hover-color);

            i {
                font-size: 22px;
            }
        }
    }

    .denominations-total {
        font-size: 16px;
        font-weight: bold;
        text-align: center;
    }

    .total {
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 28px;
        font-weight: bold;
    }
}