import {Component, Prop, Vue} from "vue-facing-decorator";
import {CleaveDirective} from "../../directives/CleaveDirective";
import {DropdownComponent, FormModalOrDrawerComponent, InputDateTimeComponent} from "@groupk/vue3-interface-sdk";
import UploadInputComponent from "../UploadInputComponent/UploadInputComponent";
import {AppApiOut_type, AppType} from "@groupk/mastodon-core";
import {sinkNever} from "@groupk/horizon2-core";

@Component({
    directives: {
        cleave: CleaveDirective,
    },
    components: {
        'date-time-input': InputDateTimeComponent,
        'form-modal-or-drawer': FormModalOrDrawerComponent,
        'upload-input': UploadInputComponent,
        'dropdown': DropdownComponent
    },
    emits: ['close', 'edit', 'create']
})
export default class AppIntegrationListComponent extends Vue {
    @Prop({required: true}) apps!: AppApiOut_type[];

    opened: boolean = false;
    loading: boolean = false;

    getImageForAppType(type: AppType) {
        if(type === AppType.STRIPE) return 'stripe';
        else if(type === AppType.RESERVIT) return 'reservit';
        else if(type === AppType.CUSTOM) return 'customApp';
        else sinkNever(type);
    }

    async mounted() {
        setTimeout(() => this.opened = true, 0);
    }

    selectExisting(app: AppApiOut_type) {
        this.$emit('edit', app);
    }

    createNew() {
        this.$emit('create');
    }

    close() {
        this.opened = false;
        setTimeout(() => this.$emit('close'), 300);
    }
}