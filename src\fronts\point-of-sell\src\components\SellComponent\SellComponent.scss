.sell-component {
    display: flex;
    height: 100%;
    width: 100%;
    user-select: none;

    .left {
        display: flex;
        flex-direction: column;
        gap: 25px;
        flex-grow: 2;
        height: 100%;
        padding: 25px 20px;
        box-sizing: border-box;
        overflow: auto;

        &::-webkit-scrollbar {
            display: none;
        }

        .search-input {
            position: relative;

            input {
                padding: 15px 20px;
                color: #000;
                font-family: Montserrat, sans-serif;
                font-size: 18px;
                border: none;
            }

            i {
                top: 50%;
                transform: translateY(-50%);
                position: absolute;
                right: 20px;
            }
        }

        .categories {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            grid-gap: 10px;

            .category {
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                gap: 4px;
                padding: 10px 15px;
                border-radius: 8px;
                background: white;
                cursor: pointer;
                text-align: center;
                word-break: break-word;

                &:hover {
                    background: rgba(0, 102, 255, 0.2);
                }

                .name {
                    font-family: Montserrat, sans-serif;
                    font-size: 18px;
                    font-weight: 600;
                }

                .items {
                    font-family: Montserrat, sans-serif;
                    font-size: 14px;
                }

                &.active {
                    background: var(--primary-color);
                    color: var(--primary-text-color);
                }
            }
        }

        .too-much {
            font-style: italic;
            text-align: center;
            margin-top: 20px;
        }

        .products {
            display: grid;
            grid-template-columns: repeat(6, 1fr);
            grid-gap: 15px;

            &.images {
                grid-template-columns: repeat(5, 1fr);
            }

            &.column-4 {
                grid-template-columns: repeat(4, 1fr);
            }

            &.column-3 {
                grid-template-columns: repeat(3, 1fr);
            }


            .product {
                display: flex;
                flex-direction: column;
                cursor: pointer;
                position: relative;

                &:hover {
                    .top {
                        background: #e1e1e1;
                    }
                }

                .left-stock {
                    position: absolute;
                    display: flex;
                    gap: 4px;
                    align-items: flex-end;
                    justify-content: center;
                    top: 0;
                    left: 0;
                    padding: 5px 5px;
                    border-bottom-right-radius: 4px;
                    background: var(--primary-color);
                    color: var(--primary-text-color);
                    font-size: 13px;
                    font-weight: bold;

                    span {
                        font-size: 10px;
                    }
                }

                .quantity {
                    position: absolute;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    top: 5px;
                    right: 5px;
                    height: 20px;
                    width: 20px;
                    border-radius: 50%;
                    background: var(--primary-color);
                    color: var(--primary-text-color);
                    font-size: 13px;
                    font-weight: bold;
                }

                .top {
                    flex-grow: 2;
                    padding: 10px 5px;
                    border-radius: 4px 4px 0 0;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    text-align: center;
                    background: white;
                    font-weight: 600;
                    font-size: 14px;
                    min-height: 60px;
                    word-break: break-word;

                    &.dark {
                        color: white;
                    }
                }

                .bottom {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    background: #E8E8E8;
                    padding: 10px;
                    border-radius: 0 0 4px 4px;

                }
            }

            &.images {
                .product {
                    .top {
                        flex-direction: column;
                        min-height: 124px;
                        padding: 0;

                        .image {
                            flex-grow: 2;
                            width: 100%;
                            background-size: contain;
                            background-repeat: no-repeat;
                            background-position: center;
                            border-radius: 4px 4px 0 0;
                        }

                        span {
                            display: block;
                            padding: 10px;
                        }
                    }
                }
            }
        }

        .group-dimmer {
            position: fixed;
            inset: 0;
            background: rgba(0, 0, 0, 0.7);
            z-index: 998;
        }

        .group-container {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 999;
        }
    }

    .right {
        flex-shrink: 0;
        width: var(--sidebar-width);
        background: white;
        position: relative;
    }

    .mobile-bottom-cart-component {
        display: none;
    }

    @media (max-width: 900px) {
        .left {
            height: 100%;
            box-sizing: border-box;
            position: relative;
            padding:  0 0 160px 0;

            .search-input {
                padding: 10px 20px 0 20px;

                i {
                    top: 38px;
                    right: 40px;
                }
            }

            .products {
                display: grid;
                grid-template-columns: repeat(3, 1fr);
                grid-gap: 15px;
                padding: 0 20px;

                &.images {
                    grid-template-columns: repeat(3, 1fr);
                }
            }

            .categories {
                display: flex;
                overflow: auto;
                flex-shrink: 0;
                padding: 0 20px;
                white-space: nowrap;
            }
        }

        .right {
            position: absolute;
            inset: 0;
            width: auto;
            z-index: 550;
            display: none;

            &.opened {
                display: initial;
            }
        }

        .group-container {
            position: fixed;
            inset: 20px !important;
            transform: none !important;
        }
    }

    .floating-step-selector {
        display: flex;
        align-items: center;
        gap: 10px;
        position: absolute;
        bottom: 20px;
        left: calc((100% - var(--sidebar-width)) * 0.5);
        transform: translateX(-50%);
        padding: 10px;
        background: white;
        border-radius: 12px;

        .step {
            padding: 15px 12px;
            background: #f2f4f7;
            border-radius: 8px;

            &.selected {
                background: var(--primary-color);
                color: var(--primary-text-color);
            }
        }
    }
}