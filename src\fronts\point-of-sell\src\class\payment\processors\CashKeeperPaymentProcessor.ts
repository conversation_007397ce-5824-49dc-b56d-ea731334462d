import {CashkeeperConnection, CashkeeperState, CashkeeperWarning} from "@groupk/cashkeeper-protocol";
import {AutoWired, BufferUtils, EnumType, EventEmitter} from "@groupk/horizon2-core";
import {OrderPaymentApiOut} from "@groupk/mastodon-core";
import {SocketNative, TcpSocketWrapper} from "@groupk/native-bridge";
import {PaymentProtocolConfig, PhysicalPaymentProtocol} from "./PhysicalPaymentProtocol";
import {PosProfileRepository} from "../../../../../../shared/repositories/PosProfileRepository";
import {ErrorHandler} from "../../ErrorHandler";
import {AppBus} from "../../../config/AppBus";
import {PaymentMethodSettingsCashKeeper} from "@groupk/mastodon-core";

function GetKeyWithValue<Enum extends EnumType>(pEnum: Enum, value : unknown) : keyof Enum{
	const keys = Object.keys(pEnum);
	for(const k of keys){
		if(pEnum[k] === value){
			return k;
		}
	}
	throw new Error('key_not_found_with_value('+value+')');
}

export class CashKeeperPaymentProcessor extends EventEmitter<{
	'totalPaidChanged': number,
	'totalRefundChanged': number,
	'done': boolean
}> implements PhysicalPaymentProtocol {
	private pendingPayment: OrderPaymentApiOut|null = null;

	private tcpWrapper: TcpSocketWrapper|null = null;
	private cashKeeperConnection: CashkeeperConnection|null = null;

	private settings: PaymentMethodSettingsCashKeeper;

	paid: number = 0;
	refund: number = 0;

	config: PaymentProtocolConfig = {
		displayLivePaidAmount: true,
		autoCloseOnSuccess: false,
		cancelable: true
	};

	@AutoWired(PosProfileRepository) accessor posProfileRepository!: PosProfileRepository
	@AutoWired(SocketNative) accessor nativeSocket!: SocketNative
	@AutoWired(ErrorHandler) accessor errorHandler!: ErrorHandler
	@AutoWired(AppBus) accessor appBus!: AppBus

	constructor(settings: PaymentMethodSettingsCashKeeper) {
		super();
		this.settings = settings;
	}

	async connect() {
		this.tcpWrapper = await this.nativeSocket.tcpConnectWrapper(this.settings.address, this.settings.masterPort);

		return new Promise((resolve, reject) => {
			if(!this.tcpWrapper) return reject();
			this.tcpWrapper.on('connected', ()=> {
				if(!this.tcpWrapper) return reject();

				this.cashKeeperConnection = new CashkeeperConnection((data) => {
					if(!this.tcpWrapper) throw new Error('connection_have_been_closed');

					let payload: number[] = [];
					for (let i = 0; i < data.length; i++) {
						payload.push(data.charCodeAt(i));
					}
					this.tcpWrapper.send(payload);
				}, {officeMode: false, securitySeed: this.settings.securitySeed});

				this.tcpWrapper.on('data', (bytes) => {
					if(!this.cashKeeperConnection) return;

					let data: string = String.fromCharCode(...(typeof bytes === 'string' ? BufferUtils.hex2Buffer(bytes) : bytes));
					this.cashKeeperConnection.pushData(data);
				});

				this.tcpWrapper.on('closed', ()=>{
					console.warn('CONNECTION CLOSED')
				});

				this.initiateCashKeeperListeners();

				this.cashKeeperConnection.connect();

				resolve(undefined);
			});
		});

	}

	initiateCashKeeperListeners() {
		if(!this.cashKeeperConnection) return;

		this.cashKeeperConnection.on('warning', (warning) => {
			if(this.pendingPayment && warning === CashkeeperWarning.NotEnoughChange) {
				console.log('not_enough_change');
				if(!this.cashKeeperConnection) throw new Error('cash_keeper_connection_have_been_closed');
				this.cashKeeperConnection.disable(true);
			}
		});

		this.cashKeeperConnection.on('stateChange', (state) => {
			console.log('STATE:', GetKeyWithValue(CashkeeperState, state))
		});

		this.cashKeeperConnection.on('amountIn', (state)=>{
			console.log('AMOUNT IN:',state.totalPending);
			this.paid = state.totalPending;
			this.emit('totalPaidChanged', state.totalPending);
		});

		this.cashKeeperConnection.on('amountOut', (state) => {
			console.warn('AMOUNT OUT:', state.payedSoFar);
			this.refund = state.payedSoFar;
			this.emit('totalRefundChanged', state.payedSoFar);
		});

		this.cashKeeperConnection.on('disabled', async (event)=>{
			if('errorCode' in event){
				this.emit('done', false);
			} else {
				if(this.pendingPayment && (this.paid - this.refund === this.pendingPayment.amount)) {
					this.emit('done', true);
				} else {
					this.emit('done', false);
				}
			}

			await this.disconnect();
		});

		this.cashKeeperConnection.on('processInterrupted', async (event)=>{
			console.warn('PROCESS INTERRUPTED', event);
			if(!this.cashKeeperConnection) throw new Error('cash_keeper_connection_have_been_closed');
			if(event.pendingValue !== 0 || event.targetValue !== 0) await this.cashKeeperConnection.disable(true);
		});
	}

	async initiatePayment(payment: OrderPaymentApiOut) {
		await this.connect();
		console.log('connected');
		if(!this.cashKeeperConnection) throw new Error('cash_keeper_connection_have_been_closed');
		this.cashKeeperConnection.on('ready', () => {
			console.log(payment.amount);
			this.cashKeeperConnection?.totalize(payment.amount);
			this.pendingPayment = payment;
		});
	}

	async cancel() {
		if(this.cashKeeperConnection) {
			await this.cashKeeperConnection.totalize(0);
		}
	}

	async disconnect() {
		if(this.cashKeeperConnection) {
			await this.cashKeeperConnection.disconnect();
			this.cashKeeperConnection = null;
		}
		if(this.tcpWrapper) {
			await this.tcpWrapper.close();
			this.tcpWrapper = null;
		}
		this.pendingPayment = null;
	}
}