import {Component, Vue} from "vue-facing-decorator";
import {AutoWired, UuidUtils} from "@groupk/horizon2-core";
import {Router} from "@groupk/horizon2-front";
import {
	CashlessHttpCustomerContract,
	CashlessHttpOnlineFundingContract,
	PublicOnlineFundingApiIn,
	PublicOnlineFundingApiOut
} from "@groupk/mastodon-core";
import {PublicOnlineFundingCustomerApiIn} from "@groupk/mastodon-core";
import {CashlessCustomerRepository} from "../../../../../shared/repositories/CashlessCustomerRepository";
import SpecificState from "../../SpecificState";
import {AppState} from "../../../../../shared/AppState";
import MangoPayCheckoutPaymentComponent
	from "../../../../../shared/components/PaymentProcessorComponents/MangoPayCheckoutPaymentComponent/MangoPayCheckoutPaymentComponent.vue";
import {PaymentProtocol} from "@groupk/mastodon-core";
import StripeCheckoutPaymentComponent
	from "../../../../../shared/components/PaymentProcessorComponents/StripeCheckoutPaymentComponent/StripeCheckoutPaymentComponent.vue";
import TestCheckoutPaymentComponent
	from "../../../../../shared/components/PaymentProcessorComponents/TestCheckoutPaymentComponent/TestCheckoutPaymentComponent.vue";
import {FormModalOrDrawerComponent} from "@groupk/vue3-interface-sdk";
import {translateResponseError} from "../../../../../shared/RepositoryExtensions";

@Component({
	components: {
		'mangopay-checkout-payment-component': MangoPayCheckoutPaymentComponent,
		'stripe-checkout-payment-component': StripeCheckoutPaymentComponent,
		'test-checkout-payment-component': TestCheckoutPaymentComponent,
		'form-modal-or-drawer': FormModalOrDrawerComponent
	}
})
export default class refill extends Vue {
	amount: string = '0';
	cursorPosition: number = 0;
	smallDevice: boolean = false;

	missingCustomerData = {
		firstname: '',
		lastname: '',
		email: '',
	}

	showMissingDataModal: boolean = false;

	error: string|null = null;
	onlineFunding: PublicOnlineFundingApiOut|null = null;
	PaymentProtocol = PaymentProtocol;

	@AutoWired(CashlessCustomerRepository) accessor cashlessCustomerRepository!: CashlessCustomerRepository;
	@AutoWired(Router) accessor router!: Router;
	@AutoWired(AppState) accessor appState!: AppState;
	@AutoWired(SpecificState) accessor specificState!: SpecificState;

	beforeMount(){
		if(window.innerHeight < 680) this.smallDevice = true;
	}

	clickKey(value: string) {
		if(value === 'COMMA') {
			if(!this.amount.includes(',')) {
				this.amount += ',00';
				this.cursorPosition++;
			}
		} else if(value === 'BACK') {
			if(this.cursorPosition === 0) return;
			this.cursorPosition--;

			if(this.amount.charAt(this.cursorPosition) === ',') {
				this.amount = this.amount.substring(0, this.cursorPosition);
			} else if(this.cursorPosition === 0) {
				this.amount = '0';
			} else {
				if(this.isCursorAfterComma()) {
					this.amount = this.setCharAt(this.amount, this.cursorPosition, '0');
				} else {
					this.amount = this.removeByIndex(this.amount, this.cursorPosition);
				}
			}
		} else {
			if(this.getBeforeCommaLength() > 5) return;
			if(this.cursorPosition === 0 && value === '0') return;
			if(this.isCursorAfterComma() && this.cursorPosition === this.getBeforeCommaLength() + 3) return;
			this.amount = this.setCharAt(this.amount, this.cursorPosition, value);
			this.cursorPosition++;
		}
	}

	formatNumber(){
		const [beforeComma, after] = this.amount.split(',');
		return beforeComma.replace(/\B(?=(\d{3})+(?!\d))/g, " ") + (after !== undefined ? (',' + after) : '');
	}

	getBeforeCommaLength() {
		return this.amount.split(',')[0].length;
	}

	isCursorAfterComma() {
		for(let i = 0; i < this.amount.length || i < this.cursorPosition; i++) {
			if(this.amount.charAt(i) === ',') return true;
		}
		return false;
	}

	setCharAt(str: string, index: number, chr: string) {
		if(index > str.length - 1) return str + chr;
		return str.substring(0, index) + chr + str.substring(index + 1);
	}

	removeByIndex(str: string, index: number) {
		return str.slice(0, index) + str.slice(index + 1);
	}

	async validateAmount() {
		if(!this.specificState.currentWallet) return;
		console.log(this.specificState);
		if(
			(
				this.specificState.customer === null &&
				!this.missingCustomerData.firstname &&
				!this.missingCustomerData.lastname &&
				!this.missingCustomerData.email
			) ||
			(this.specificState.customer && this.specificState.customer.firstname === null && !this.missingCustomerData.firstname) ||
			(this.specificState.customer && this.specificState.customer.lastname === null && !this.missingCustomerData.lastname) ||
			(this.specificState.customer && this.specificState.customer.email === null && !this.missingCustomerData.email)
		) {
			this.showMissingDataModal = true;
			return;
		}

		this.showMissingDataModal = false;

		const amountWithComma = this.amount.includes(',') ? this.amount : this.amount + ',00';

		const response = await this.cashlessCustomerRepository.callContract('createFunding', {establishmentUid: this.appState.requireUrlEstablishmentUid()}, new PublicOnlineFundingApiIn({
			fiatAmount: parseInt(amountWithComma.replaceAll(',', '')),
			walletUid: this.specificState.currentWallet.uid,
			paymentReturnUrl: window.location.origin + '/establishment/' + UuidUtils.visualToScoped(this.appState.requireUrlEstablishmentUid()) + '/payment-return/{{funding}}',
			customer: new PublicOnlineFundingCustomerApiIn({
				firstname: this.specificState.customer?.firstname ?? this.missingCustomerData.firstname,
				lastname: this.specificState.customer?.lastname ?? this.missingCustomerData.lastname,
				email: this.specificState.customer?.email ?? this.missingCustomerData.email
			})
		}));
		
		if(response.isSuccess()) {
			this.onlineFunding = response.success();
		} else {
			this.error = translateResponseError<typeof CashlessHttpCustomerContract, 'createFunding'>(response, {
				invalid_chip_id: 'Numéro de support invalide',
				invalid_customer: undefined,
				chip_unknown: 'Support de paiement inconnu',
				online_funding_amount_too_low: 'Le montant minimum de rechargement n\'est pas atteint',
				online_funding_amount_too_high: 'Le montant maximum de rechargement est dépassé',
				online_funding_not_activated: 'Le service de rechargement en ligne n\'est pas disponible',
				wallet_funding_refused: 'Le rechargement de ce support n\'est pas autorisé',
				invalid_data: undefined,
			});
		}
	}

	paymentSuccess() {
		this.router.previous();
	}
}