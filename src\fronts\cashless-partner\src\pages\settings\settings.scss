#settings-page {
    height: 100%;

    .main-content {
        padding: 20px var(--desktop-padding);

        @media (max-width: 900px) {
            padding: 20px var(--mobile-padding);
            margin-top: 75px;
        }


        .title {
            font-size: 16px;
            font-weight: 500;
        }

        .table-scroll-wrapper {
            margin-top: 10px;

            .name {
                width: 100%;
            }
        }

        .table-action {
            button {
                margin: auto;
            }
        }


        .setting-group {
            display: flex;
            flex-direction: column;
            gap: 10px;
            //border-bottom: 1px solid var(--border-color);

            > .title {
                padding: 20px 0 0 0;
                font-weight: 500;
            }

            .content {
                padding: 0 0 20px 0;
            }

            @media (max-width: 900px) {
                .title {
                    padding: 20px 0 0 0;
                }

                .content {
                    padding: 0 0 20px 0;
                }
            }

            .validate {
                display: flex;
                align-items: center;
            }
        }
    }

    .selected-establishment-account {
        padding: 40px;
        display: flex;
        flex-direction: column;
        gap: 20px;

        @media screen and (max-width: 900px) {
            padding: 20px;
        }

        .content {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        @media (max-width: 900px) {
            padding: 20px;
        }
    }

    .roles {
        display: flex;
        flex-direction: column;
        gap: 10px;

        .role {
            display: flex;
            flex-direction: column;
            gap: 8px;
            padding: 20px;
            border-radius: 8px;
            background: var(--secondary-hover-color);
            cursor: pointer;
            border: 1px solid transparent;

            &.selected {
                background: var(--primary-hover-color);
                border: 1px solid var(--primary-color);
            }

            .icon {
                display: flex;
                align-items: center;
                justify-content: center;
                height: 42px;
                width: 42px;
                background: white;
                border-radius: 6px;
                margin-bottom: 10px;
            }

            .top {
                display: flex;
                justify-content: space-between;

                i {
                    font-size: 18px;
                    color: var(--primary-color)
                }
            }

            .title {
                font-size: 16px;
                font-weight: bold;
            }

            .description {
                font-size: 14px;
            }

            .bottom {
                display: flex;
                justify-content: flex-end;
                margin-top: 5px;

                .tertiary:hover {
                    background: white;
                }
            }
        }
    }

    .permissions {
        display: grid;
        grid-template-columns: 1fr 1fr;
        grid-gap: 10px 20px;

        .permission {
            display: flex;
            gap: 10px;

            &.not-ok {
                opacity: 0.6;
            }

            i {
                margin-top: 3px;
            }

            .fa-check {
                color: var(--primary-color);
            }
        }
    }

    .save-button{
        padding: 12px;
    }

    .empty-row {
        text-align: center;
    }
}