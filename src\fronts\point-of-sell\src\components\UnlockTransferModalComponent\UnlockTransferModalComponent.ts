import {Component, Prop, Vue} from "vue-facing-decorator";
import KeypadComponent from "../KeypadComponent/KeypadComponent.vue";
import {KeypadKey} from "../KeypadComponent/KeypadComponent";
import {LocalOrderTransfer} from "../../model/LocalOrder";
import {AutoWired} from "@groupk/horizon2-core";
import {LocalOrderTransferRepository} from "../../repositories/LocalOrderTransferRepository";
import {LocalOrderRepository} from "../../repositories/LocalOrderRepository";

@Component({
    components: {
        'keypad': KeypadComponent
    },
    emits: ['close']
})
export default class UnlockTransferModalComponent extends Vue {
    @Prop({required: true}) localOrderTransfer!: LocalOrderTransfer;

    unlockCode: string = '';
    invalidCode: boolean = false;

    @AutoWired(LocalOrderTransferRepository) accessor localOrderTransferRepository!: LocalOrderTransferRepository;
    @AutoWired(LocalOrderRepository) accessor localOrderRepository!: LocalOrderRepository;

    clickedSecurityCodeKey(key: number) {
        if(this.invalidCode && this.unlockCode.length === 4) {
            this.unlockCode = '';
        }
        this.invalidCode = false;
        if(key === KeypadKey.BACKSPACE) {
            this.unlockCode = this.unlockCode.slice(0, -1);
        } else {
            if(this.unlockCode.length < 4) {
                this.unlockCode += key;
            }
        }
    }

    async tryUnlockTransfer() {
        if(this.unlockCode === this.localOrderTransfer.unlockCodeOk) {
            const localOrder = await this.localOrderRepository.findOne(this.localOrderTransfer.order.uid);
            if(!localOrder) return;
            localOrder.transferred = true;

            await this.localOrderRepository.save(localOrder);

            this.localOrderTransfer.fetchedFromTargetDatetime = new Date().toISOString();
            this.localOrderTransfer.confirmedFromTargetDatetime = new Date().toISOString();
            await this.localOrderTransferRepository.save(this.localOrderTransfer);

            this.$emit('unlocked', this.localOrderTransfer);
            this.close();
        } else if(this.unlockCode === this.localOrderTransfer.unlockCodeKo) {
            this.localOrderTransfer.canceled = true;
            await this.localOrderTransferRepository.save(this.localOrderTransfer);

            this.$emit('unlocked', this.localOrderTransfer);
            this.close();
        } else {
            this.invalidCode = true;
        }
    }

    close() {
        this.$emit('close');
    }
}