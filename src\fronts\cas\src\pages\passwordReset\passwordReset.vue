<script lang="ts" src="./passwordReset.ts" />

<style lang="sass" scoped>
@use './passwordReset.scss' as *
</style>

<template>
    <div id="password-reset-page">
        <div class="left">
            <form class="form step" v-if="true">
                <template v-if="!resetApiOut">
                    <div class="title-group">
                        <img v-if="currentPlatform" class="logo" :src="currentPlatform.url + 'img/logo-long.svg'" >

                        <h3> Réinitialisation de mot de passe </h3>
                        <span v-if="currentPlatform"> Renseignez votre email pour modifier votre mot de passe </span>
                    </div>

                    <div class="input-group">
                        <label for="title"> Email </label>
                        <div class="ui input">
                            <input v-model="email" autocomplete="email" spellcheck="false" type="email" placeholder="Email de votre compte" />
                        </div>
                    </div>

                    <div class="form-error" v-if="error">
                        <i class="fa-regular fa-circle-exclamation"></i>
                        <div class="details">
                            {{ error }}
                        </div>
                    </div>

                    <div class="buttons">
                        <button type="button" class="ui grey button" :class="{loading: sending, disabled: sending}" @click="sendResetEmail()">
                            <i class="fa-regular fa-envelope" style="margin-top: 3px"></i>
                            Envoyer un mail de réinitialisation
                        </button>
                    </div>
                </template>

                <template v-else-if="!emailCode.validated">
                    <div class="title-group">
                        <img v-if="currentPlatform" class="logo" :src="currentPlatform.url + 'img/logo-long.svg'" >

                        <h3> Un email vous a été envoyé </h3>
                        <span v-if="currentPlatform"> Renseignez le code reçu par email pour modifier votre mot de passe </span>
                    </div>

                    <div class="input-group">
                        <label for="title"> Code reçu par email </label>

                        <div class="six-inputs">
                            <input class="code-input" maxlength="1" v-model="emailCode.code[0]" @input="codeInputChanged(0)" @keydown="codeInputKeydown(0, $event)" />
                            <input class="code-input" maxlength="1" v-model="emailCode.code[1]" @input="codeInputChanged(1)" @keydown="codeInputKeydown(1, $event)" />
                            <input class="code-input" maxlength="1" v-model="emailCode.code[2]" @input="codeInputChanged(2)" @keydown="codeInputKeydown(2, $event)" />
                            <div class="minus-separator">
                                -
                            </div>
                            <input class="code-input" maxlength="1" v-model="emailCode.code[3]" @input="codeInputChanged(3)" @keydown="codeInputKeydown(3, $event)" />
                            <input class="code-input" maxlength="1" v-model="emailCode.code[4]" @input="codeInputChanged(4)" @keydown="codeInputKeydown(4, $event)" />
                            <input class="code-input" maxlength="1" v-model="emailCode.code[5]" @input="codeInputChanged(5)" @keydown="codeInputKeydown(5, $event)" />
                        </div>
                    </div>

                    <div class="form-error" v-if="error">
                        <i class="fa-regular fa-circle-exclamation"></i>
                        <div class="details">
                            {{ error }}
                        </div>
                    </div>

                    <div class="buttons">
                        <button type="button" class="ui grey button" :class="{loading: validatingCode, disabled: validatingCode}" @click="verifyResetCode()">
                            Continuer
                        </button>
                    </div>
                </template>

                <template v-else-if="!changedPassword">
                    <div class="title-group">
                        <img v-if="currentPlatform" class="logo" :src="currentPlatform.url + 'img/logo-long.svg'" >

                        <h3> Modifiez votre mote de passe </h3>
                        <span v-if="currentPlatform"> Rentrez votre nouveau mot de passe </span>
                    </div>

                    <div class="input-group">
                        <label for="title"> Nouveau mot de passe </label>
                        <input v-model="newPassword.new" type="password" placeholder="Mot de passe" />
                    </div>

                    <div class="input-group">
                        <label for="title"> Valider le mot de passe </label>
                        <input v-model="newPassword.validation" type="password" placeholder="Valider le mot de passe" />
                    </div>

                    <div class="form-error" v-if="error">
                        <i class="fa-regular fa-circle-exclamation"></i>
                        <div class="details">
                            {{ error }}
                        </div>
                    </div>

                    <div class="buttons">
                        <button type="button" class="ui grey button" :class="{loading: savingPassword, disabled: savingPassword}" @click="modifyPassword()">
                            <i class="fa-regular fa-check"></i>
                            Valider
                        </button>
                    </div>
                </template>

                <template v-else>
                    <div class="title-group">
                        <img v-if="currentPlatform" class="logo" :src="currentPlatform.url + 'img/logo-long.svg'" >

                        <h3> Mot de passe changé </h3>
                        <span v-if="currentPlatform"> Le mot de passe de votre compte a bien été modifié </span>
                    </div>

                    <div class="buttons">
                        <a :href="getLoginUrl()" class="ui grey button" :class="{loading: savingPassword, disabled: savingPassword}">
                            Aller à la connexion
                            <i class="fa-regular fa-arrow-right"></i>
                        </a>
                    </div>
                </template>
            </form>

            <form v-else class="form step">
                <div class="input-group">
                    <label for="title"> Code reçu par email </label>
                    <div class="ui input">
                        <input spellcheck="false" type="text" placeholder="Code reçu par email" />
                    </div>
                </div>
            </form>
        </div>

        <div class="littl-preview" v-if="currentPlatform && currentPlatform.id === 'Littl'">
            <div class="littl-container">
                <div class="head">
                    <span class="title"> Contrôlez vos QR codes à distance </span>
                    <span class="subtitle"> Modifiez le lien de votre QR code à tout moment, même une fois imprimé </span>
                </div>
                <video autoplay muted loop>
                    <source src="/littl.mp4" type="video/webm" />
                </video>
            </div>
        </div>
        <div v-else class="right" :style="currentPlatform ? `background-image: url(${currentPlatform.url + 'img/login.png'})` : ''"></div>
    </div>
</template>