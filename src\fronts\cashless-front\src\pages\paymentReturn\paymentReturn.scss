#payment-return-page {
    height: 100%;
    box-sizing: border-box;
    overflow: auto;
    background: white;

    .centered-content {
        display: flex;
        flex-direction: column;
        gap: 20px;
        padding: 80px 12px;
        margin: auto;
        max-width: 500px;
        width: 100%;
        box-sizing: border-box;
    }

    .order-confirmation {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 20px;

        > i {
            color: var(--primary-color);
            font-size: 80px;
        }

        .head {
            display: flex;
            flex-direction: column;
            gap: 4px;

            .title {
                font-size: 22px;
                font-weight: bold;
                text-align: center;
            }

            .infos {
                font-size: 16px;
                font-weight: 500;
                color: #494949;
                text-align: center;

                a {
                    color: black;
                }
            }
        }

        .recap {
            padding: 20px;
            background: var(--secondary-hover-color);
            box-sizing: border-box;
            border-radius: 8px;
            display: grid;
            grid-template-columns: 1fr 1fr;
            grid-gap: 10px;
            width: 100%;
            margin-top: 10px;

            .data {
                display: flex;
                flex-direction: column;
                align-items: center;

                .title {
                    font-size: 15px;
                    font-weight: 500;
                    color: #494949;
                }

                .value {
                    color: black;
                    font-weight: 600;
                    font-size: 18px;
                }
            }
        }
    }

}