@use './variables';

.button {
  all: unset;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: flex-start;
  padding: 12px 25px;
  gap: 10px;
  background: variables.$theseum-blue;
  border-radius: 8px;
  position: relative;
  cursor: pointer;

  font-style: normal;
  font-size: 15px;
  line-height: 15px;
  color: white;

  &.disabled {
    opacity: 0.5;
    pointer-events: none;
  }

  &.tertiary {
    background: none;
    color: black;
  }

  &:active {

  }

  &.black {
    background: black;
    color: white;
  }

  &.white {
    background: white;
    color: black;
    border: 1px solid black;
  }

  &.red {
    background: variables.$red;
  }

  &.yellow {
    background: #F9D939;
    color: black;
  }

  &.loading::before {
    position: absolute;
    content: '';
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 18px;
    height: 18px;
    border-radius: 500rem;
    border: 0.2em solid rgba(0, 0, 0, 0.15);
  }

  &.loading::after {
    position: absolute;
    content: '';
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 18px;
    height: 18px;
    border-radius: 500rem;
    -webkit-animation: loader 0.6s infinite linear;
    animation: loader 0.6s infinite linear;
    border: 0.2em solid currentColor;
    color: #fff;
    -webkit-box-shadow: 0 0 0 1px transparent;
    box-shadow: 0 0 0 1px transparent;
    border-left-color: transparent;
    border-right-color: transparent;
    border-bottom-color: transparent;
  }

  &.loading {
    color: transparent;
  }
}

@-webkit-keyframes loader {
  100% {
    -webkit-transform: translate(-50%, -50%) rotate(360deg);
    transform: translate(-50%, -50%) rotate(360deg);
  }
}
@keyframes loader {
  100% {
    -webkit-transform: translate(-50%, -50%) rotate(360deg);
    transform: translate(-50%, -50%) rotate(360deg);
  }
}
