.profile-categories-component {
    display: flex;
    flex-direction: column;
    gap: 20px;
    padding: 20px 40px;
    min-height: 100%;

    @media (max-width: 900px) {
        padding: 20px;
    }

    .empty {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 20px;
        padding: 40px;
    }

    .categories-group {
        display: flex;
        align-items: center;
        gap: 10px;

        @media (max-width: 900px) {
            flex-direction: column;
            align-items: stretch;
        }

        .categories {
            display: flex;
            gap: 10px;
            overflow: auto;
            padding-bottom: 4px;

            .category {
                flex-shrink: 0;
                display: flex;
                align-items: center;
                gap: 10px;
                padding: 10px 20px 10px 6px;
                border-radius: 8px;
                background: white;
                cursor: pointer;
                border: 1px solid #c2c2c2;

                .left {
                    display: flex;
                    flex-direction: column;
                    justify-content: space-between;
                    flex-grow: 2;

                    .name {
                        font-family: Montserrat, sans-serif;
                        font-size: 18px;
                        font-weight: 600;
                    }

                    .items {
                        font-family: Montserrat, sans-serif;
                        font-size: 14px;
                    }
                }

                .handle, .edit {
                    padding: 10px;
                    border-radius: 6px;
                    flex-shrink: 0;

                    &:hover {
                        background: var(--secondary-hover-color);
                        cursor: grab;
                    }

                    i {
                        font-size: 15px;
                    }
                }

                .edit {
                    &:hover {
                        cursor: pointer;
                    }
                }


                &.active {
                    background: #06F;
                    color: white;
                    border: none;

                    .handle, .edit {
                        &:hover {
                            background: #2f82fc !important;
                        }
                    }
                }
            }
        }

        .new-category {
            flex-shrink: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            padding: 10px 20px 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            height: 100%;
            box-sizing: border-box;

            &:hover {
                background: var(--secondary-hover-color);
            }

            i {
                font-size: 22px;
            }
        }

        .categories-group-right {
            flex-shrink: 0;
            flex-grow: 2;
            display: flex;
            justify-content: flex-end;
            align-items: center;
            gap: 10px;
            height: 100%;
            box-sizing: border-box;
        }
    }

    .products {
        background: #F2F4F7;
        border-radius: 8px;
        border: 1px solid #E2E2E2;
        overflow: auto;

        .category-products {
            padding: 20px;
            display: grid;
            grid-template-columns: repeat(6, 1fr);
            grid-gap: 15px;
            box-sizing: border-box;

            .color-drop.hovered {
                border: 2px solid blue;
                border-radius: 4px;
            }

            @media (max-width: 1500px) {
                grid-template-columns: repeat(4, 1fr);
            }

            @media (max-width: 1200px) {
                grid-template-columns: repeat(3, 1fr);
            }

            .product {
                display: flex;
                flex-direction: column;
                cursor: pointer;
                height: 120px;

                @media (max-width: 900px) {
                    border: 1px solid var(--border-color);
                    border-radius: 4px;
                    overflow: hidden;
                }

                &.draggable-mirror {
                    position: relative;
                    z-index: 9;
                    cursor: pointer;
                }

                &.draggable-source--is-dragging {
                    background: #E2E2E2;
                    opacity: 0.5;
                    z-index: 0;
                }

                .top {
                    flex-grow: 2;
                    padding: 10px 5px;
                    border-radius: 4px 4px 0 0;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    text-align: center;
                    background: white;
                    font-weight: 600;
                    font-size: 14px;
                    min-height: 60px;
                }

                .bottom {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    background: #E8E8E8;
                    padding: 10px;
                    border-radius: 0 0 4px 4px;
                }
            }
        }
    }

    .device-emulator-dimmer {
        position: fixed;
        inset: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        background: rgba(0, 0, 0, 0.5);
    }

    .device-emulator {
        position: relative;
        border-radius: 50px;
        border: 15px solid black;
        box-shadow: 0 0 0 2px #cdcdcd;
        background: white;
        height: 80vh;
        aspect-ratio: 1/ 2;
        overflow: hidden;

        .notch {
            position: absolute;
            top: -10px;
            left: 50%;
            height: 30px;
            width: 40%;
            background: black;
            border-radius: 0 0 10px 10px;
            transform: translateX(-50%);
        }

        .emulated-products {
            padding: 30px 5px;
            height: 100%;
            box-sizing: border-box;
            overflow: auto;

            .category-products {
                display: grid;
                grid-template-columns: 1fr 1fr 1fr;
                grid-gap: 5px;

                .product {
                    display: flex;
                    flex-direction: column;
                    cursor: pointer;
                    height: 120px;

                    &.draggable-mirror {
                        position: relative;
                        z-index: 999;
                        cursor: pointer;
                    }

                    &.draggable-source--is-dragging {
                        background: #E2E2E2;
                        opacity: 0.5;
                        z-index: 999;
                    }

                    .top {
                        flex-grow: 2;
                        padding: 10px 5px;
                        border-radius: 4px 4px 0 0;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        text-align: center;
                        background: #cccccc;
                        font-weight: 600;
                        font-size: 14px;
                        min-height: 60px;
                    }

                    .bottom {
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        background: #E8E8E8;
                        padding: 10px;
                        border-radius: 0 0 4px 4px;
                    }
                }
            }
        }
    }

    .empty-category-text {
        grid-column: 1/-1;
        text-align: center;
        font-style: italic;
        font-size: 14px;
    }

    @media (min-width: 900px) {
        .mobile-button {
            display: none;
        }
    }
}