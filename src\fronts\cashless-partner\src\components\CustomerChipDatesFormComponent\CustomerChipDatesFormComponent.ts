import {Component, Prop, Vue} from "vue-facing-decorator";
import {FormModalOrDrawerComponent} from "@groupk/vue3-interface-sdk";
import {AutoWired, VisualScopedUuid} from "@groupk/horizon2-core";
import {UuidScopeEstablishment} from "@groupk/mastodon-core";
import {CustomerRepository} from "../../../../../shared/repositories/CustomerRepository";
import {CustomerChipApiIn, CustomerChipApiOut} from "@groupk/mastodon-core";
import {CustomerChipRepository} from "../../../../../shared/repositories/CustomerChipRepository";
import DateUtils from "../../../../../shared/utils/DateUtils";
import {CleaveDirective} from "../../../../../shared/directives/CleaveDirective";

@Component({
	directives: {
		cleave: CleaveDirective,
	},
	components: {
		'form-modal-or-drawer': FormModalOrDrawerComponent
	},
	emits: ['close']
})
export default class CustomerChipDatesFormComponent extends Vue {
	@Prop() establishmentUid!: VisualScopedUuid<UuidScopeEstablishment>;
	@Prop() customerChip!: CustomerChipApiOut;

	customerChipDatesData: {startDate: string, startHour: string, endDate: string, endHour: string} = {
		startDate: '',
		startHour: '',
		endDate: '',
		endHour: ''
	}

	cleaveDate = {
		date: true,
		delimiter: '/',
		datePattern: ['d', 'm', 'Y']
	}

	cleaveHour = {
		time: true,
		delimiter: 'h',
		timePattern: ['h', 'm']
	}

	opened: boolean = false;
	error: string|null = null;

	@AutoWired(CustomerRepository) accessor customerRepository!: CustomerRepository;
	@AutoWired(CustomerChipRepository) accessor customerChipRepository!: CustomerChipRepository;

	mounted() {
		setTimeout(() => this.opened = true, 0);

		this.customerChipDatesData.startDate = DateUtils.formatDateFrFormat(this.customerChip.startingDatetime);
		this.customerChipDatesData.startHour = DateUtils.formatDateHour(this.customerChip.startingDatetime);
		this.customerChipDatesData.endDate = this.customerChip.endingDatetime ? DateUtils.formatDateFrFormat(this.customerChip.endingDatetime) : '';
		this.customerChipDatesData.endHour = this.customerChip.endingDatetime ? DateUtils.formatDateHour(this.customerChip.endingDatetime) : '';
	}

	close() {
		this.opened = false;
		setTimeout(() => this.$emit('close'), 300);
	}

	async save() {
		this.error = null;

		let dataError: string|null = null;
		if(this.customerChipDatesData.startDate.length !== 10) {
			dataError = 'La date de début est invalide';
		} else if(this.customerChipDatesData.startHour.length !== 5) {
			dataError = 'L\'heure de début est invalide';
		} else if(this.customerChipDatesData.endDate.length > 0 && this.customerChipDatesData.endDate.length !== 10) {
			dataError = 'La date de fin est invalide';
		} else if(this.customerChipDatesData.endDate.length > 0 && this.customerChipDatesData.endHour.length !== 5) {
			dataError = 'L\'heure de fin est invalide';
		}

		if(dataError) {
			this.error = dataError;
			return;
		}

		const startDate = new Date(DateUtils.convertFrToIso(this.customerChipDatesData.startDate) + 'T' + this.customerChipDatesData.startHour.replace('h', ':') + ':00');
		const endDate = this.customerChipDatesData.endDate.length > 0 ? new Date(DateUtils.convertFrToIso(this.customerChipDatesData.endDate) + 'T' + this.customerChipDatesData.endHour.replace('h', ':') + ':00') : null;

		if(endDate && endDate.getTime() < startDate.getTime()) {
			this.error = 'L\'heure de fin doit être supérieure à la date de début';
			return;
		}

		const apiIn = new CustomerChipApiIn({
			chipVisualId: null,
			chipId: this.customerChip.chipId,
			chipUid: this.customerChip.chipUid,
			customerUid: this.customerChip.customerUid,
			startingDatetime: startDate.toISOString(),
			endingDatetime: endDate ? endDate.toISOString() : null
		});
		const response = await this.customerChipRepository.callContract('update', {establishmentUid: this.establishmentUid, customerChipUid: this.customerChip.uid}, apiIn);
		if(response.isSuccess()) {
			this.$emit('updated', response.success())
			this.$emit('close', response.success())
		} else if(response.isError()) {
			const error = response.error();
			if(!error || !('error' in error)) {
				this.error = 'Une erreur inconnue est survenue';
			} else if(error.error === 'invalid_data') {
				this.error = error.error_details;
			} else if(error.error === 'invalid_chip_id') {
				this.error = 'Cet identifiant de puce est invalide';
			} else if(error.error === 'customer_chip_time_overlaps') {
				this.error = 'Cette puce est déjà liée à un utilisateur';
			}
		} else {
			this.error = 'Une erreur inconnue est survenue';
		}
	}

	getCurrentTimezone() {
		return Intl.DateTimeFormat().resolvedOptions().timeZone;
	}
}