<script lang="ts" src="./OrderAdvancedPaymentInterfaceComponent.ts" />

<style lang="sass">
@import './OrderAdvancedPaymentInterfaceComponent.scss'
</style>

<template>
    <div class="order-advanced-payment-interface-component">
        <div class="top">
            Payer la commande

            <div class="button-group">
                <button :class="{active: activeTab === 'PRODUCTS'}" @click="changeActiveTab('PRODUCTS')"> Produits </button>
                <button :class="{active: activeTab === 'CUSTOM'}" @click="changeActiveTab('CUSTOM')"> Montant libre </button>
                <button :class="{active: activeTab === 'SPLIT'}" @click="changeActiveTab('SPLIT')"> Split </button>
            </div>

            <div class="close" @click="close()">
                <i class="fa-regular fa-xmark"></i>
            </div>
        </div>

        <div class="content-grid">
            <div class="left">

                <template v-if="activeTab === 'PRODUCTS'">
                    <div class="title-group">
                        <span class="title"> Produits à payer </span>
                        <span class="subtitle"> Sélectionnez des produits pour les payer </span>
                    </div>

                    <div class="amount-segment">
                        <div class="title-group">
                            <span class="title"> Reste à payer </span>
                            <span class="subtitle"> Reste à payer sur le total de la commande </span>
                        </div>

                        <span class="amount"> {{ $filters.Money(getOrderTotals.leftToPay) }} </span>
                    </div>

                    <div>
                        <button class="big grey button" @click="toggleAll()">
                            <i class="fa-regular fa-square" v-if="!areAllToggled()"></i>
                            <i class="fa-regular fa-square-check" v-else></i>
                            <span v-if="!areAllToggled()"> Tout sélectionner </span>
                            <span v-else> Tout désélectionner </span>
                        </button>
                    </div>

                    <div class="purchases" v-if="posState.currentOrder">
                        <template v-for="purchase in posState.currentOrder.order.purchases">
                            <div
                                class="purchase"
                                :class="{
                                    discounted: posState.getPurchaseDiscount(purchase) !== null,
                                    disabled: ((getOrderTotals.leftToPay - currentPayment.amount) < getOrderTotals.purchases.perRootPurchase[purchase.uid].withTaxes && !isPurchaseToggled(purchase)) || isPurchasePayed(purchase)
                                }"
                                v-if="posState.getPurchaseQuantity(purchase) > 0"
                                @click="togglePurchase(purchase)"
                            >
                                <div class="top">
                                    <div class="select">
                                        <i v-if="!isPurchaseToggled(purchase)" class="fa-regular fa-square"></i>
                                        <i v-else class="fa-regular fa-square-check"></i>
                                    </div>
                                    <div class="quantity"> {{ posState.getPurchaseQuantity(purchase) }} </div>
                                    <div class="data">
                                        <span class="name"> {{ $filters.Length(posState.orderExecutorModel.requireProductRevision(purchase.productRevisionUid).name, 35) }} </span>
                                        <span class="total">
                                        <span class="price">{{ $filters.Money(posState.getPurchaseTotals(purchase).withTaxes) }}</span>

                                        <span v-if="posState.getPurchaseDiscount(purchase)">
                                             &#8239;
                                            {{ $filters.Money(getOrderTotals.purchases.perRootPurchase[purchase.uid].withTaxes)}}
                                        </span>
                                    </span>
                                    </div>
                                </div>

<!--                                <button class="grey button" @click.stop=""> Split </button>-->
                            </div>
                        </template>
                    </div>
                </template>

                <template v-if="activeTab === 'CUSTOM'">
                    <div class="title-group">
                        <span class="title"> Résumé de la commande </span>
                        <span class="subtitle"> Sélectionnez des produits pour les payer </span>
                    </div>

                    <div class="amount-segment">
                        <div class="title-group">
                            <span class="title"> Reste à payer </span>
                            <span class="subtitle"> Reste à payer sur le total de la commande </span>
                        </div>

                        <span class="amount"> {{ $filters.Money(getOrderTotals.leftToPay) }} </span>
                    </div>

                    <div class="keypad">
                        <keypad @clicked-key="updatePaymentAmount($event)"></keypad>
                    </div>
                </template>

                <template v-if="activeTab === 'SPLIT'">
                    <div class="title-group">
                        <span class="title"> Parts à payer </span>
                        <span class="subtitle"> Sélectionnez des parts pour les payer </span>
                    </div>

                    <div class="amount-segment">
                        <div class="title-group">
                            <span class="title"> Reste à payer </span>
                            <span class="subtitle"> Reste à payer sur le total de la commande </span>
                        </div>

                        <span class="amount"> {{ $filters.Money(getOrderTotals.leftToPay) }} </span>
                    </div>

                    <div v-if="!splits" class="amount-segment" @click="showKeypad = true">
                        <div class="title-group">
                            <span class="title"> Définir un nombre de parts </span>
                            <span class="subtitle"> Définissez le nombre de part dans lequel vous souhaitez séparer le reste à payer </span>
                        </div>

                        <i class="fa-regular fa-pen-line"></i>
                    </div>

                    <div class="purchases" v-else>
                        <template v-for="(split, i) in splits">
                            <div
                                class="purchase"
                                v-if="!split.payed" @click="toggleSplit(split)"
                                :class="{
                                     disabled: (getOrderTotals.leftToPay - currentPayment.amount) < split.amount && !isSplitToggled(split)
                                }"
                            >
                                <div class="top">
                                    <div class="select">
                                        <i v-if="!isSplitToggled(split)" class="fa-regular fa-square"></i>
                                        <i v-else class="fa-regular fa-square-check"></i>
                                    </div>

                                    <div class="data no-padding-left">
                                        <span class="name"> Part {{ i + 1 }} </span>
                                        <span class="total">
                                            <span class="price"> Part {{ i + 1 }} / {{ splits.length }}</span>
                                        </span>
                                    </div>
                                    {{ $filters.Money(split.amount) }}
                                </div>
                            </div>
                        </template>
                    </div>

                </template>

                <button
                    class="custom-pay mobile-only"
                    @click="mobileShowNext = true"
                    :class="{disabled: currentPayment.amount === 0}"
                >
                    Suivant - {{ $filters.Money(currentPayment.amount) }}
                </button>
            </div>
            <div class="right" :class="{'mobile-show-next': mobileShowNext}">
                <div class="top">
                    <div class="amount-segment">
                        <div class="title-group">
                            <span class="title"> Montant du paiement </span>
                            <span class="subtitle"> Montant du paiement en cours </span>
                        </div>

                        <span class="amount"> {{ $filters.Money(currentPayment.amount) }} </span>
                    </div>

                    <div class="amount-segment" v-if="willPaymentBeAutomatic && currentPayment.amount - getOrderTotals.leftToPay > 0">
                        <div class="title-group">
                            <span class="title"> À rendre </span>
                            <span class="subtitle"> Montant à rendre au client </span>
                        </div>

                        <span class="amount"> {{ $filters.Money(currentPayment.amount - getOrderTotals.leftToPay) }} </span>
                    </div>

                    <div class="payment-methods">
                        <template  v-for="methodUid of posState.pointOfSale.paymentMethodsUid">
                            <div
                                class="payment-method"
                                :class="{selected: methodUid === selectedPaymentMethod.uid}"
                                @click="selectedPaymentMethod = posState.requirePaymentMethodWithUid(methodUid)"
                                v-if="posState.requirePaymentMethodWithUid(methodUid).type !== 'WEB'"
                            >
                                {{ posState.requirePaymentMethodWithUid(methodUid).name }}
                            </div>
                        </template>

                    </div>
                </div>

                <div class="pay-buttons">
                    <button class="mobile-only custom-pay grey button" @click="mobileShowNext = false">
                        Retour
                    </button>

                    <button
                        class="custom-pay"
                        @click="pay()"
                        :class="{disabled: currentPayment.amount === 0}"
                    >
                        Payer {{ $filters.Money(currentPayment.amount) }} en {{ selectedPaymentMethod.name }}
                    </button>
                </div>
            </div>
        </div>

        <div class="keypad-container" v-if="showKeypad">
            <div class="keypad">
                <div class="head">
                    <span class="title">
                        Nombre de parts
                    </span>

                    <div class="close" @click="showKeypad = false; partsAmount = 0;">
                        <i class="fa-regular fa-xmark"></i>
                    </div>
                </div>


                <keypad
                    :simple-mode="true"
                    @clicked-key="updatePartsAmount($event)"
                >
                    <template v-slot:display>
                        <div class="selector">
                            <div class="minus" @click="partsAmount > 1 ? partsAmount-- : ''">
                                <i class="fa-regular fa-minus"></i>
                            </div>

                            <span class="amount"> {{ partsAmount }} </span>

                            <div class="plus" @click="partsAmount++">
                                <i class="fa-regular fa-plus"></i>
                            </div>
                        </div>
                    </template>
                </keypad>

                <button class="primary button" :class="{disabled: partsAmount < 1}" @click="generateSplits(); showKeypad = false;">
                    Valider
                </button>
            </div>
        </div>


        <payment
            v-if="pendingPaymentData"
            :pos-state="posState"
            :pending-payment-data="pendingPaymentData"
            @paid="paymentSuccessHandler()"
            @close="pendingPaymentData = null"
        ></payment>
    </div>
</template>