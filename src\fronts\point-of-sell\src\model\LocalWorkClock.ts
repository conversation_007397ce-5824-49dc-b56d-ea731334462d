import {
	uuidScopeProductWorkClock,
	UuidScopeProductWorkClock, WorkClockApiIn
} from "@groupk/mastodon-core"
import {
	<PERSON><PERSON><PERSON><PERSON>,
	EntityAsSimpleObject,
	EntityClass,
	IntegerField,
	VisualScopedUuid, VisualScopedUuidField,
} from "@groupk/horizon2-core";

@EntityClass(WorkClockApiIn)
export class LocalWorkClock extends WorkClockApiIn {
	@VisualScopedUuidField(uuidScopeProductWorkClock) uid: VisualScopedUuid<UuidScopeProductWorkClock>;
	@BoolField() synced: boolean = false;
	@IntegerField({bigint: true}) lastLocalUpdateDate: number = new Date().getTime();

	constructor({uid, establishmentAccountUid, iotDeviceUid, clockIn, datetime}: Omit<EntityAsSimpleObject<LocalWorkClock>, 'synced'|'lastLocalUpdateDate'>) {
		super({uid, establishmentAccountUid, iotDeviceUid, clockIn, datetime});
		this.uid = uid;
	}
}