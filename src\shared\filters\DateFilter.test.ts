import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { SinceFilter } from './DateFilter';

describe('SinceFilter', () => {
    beforeEach(() => {
        // Mock Date.now() to return a fixed timestamp for consistent testing
        vi.useFakeTimers();
        vi.setSystemTime(new Date('2024-01-15T12:00:00Z'));
    });

    afterEach(() => {
        vi.useRealTimers();
    });

    describe('Past dates', () => {
        it('should handle seconds ago', () => {
            const date = new Date('2024-01-15T11:59:30Z').toISOString();
            expect(SinceFilter(date)).toBe('Il y a 30 secondes');
        });

        it('should handle 1 second ago', () => {
            const date = new Date('2024-01-15T11:59:59Z').toISOString();
            expect(SinceFilter(date)).toBe('Il y a 1 seconde');
        });

        it('should handle minutes ago', () => {
            const date = new Date('2024-01-15T11:55:00Z').toISOString();
            expect(SinceFilter(date)).toBe('Il y a 5 minutes');
        });

        it('should handle 1 minute ago', () => {
            const date = new Date('2024-01-15T11:59:00Z').toISOString();
            expect(SinceFilter(date)).toBe('Il y a 1 minute');
        });

        it('should handle hours ago', () => {
            const date = new Date('2024-01-15T09:00:00Z').toISOString();
            expect(SinceFilter(date)).toBe('Il y a 3 heures');
        });

        it('should handle 1 hour ago', () => {
            const date = new Date('2024-01-15T11:00:00Z').toISOString();
            expect(SinceFilter(date)).toBe('Il y a 1 heure');
        });

        it('should handle days ago', () => {
            const date = new Date('2024-01-12T12:00:00Z').toISOString();
            expect(SinceFilter(date)).toBe('Il y a 3 jours');
        });

        it('should handle 1 day ago', () => {
            const date = new Date('2024-01-14T12:00:00Z').toISOString();
            expect(SinceFilter(date)).toBe('Il y a 1 jour');
        });

        it('should handle months ago', () => {
            const date = new Date('2023-10-15T12:00:00Z').toISOString();
            expect(SinceFilter(date)).toBe('Il y a 3 mois');
        });

        it('should handle years ago', () => {
            const date = new Date('2022-01-15T12:00:00Z').toISOString();
            expect(SinceFilter(date)).toBe('Il y a 2 ans');
        });

        it('should handle 1 year ago', () => {
            const date = new Date('2023-01-15T12:00:00Z').toISOString();
            expect(SinceFilter(date)).toBe('Il y a 1 an');
        });
    });

    describe('Future dates', () => {
        it('should handle seconds in future', () => {
            const date = new Date('2024-01-15T12:00:30Z').toISOString();
            expect(SinceFilter(date)).toBe('dans 30 secondes');
        });

        it('should handle 1 second in future', () => {
            const date = new Date('2024-01-15T12:00:01Z').toISOString();
            expect(SinceFilter(date)).toBe('dans 1 seconde');
        });

        it('should handle minutes in future', () => {
            const date = new Date('2024-01-15T12:05:00Z').toISOString();
            expect(SinceFilter(date)).toBe('dans 5 minutes');
        });

        it('should handle 1 minute in future', () => {
            const date = new Date('2024-01-15T12:01:00Z').toISOString();
            expect(SinceFilter(date)).toBe('dans 1 minute');
        });

        it('should handle hours in future', () => {
            const date = new Date('2024-01-15T15:00:00Z').toISOString();
            expect(SinceFilter(date)).toBe('dans 3 heures');
        });

        it('should handle 1 hour in future', () => {
            const date = new Date('2024-01-15T13:00:00Z').toISOString();
            expect(SinceFilter(date)).toBe('dans 1 heure');
        });

        it('should handle days in future', () => {
            const date = new Date('2024-01-18T12:00:00Z').toISOString();
            expect(SinceFilter(date)).toBe('dans 3 jours');
        });

        it('should handle 1 day in future', () => {
            const date = new Date('2024-01-16T12:00:00Z').toISOString();
            expect(SinceFilter(date)).toBe('dans 1 jour');
        });

        it('should handle months in future', () => {
            const date = new Date('2024-04-15T12:00:00Z').toISOString();
            expect(SinceFilter(date)).toBe('dans 3 mois');
        });

        it('should handle years in future', () => {
            const date = new Date('2026-01-15T12:00:00Z').toISOString();
            expect(SinceFilter(date)).toBe('dans 2 ans');
        });

        it('should handle 1 year in future', () => {
            const date = new Date('2025-01-15T12:00:00Z').toISOString();
            expect(SinceFilter(date)).toBe('dans 1 an');
        });
    });

    describe('Edge cases', () => {
        it('should handle current time (0 seconds)', () => {
            const date = new Date('2024-01-15T12:00:00Z').toISOString();
            expect(SinceFilter(date)).toBe('Il y a 0 seconde');
        });
    });

    describe('Real-world scenarios', () => {
        it('should correctly calculate months from July 8, 2025 to December 4, 2025', () => {
            // Mock current date to July 8, 2025
            vi.setSystemTime(new Date('2025-07-08T12:00:00Z'));

            const date = new Date('2025-12-04T12:00:00Z').toISOString();
            expect(SinceFilter(date)).toBe('dans 4 mois'); // Should be approximately 4 months
        });

        it('should correctly calculate months from December 4, 2025 to July 8, 2025', () => {
            // Mock current date to December 4, 2025
            vi.setSystemTime(new Date('2025-12-04T12:00:00Z'));

            const date = new Date('2025-07-08T12:00:00Z').toISOString();
            expect(SinceFilter(date)).toBe('Il y a 4 mois'); // Should be approximately 4 months
        });
    });
});
