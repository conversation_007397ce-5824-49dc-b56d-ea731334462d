<script lang="ts" src="./profile.ts">
</script>

<style lang="sass">
@use './profile.scss' as *
</style>

<template>
    <div id="profile-page" class="page">
        <forbidden-message v-if="forbidden"></forbidden-message>
        <layout
            :drawer-opened="openSideDrawer"
            :display-on-mid-screen="currentPage === 'CATEGORIES'"
            v-else
        >
            <template v-slot:content>
                <content-header :parameters="headerParameters"  @selected-tab="currentPage = $event.id"></content-header>

                <div v-if="loading"></div>
                <template v-else>
                    <profile-categories
                        v-if="currentPage === 'CATEGORIES'"
                        :profile="profile"
                        :categories="categories"
                        :products="products"
                        @updated="updateProfile()"
                        @clicked-manage-products="openSideDrawer = true"
                    ></profile-categories>

                    <profile-devices
                        v-if="currentPage === 'DEVICES'"
                        :profile="profile"
                        :account-profiles="accountProfiles"
                        :devices="devices"
                        :cashless-devices="cashlessDevices"
                    ></profile-devices>

                    <profile-settings
                        v-if="currentPage === 'SETTINGS'"
                        :profile="profile"
                        :payment-methods="paymentMethods"
                        :products="products"
                    ></profile-settings>

                    <div class="bottom-save-container">
                        <button class="big button" :class="{loading: saving, disabled: saving}" @click="updateProfile()"> Sauvegarder </button>
                    </div>
                </template>

            </template>

            <template v-slot:right>
                <div class="right-panel" v-if="currentPage === 'CATEGORIES'">
                    <div class="top">
                        <div class="close mobile-only" @click="openSideDrawer = false">
                            <i class="fa-regular fa-arrow-left"></i>
                            Retour
                        </div>

                        <div class="header">
                            <div class="left">
                                <h2> Produits </h2>
                                <span> Cliquez sur des produits pour les ajouter dans la catégorie </span>
                            </div>
                        </div>

                        <div class="input-group">
                            <div class="fluid input">
                                <input v-model="productSearch" type="text" placeholder="Rechercher un produit"/>
                            </div>
                        </div>
                    </div>

                    <div class="drop-all-container" v-if="getFilteredProducts().length > 0">
                        <button class="small white button" @click="addAllFiltered()">
                            <i class="fa-regular fa-arrow-left"></i>
                            Ajouter les {{ getFilteredProducts().length }} produits
                        </button>
                    </div>

                    <span class="limited" v-if="getFilteredProducts(true).length === 100">
                        L'affichage des produits à été limités à 100 afin de ne pas nuire aux performances de l'application.
                        Effectuez une recherche pour affiner les résultats.
                    </span>

                    <div class="available-products">
                        <div v-if="products.length === 0" class="no-product">
                            Votre établissement ne contient aucun produit
                        </div>
                        <div
                            class="product"
                            v-for="product in getFilteredProducts(true)"
                            :class="{selected: selectedCategory && selectedCategory.productIds.includes(product.id)}"
                            @click="clickedProduct(product)"
                        >
                            <div
                                class="product-image"
                                :style="`background-image: url(${product.img ? product.img : '/no-picture.svg'})`"
                            ></div>
                            <div class="right">
                                <span class="name"> {{ product.name }}</span>
                                <span class="description">
                                    {{ $filters.Money(product.prices[0]) }}
                                </span>
                            </div>
                            <i class="fa-regular fa-circle-plus"></i>
                        </div>
                    </div>
                </div>
            </template>
        </layout>

        <toast-manager></toast-manager>
    </div>
</template>