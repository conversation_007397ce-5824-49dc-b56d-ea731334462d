import {ProfileKioskProductItem} from "@groupk/mastodon-core";
import {VisualScopedUuid} from "@groupk/horizon2-core";
import {UuidScopeIot_deviceApp} from "@groupk/mastodon-core";
import {
    IotDeviceConfig_type
} from "@groupk/mastodon-core";

export class ProfileKioskProductItemData {
    code: number = 0;
    productId: number|null = null;
    iotDeviceUid: VisualScopedUuid<UuidScopeIot_deviceApp>|undefined = undefined;
    iotDeviceConfig: IotDeviceConfig_type | undefined = undefined;
    unavailablePeriodSeconds: number | undefined = undefined;


    constructor(profileKioskProductItem: ProfileKioskProductItem|null = null) {
        if(profileKioskProductItem) {
            this.code = profileKioskProductItem.code;
            this.productId = profileKioskProductItem.productId;
            this.iotDeviceUid = profileKioskProductItem.iotDeviceUid;
            this.iotDeviceConfig = profileKioskProductItem.iotDeviceConfig;
            this.unavailablePeriodSeconds = profileKioskProductItem.unavailablePeriodSeconds;
        }
    }

    toProfileKioskProductItem() {
        if(!this.productId) throw new Error('missing_product_id');
        return new ProfileKioskProductItem({
            code: this.code,
            productId: this.productId,
            iotDeviceUid: this.iotDeviceUid,
            iotDeviceConfig: this.iotDeviceConfig,
            unavailablePeriodSeconds: this.unavailablePeriodSeconds,
        });
    }
}