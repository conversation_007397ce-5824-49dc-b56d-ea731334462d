

#index-page {
    display: flex;
    flex-direction: column;
    gap: 0;
    height: 100%;
    overflow: auto;
    box-sizing: border-box;
    max-width: 700px;
    margin: auto;

    .top {
        display: flex;
        flex-direction: column;
        gap: 40px;
        padding: 20px 20px 30px 20px;
        background: #e9f8fb;

        .top-actions {
            display: flex;
            justify-content: space-between;
            align-items: center;
            gap: 10px;
            width: 100%;

            .logo {
                font-size: 12px;

                .bold {
                    font-size: 16px;
                    font-weight: 500;
                }
            }

            .button:hover {
                background: white;
                filter: brightness(1);
            }
        }

        .main-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 20px;

            .balance-container {
                display: flex;
                flex-direction: column;
                align-items: center;
                gap: 4px;

                .label {
                    font-family: Inter;
                    font-size: 14px;
                }

                .balance {
                    font-family: Inter;
                    font-size: 42px;
                    font-weight: 600;
                }
            }

            .actions {
                display: flex;
                align-items: center;
                justify-content: center;
                gap: 10px;

                .button {
                    background: white;
                }
            }

            .warning {
                display: flex;
                align-items: center;
                gap: 15px;
                padding: 15px;
                border-radius: 8px;
                background: #fff3e5;
                color: #ff8800;

                i {
                    font-size: 24px;
                }

                .right {
                    display: flex;
                    flex-direction: column;
                    gap: 2px;

                    .title {
                        font-size: 15px;
                        font-weight: bold;
                    }

                    .description {
                        font-size: 14px;
                    }
                }
            }
        }

        h3 {
            display: flex;
            align-items: center;
            gap: 10px;
            font-family: Inter;
            font-size: 20px;
            font-weight: 600;
            margin: 0;

            span {
                font-size: 14px;
                font-weight: 400;
            }
        }

        .chips-container {
            display: flex;
            flex-direction: column;
            gap: 10px;

            .chips {
                display: flex;
                align-items: center;
                gap: 10px;

                .chip {
                    padding: 10px 20px;
                    background: white;
                    font-size: 14px;
                    border-radius: 50px;

                    &.active {
                        background: rgba(42, 185, 217, 1);
                        color: white;
                    }
                }
            }
        }
    }

    .transactions-container {
        display: flex;
        flex-direction: column;
        gap: 10px;
        padding: 10px 20px 0 20px;
        background: white;
        height: 100%;
        border-top-left-radius: 20px;
        border-top-right-radius: 20px;

        .transactions {
            display: flex;
            flex-direction: column;

            .transaction {
                display: flex;
                align-items: center;
                justify-content: space-between;
                padding: 10px 0;

                .left {
                    display: flex;
                    align-items: center;
                    gap: 20px;

                    .icon {
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        height: 36px;
                        width: 36px;
                        background: #F2F2F2;
                        border-radius: 50%;

                        i {
                            font-size: 16px;
                        }
                    }

                    .data {
                        display: flex;
                        flex-direction: column;
                        gap: 4px;

                        .label {
                            font-size: 14px;
                            font-weight: 600;
                        }

                        .date {
                            font-size: 12px;
                        }
                    }
                }

                .amount  {
                    font-size: 14px;
                    font-weight: 600;
                }

                &.green {
                    i, .data .label, .amount {
                        color: #09D14D;
                    }

                    .icon {
                        background: rgba(9, 209, 77, 0.1);
                    }
                }

                &.red {
                    i, .data .label, .amount {
                        color: #dd3535;
                    }

                    .icon {
                        background: rgba(221, 53, 53, 0.1);
                    }
                }
            }
        }
    }
}
