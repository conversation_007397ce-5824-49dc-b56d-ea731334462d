import {SocketNative, TcpSocketWrapper, UsbDeviceDescriptor, UsbNative} from "@groupk/native-bridge";
import {SimplifiedPrintingPart} from "../printApiSdk/SimplifiedPrintingPartData";
import {SimplifiedPrintingPartRenderer} from "../printApiSdk/SimplifiedPrintingPartRenderer";
import PrinterHelper, {PrinterBrand, PrinterConfig, PrinterDescriptor,} from "../printApiSdk/PrinterHelper";
import {
	AutoWired, EventEmitter,
	GetInstance,
	ImageCompat,
	ImageUtils,
	TimeUtils,
	UuidUtils,
	VisualScopedUuid
} from "@groupk/horizon2-core";
import {AppBus} from "../config/AppBus";
import {Toast} from "../components/ToastManagerComponent/ToastManagerComponent";
import {ErrorHandler} from "../class/ErrorHandler";
import {FormArea} from "../printApiSdk/CanvasRendererSDK";
import {PrinterType, SelectedPrinter, TcpPrinter} from "../model/PrinterTypes";
import {EscposPrinter} from "@groupk/escpos-protocol";
import {LocalOrder} from "../model/LocalOrder";
import {PosState} from "../model/PosState";
import KitchenProductHandler from "../class/KitchenProductHandler";
import {
	EstablishmentDeviceAppV2ApiOut, EstablishmentDeviceAppV2SetStateApiIn, EstablishmentDeviceEscposPrinterSettingsApi,
	EstablishmentDeviceV2ApiOut,
	PrintingLocationApiOut,
	UuidScopeIot_deviceApp,
	UuidScopeProductOrderPurchaseItem
} from "@groupk/mastodon-core";
import {
	EstablishmentDeviceKnownService
} from "@groupk/mastodon-core";
import {EstablishmentDeviceRepository} from "../../../../shared/repositories/EstablishmentDeviceRepository";
import {EstablishmentDeviceEscposPrinterStateApi} from "@groupk/mastodon-core";
import {
	PurchasePrintProgressModalComponentData, PurchasePrintProgressModalComponentDataPerPrinter
} from "../components/PurchasePrintProgressModalComponent/PurchasePrintProgressModalComponent";

export interface PosPrinter {
	getId(): string
	getBrand(): PrinterBrand
	getType(): PrinterType
}

export type CustomPrinterConfigs = {[printer: string]: PrinterConfig};

interface PrintingSessionData {
	printingStep: number;
	order: LocalOrder;
	itemsToPrint: VisualScopedUuid<UuidScopeProductOrderPurchaseItem>[];
	ticketsToPrintData: { printingLocation: PrintingLocationApiOut, parts: SimplifiedPrintingPart[]}[];
	printedTickets: Set<number>;
	callbacks?: {
		successCallback?: () => void,
		errorCallback?: () => void
	};
}

interface PrinterInfo {
	serialNumber: string;
	firmwareVersion: string;
	makerName: string;
	modelName: string;
}

const NoPrinterToast: Toast = {
	title: 'Aucune imprimante sélectionnée',
	description: 'Veuillez sélectionner une imprimante dans les paramètres afin d’imprimer',
	color: 'red',
	closable: true,
	duration: 4000,
	action: {
		name: 'Paramètres',
		icon: 'fa-regular fa-cog',
		page: 'settings'
	}
}

export class PrinterRepository extends EventEmitter<{
	'print-progress-update': PurchasePrintProgressModalComponentDataPerPrinter
}> {
	private lsKey = 'v2-receipt-printer';
	private configsLsKey = 'v2-custom-printer-configs';
	private tcpPrintersLsKey = 'v2-tcp-printers';

	/**
	 * @deprecated
	 */
	private oldLsKey = 'receipt-printer';

	private printingPartRendererConfig = {margins: {top: 1, left: 1, right: 1, bottom: 1}};

	currentKitchenPrintingState: PurchasePrintProgressModalComponentDataPerPrinter|null = null;
	private currentPrintingSession: PrintingSessionData|null = null;

	@AutoWired(AppBus) accessor appBus!: AppBus;
	@AutoWired(ErrorHandler) accessor errorHandler!: ErrorHandler;
	@AutoWired(UsbNative) accessor usbNative!: UsbNative;
	@AutoWired(SocketNative) accessor socketNative!: SocketNative;

	saveCustomConfig(printerId: string, config: PrinterConfig) {
		let rawConfigs = localStorage.getItem(this.configsLsKey);
		if(!rawConfigs) rawConfigs = '{}';
		const configs: CustomPrinterConfigs = JSON.parse(rawConfigs);
		configs[printerId] = config;
		localStorage.setItem(this.configsLsKey, JSON.stringify(configs));
	}

	getCustomConfigForPrinter(printerId: string): PrinterConfig|null {
		let rawConfigs = localStorage.getItem(this.configsLsKey);
		if(!rawConfigs) return null;
		const configs: CustomPrinterConfigs = JSON.parse(rawConfigs);
		return configs[printerId] || null;
	}

	setReceiptPrinter(printer: SelectedPrinter) {
		localStorage.setItem(this.lsKey, JSON.stringify(printer));
	}

	getReceiptPrinter(): SelectedPrinter|null {
		const oldLocalStorage = localStorage.getItem(this.oldLsKey);
		if(oldLocalStorage !== null) {
			const usbDevice: UsbDeviceDescriptor = JSON.parse(oldLocalStorage);
			this.setReceiptPrinter({
				id: usbDevice.id,
				type: PrinterType.USB
			});
			localStorage.removeItem(this.oldLsKey)
		}

		const rawDevice = localStorage.getItem(this.lsKey);
		if(!rawDevice) return null;
		return JSON.parse(rawDevice);
	}

	deleteReceiptPrinter(){
		try{
			localStorage.removeItem(this.lsKey);
			localStorage.removeItem(this.configsLsKey);
		}catch(e){

		}
	}

	saveTcpPrinter(printerToSave: TcpPrinter) {
		const printers = this.getTcpPrinters();
		const index = printers.findIndex((printer) => printer.uid === printerToSave.uid);
		if(index === -1) printers.push(printerToSave);
		else printers.splice(index, 1, printerToSave);
		localStorage.setItem(this.tcpPrintersLsKey, JSON.stringify(printers));
	}

	getTcpPrinters(): TcpPrinter[] {
		let rawPrinters = localStorage.getItem(this.tcpPrintersLsKey);
		if(!rawPrinters) return [];
		const parsed: Record<string, any>[] = JSON.parse(rawPrinters);
		return parsed.map((printer) => new TcpPrinter(
			printer.uid,
			printer.ip,
			printer.port,
			printer.brand,
			printer.name,
		));
	}

	async connectToUsbPrinter(deviceId: string): Promise<UsbDeviceDescriptor|null> {
		try {
			// const result = await this.usbNative.requestPermission(deviceId);
			// if(!result) return null;
			let devices = await this.usbNative.getDevices();
			for(let device of devices) {
				try {
					await this.usbNative.requestPermission(device.id);
				} catch(err) {}
			}
			devices = await this.usbNative.getDevices();
			return devices.find((updatedDevice) => updatedDevice.serialNumber === deviceId) ?? null;
		} catch(err) {
			return null;
		}
	}

	async convertPrintingPartsToImageAndAreas(parts: SimplifiedPrintingPart[], descriptor: PrinterDescriptor): Promise<{base64: string, areas: FormArea[]}> {
		const simplifiedPrintingPartRenderer = new SimplifiedPrintingPartRenderer(this.printingPartRendererConfig);
		simplifiedPrintingPartRenderer.addParts(parts);

		if(!descriptor.maxSize.width) {
			this.errorHandler.logToSentry({
				title: 'Printer is not configured : missing maxSize.width',
				description: 'Invalid PrinterDescriptor',
				printDescriptor: descriptor
			})
			throw new Error('printer_not_configured_missing_width');
		}
		if(!descriptor.maxSize.height) {
			this.errorHandler.logToSentry({
				title: 'Printer is not configured : missing maxSize.height',
				description: 'Invalid PrinterDescriptor',
				printDescriptor: descriptor
			})
			throw new Error('printer_not_configured_missing_height');
		}
		const renderer = simplifiedPrintingPartRenderer.getCanvasRenderer({targetSize: {width: descriptor.maxSize.width, height: descriptor.maxSize.height}});

		return {base64: renderer.exportToImage(), areas: renderer.getRenderedAreas()};
	}

	async printToSelectedPrinter(parts: SimplifiedPrintingPart[], overrideToast: Toast|null = null) {
		console.log(await this.printToBase64(parts));
		const selectedPrinter = this.getReceiptPrinter();
		if(!selectedPrinter) {
			const toastToShow = overrideToast ?? NoPrinterToast;
			this.appBus.emit('displayToast', toastToShow);
			throw new Error('printer_must_be_connected');
		}

		if(selectedPrinter.type === PrinterType.TCP) await this.printToTcp(parts, selectedPrinter);
		if(selectedPrinter.type === PrinterType.USB) await this.printToUsb(parts, selectedPrinter);
	}

	private async printToUsb(parts: SimplifiedPrintingPart[], selectedPrinter: SelectedPrinter) {
		if(selectedPrinter.type !== PrinterType.USB) throw new Error('printer_must_be_usb');

		const updatedPrinter = await this.connectToUsbPrinter(selectedPrinter.id);
		console.log(updatedPrinter);
		if(!updatedPrinter || !updatedPrinter.connected || !updatedPrinter.serialNumber) {
			this.appBus.emit('displayToast', NoPrinterToast);
			throw new Error('printer_must_be_connected');
		}

		const customPrinterConfig: PrinterConfig|null = this.getCustomConfigForPrinter(selectedPrinter.id);
		const descriptor = PrinterHelper.getDescriptorForBrand((updatedPrinter.manufacturerName ?? '').toUpperCase() as PrinterBrand, {product: updatedPrinter.productName ?? undefined, printerRelayConfig: customPrinterConfig ?? undefined})
		const data = await this.convertPrintingPartsToImageAndAreas(parts, descriptor);
		const converter = PrinterHelper.getForPrinterLanguage(descriptor.acceptedLanguages[0]);

		console.log(data.base64);
		const outputBytes = await converter.convertFromBase64(data.base64, data.areas);

		let interfaceId: number|null = null;
		for(const usbInterface of updatedPrinter.interfaces) {
			for(const endpoint of usbInterface.endpoints) {
				if (endpoint.type === 'BULK' && endpoint.direction === 'out') {
					interfaceId = usbInterface.id;
				}
			}
		}

		if(interfaceId === null) throw new Error('printer_have_no_out_interface');

		this.usbNative.bulkConnection(updatedPrinter.id, interfaceId)
			.then((wrapper)=>{
				wrapper.on('connected', async ()=> {
					wrapper.send(outputBytes);
					wrapper.close();
				});
			}).catch((err: Error) => {
				this.appBus.emit('displayToast', {
					title: 'L\'impression a échoué',
					description: err.message || String(err),
					color: 'red',
					closable: true,
					duration: 3000
				});
				throw new Error("send_raw_failed");
			});
	}

	private async printToTcp(parts: SimplifiedPrintingPart[], selectedPrinter: SelectedPrinter) {
		if(selectedPrinter.type !== PrinterType.TCP) throw new Error('printer_must_be_tcp');

		const tcpPrinters: TcpPrinter[] = this.getTcpPrinters();
		const tcpPrinter = tcpPrinters.find((printer) => printer.getId() === selectedPrinter.id);
		if(!tcpPrinter) {
			this.errorHandler.logToToastManager({
				title: 'Imprimante inconnue',
				description: 'Configurez une imprimante dans les paramètres de la caisse'
			});
			throw new Error('unknown_tcp_printer');
		}

		const customPrinterConfig: PrinterConfig|null = this.getCustomConfigForPrinter(tcpPrinter.getId());
		const descriptor = PrinterHelper.getDescriptorForBrand((tcpPrinter.getBrand() ?? '').toUpperCase() as PrinterBrand, {product: undefined, printerRelayConfig: customPrinterConfig ?? undefined})
		const data = await this.convertPrintingPartsToImageAndAreas(parts, descriptor);
		const converter = PrinterHelper.getForPrinterLanguage(descriptor.acceptedLanguages[0]);
		const outputBytes = await converter.convertFromBase64(data.base64, data.areas);

		this.socketNative.tcpConnectWrapper(tcpPrinter.ip, parseInt(tcpPrinter.port, 10)).then((wrapper) => {

			wrapper.on('connected', () => {
				wrapper.send(outputBytes);
				wrapper.close();
			});

			wrapper.on('closed', () => {});
		}).catch(() => {
			this.errorHandler.logToToastManager({
				title: 'Connexion impossible',
				description: 'La connexion à l\'imprimante réseau à échoué'
			});
		});
	}

	async printToBase64(parts: SimplifiedPrintingPart[]) {
		return (await this.convertPrintingPartsToImageAndAreas(parts, {
			brand: null,
			model: null,
			minSize: {width: 600, height: -8},
			maxSize: {width: 600, height: -8},
			acceptedLanguages: [],
			density: {height: 8, width: 8}
		})).base64;
	}

	async printToKitchen(printingStep: number, order: LocalOrder, itemsToPrint: VisualScopedUuid<UuidScopeProductOrderPurchaseItem>[], callbacks?: {
		successCallback?: () => void,
		errorCallback?: () => void
	}) {
		const ticketsToPrintData = KitchenProductHandler.getPrintingParts(printingStep, order, itemsToPrint);

		this.currentPrintingSession = {
			printingStep,
			order,
			itemsToPrint,
			ticketsToPrintData,
			printedTickets: new Set(),
			callbacks
		};

		this.initializePrintingSession(ticketsToPrintData);
		await this.processPrintingTickets();
	}

	async retryFailedPrinting() {
		if (!this.currentPrintingSession) {
			throw new Error('No printing session to retry');
		}
		await this.processPrintingTickets();
	}

	private initializePrintingSession(ticketsToPrintData: { printingLocation: PrintingLocationApiOut, parts: SimplifiedPrintingPart[]}[]) {
		const posState = GetInstance(PosState);

		if(ticketsToPrintData.length > 0) {
			posState.showTicketPrintingModal = true;

			this.currentKitchenPrintingState = {
				printers: {}
			};
			let index = 1;
			for(const _ of ticketsToPrintData) {
				this.currentKitchenPrintingState.printers[index] = {
					totalSteps: 5,
					currentStep: 0,
					currentStepName: 'En attente'
				};
				index++;
			}
			this.emit('print-progress-update', this.currentKitchenPrintingState);
		}
	}

	private async processPrintingTickets() {
		if (!this.currentPrintingSession) return;

		try {
			let index = 1;
			for(let printData of this.currentPrintingSession.ticketsToPrintData) {
				// Skip already printed tickets
				if (this.currentPrintingSession.printedTickets.has(index)) {
					index++;
					continue;
				}

				await this.processSingleTicket(printData, index);

				console.log('added');
				this.currentPrintingSession.printedTickets.add(index);

				index++;
			}

			// All tickets printed successfully
			this.currentPrintingSession.callbacks?.successCallback?.();
		} catch (error) {
			// At least one ticket failed
			this.currentPrintingSession.callbacks?.errorCallback?.();
			throw error; // Re-throw to maintain error propagation
		}
	}

	private async processSingleTicket(printData: { printingLocation: PrintingLocationApiOut, parts: SimplifiedPrintingPart[]}, index: number) {
		this.updateKitchenState(index, {
			totalSteps: 5,
			currentStep: 1,
			currentStepName: 'Génération du billet...'
		});

		const validationResult = this.validatePrinterConfiguration(printData, index);
		if (!validationResult.isValid) {
			throw new Error('Printer validation failed'); // Changed from return to throw
		}

		const image = await this.renderTicketToImage(printData);

		this.updateKitchenState(index, {
			totalSteps: 5,
			currentStep: 2,
			currentStepName: 'Connexion à l\'imprimante...'
		});

		await this.connectAndPrintTicket(validationResult.iotDeviceApp, validationResult.iotDevice, image, index);
	}

	private validatePrinterConfiguration(printData: { printingLocation: PrintingLocationApiOut, parts: SimplifiedPrintingPart[]}, index: number):
		{ isValid: false } | { isValid: true, iotDeviceApp: EstablishmentDeviceAppV2ApiOut, iotDevice: EstablishmentDeviceV2ApiOut } {
		const posState = GetInstance(PosState);

		if(!printData.printingLocation.iotDeviceAppUidList || printData.printingLocation.iotDeviceAppUidList.length === 0) {
			this.appBus.emit('displayToast', {
				title: 'Aucune imprimante configurée',
				description: 'Le point d\'impression ' + printData.printingLocation.name + ' n\'a aucune imprimante de configurée',
				color: 'orange',
				closable: true,
				duration: 3000,
			});
			return { isValid: false };
		}

		const iotDeviceApp: EstablishmentDeviceAppV2ApiOut = posState.requireDeviceAppWithUid(UuidUtils.visualToScoped<UuidScopeIot_deviceApp>(printData.printingLocation.iotDeviceAppUidList[0]));
		if(iotDeviceApp.settings.type !== EstablishmentDeviceKnownService.ESCPOS_PRINTER) {
			this.appBus.emit('displayToast', {
				title: 'Aucune imprimante configurée',
				description: 'L\'imprimante du point d\'impression ' + printData.printingLocation.name + ' n\'a aucun service configuré',
				color: 'orange',
				closable: true,
				duration: 3000,
			});
			return { isValid: false };
		}

		const iotDevice = posState.requireDeviceWithUid(iotDeviceApp.deviceUid);
		return { isValid: true, iotDeviceApp, iotDevice };
	}

	private async renderTicketToImage(printData: { printingLocation: PrintingLocationApiOut, parts: SimplifiedPrintingPart[]}) {
		const simplifiedPrintingPartRenderer = new SimplifiedPrintingPartRenderer(this.printingPartRendererConfig);
		simplifiedPrintingPartRenderer.addParts(printData.parts);

		const descriptor = {
			brand: null,
			model: null,
			minSize: {width: 600, height: -8},
			maxSize: {width: 600, height: -8},
			acceptedLanguages: [],
			density: {height: 8, width: 8}
		};

		const renderer = simplifiedPrintingPartRenderer.getCanvasRenderer({targetSize: {width: descriptor.maxSize.width, height: descriptor.maxSize.height}});
		console.log(renderer.exportToImage());

		return await ImageUtils.decoder.fromCanvas(renderer.getCanvasElement());
	}

	private async connectAndPrintTicket(iotDeviceApp: EstablishmentDeviceAppV2ApiOut, iotDevice: EstablishmentDeviceV2ApiOut, image: ImageCompat, index: number) {
		const posState = GetInstance(PosState);

		await new Promise(async (resolve, reject) => {
			let printed: boolean = false;

			try {
				const tcpWrapper = await this.socketNative.tcpConnectWrapper(iotDevice.localIpList![0], (iotDeviceApp.settings as EstablishmentDeviceEscposPrinterSettingsApi).port ?? EstablishmentDeviceEscposPrinterSettingsApi.PORT_DEFAULT);
				const printer = new EscposPrinter(tcpWrapper);

				printer.socket.on('connected', async ()=> {
					try {
						const printerInfo = await printer.identify();

						if(!posState.haveSendPrinterInfos[printerInfo.serialNumber]) {
							this.updatePrinterInfo(iotDeviceApp, printerInfo).catch();
							posState.haveSendPrinterInfos[printerInfo.serialNumber] = true;
						}

						this.updateKitchenState(index, {
							totalSteps: 5,
							currentStep: 3,
							currentStepName: 'Statut de l\'imprimante...'
						});

						const checkState = async ()=> {
							const status = await printer.status(10000);
							if(status.paperSensor.nearEnd) {
								this.appBus.emit('displayToast', {
									title: 'Papier presque vide',
									description: 'Le rouleau de l\'imprimante est bientôt vide, pensez à le changer.',
									color: 'orange',
									closable: true,
									duration: 3000,
								});
							}
						};
						await checkState();

						this.updateKitchenState(index, {
							totalSteps: 5,
							currentStep: 4,
							currentStepName: 'Impression en cours...'
						});

						await this.sendPrintCommand(printer, image);
						await TimeUtils.delay(2000); // 100mm/s, 10cm/s => consider 20cm for now // TODO use serializer.estimatePrintTime
						await checkState(); // asking for something AFTER sending something to print is to ensure the previous data/buffer was sent properly

						printed = true;
					} finally {
						try{
							printer.close();
						} catch (e) {}

						if(printed) {
							this.updateKitchenState(index, {
								totalSteps: 5,
								currentStep: 5,
								currentStepName: 'Terminé'
							});
							resolve(undefined);
						}
						else {
							this.handlePrintError(index);
							reject(undefined);
						}
					}
				});

				printer.socket.on('closed', ()=>{
					if(!printed) {
						this.appBus.emit('displayToast', {
							title: 'L\'impression à échoué',
							description: 'Le ticket n\'a pas été imprimé en cuisine',
							color: 'orange',
							closable: true,
							duration: 3000,
						});

						this.handlePrintError(index);
						reject(undefined);
					}
					console.log('closed', Date.now());
				});
			} catch(err) {
				this.appBus.emit('displayToast', {
					title: 'L\'impression à échoué',
					description: JSON.stringify(err),
					color: 'orange',
					closable: true,
					duration: 3000,
				});
				this.handlePrintError(index);
				reject(undefined);
			}
		});
	}

	private async updatePrinterInfo(iotDeviceApp: EstablishmentDeviceAppV2ApiOut, printerInfo: PrinterInfo) {
		const posState = GetInstance(PosState);
		const establishmentDeviceRepository = GetInstance(EstablishmentDeviceRepository);

		await establishmentDeviceRepository.callContract('setState', {establishmentUid: posState.establishmentUid, deviceAppUid: iotDeviceApp.uid}, new EstablishmentDeviceAppV2SetStateApiIn({
			name: iotDeviceApp.name,
			version: iotDeviceApp.version,
			state: new EstablishmentDeviceEscposPrinterStateApi({
				type: EstablishmentDeviceKnownService.ESCPOS_PRINTER,
				firmwareVersion: printerInfo.firmwareVersion,
				makerName: printerInfo.makerName,
				serialNumber: printerInfo.serialNumber,
				modelName: printerInfo.modelName,
			}),
			localIpList: undefined,
			deviceState: undefined,
			batteryLevel: undefined,
			ssidName: undefined
		}));
	}

	private async sendPrintCommand(printer: EscposPrinter<TcpSocketWrapper>, image: ImageCompat) {
		let escpos : number[] = [
			...printer.serializer.start(),
			...printer.serializer.printImage(image),
			...printer.serializer.lineFeed(5),
			...printer.serializer.cut('partial')
		];

		printer.sendRaw(escpos);
	}

	handlePrintError(idToUpdate: number) {
		this.updateKitchenState(idToUpdate, {
			totalSteps: 1,
			currentStep: 1,
			currentStepName: 'Erreur',
			error: true
		});
	}

	updateKitchenState(idToUpdate: number, state: PurchasePrintProgressModalComponentData) {
		if(!this.currentKitchenPrintingState) return;
		this.currentKitchenPrintingState.printers[idToUpdate] = state;
		this.emit('print-progress-update', this.currentKitchenPrintingState);
	}
}