.order-advanced-payment-interface-component {
    position: fixed;
    right: 25px;
    top: 25px;
    bottom: 25px;
    z-index: 999;
    width: max(788px, 60%) ;
    display: flex;
    flex-direction: column;
    align-items: stretch;
    border-radius: 12px;
    background: #F2F4F7;
    // Avoid content small visual overflow
    padding: 1px;
    box-sizing: border-box;
    overflow: hidden;

    @media (max-width: 900px) {
        inset: 0;
        border-radius: 0;
        width: 100%;
        overflow: auto;
    }

    &:not(.performance-mode) {
        transition: transform .3s cubic-bezier(0.22, 1, 0.36, 1);
    }

    > .top {
        position: relative;
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 10px;
        padding: 15px;
        font-size: 16px;
        font-weight: 600;

        .button-group {
            display: flex;
            border-radius: 8px;
            overflow: hidden;

            button {
                all: unset;
                padding: 10px 20px;
                background: white;
                font-size: 15px;
                font-weight: 500;

                &.active {
                    background: var(--primary-color);
                    color: var(--primary-text-color);
                }
            }
        }

        .close {
            position: absolute;
            right: 25px;
            top: 50%;
            transform: translateY(-50%);
            display: flex;
            align-items: center;
            justify-content: center;
            height: 42px;
            width: 42px;
            background: white;
            border-radius: 50%;

            @media (max-width: 900px) {
                top: 5px;
                right: 5px;
                transform: none;
            }
        }
    }

    .content-grid {
        position: relative;
        flex-grow: 2;
        flex-shrink: 0;
        display: grid;
        grid-template-columns: 1fr 1fr;
        grid-gap: 10px;
        height: 1px;
        overflow: hidden;

        @media (max-width: 900px) {
            grid-template-columns: 1fr;
        }

        .left, .right {
            display: flex;
            flex-direction: column;
            gap: 20px;
            background: white;
            padding: 20px;
            box-sizing: border-box;
            overflow: hidden;
        }

        .left {
            border-top-right-radius: 12px;
            overflow: auto;
        }

        .right {
            border-top-left-radius: 12px;

            .top {
                flex-grow: 2;
                display: flex;
                flex-direction: column;
                gap: 20px;
            }

            @media (max-width: 900px) {
                position: absolute;
                inset: 0;
                transform: translateX(100%);
                transition: transform .3s cubic-bezier(0.22, 1, 0.36, 1);

                &.mobile-show-next {
                    transform: none;
                }
            }
        }

        @media (max-width: 900px) {
            .left, .right {
                border-radius: 0;
            }
        }
    }

    .pay-buttons {
        display: flex;
        flex-direction: column;
        gap: 10px;
    }

    .custom-pay {
        all: unset;
        flex-shrink: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        height: 56px;
        width: 100%;
        border-radius: 8px;
        background: var(--primary-color);
        color: var(--primary-text-color);
        font-family: Montserrat, sans-serif;
        font-size: 15px;
        font-weight: 700;
        cursor: pointer;

        @media (max-width: 900px) {
            &.mobile-only {
                display: flex !important;
            }
        }

        &:hover {
            background: var(--primary-button-hover-color);
            color: var(--primary-text-color);
        }

        &.grey {
            background: #F2F2F2;
            color: black;
        }

        &.disabled {
            opacity: 0.5;
            pointer-events: none;
        }
    }

    .title-group {
        display: flex;
        gap: 5px;
        margin-bottom: 0;

        .title {
            font-size: 16px;
            font-weight: 600;
        }

        .subtitle {
            font-size: 14px;
            font-weight: 400;
        }
    }

    .amount-segment {
        display: flex;
        align-items: center;
        gap: 20px;
        padding: 20px;
        border-radius: 8px;
        background: #F2F4F7;

        .title-group {
            flex-grow: 2;
        }

        .amount {
            font-size: 18px;
            font-weight: bold;
            width: 100px;
            text-align: right;
        }

        i {
            font-size: 18px;
        }
    }

    .purchases {
        flex-grow: 2;
        display: flex;
        flex-direction: column;
        gap: 10px;
        padding: 0 !important;
        overflow: auto;
        max-height: 100%;

        &::-webkit-scrollbar {
            display: none;
        }

        .purchase {
            all: unset;
            display: flex;
            padding: 10px 15px;
            border-radius: 8px;
            background: #F2F4F7;
            gap: 10px;

            &.disabled {
                opacity: 0.5;
                pointer-events: none;
            }

            .quantity {
                flex-shrink: 0;
                display: flex;
                width: 25px;
                height: 25px;
                justify-content: center;
                align-items: center;
                background: var(--primary-color);
                color: var(--primary-text-color);
                border-radius: 50%;

                font-family: Montserrat, sans-serif;
                font-size: 12px;
                font-weight: 700;
                line-height: 20px;
            }

            .data {
                flex-grow: 2;
                display: flex;
                flex-direction: column;
                gap: 4px;
                padding: 5px 10px;

                &.no-padding-left {
                    padding-left: 0;
                }

                .name {
                    font-family: Montserrat, sans-serif;
                    font-size: 14px;
                    font-weight: 700;
                }

                .total {
                    font-family: Montserrat, sans-serif;
                    font-size: 13px;
                }
            }

            &.discounted {
                .quantity {
                    background: #FFF066;
                    color: black;
                }

                .price {
                    text-decoration: line-through;
                }
            }

            .select {
                padding-right: 10px;
                i {
                    font-size: 20px;
                }
            }

            .grey.button {
                background: #E8E8E8;
            }
        }

        .remove, .discount {
            flex-shrink: 0;
            padding: 10px;
            cursor: pointer;

            &:hover {
                background: #e6e9ec;
                border-radius: 4px;
            }

            i {
                font-size: 18px;
            }
        }
    }

    .payment-methods {
        display: grid;
        grid-template-columns: 1fr 1fr;
        grid-gap: 10px;

        .payment-method {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 15px;
            border-radius: 8px;
            background: #F2F4F7;
            font-size: 15px;

            &.selected {
                background: var(--primary-color);
                color: var(--primary-text-color)
            }
        }
    }

    .left .keypad, .keypad-container {
        flex-grow: 2;
        display: flex;
        flex-direction: column;
        justify-content: flex-end;

        .keypad-component {
            border: none;
            grid-gap: 5px;

            .line {
                border: none;
                grid-gap: 5px;

                .key {
                    border: none;
                    background: #F2F4F7;
                    border-radius: 6px;
                }
            }
        }
    }

    .keypad-container {
        position: fixed;
        background: rgba(0, 0, 0, 0.7);
        inset: 0;
        display: flex;
        align-items: center;
        justify-content: center;

        .keypad {
            display: flex;
            flex-direction: column;
            gap: 20px;
            background: white;
            width: 300px;
            border-radius: 8px;
            padding: 20px;

            .head {
                display: flex;
                align-items: center;
                justify-content: space-between;

                .title {
                    font-size: 18px;
                    font-weight: bold;
                }

                .close {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    height: 42px;
                    width: 42px;
                    background: #E8E8E8;
                    border-radius: 50%;
                }
            }


            .primary.button {
                height: 30px;
                font-size: 16px;
            }

            .selector {
                display: flex;
                align-items: center;
                justify-content: center;
                padding: 20px 0 30px 0;

                .amount {
                    width: 80px;
                    font-size: 24px;
                    font-weight: 500;
                    text-align: center;
                }

                .plus, .minus {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    height: 42px;
                    width: 42px;
                    background: #E8E8E8;
                    border-radius: 50%;
                    font-size: 18px;
                    font-weight: 400;
                }
            }

        }
    }
}