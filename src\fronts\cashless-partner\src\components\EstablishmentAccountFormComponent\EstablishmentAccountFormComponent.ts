import {Component, Prop, Vue} from "vue-facing-decorator";
import {FormModalOrDrawerComponent} from "@groupk/vue3-interface-sdk";
import {EstablishmentAccountApiIn, EstablishmentAccountType, UuidScopeEstablishment, ApplicationPermission, EstablishmentAccountPermissionModel, MastodonEstablishmentAccountContractAggregate} from "@groupk/mastodon-core";
import {AutoWired, VisualScopedUuid} from "@groupk/horizon2-core";
import {EstablishmentAccountRepository} from "../../../../../shared/repositories/EstablishmentAccountRepository";
import {CleaveDirective} from "../../../../../shared/directives/CleaveDirective";

export function EstablishmentAccountFormComponentHasRequiredPermissions(ownedPermissions: ApplicationPermission[]): boolean {
	return EstablishmentAccountPermissionModel.hasPermissionsForContracts(ownedPermissions, [
		MastodonEstablishmentAccountContractAggregate.create,
	]);
}

@Component({
	directives: {
		cleave: CleaveDirective,
	},
	components: {
		'form-modal-or-drawer': FormModalOrDrawerComponent,
	},
	emits: ['created', 'close']
})
export default class EstablishmentAccountFormComponent extends Vue {
	@Prop() establishmentUid!: VisualScopedUuid<UuidScopeEstablishment>;

	establishmentAccount: EstablishmentAccountApiIn = new EstablishmentAccountApiIn({
		firstname: '',
		lastname: '',
		type: EstablishmentAccountType.HUMAN
	});

	password: string = '';
	passwordConfirmation: string = '';

	opened: boolean = false;
	error: string|null = null;

	@AutoWired(EstablishmentAccountRepository) accessor establishmentAccountRepository!: EstablishmentAccountRepository;

	mounted() {
		setTimeout(() => this.opened = true, 0);
	}

	close() {
		this.opened = false;
		setTimeout(() => this.$emit('close'), 300);
	}

	async create() {
		if(this.password.length === 0 || this.password !== this.passwordConfirmation) {
			this.error = 'Les mots de passe saisis ne correspondent pas';
			return;
		}

		this.establishmentAccount.password = this.password;
		console.log(this.establishmentAccount);
		const result = await this.establishmentAccountRepository.callContract('create', {establishmentUid: this.establishmentUid}, this.establishmentAccount);
		if(result.isSuccess()) {
			this.$emit('created', result.success());
		} else {
			const error = result.error();
			if(error && 'error' in error && error.error === 'account_identifierAlreadyUsed') {
				this.error = 'L\'identifiant renseigné est déjà utilisé par un autre compte';
			} else if(error && 'error' in error && error.error === 'emailBlacklisted') {
				this.error = 'L\'identifiant renseigné interdit sur cette plateforme';
			} else if(error && 'error' in error && error.error === 'invalid_data') {
				this.error = 'Données incorrectes';
			} else {
				this.error = 'Erreur inconnue';
			}
		}
	}
}