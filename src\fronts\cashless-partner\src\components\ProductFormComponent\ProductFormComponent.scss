.price-warning {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 15px;
    border-radius: 8px;
    background: #FEF0C7;
    color: #F79009;
    font-weight: bold;
    font-size: 14px;
}

.logo-input {
    display: grid;
    grid-template-columns: 1fr 90px;
    grid-gap: 10px;

    .icon-bank-button {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        gap: 5px;
        border-radius: 8px;
        border: 1px solid #D8D8D8;
        padding: 10px;
        cursor: pointer;

        .icons {
            display: flex;
            gap: 5px;
        }

        &:hover {
            background: var(--secondary-hover-color);
        }

        span {
            font-size: 13px;
            text-align: center;
        }
    }

    .icon-bank-dimmer {
        position: fixed;
        z-index: 9998;
        inset: 0;
    }

    .icon-bank {
        position: fixed;
        z-index: 9999;

        @media (max-width: 900px) {
            left: 10px;
            right: 10px;
            bottom: 10px;
        }
    }
}