<script lang="ts" src="./CaisseApSettingsFormComponent.ts">
</script>

<style lang="sass" scoped>
@import './CaisseApSettingsFormComponent.scss'
</style>

<template>
    <div class="caisse-ap-settings-form-component">
        <div class="form">
            <div class="input-group">
                <label for="title"> Nom de l'appareil </label>
                <div class="ui input">
                    <input v-model="settings.name" type="text" placeholder="Nom de l'appareil" />
                </div>
            </div>

            <div class="input-group">
                <label for="title"> IP du TPE </label>
                <div class="ui input">
                    <input v-model="settings.ip" type="text" placeholder="IP du TPE" />
                </div>
            </div>

            <div class="input-group">
                <label for="title"> Port du TPE </label>
                <div class="ui input">
                    <input v-model.number="settings.port" type="text" placeholder="Port du TPE" />
                </div>
            </div>

            <div class="buttons">
                <button class="tertiary button" :class="{loading: connectionTest === 'LOADING', disabled: connectionTest === 'LOADING'}" @click="testConnection()">
                    <i class="fa-regular fa-signal-stream"></i>
                    Tester la connection
                </button>
            </div>

            <div class="form-error" v-if="connectionTest === 'ERROR'">
                <i class="fa-solid fa-exclamation-circle"></i>
                <div class="details">
                    <span class="title"> Connexion impossible </span>
                    <span class="description"> Il semble que les informations fournies ne permettent pas de se connecter au TPE </span>
                </div>
            </div>

            <div class="form-success" v-if="connectionTest === 'SUCCESS'">
                <i class="fa-solid fa-circle-check"></i>
                <div class="details">
                    <span class="title"> Connexion réussie </span>
                    <span class="description"> Les informations ont bien permis de se connecter au TPE </span>
                </div>
            </div>

            <div class="buttons">
                <button class="ui white button" @click="close()">
                    Annuler
                </button>

                <button v-if="editing" class="ui red button" @click="deleteSettings()">
                    Supprimer
                </button>

                <button class="ui black button" @click="save()">
                    <i class="fa-regular fa-check" style="margin-top: 3px"></i>
                    Appliquer les paramètres
                </button>
            </div>
        </div>
    </div>
</template>