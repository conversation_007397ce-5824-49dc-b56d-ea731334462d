import {Component, Prop, Vue} from "vue-facing-decorator";
import {LocalOrder} from "../../model/LocalOrder";
import {UuidUtils} from "@groupk/horizon2-core";
import {OrderApiOut} from "@groupk/mastodon-core";

@Component({
    emits: ['close']
})
export default class OrderDebugModalComponent extends Vue {
    @Prop({required: true}) localOrder!: LocalOrder|OrderApiOut;

    getFormattedUid() {
        if(this.localOrder instanceof LocalOrder) return UuidUtils.visualToScoped(this.localOrder.order.uid);
        else return UuidUtils.visualToScoped(this.localOrder.uid);
    }

    getLastLocalUpdateDate(): string {
        if(this.localOrder instanceof LocalOrder) return new Date(this.localOrder.lastLocalUpdateDate).toISOString();
        else return new Date(this.localOrder.creationDatetime).toISOString();
    }

    close() {
        this.$emit('close');
    }
}