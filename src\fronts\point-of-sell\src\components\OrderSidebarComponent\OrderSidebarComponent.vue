<script lang="ts" src="./OrderSidebarComponent.ts">
</script>

<style lang="sass">
@import './OrderSidebarComponent.scss'
</style>

<template>
    <div v-if="!posState.currentOrder" class="empty-order-sidebar-component">
        <i class="fa-regular fa-cart-plus"></i>
        Cliquez sur un produit pour créer une commande
    </div>
    <div v-else class="order-sidebar-component" :class="{'performance-mode': posProfile.performanceMode}">
        <div class="quick-actions">
            <div class="small action mobile-only" @click="close()">
                <i class="fa-regular fa-chevron-down"></i>
            </div>
            <div class="action mobile-hidden" @click="posState.currentOrder = null">
                <i class="fa-regular fa-hourglass-clock"></i>
            </div>
            <div class="action" @click="showPayments = true" v-if="posState.currentOrder.order.transferredTo === undefined">
                <span class="number"> {{ posState.currentOrder.order.payments.length }} </span>
                Paiement{{ posState.currentOrder.order.payments.length > 1 ? 's' : '' }}
            </div>
            <div class="action" @click="OrderUtils.printReceipt(posState.currentOrder)" v-if="posState.currentOrder.order.transferredTo === undefined">
                Reçu
            </div>

            <dropdown-button
                v-if="posState.currentOrder.order.transferredTo === undefined"
                button-class="action"
                icon="fa-regular fa-ellipsis-vertical"
                :touch-compatible="true"
                :icon-only="true"
                alignment="RIGHT"
                :actions="[{
                    title: '',
                    actions: OrderUtils.getOrderDropdownActions(posState.currentOrder)
                }]"
                @clicked="dropdownClicked($event)"
            ></dropdown-button>
        </div>

        <div class="transferred" v-if="posState.currentOrder.order.transferredTo !== undefined">
            <span class="title"> Commande envoyée sur Reservit </span>
            <span class="description"> La commande n'est plus modifiable car elle a été envoyée sur Reservit </span>
        </div>


        <div class="dropdowns" v-if="posState.isDiningEnabled() && posState.currentOrder.order.transferredTo === undefined">
<!--            <div class="small action mobile-only" @click="close()">-->
<!--                <i class="fa-regular fa-chevron-down"></i>-->
<!--            </div>-->
<!--            <div class="action mobile-hidden" @click="posState.currentOrder = null">-->
<!--                <i class="fa-regular fa-hourglass-clock"></i>-->
<!--            </div>-->

            <div class="icon input-group">
                <i class="fa-regular fa-hashtag"></i>

                <dropdown
                    :values="getTableDropdownValues()"
                    :default-selected="posState.currentOrder.diningExtra.tableUid ?? null"
                    placeholder="Table"
                    @update="posState.currentOrder.diningExtra.tableUid = $event; saveAndResync(posState.currentOrder)"
                ></dropdown>
            </div>

            <div class="icon input-group">
                <i class="fa-regular fa-fork-knife"></i>

                <dropdown
                    :values="getGuestCountDropdownValues()"
                    :default-selected="posState.currentOrder.diningExtra.guestCount ?? undefined"
                    placeholder="Couverts"
                    @update="posState.currentOrder.diningExtra.guestCount = $event; saveAndResync(posState.currentOrder)"
                ></dropdown>
            </div>
        </div>

        <div class="group-by" v-if="posState.isDiningEnabled() && posState.currentOrder.order.transferredTo === undefined">
            <div class="button-group">
                <button :class="{selected: posState.orderGroupBy === 'PRODUCT'}" @click="posState.orderGroupBy = 'PRODUCT'">
                    Par produit
                </button>
                <button :class="{selected: posState.orderGroupBy === 'STEP'}" @click="posState.orderGroupBy = 'STEP'">
                    Par étape
                </button>
            </div>
        </div>

        <div class="header">
            <span class="title"> Commande </span>
            <span class="action" @click="cancelAllOrder()" v-if="posState.currentOrder.order.transferredTo === undefined"> Tout supprimer </span>
        </div>

        <div class="purchases" v-if="posState.orderGroupBy === 'PRODUCT' || posState.currentOrder.order.transferredTo !== undefined">
            <template v-for="purchase in posState.currentOrder.order.purchases">
                <div
                    class="purchase"
                    :data-testid="purchase.uid"
                    :class="{discounted: posState.getPurchaseDiscount(purchase) !== null}"
                    v-if="posState.getPurchaseQuantity(purchase) > 0"
                >
                    <div class="top">
                        <div class="quantity"> {{ posState.getPurchaseQuantity(purchase) }} </div>
                        <div class="data">
                            <span class="name"> {{ $filters.Length(posState.orderExecutorModel.requireProductRevision(purchase.productRevisionUid).name, 35) }} </span>

                            <div
                                class="product-composition"
                                v-if="posState.orderExecutorModel.requireProductRevision(purchase.productRevisionUid).type === null && ProductUtils.doesProductNeedManualConfiguration(posState.orderExecutorModel.requireProductWithRevision(purchase.productRevisionUid))"
                            >
                                <span v-for="(purchaseItem, index) in posState.currentOrder.getPurchaseItemsRecursive(purchase)">
                                    <template v-if="purchaseItem.productRevisionUid !== purchase.productRevisionUid">
<!--                                                    x{{ purchaseItem.item.quantity }}-->
                                        {{ posState.orderExecutorModel.requireProductRevision(purchaseItem.productRevisionUid).name }}
                                    </template>
                                </span>
                            </div>

                            <span class="total">
                            <span class="price">{{ $filters.Money(posState.getPurchaseTotals(purchase).withTaxes) }}</span>

                            <span v-if="posState.getPurchaseDiscount(purchase)">
                                 &#8239;
                                {{ $filters.Money(getOrderTotals.purchases.perRootPurchase[purchase.uid].withTaxes)}}
                            </span>
                        </span>
                        </div>
                        <div
                            class="remove"
                            :data-testid="`remove-${purchase.uid}`"
                            @click="removeOneFromPurchase(purchase)" v-if="posState.currentOrder.order.transferredTo === undefined"
                        >
                           <i class="fa-regular fa-xmark"></i>
                        </div>
                    </div>
                </div>
            </template>
        </div>

        <div class="order-steps" v-else-if="posState.orderGroupBy === 'STEP'">
            <div class="order-step" :class="{selected: step === posState.currentOrder.currentStep}" v-for="step of posState.currentOrder.getSteps()" @click="posState.currentOrder.currentStep = step">
                <div class="head">
                    <span class="title">
                        {{ step === 0 ? 'Direct' : ('Suite ' + step) }}
                    </span>
                    <dropdown-button
                        class="custom-dropdown-button"
                        icon="fa-regular fa-ellipsis-v"
                        button-class="order-actions"
                        :touch-compatible="true"
                        :icon-only="true"
                        alignment="RIGHT"
                        :actions="[{
                            title: '',
                            actions: getOrderStepDescriptorDropdownValues()
                        }]"
                        @clicked="$event.id !== 'select-step' ? OrderUtils.addPurchaseStatus($event.id, step) : posState.currentOrder.currentStep = step"
                    ></dropdown-button>
                </div>

                <div class="purchases">
                    <template v-for="purchase in posState.currentOrder.order.purchases">
                        <template v-for="purchaseItem in posState.currentOrder.getPurchaseItemsRecursive(purchase)">
                            <div
                                class="purchase"
                                :data-testid="purchaseItem.item.uid"
                                v-if="!posState.orderExecutorModel.isPurchaseItemCanceled(purchaseItem.item) && purchaseItem.item.quantity > 0 && posState.currentOrder.diningExtra.purchaseItemSteps.find((piStep) => piStep.purchaseItemUid === purchaseItem.item.uid && piStep.step === step)"
                            >
                                <div class="top">
                                    <div class="quantity"> {{ purchaseItem.item.quantity }} </div>
                                    <div class="data">
                                        <span class="parent" v-if="posState.orderExecutorModel.requireProductRevision(purchase.productRevisionUid).type === 'FM'">
                                            {{ posState.orderExecutorModel.requireProductRevision(purchase.productRevisionUid).name }}
                                        </span>
                                        <span class="name"> {{ $filters.Length(posState.orderExecutorModel.requireProductRevision(purchaseItem.productRevisionUid).name, 35) }} </span>

                                        <div
                                            class="product-composition"
                                            v-if="posState.orderExecutorModel.requireProductRevision(purchase.productRevisionUid).type === null && ProductUtils.doesProductNeedManualConfiguration(posState.orderExecutorModel.requireProductWithRevision(purchase.productRevisionUid))"
                                        >
                                            <span v-for="(purchaseItem, index) in posState.currentOrder.getPurchaseItemsRecursive(purchase)">
                                                <template v-if="purchaseItem.productRevisionUid !== purchase.productRevisionUid">
<!--                                                    x{{ purchaseItem.item.quantity }}-->
                                                    {{ posState.orderExecutorModel.requireProductRevision(purchaseItem.productRevisionUid).name }}
                                                </template>
                                            </span>
                                        </div>

                                    </div>

                                    <div class="status-image-container">
                                        <img
                                            class="status-image"
                                            v-if="
                                            purchaseItem.item.statusUpdates[purchaseItem.item.statusUpdates.length - 1].status === 'S' &&
                                            posState.orderExecutorModel.requireStatus(purchaseItem.item.statusUpdates[purchaseItem.item.statusUpdates.length - 1].statusUid).iconUploadUid
                                        "
                                            :src="getUploadImageUrl(requireStatusIconUploadUid(posState.orderExecutorModel.requireStatus(purchaseItem.item.statusUpdates[purchaseItem.item.statusUpdates.length - 1].statusUid))) "
                                        />

                                        <span class="count" v-if="countLastStatusUpdates(purchaseItem.item) > 1"> x{{ countLastStatusUpdates(purchaseItem.item) }} </span>
                                    </div>

                                    <div
                                        class="remove"
                                        :data-testid="`remove-${purchaseItem.item.uid}`"
                                        @click="removePurchaseItem(purchase, purchaseItem.item)" v-if="posState.currentOrder.order.transferredTo === undefined"
                                    >
                                        <i class="fa-regular fa-xmark"></i>
                                    </div>

                                    <dropdown-button
                                        v-if="posState.currentOrder.order.transferredTo === undefined"
                                        class="custom-dropdown-button"
                                        icon="fa-regular fa-ellipsis-v"
                                        button-class="order-actions"
                                        :touch-compatible="true"
                                        :icon-only="true"
                                        alignment="RIGHT"
                                        :actions="[{
                                        title: '',
                                        actions: getPurchaseItemDescriptorDropdownValues(purchase, purchaseItem.item)
                                    }]"
                                        @clicked="purchaseItemActionClicked($event, step, purchase, purchaseItem.item)"
                                    ></dropdown-button>
                                </div>

                                <div class="bottom" v-if="purchaseItem.item.metadataList.length > 0">
                                    <div class="metadata" v-for="metadata of purchaseItem.item.metadataList">
                                        <span class="name">
                                            {{ posState.requireMetadataDescriptorWithUid(metadata.descriptorUid).descriptorTemplate.name }} :
                                        </span>

                                        {{ $filters.metadataValue(metadata.value) }}
                                    </div>
                                </div>
                            </div>
                        </template>
                    </template>
                </div>
            </div>

            <div class="buttons">
                <button class="grey button" @click="posState.currentOrder.currentStep++; saveAndResync(posState.currentOrder)">
                    <i class="fa-regular fa-plus"></i>
                    Ajouter une suite
                </button>
            </div>
        </div>

        <div class="summary" v-if="!posState.isDiningEnabled() && posState.currentOrder.order.transferredTo === undefined">
            <div class="separator"></div>

            <div class="line bold">
                <span> Total </span>
                <span> {{ $filters.Money(getOrderTotals.purchases.withTaxesBeforeDiscount) }} </span>
            </div>
            <div class="line" v-if="orderTotalDiscounts > 0">
                <span> Réductions </span>
                <span> {{ $filters.Money(0 - orderTotalDiscounts) }} </span>
            </div>
            <div class="line">
                <span> Payé </span>
                <span> {{ $filters.Money(0 - getOrderTotals.payments.payed) }} </span>
            </div>
            <div class="line bold last">
                <span> Reste à payer </span>
                <span> {{ $filters.Money(getOrderTotals.leftToPay) }} </span>
            </div>
        </div>

        <div class="payment" v-if="posState.currentOrder.order.transferredTo === undefined">
            <div class="discount" @click="showDiscountManager = true" v-if="posProfile.allowPurchaseDiscounts">
                <i class="fa-regular fa-badge-percent"></i>
                Ajouter une réduction
            </div>
            <div class="quick-pays" v-if="posProfile.displayQuickPay">
                <button
                    v-if="posState.pointOfSale.paymentMethodsUid[0]"
                    class="quick-pay black"
                    :class="{disabled: getOrderTotals.leftToPay <= 0}"
                    @click="quickPay(posState.requirePaymentMethodWithUid(posState.pointOfSale.paymentMethodsUid[0]))"
                >
                    {{ posState.requirePaymentMethodWithUid(posState.pointOfSale.paymentMethodsUid[0]).name }}
                </button>
                <button
                    v-if="posState.pointOfSale.paymentMethodsUid[1]"
                    class="quick-pay white"
                    :class="{disabled: getOrderTotals.leftToPay <= 0}"
                    @click="quickPay(posState.requirePaymentMethodWithUid(posState.pointOfSale.paymentMethodsUid[1]))"
                >
                    {{ posState.requirePaymentMethodWithUid(posState.pointOfSale.paymentMethodsUid[1]).name }}
                </button>
            </div>
            <div class="custom-pay" v-if="getOrderTotals.leftToPay <= 0" @click="posState.currentOrder = null; close();">
                Fermer
            </div>
            <div v-else class="custom-pay" :class="{disabled: getOrderTotals.leftToPay <= 0}" @click="showAdvancedPaymentInterface = true">
                Paiement
                <span class="small"> reste {{ $filters.Money(getOrderTotals.leftToPay) }} </span>
            </div>
        </div>

        <div class="discount-manager" :class="{displayed: showDiscountManager}">
            <order-sidebar-discount
                v-if="posState.currentOrder"
                :local-order="posState.currentOrder"
                :pos-state="posState"
                @close="showDiscountManager = false"
            ></order-sidebar-discount>
        </div>

        <div class="discount-manager" :class="{displayed: showPayments}">
            <order-sidebar-payments
                v-if="posState.currentOrder"
                :local-order="posState.currentOrder"
                :pos-state="posState"
                @close="showPayments = false"
            ></order-sidebar-payments>
        </div>

        <div class="keypad-dimmer" :class="{displayed: showKeypad||showDiscountManager}" @click="showKeypad = false"></div>
        <div class="hiding-keypad" :class="{opened: showKeypad}">
            <div class="payment-methods">
                <div
                    v-for="methodUid of posState.pointOfSale.paymentMethodsUid"
                    class="payment-method"
                    :class="{selected: selectedPaymentMethod && selectedPaymentMethod.uid === methodUid}"
                    @click="selectedPaymentMethod = posState.requirePaymentMethodWithUid(methodUid)"
                >
                    <i v-if="selectedPaymentMethod && selectedPaymentMethod.uid === methodUid" class="fa-regular fa-circle-dot"></i>
                    <i v-else class="fa-regular fa-circle"></i>
                    <span class="name"> {{ $filters.Length(posState.requirePaymentMethodWithUid(methodUid).name, 8) }} </span>
                </div>
            </div>
            <keypad @clicked-key="updatePaymentAmount($event)">
                <template v-slot:display>
                    <div class="display">
                        <span class="amount" :class="{red: currentPaymentAmount > getOrderTotals.leftToPay}">
                            {{ $filters.Money(currentPaymentAmount) }}
                        </span>
                        <span class="overpaid" v-if="willPaymentBeAutomatic && currentPaymentAmount - getOrderTotals.leftToPay > 0">
                            A rendre : {{ $filters.Money(currentPaymentAmount - getOrderTotals.leftToPay) }}
                        </span>
                        <span class="left-to-pay">
                            Reste à payer : {{ $filters.Money(getOrderTotals.leftToPay) }}
                        </span>
                    </div>
                </template>
            </keypad>
            <div class="actions">
                <div class="cancel" @click="showKeypad = false">
                    Annuler
                </div>
                <div class="pay" :class="{disabled: (!willPaymentBeAutomatic && currentPaymentAmount > getOrderTotals.leftToPay) || currentPaymentAmount === 0}" @click="pay()">
                    Payer
                    <span v-if="selectedPaymentMethod" class="small"> en {{ $filters.Length(selectedPaymentMethod.name, 12) }} </span>
                </div>
            </div>
        </div>

        <div class="right-modal-dimmer" v-if="showAdvancedPaymentInterface"></div>
        <right-modal
            :bottom-button-text="'Fermer'"
            :stacked="showCustomerBind"
            v-if="false"
            @bottom-button-clicked="generatedTickets = false"
        >
            <div class="generated-tickets">
                <div class="head">
                    <span class="title"> Billets générés </span>
                    <span class="description"> Liste des billets générés par la commande </span>
                </div>

                <div class="buttons">
                    <button class="white button" v-if="selectMultipleTickets"> Tout </button>
                    <button class="button" :class="{white: !selectMultipleTickets}" @click="selectMultipleTickets = !selectMultipleTickets">
                        <i v-if="!selectMultipleTickets" class="fa-regular fa-square"></i>
                        <i v-else class="fa-regular fa-square-check"></i>
                        Selection multiple
                    </button>
                </div>

                <div class="tickets">
                    <div class="ticket" v-for="i in 3">
                        <div v-if="selectMultipleTickets">
                            <i class="fa-regular fa-square"></i>
                        </div>

                        <div class="left">
                            <span class="name"> Billet Enfant </span>
                            <span class="subtitle"> Non relié </span>
                        </div>

                        <div class="actions">
                            <div class="action">
                                <i class="fa-regular fa-print"></i>
                            </div>
                            <div class="action" @click="showCustomerBind = true">
                                <i class="fa-regular fa-user-circle"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </right-modal>

        <customers-list v-if="showCustomerBind" @close="showCustomerBind = false"></customers-list>

        <payment
            v-if="pendingPaymentData"
            :pos-state="posState"
            :pending-payment-data="pendingPaymentData"
            @close="closePaymentModal()"
        ></payment>

        <order-advanced-payment-interface
            v-if="showAdvancedPaymentInterface"
            :pos-state="posState"
            @close="showAdvancedPaymentInterface = false; closePaymentModal()"
        ></order-advanced-payment-interface>

        <metadata-descriptor-form
            v-if="showMetadataDescriptorModal"
            :title="showMetadataDescriptorModal.descriptor.descriptorTemplate.name"
            :metadata-descriptor="showMetadataDescriptorModal.descriptor"
            :default-value="showMetadataDescriptorModal.currentValue"
            @validated="validatedMetadataDescriptor($event)"
            @close="showMetadataDescriptorModal = null"
        ></metadata-descriptor-form>
    </div>
</template>
