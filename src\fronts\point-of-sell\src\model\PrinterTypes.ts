import {Toast} from "../components/ToastManagerComponent/ToastManagerComponent";
import {PrinterBrand, PrinterConfig} from "../printApiSdk/PrinterHelper";
import {PosPrinter} from "../repositories/PrinterRepository";
import {AutoWired, Uuid} from "@groupk/horizon2-core";
import {UsbDeviceDescriptor} from "@groupk/native-bridge";
import {AppBus} from "../config/AppBus";

const NoPrinterToast: Toast = {
	title: 'Aucune imprimante sélectionnée',
	description: 'Veuillez sélectionner une imprimante dans les paramètres afin d’imprimer',
	color: 'red',
	closable: true,
	duration: 4000,
	action: {
		name: 'Paramètres',
		icon: 'fa-regular fa-cog',
		page: 'settings'
	}
}

export class PrinterConfigData {
	printerId: string;
	printerType: PrinterType;
	loadedMediaWidth: string = '';
	density: string = '';
	autoPrint: boolean = false;

	constructor(printerId: string, printerType: PrinterType) {
		this.printerId = printerId;
		this.printerType = printerType;
	}

	toPrinterConfig(): PrinterConfig {
		return {
			loadedMediaWidth: parseInt(this.loadedMediaWidth, 10),
			density: parseInt(this.density, 10),
			autoPrint: this.autoPrint
		}
	}

	fillFromConfig(config: PrinterConfig) {
		this.loadedMediaWidth = config.loadedMediaWidth+'';
		this.density = config.density+'';
		this.autoPrint = config.autoPrint??false;
	}
}

export class TcpPrinter implements PosPrinter {
	uid: Uuid;
	ip: string;
	port: string;
	brand: PrinterBrand;
	name: string;

	constructor(uid: Uuid, ip: string, port: string, brand: PrinterBrand, name: string) {
		this.uid = uid;
		this.ip = ip;
		this.port = port;
		this.brand = brand;
		this.name = name;
	}

	getId(): string {
		return this.uid;
	}

	getBrand(): PrinterBrand {
		return this.brand;
	}

	getType(): PrinterType {
		return PrinterType.TCP
	}
}

export class UsbPrinter implements PosPrinter {
	usbDevice: UsbDeviceDescriptor;

	@AutoWired(AppBus) accessor appBus!: AppBus;

	constructor(usbDevice: UsbDeviceDescriptor) {
		this.usbDevice = usbDevice;
	}

	getId(): string {
		if (!this.usbDevice.serialNumber) {
			this.appBus.emit('displayToast', NoPrinterToast);
			throw new Error('device_is_not_connected');
		}
		return this.usbDevice.serialNumber;
	}

	getBrand(): PrinterBrand {
		return this.usbDevice.manufacturerName as PrinterBrand;
	}

	getType(): PrinterType {
		return PrinterType.USB
	}
}

export enum PrinterType {
	USB = 'USB',
	TCP = 'TCP'
}

export type SelectedPrinter = {
	type: PrinterType;
	id: string;
}