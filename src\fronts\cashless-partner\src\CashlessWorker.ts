import {AppBus} from "./config/AppBus";
import {AutoWired, VisualScopedUuid} from "@groupk/horizon2-core";
import {JsonRpcWsClientContractor} from "@groupk/horizon2-front";
import {WsAuthTokenApiIn} from "@groupk/mastodon-core";
import {MastodonJsonRpcNetworkContract} from "@groupk/mastodon-core";
import {CashlessJsonRpcTransactionContract} from "@groupk/mastodon-core";
import {UuidScopeEstablishment} from "@groupk/mastodon-core";
import {MainConfig} from "../../../shared/MainConfig";
import {AuthStateModel} from "../../../shared/AuthStateModel";

export class CashlessWorker {
	private connection: JsonRpcWsClientContractor;
	private nextReconnectTimeout: number = 0;

	@AutoWired(MainConfig) private accessor config !: MainConfig;
	@AutoWired(AppBus) private accessor appBus!: AppBus;
	@AutoWired(AuthStateModel) private accessor authCenter!: AuthStateModel;

	constructor(establishmentUid: VisualScopedUuid<UuidScopeEstablishment>) {
		this.connection = new JsonRpcWsClientContractor(this.config.configuration.remoteWsEndpoint);

		this.connection.on('close', async () => {
			console.log('DISCONNECTED FROM REMOTE');
			this.appBus.emit('disconnectedFromRemote', undefined);
			this.scheduleReconnect();
		});
		this.connection.on('connected', async () => {
			console.log('RECONNECT');

			const token = this.authCenter.getStateSync();
			if (token === null) {
				this.connection.close();
				return;
			}

			const mastodonContract = this.connection.getForContract(MastodonJsonRpcNetworkContract);
			const authResult = await mastodonContract.request('mastodon_auth',
				new WsAuthTokenApiIn(token.token, establishmentUid)
			);
			if (authResult.isError()) {
				// AppState.getInstance().setAccessToken(null);
				this.connection.close();
				return;
			}

			const cashlessTransactionContract = this.connection.getForContract(CashlessJsonRpcTransactionContract);
			cashlessTransactionContract.onNotify('cashless_transaction_updated', (transaction)=>{
				console.log('new tx', transaction);
				this.appBus.emit('transactionReceived', transaction);
			});

			this.appBus.emit('connectedToRemote', undefined);
		});


	}

	start() {
		this.scheduleReconnect(0);
	}

	scheduleReconnect(delay: number = 5 * 1000) {
		if (this.nextReconnectTimeout === 0 && this.authCenter.getStateSync() !== null && !this.isConnected) {
			this.nextReconnectTimeout = window.setTimeout(async () => {
				this.nextReconnectTimeout = 0;
				this.connection.connect().catch(() => {
					this.scheduleReconnect();
				});
			}, delay);
		}
	}

	get isConnected() {
		return this.connection.isConnected;
	}
}