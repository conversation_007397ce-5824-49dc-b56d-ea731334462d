<script lang="ts" src="./refundRequests.ts">
</script>

<style lang="sass">
@use './refundRequests.scss' as *
</style>

<template>
  <div id="refund-requests-page">
    <layout :drawer-opened="selectedRefund">
      <template v-slot:content>
        <content-header
            :parameters="headerParameters"
        ></content-header>

        <div class="page-content">
          <div class="loading-container" v-if="loading">
            <div class="loader"></div>
          </div>

          <div class="table-scroll-wrapper" v-else>

            <div class="top-buttons">
              <button class="red button" v-if="getDuplicatedRefunds().length > 0 && !errorMode"
                      @click="errorMode = true">
                <i class="fa-regular fa-exclamation-triangle"></i>
                Gérer les doublons
              </button>
              <button class="grey button" v-else-if="getDuplicatedRefunds().length > 0 && errorMode"
                      @click="errorMode = false">
                Voir toutes les données
              </button>
            </div>

            <table class="data-table">
              <thead>
              <tr>
                <td> Utilisateur</td>
                <td> Puce</td>
                <!--                                <td> Tél </td>-->
                <td> IBAN</td>
                <td> État</td>
                <td class="no-break"> Montant remboursé</td>
                <td class="no-break"> Solde restant </td>
                <td></td>
              </tr>
              </thead>
              <tbody>
              <tr v-if="refunds.length === 0">
                <td class="empty" colspan="100%"> Aucune demande de remboursement</td>
              </tr>
              <tr
                  v-for="refund of errorMode ? getDuplicatedRefunds() : refunds"
                  :class="{selected: selectedRefund && selectedRefund.uid === refund.uid}"
                  @click="toggleSelectedRefund(refund)"
              >
                <td>
                  <div class="ticket-data">
                    <div class="ticket-icon">
                      {{ refund.lastname[0] }}{{ refund.firstname[0] }}
                    </div>

                    <div class="ticket-name">
                      {{ refund.lastname }}
                      {{ refund.firstname }}
                      <span class="ticket-description"> {{ refund.email }} </span>
                    </div>
                  </div>
                </td>
                <td class="no-break">
                  {{ $filters.Chip(refund.chipVisualId) }}
                </td>
                <!--                                <td class="no-break">-->
                <!--                                    <i class="fa-solid fa-phone fa-fw"></i>-->
                <!--                                    {{ refund.phoneNumber }}-->
                <!--                                </td>-->
                <td>
                  {{ formatIban(refund.iban) }}
                </td>
                <td>
                  <div class="label no-break" v-if="refund.plannedError===null"
                       :class="{green: refund.status === 'SUCCESS', grey: refund.status === 'PENDING', red: refund.status === 'ERROR'}">
                    {{ translateRefundStatus(refund.status) }}
                  </div>
                  <div class="label no-break red" v-else>
                    {{ translateRefundStatus(refund.plannedError) }}
                  </div>
                </td>
                <td> {{ refund.plannedAmount ? $filters.Money(refund.plannedAmount) : '-' }}</td>
                <td> {{ refund.plannedDebitedFunds ? $filters.Money(refund.plannedDebitedFunds) : '-' }}</td>
                <td>
                  <dropdown-button
                      button-class="tertiary icon button"
                      icon="fa-regular fa-ellipsis-vertical"
                      @clicked="dropdownClicked(refund, $event)"
                      :alignment="'RIGHT'"
                      :actions="[{
                                            title: '',
                                            actions: [{
                                                id: 'cancel',
                                                name: 'Annuler',
                                                icon: 'fa-regular fa-trash-alt fa-fw',
                                                color: 'red'
                                            }]
                                        }]"
                  ></dropdown-button>
                </td>
              </tr>
              </tbody>
            </table>
          </div>
        </div>

      </template>

      <template v-slot:right>
        <div v-if="!selectedRefund" class="empty-right-panel">
          <img src="../../assets/img/select-hint.svg"/>
          Cliquez sur un remboursement pour <br/> le sélectionner
        </div>
        <div v-else class="selected-refund">
          <div class="close" @click="toggleSelectedRefund(selectedRefund)">
            <i class="fa-regular fa-xmark"></i>
            <span> Fermer </span>
          </div>

          <div class="content">
            <div class="customer">
              <div class="ticket-data">
                <div class="ticket-icon">
                  {{ selectedRefund.lastname[0] }}{{ selectedRefund.firstname[0] }}
                </div>

                <div class="ticket-name">
                  {{ selectedRefund.lastname }}
                  {{ selectedRefund.firstname }}
                  <span class="ticket-description"> {{ selectedRefund.email }} </span>
                </div>
              </div>
            </div>

            <div class="data-grid">
              <div class="column">
                <span class="value">  {{ selectedRefund.phoneNumber }} </span>
                <span class="name"> Numéro de tél. </span>
              </div>

              <div class="column">
                <span class="value">  {{ $filters.Chip(selectedRefund.chipVisualId) }} </span>
                <span class="name"> Numéro de puce </span>
              </div>
            </div>

            <div class="properties-table">
              <div class="row">
                <span class="title"> IBAN </span>
                {{ selectedRefund.iban }}
              </div>
              <div class="row">
                <span class="title"> BIC </span>
                {{ selectedRefund.bic }}
              </div>
              <div class="row">
                <span class="title"> Montant remboursé </span>
                {{ selectedRefund.plannedAmount?$filters.Money(selectedRefund.plannedAmount): '-' }}
              </div>
              <div class="row">
                <span class="title"> Solde restant </span>
                {{ selectedRefund.plannedDebitedFunds?$filters.Money(selectedRefund.plannedDebitedFunds): '-' }}
              </div>
              <div class="row">
                <span class="title"> Montant non éligible à remboursement </span>
                {{ selectedRefund.plannedDebitedFunds && selectedRefund.plannedAmount  ? $filters.Money(selectedRefund.plannedDebitedFunds - selectedRefund.plannedAmount)  : '-' }}
              </div>
              <div class="row">
                <span class="title"> Montant pris de la puce </span>
                <div class="label no-break"
                     :class="{green: selectedRefund.status === 'SUCCESS', grey: selectedRefund.status === 'PENDING', red: selectedRefund.status === 'ERROR'}">
                  {{ translateRefundStatus(selectedRefund.status) }}
                </div>
              </div>
              <div class="row">
                <span class="title"> Transaction réseau en cours
                <span class="subtitle">Si oui, le remboursement sera ignoré.</span>
                </span>
                <div class="label no-break grey">
                  {{ selectedRefund.plannedError==="PENDING_TRANSACTIONS"?"Oui":"Non" }}
                </div>
              </div>
              <div class="row">
                <span class="title"> Code d'erreur </span>
                {{ selectedRefund.errorCode }}
              </div>
            </div>
          </div>
        </div>
      </template>
    </layout>
    <sepa-refund-form
        v-if="showSEPARefundModal"
        :refunds="refunds"
        @close="showSEPARefundModal = false"
        @saved="showSEPARefundModal=false"
    ></sepa-refund-form>
    <toast-manager></toast-manager>
  </div>
</template>