<script lang="ts" src="./links.ts"/>

<style lang="sass">
@use './links.scss' as *
</style>

<template>
    <div id="index-page" class="page">
        <filter-table-layout
            :header-parameters="headerParameters"
            :allowed-filters="allowedFilters"
            :table-columns="tableColumns"
            :filters="filters"
            :drawer-opened="selectedLink !== null"
            :filter-parameters="filterParameters"
            :saved-filters="savedFilters"
            :applied-filters="appliedFilters"
            @filters-changed="searchLinks($event);"
            @changed-column-preferences="saveColumnPreferences($event)"
            @search="search($event)"
            @save-filter="saveFilter($event)"
            @select-filter="selectFilter($event)"
            @delete-filter="deleteFilter($event)"
        >
            <template v-slot:actions-left>
                <div class="input-group">
                    <dropdown
                        :placeholder="'Filtrer un label'"
                        :values="labelsDropdownValues"
                        @update="filteredTag = $event;"
                    ></dropdown>
                </div>
            </template>
            <template v-slot:table-data>
                <div class="table-dimmer" v-if="loading">
                    <div class="loader-container">
                        <div class="loader"></div>
                    </div>
                </div>
                <tr class="table-no-data" v-else-if="links.length === 0">
                    <td colspan="100%">
                        <div class="no-data-container">
                            <div class="first-link-button" @click="startCreateLink()">
                                Créez votre premier lien
                            </div>
                        </div>
                    </td>
                </tr>
                <tr v-for="link in filteredByTags" :class="{selected: selectedLink && selectedLink.id === link.id}" @click="selectLink(link)">
                    <td :class="{'mobile-hidden': column.mobileHidden, 'link-td': column.name === 'id', 'no-wrap': column.name === 'creationDate'}" v-for="column in tableColumns.filter((column) => column.displayed)">
                        <div class="link-id-column" v-if="column.name === 'id'">
                            <span> /{{ link.id }} </span>
                            <i v-if="copiedLink === link.id" class="fa-regular fa-check copied"></i>
                            <i v-else class="fa-regular fa-copy" @click.stop="copyLinkId(link)"></i>
                        </div>
                        <div class="target-uri" v-if="column.name === 'targetUri'"> {{ getTargetUrlWithType(link).uri }}</div>
                        <template v-else-if="column.name === 'restrictions'">
                            <template v-if="link.targets.length == 0">
                                -
                            </template>
                            <div class="platforms" v-else>
                                <i v-for="platform in getPlatforms(link)" :class="getTypeIcon(platform)"></i>
                            </div>
                        </template>
                        <template v-else-if="column.name === 'clics'">
                            {{ link.clickCount }}
                        </template>

                        <template v-else-if="column.name === 'creationDate'">
                            {{ $filters.Date(link.creationDatetime) }}
                        </template>

                        <div class="tags" v-else-if="column.name === 'labels'">
                            <div v-if="!link.tags || link.tags.length === 0"> - </div>
                            <div class="small label" v-for="tag of (link.tags || [])"> {{ tag }} </div>
                        </div>
                    </td>
                </tr>
            </template>
            <template v-slot:right>
                <div v-if="selectedLink === null" class="empty-right-panel">
                    <img :src="$assets.selectHint" />
                    Cliquez sur un lien pour <br/> le sélectionner
                </div>
                <div v-else class="selected-link">

                    <div class="close" @click="selectedLink = null">
                        <i class="fa-regular fa-xmark"></i>
                        <span> Fermer</span>
                    </div>

                    <div class="header">
                        <div class="left">
                            <h2>
                                /{{ selectedLink.id }}
                                <i v-if="copiedLink === selectedLink.id" class="fa-regular fa-check copied copy-id"></i>
                                <i v-else class="fa-regular fa-copy copy-id" @click="copyLinkId(selectedLink)"></i>
                            </h2>
                            <span> Lien raccourci </span>
                        </div>


                        <div class="label-area">
                            <div class="labels">
                                <div class="label" v-for="tag of selectedLink.tags"> {{ tag }} </div>
                            </div>
                            <div class="big grey label add-label" @click="showAddLabel = true">
                                <i class="fa-regular fa-plus"></i>
                                Ajouter un label
                            </div>

                            <link-tags-form
                                v-if="selectedLink && showAddLabel"
                                :existing-tags="existingTags"
                                :editing-link="selectedLink"
                                @updated="updateLinks($event)"
                                @close="showAddLabel = false"
                            ></link-tags-form>
                        </div>
                    </div>

                    <div class="qr-code-container">
                        <img class="qrcode" :src="generateQrCode(selectedLink.fullUrl, 0, 0)" alt="qrcode" />
                        <button class="small white button" @click="downloadQr()">
                            <i class="fa-regular fa-arrow-down-to-line"></i>
                            Télécharger
                        </button>
                    </div>

                    <div class="target-groups">
                        <span v-if="selectedLink.targets.length === 0">
                          Ce lien ne redirige vers aucune page
                        </span>

                        <div class="target-groups" v-if="groupedTargetsManager">
                            <div class="target-group" v-for="group in groupedTargetsManager.groupedTargets">
                                <div class="top">
                                    {{ getTargetGroupName(group) }}

                                    <div class="actions">
                                        <div class="action" @click="editTargetGroup(selectedLink.id, group)">
                                            <i class="fa-regular fa-pen-line"></i>
                                        </div>
                                        <div v-if="!isDefaultGroup(group)" class="red action"
                                             @click="deletingGroupId = group.id">
                                            <i class="fa-regular fa-trash-alt"></i>
                                        </div>
                                    </div>
                                </div>
                                <div class="content">
                                    <div class="grid">
                                        <div>
                                            <i class="fa-fw fa-regular fa-earth-americas"></i>
                                            <span class="title"> Par défaut </span>
                                        </div>
                                        <a target="_blank" v-if="group.all" :href="group.all.targetUri">
                                            {{ group.all.targetUri }}
                                        </a>
                                        <div v-else> Aucune redirection </div>

                                        <div>
                                            <i class="fa-fw fa-brands fa-windows"></i>
                                            <span class="title"> Windows</span>
                                        </div>
                                        <a target="_blank" v-if="group.windows" :href="group.windows.targetUri">
                                            {{ group.windows.targetUri }}
                                        </a>
                                        <div v-else> Aucune redirection </div>

                                        <div>
                                            <i class="fa-fw fa-brands fa-android"></i>
                                            <span class="title"> Android </span>
                                        </div>
                                        <a target="_blank" v-if="group.android" :href="group.android.targetUri">
                                            {{ group.android.targetUri }} </a>
                                        <div v-else> Aucune redirection </div>

                                        <div>
                                            <i class="fa-fw fa-brands fa-apple"></i>
                                            <span class="title"> iOS </span>
                                        </div>
                                        <a target="_blank" v-if="group.ios" :href="group.ios.targetUri">
                                            {{ group.ios.targetUri }} </a>
                                        <div v-else> Aucune redirection </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="bottom-actions">
                            <button class="white button" @click="createTargetGroup()">
                                <i class="fa-regular fa-plus"></i>
                                Ajouter des restrictions de date
                            </button>

                            <button class="white button" :class="{loading: loadingResetClicks, disabled: loadingResetClicks}" @click="resetingLinkClicks = true">
                                Réinitialiser le nombre de clics
                            </button>

                            <button class="red button" @click="deletingLink = true">
                                <i class="fa-regular fa-trash-alt"></i>
                                Supprimer le Littl
                            </button>
                        </div>
                    </div>
                </div>
            </template>
        </filter-table-layout>


        <modal-or-drawer :state="creatingLink" @close="creatingLink = false">
            <link-component v-if="creatingLink"
                            :focus="true"
                            :establishment-uid="establishmentUid"
                            @cancel="creatingLink=false"
                            @saved="updateLinks($event);creatingLink = false;"
            ></link-component>
        </modal-or-drawer>

        <modal-or-drawer :state="editingLinkTargets !== null" @close="editingLinkTargets = null">
            <div class="form" v-if="editingLinkTargets">
                <div class="title-group">
                    <h3> Modifier les redirections </h3>
                    <span> Modifier les redirections de votre lien court préservera les statistiques de clic actuelles</span>
                </div>

                <div class="input-group">
                    <label for="title">
                        <i class="fa-fw fa-regular fa-earth-americas"></i>
                        Par défaut
                    </label>
                    <div class="ui input">
                        <input v-model="editingLinkTargets.all" type="text" placeholder="Lien"/>
                    </div>
                </div>

                <div class="input-group">
                    <label for="title">
                        <i class="fa-fw fa-brands fa-windows"></i>
                        Windows
                    </label>
                    <div class="ui input">
                        <input v-model="editingLinkTargets.windows" type="text" placeholder="Lien"/>
                    </div>
                </div>

                <div class="input-group">
                    <label for="title">
                        <i class="fa-fw fa-brands fa-android"></i>
                        Android
                    </label>
                    <div class="ui input">
                        <input v-model="editingLinkTargets.android" type="text" placeholder="Lien"/>
                    </div>
                </div>

                <div class="input-group">
                    <label for="title">
                        <i class="fa-fw fa-brands fa-apple"></i>
                        iOS
                    </label>
                    <div class="ui input">
                        <input v-model="editingLinkTargets.ios" type="text" placeholder="Lien"/>
                    </div>
                </div>

                <div class="date-restrictions">
                    <div class="top">
                        <div>
                            <toggle :default-toggled-value="editingLinkTargets.allowDateRestrictions"
                                    @toggled="editingLinkTargets.allowDateRestrictions = $event"></toggle>
                        </div>

                        <div class="header">
                            <span class="title"> Restreindre les redirections à une plage de dates</span>
                            <div class="subtitle"> Les liens ne seront valides que durant la plage de dates que vous
                                avez choisi
                            </div>
                        </div>
                    </div>
                </div>

                <div class="dates-grid" v-if="editingLinkTargets.allowDateRestrictions">
                    <div class="input-group">
                        <label for="title"> A partir du </label>

                        <input-date-time
                            :date="editingLinkTargets.startDate"
                            :time="editingLinkTargets.startTime"
                            @updated-date="editingLinkTargets.startDate = $event"
                            @updated-time="editingLinkTargets.startTime = $event"
                        ></input-date-time>
                    </div>

                    <div class="input-group">
                        <label for="title"> Jusqu'au </label>
                        <input-date-time
                            :date="editingLinkTargets.endDate"
                            :time="editingLinkTargets.endTime"
                            @updated-date="editingLinkTargets.endDate = $event"
                            @updated-time="editingLinkTargets.endTime = $event"
                        ></input-date-time>
                    </div>
                </div>

                <div class="error" v-if="linkTargetsFormError">
                    {{ linkTargetsFormError }}
                </div>

                <div class="buttons">
                    <button class="ui white button" @click="editingLinkTargets = null">
                        <i class="fa-regular fa-xmark" style="margin-top: 3px"></i>
                        Annuler
                    </button>
                    <button class="ui black button" @click="editTargetsGroup()"
                            :class="{loading: loadingLinkTargets, disabled: loadingLinkTargets}">
                        <i class="fa-regular fa-check" style="margin-top: 3px"></i>
                        Sauvegarder
                    </button>
                </div>
            </div>
        </modal-or-drawer>

        <modal-or-drawer :state="deletingLink" @close="deletingLink = false" :editSecured="false">
            <template v-if="deletingLink && selectedLink">
                <div class="title-group">
                    <h3> Supprimer ce lien ? </h3>
                    <span> Êtes-vous certain de vouloir supprimer ce lien ? Cette opération est irréversible.</span>
                </div>
                <div class="buttons">
                    <button class="ui white button" @click="deletingLink = false">
                        <i class="fa-regular fa-xmark" style="margin-top: 3px"></i>
                        Annuler
                    </button>
                    <div class="ui red button" :class="{loading: loadingDeleteLink, disabled: loadingDeleteLink}"
                         @click="deleteLink(selectedLink.id)">
                        <i class="fa-regular fa-trash-alt" style="margin-top: 3px"></i>
                        Supprimer
                    </div>
                </div>
            </template>
        </modal-or-drawer>

        <modal-or-drawer :state="resetingLinkClicks" @close="resetingLinkClicks = false" :editSecured="false">
            <template v-if="resetingLinkClicks && selectedLink">
                <div class="title-group">
                    <h3> Réinitialiser le nombre de clics de ce lien ? </h3>
                    <span> Êtes-vous certain de vouloir réinitialiser ce lien ? Cette opération est irréversible. </span>
                </div>
                <div class="buttons">
                    <button class="ui white button" @click="resetingLinkClicks = false">
                        <i class="fa-regular fa-xmark" style="margin-top: 3px"></i>
                        Annuler
                    </button>
                    <div class="ui primary button" :class="{loading: loadingResetClicks, disabled: loadingResetClicks}"
                         @click="resetClicks(selectedLink.id)">
                        Réinitialiser
                    </div>
                </div>
            </template>
        </modal-or-drawer>

        <modal-or-drawer :state="deletingGroupId" @close="deletingGroupId = null" :editSecured="false">
            <template v-if="deletingGroupId && selectedLink">
                <div class="title-group">
                    <h3> Supprimer ce groupe de redirections ? </h3>
                    <span> Êtes-vous certain de vouloir supprimer ce groupe ? Cette opération est irréversible.</span>
                </div>
                <div class="buttons">
                    <button class="ui white button" @click="deletingGroupId = null">
                        <i class="fa-regular fa-xmark" style="margin-top: 3px"></i>
                        Annuler
                    </button>
                    <div
                        class="ui red button"
                        :class="{ loading: deletingGroup, disabled: deletingGroup }"
                        @click="deleteGroup(deletingGroupId)"
                    >
                        <i class="fa-regular fa-trash-alt" style="margin-top: 3px"></i>
                        Supprimer
                    </div>
                </div>
            </template>
        </modal-or-drawer>

        <modal-or-drawer :state="showQrCode" @close="showQrCode = false" :editSecured="false">
            <template v-if="showQrCode && selectedLink">
                <div class="title-group">
                    <h3> QR Code vers {{ selectedLink.fullUrl }} </h3>
                    <span> Intégrez ce QR code dans prints puis changez le lien de redirection à tout moment </span>
                </div>

                <div class="content qr">
                    <img class="qrcode" :src="generateQrCode(selectedLink.fullUrl, 0, 0)" alt="qrcode" />
                </div>

                <div class="buttons">
                    <button class="ui button" @click="downloadQr()">
                        <i class="fa-regular fa-download" style="margin-top: 3px"></i>
                        Télécharger
                    </button>
                    <button class="ui white button" @click="showQrCode = false">
                        <i class="fa-regular fa-xmark" style="margin-top: 3px"></i>
                        Fermer
                    </button>
                </div>
            </template>
        </modal-or-drawer>
    </div>
</template>