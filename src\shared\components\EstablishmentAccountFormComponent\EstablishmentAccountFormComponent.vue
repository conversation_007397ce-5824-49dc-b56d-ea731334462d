<script lang="ts" src="./EstablishmentAccountFormComponent.ts">
</script>

<style lang="sass">
@use './EstablishmentAccountFormComponent.scss' as *
</style>

<template>
    <div id="establishment-account-form-component">
        <form-modal-or-drawer
            :state="opened"
            :title="'Ajouter un compte employé'"
            :subtitle="'Création d\'un nouveau compte employé'"
            @close="close()"
        >
            <template v-slot:content>
                <div class="input-group">
                    <label> Prénom de l'employé </label>

                    <div class="fluid input">
                        <input v-model="establishmentAccount.firstname" class="fluid" placeholder="Prénom" />
                    </div>
                </div>

                <div class="input-group">
                    <label> Nom de l'employé </label>

                    <div class="fluid input">
                        <input v-model="establishmentAccount.lastname" class="fluid" placeholder="Nom" />
                    </div>
                </div>

                <div class="form-error" v-if="error">
                    <i class="fa-solid fa-exclamation-circle"></i>
                    <div class="details">
                        <span class="title"> Erreur </span>
                        <span class="description">{{ error }}</span>
                    </div>
                </div>
            </template>

            <template v-slot:buttons>
                <button type="button" class="button" @click="create()">
                    <i class="fa-regular fa-user-plus"></i>
                    Créer le compte
                </button>
            </template>
        </form-modal-or-drawer>
    </div>
</template>