import {Component, Prop, Ref, Vue} from "vue-facing-decorator";
import {SimpleProductApiOut, SimpleProductCategoryApiOut} from "@groupk/mastodon-core";
import SortableArray from "../../SortableArray/SortableArray.vue";
import {AutoWired, Uuid} from "@groupk/horizon2-core";
import {CategoriesRepository} from "../../../../../../shared/repositories/CategoriesRepository";
import {AppBus} from "../../../config/AppBus";
import CategoryFormComponent from "../../CategoryFormComponent/CategoryFormComponent.vue";
import {FormModalOrDrawerComponent} from "@groupk/vue3-interface-sdk";
import ImportProductInCategoryComponent from "../../ImportProductInCategoryComponent/ImportProductInCategoryComponent.vue";
import {AppState} from "../../../../../../shared/AppState";
import {ProfileData} from "../../../../../../shared/mastodonCoreFront/cashless/ProfileData";
import {CategoryData} from "../../../../../../shared/mastodonCoreFront/cashless/CategoryData";

@Component({
	components: {
		'form-modal-or-drawer': FormModalOrDrawerComponent,
		sortable: SortableArray,
		'import-product-in-category': ImportProductInCategoryComponent,
		'category-form': CategoryFormComponent
	},
	emits: ['updated']
})
export default class ProfileCategoriesComponent extends Vue {
	@Prop({required: true}) profile!: ProfileData;
	@Prop({required: true}) categories!: SimpleProductCategoryApiOut[];
	@Prop({required: true}) products!: SimpleProductApiOut[];

	allowedCategories: {
		parent: SimpleProductCategoryApiOut,
		categories: SimpleProductCategoryApiOut[]
	}[] = [];

	selectedCategory: SimpleProductCategoryApiOut|null = null;
	showCategoryModal: boolean = false;
	showAddCategoryToParentModal: boolean = false;
	editingCategory: SimpleProductCategoryApiOut|null = null;
	showImportProductsModal: boolean = false;
	showDeviceEmulator: boolean = false;

	@Ref() importProduct!: ImportProductInCategoryComponent;

	@AutoWired(CategoriesRepository) accessor categoryRepository!: CategoriesRepository;
	@AutoWired(AppState) accessor appState!: AppState;
	@AutoWired(AppBus) accessor appBus!: AppBus;

	mounted() {
		for(let parentCategory of this.parentCategories) {
			const childs: SimpleProductCategoryApiOut[] = [];
			for(let categoryUid of this.profile.allowedProductCategories) {
				try {
					const category = this.requireCategoryWithUid(categoryUid);
					if(category.parent === parentCategory.uid) {
						childs.push(category);
					}
				} catch(err){}
			}
			this.allowedCategories.push({
				parent: parentCategory,
				categories: childs
			});
		}

		if(this.allowedCategories[0] && this.allowedCategories[0].categories[0]) this.selectCategory(this.allowedCategories[0].categories[0]);

		this.appBus.on('point-of-sale-product-clicked', this.addProductToCategory);
		this.appBus.on('point-of-sale-add-filtered-products-clicked', this.addAllFiltered);
	}

	unmounted() {
		this.appBus.off('point-of-sale-product-clicked', this.addProductToCategory);
		this.appBus.off('point-of-sale-add-filtered-products-clicked', this.addAllFiltered);
	}

	get parentCategories() {
		// return this.categories.filter((category) => category.parent === null && this.profile.allowedProductCategories.includes(category.uid));
		return this.categories.filter((category) => category.parent === null);
	}

	getChildCategories(parent: Uuid) {
		return this.categories.filter((category) => category.parent === parent && this.profile.allowedProductCategories.includes(category.uid));
	}

	selectCategory(category: SimpleProductCategoryApiOut) {
		this.selectedCategory = category;
		this.appBus.emit('selectedCategory', category);
	}

	addProductToCategory(data: {product: SimpleProductApiOut, isMobile: boolean}) {
		if(!this.selectedCategory) return;

		const index = this.selectedCategory.productIds.indexOf(data.product.id);
		if (index === -1) {
			this.selectedCategory.productIds.push(data.product.id);

			this.updateCategory();
		} else if(data.isMobile) {
			this.selectedCategory.productIds.splice(index, 1);

			this.updateCategory();
		} else {
			this.appBus.emit('emit-toast', {
				title: 'Ce produit est déjà présent',
				description: 'Le produit ' + data.product.name + ' est déjà présent dans la catégorie',
				duration: 3000,
				type: 'ERROR',
				closable: true
			});
		}
	}

	addAllFiltered(filteredProducts: SimpleProductApiOut[]) {
		if(!this.selectedCategory) return;
		for (let product of filteredProducts) {
			if (!this.selectedCategory.productIds.includes(product.id)) {
				this.selectedCategory.productIds.push(product.id);
			}
		}
		this.updateCategory();
	}

	importProducts() {
		this.importProduct.importProducts();
		this.updateCategory();
		this.showImportProductsModal = false;
	}

	async updateCategory() {
		if(!this.selectedCategory) return;

		const categoryData = new CategoryData(this.selectedCategory);
		const response = await this.categoryRepository.callContract('update', {establishmentUid: this.appState.requireUrlEstablishmentUid(), categoryUid: this.selectedCategory.uid}, categoryData.toApiIn());
		if(!response.isSuccess()) {
			this.appBus.emit('emit-toast', {
				title: 'La sauvegarde a échoué',
				description: 'La sauvegarde du point de vente a échoué',
				duration: 3000,
				type: 'ERROR',
				closable: true
			});
		}
	}

	createdOrUpdatedCategory(createdCategory: SimpleProductCategoryApiOut) {
		this.showCategoryModal = false;
		this.showAddCategoryToParentModal = false;

		let sendUpdate: boolean = false;

		const index = this.categories.findIndex((category) => category.uid === createdCategory.uid);
		if(index === -1) this.categories.push(createdCategory);
		else this.categories.splice(index, 1, createdCategory);

		for(let group of this.allowedCategories) {
			if(createdCategory.parent === group.parent.uid) {
				const index = group.categories.findIndex((category) => category.uid === createdCategory.uid);
				if(index === -1) {
					group.categories.push(createdCategory);
					sendUpdate = true;
				} else group.categories.splice(index, 1, createdCategory);
			}
		}

		if(sendUpdate) this.updateProfileCategories();

		if(this.selectedCategory === null){
			// On sélectionne automatiquement la catégorie qui vient d'être créé si aucune n'est sélectionnée.
			this.selectCategory(createdCategory);
		}

		this.editingCategory = null;
	}

	updateProfileCategories(sendUpdate: boolean = true) {
		let profileCategoryUids: Uuid[] = [];
		for(const allowedCategory of this.allowedCategories) {
			if(allowedCategory.categories.length) {
				profileCategoryUids.push(allowedCategory.parent.uid);
				profileCategoryUids = profileCategoryUids.concat(allowedCategory.categories.map((category) => category.uid));
			}
		}
		this.profile.allowedProductCategories = profileCategoryUids;
		if(this.profile.terminalProduct) this.profile.terminalProduct.allowedProductCategoryList = profileCategoryUids;
		if(sendUpdate) this.$emit('updated');
	}

	requireDebitCategory() {
		const category = this.parentCategories.find((category) => category.name = 'Débit');
		if(!category) throw new Error('debit_category_not_found');
		return category
	}

	deletedCategory(deletedCategory: SimpleProductCategoryApiOut) {
		this.showCategoryModal = false;

		const index = this.categories.findIndex((category) => category.uid === deletedCategory.uid);
		if(index !== -1) this.categories.splice(index, 1);

		for(let group of this.allowedCategories) {
			if(deletedCategory.parent === group.parent.uid) {
				const index = group.categories.findIndex((category) => category.uid === deletedCategory.uid);
				if(index !== -1) group.categories.splice(index, 1);
				this.updateProfileCategories();
			}
		}
		this.editingCategory = null;
	}

	requireCategoryWithUid(categoryUid: Uuid) {
		const category = this.categories.find((category) => category.uid === categoryUid);
		if(!category) throw new Error('missing_category');
		return category;
	}

	requireProductWithUid(productId: number): SimpleProductApiOut {
		const product = this.products.find((product) => product.id === productId);
		if (!product) throw new Error('missing_product');
		return product;
	}

	clickedManageProducts() {
		this.$emit('clicked-manage-products');
	}
}