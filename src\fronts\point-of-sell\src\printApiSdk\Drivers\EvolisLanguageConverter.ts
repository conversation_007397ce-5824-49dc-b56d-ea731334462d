import {ImageUtils} from "../ImageUtils";
import type PrinterLanguageConverter from "./PrinterLanguageConverter";
import type {FormArea} from "../CanvasRendererSDK";
import {PrinterConfig} from "../PrinterHelper";

export default class EvolisLanguageConverter implements PrinterLanguageConverter {
	private debug: boolean = false;
	private debugDump: {
		ribbonC: string;
		ribbonM: string;
		ribbonY: string;
		ribbonK: string;
		ribbonO: string;
		ribbonCDecoded: string;
		ribbonMDecoded: string;
		ribbonYDecoded: string;
		ribbonKDecoded: string;
	} = {
		ribbonC: "",
		ribbonM: "",
		ribbonY: "",
		ribbonK: "",
		ribbonO: "",
		ribbonCDecoded: "",
		ribbonMDecoded: "",
		ribbonYDecoded: "",
		ribbonKDecoded: "",
	};

	toggleDebugger(debug: boolean | null) {
		if (debug === null) this.debug = !this.debug;
		else this.debug = debug;
	}

	getDebugDump() {
		return this.debugDump;
	}

	private resetDebug() {
		this.debugDump = {
			ribbonC: "",
			ribbonM: "",
			ribbonY: "",
			ribbonK: "",
			ribbonO: "",
			ribbonCDecoded: "",
			ribbonMDecoded: "",
			ribbonYDecoded: "",
			ribbonKDecoded: "",
		};
	}

	private outputRibbonToDebug(debugVarName: string, pixels: number[]) {
		let width = 648;
		let height = 1016;
		const canvas = <HTMLCanvasElement>document.createElement("canvas");
		canvas.width = width;
		canvas.height = height;

		const ctx = <CanvasRenderingContext2D>canvas.getContext("2d");
		ctx.imageSmoothingEnabled = false;
		ctx.beginPath();
		ctx.rect(0, 0, 648, 1016);
		ctx.fillStyle = "#ffffff";
		ctx.fill();

		let pixelsBufferRaw = ctx.getImageData(0, 0, 648, 1016);

		// let posDest = 0;
		for (let x = 0; x < width; ++x) {
			for (let y = 0; y < height; ++y) {
				let posDest = (y * width + x) * 4;
				let posSrc = y * width + x;
				/*pixelsBufferRaw.data[pos] = color[pos];
				pixelsBufferRaw.data[pos+1] = color[pos];
				pixelsBufferRaw.data[pos+2] = color[pos];
				pixelsBufferRaw.data[pos+3] = 255;*/
				pixelsBufferRaw.data[posDest] = pixels[posSrc];
				pixelsBufferRaw.data[posDest + 1] = pixels[posSrc];
				pixelsBufferRaw.data[posDest + 2] = pixels[posSrc];
				pixelsBufferRaw.data[posDest + 3] = 255;

				// posDest += 4;
			}
		}

		ctx.putImageData(pixelsBufferRaw, 0, 0);

		(<any>this.debugDump)[debugVarName] = canvas.toDataURL();
	}

	private generateImageFromRibbon(ribbonData: string, outputDebugVar: string) {
		let posFirstSep = ribbonData.indexOf(";");
		let posSecondSep = ribbonData.indexOf(";", posFirstSep + 1);
		let posThirdSep = ribbonData.indexOf(";", posSecondSep + 1);

		console.log("PARSE RIBBON");

		if (posFirstSep === -1 || posSecondSep === -1 || posThirdSep === -1) {
			return;
		}

		let ribbonType = ribbonData.substring(posFirstSep + 1, posSecondSep);
		let precision = parseInt(ribbonData.substring(posSecondSep + 1, posThirdSep));

		let nbBitsPerColor = this.precisionNumberToBitsLength(precision);
		let baseBits = "";

		for (let i = 0; i < nbBitsPerColor; ++i) baseBits += "0";

		console.log(ribbonType + "|" + precision + "=>" + nbBitsPerColor + "=>" + baseBits);

		let bits = "";
		let i = posThirdSep + 1;
		for (; i < ribbonData.length - 1; ++i) {
			bits += ("00000000" + ribbonData.charCodeAt(i).toString(2)).slice(-8);
		}
		console.log("END at:" + i);

		let width = 648;
		let height = 1016;

		let pixels: number[] = [];

		console.log("bits length:" + bits.length);

		let posInBits = 0;
		for (let y = 0; y < height; ++y) {
			for (let x = 0; x < width; ++x) {
				let value = parseInt(bits.substr(posInBits, nbBitsPerColor), 2);

				pixels.push((value / precision) * 255);

				posInBits += nbBitsPerColor;
			}
		}
		console.log("pixels length:" + pixels.length);

		this.outputRibbonToDebug(outputDebugVar, pixels);
	}

	async convertFromBase64(base64Str: string, formAreas?: FormArea[], printerRelayConfig?: PrinterConfig): Promise<number[]> {
		throw new Error('not_implemented');
	}

	generateRibbonData(ribbon: string, precision: number, colorData: number[]) {
		console.debug("Building ribbon Db;" + ribbon + ";" + precision + ";..");

		let bytes = "";
		bytes += "\x1BDb;" + ribbon + ";" + precision + ";";
		bytes += this.convertPixelsToRawRibbonData(precision, colorData);
		bytes += "\x0D";

		return bytes;
	}

	convertPixelsToRawRibbonData(precision: number, colorData: number[]) {
		let bytes = "";

		let currentBits = "";

		let nbBitsPerColor = this.precisionNumberToBitsLength(precision);
		let baseBits = "";

		for (let i = 0; i < nbBitsPerColor; ++i) baseBits += "0";

		console.log("=>" + nbBitsPerColor + "=>" + baseBits + " for data=" + colorData.length);

		let totalBitsAdded = 0;
		for (let color of colorData) {
			let value = Math.max(0, Math.min(precision - 1, Math.round(color * precision)));

			let newBits = (baseBits + value.toString(2)).slice(-nbBitsPerColor);
			currentBits += newBits;

			totalBitsAdded += newBits.length;
			// console.log('from '+color+' to '+value+' with precision '+precision+' => '+newBits);

			// break;
			// optimize with offset
			let eatenBits = 0;
			for (let i = 0; i < currentBits.length; i += 8) {
				let len = Math.min(currentBits.length - i, 8);
				if (len >= 8) {
					eatenBits += len;
					bytes += String.fromCharCode(parseInt(currentBits.substr(i, 8), 2));
				}
			}
			if (eatenBits > 0) {
				currentBits = currentBits.substr(eatenBits);
			}
		}

		console.log("=>total bits" + totalBitsAdded);
		console.log("=>left bits:" + currentBits);

		return bytes;
	}

	precisionNumberToBitsLength(precision: number): number {
		precision--; // "128" is actually 0-127, subtract one
		let ones = 0;
		while (precision > 0) {
			if (precision % 2 != 0) {
				ones++;
			}
			precision = Math.floor(precision / 2);
		}

		return ones;
	}
}
