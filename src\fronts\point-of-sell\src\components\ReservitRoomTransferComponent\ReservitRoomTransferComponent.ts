import {Component, Prop, Vue} from "vue-facing-decorator";
import KeypadComponent, {KeypadKey} from "../KeypadComponent/KeypadComponent.vue";
import {ReservitAppRepository} from "../../../../../shared/repositories/ReservitAppRepository";
import {
    AppReservitApiOut,
    ReservitApi_booking,
    ReservitApi_configuration,
    ReservitSearchBookingApiIn,
    UuidScopeIntegration_app,
    uuidScopeMetadata_descriptor,
    uuidScopeMetadata_metadata
} from "@groupk/mastodon-core";
import {PosState} from "../../model/PosState";
import RightModalComponent from "../RightModalComponent/RightModalComponent.vue";
import {ReservitApi_configuration_room} from "@groupk/mastodon-core";
import {AutoWired, UuidUtils} from "@groupk/horizon2-core";
import {LocalOrder} from "../../model/LocalOrder";
import {LocalOrderRepository} from "../../repositories/LocalOrderRepository";
import {
    MetadataDescriptorType,
    MetadataStringApiOut,
    UuidScopeMetadata_descriptor
} from "@groupk/mastodon-core";

@Component({
    components: {
        'keypad': KeypadComponent,
        'right-modal': RightModalComponent
    },
    emits: ['close', 'select-booking']
})
export default class ReservitRoomTransferComponent extends Vue {
    @Prop({required: true}) posState!: PosState;
    @Prop({required: true}) localOrder!: LocalOrder;

    step: number = 1;
    filteredRooms: ReservitApi_configuration_room[] = [];

    roomNumber: string = '';

    reservit!: {app: AppReservitApiOut, configuration: ReservitApi_configuration|null};
    selectedRoomBookings: {
        room: ReservitApi_configuration_room,
        bookings: ReservitApi_booking[]
    }|null = null;

    loadingBookings: boolean = false;

    @AutoWired(ReservitAppRepository) accessor reservitAppRepository!: ReservitAppRepository;
    @AutoWired(LocalOrderRepository) accessor localOrderRepository!: LocalOrderRepository;

    beforeMount() {
        if(!this.posState.reservit) throw new Error('no_reservit_app');
        this.reservit = this.posState.reservit;

        this.filterRooms();
    }

    searchTimeout: ReturnType<typeof setTimeout>|null = null;
    async search() {
        if (this.searchTimeout) {
            clearTimeout(this.searchTimeout);
            this.searchTimeout = null;
        }

        this.searchTimeout = setTimeout(async () => {
            this.filterRooms();
        }, 500);
    }

    filterRooms() {
        if(!this.reservit.configuration) return [];
        this.filteredRooms = this.reservit.configuration.rooms
            .filter(room => {
                return Object.values(room.names).some(name =>
                    name.name.toLowerCase().includes(this.roomNumber.toLowerCase())
                );
            })
            .sort((a, b) => {
                const nameA = this.getRoomName(a).name.toLowerCase();
                const nameB = this.getRoomName(b).name.toLowerCase();
                return nameA.localeCompare(nameB);
            });
    }

    getRoomName(room: ReservitApi_configuration_room) {
        return room.names.find((name) => name.lang === 'fr') || room.names[0];
    }

    updateRoomNumber(key: KeypadKey) {
        if (key < 10) {
            this.roomNumber = this.roomNumber + key;
        } else {
            if (key === KeypadKey.BACKSPACE) {
                this.roomNumber = this.roomNumber.slice(0, -1);
            } else if (key === KeypadKey.TRASH) {
                this.roomNumber = '';
            }
        }
    }

    async findBookings(room: ReservitApi_configuration_room) {
        this.loadingBookings = true;
        const response = await this.reservitAppRepository.callContract('searchBookingWithRoom',
            {
                establishmentUid: this.posState.establishmentUid,
                appUid: this.reservit.app.uid
            },
            new ReservitSearchBookingApiIn({
                roomId: room.id
            })
        );

        if(response.isSuccess()) {
            this.selectedRoomBookings = {
                room: room,
                bookings: response.success()
            }
        }

        this.loadingBookings = false;
    }

    sendOrderToBooking(booking: ReservitApi_booking): void {
        this.localOrder.order.metadataList.push(new MetadataStringApiOut({
            uid: UuidUtils.randomVisualScopedUUID(uuidScopeMetadata_metadata),
            type: MetadataDescriptorType.STRING,
            descriptorUid: UuidUtils.scopedToVisual<UuidScopeMetadata_descriptor>(this.reservit.app.bookingIdMetadataDescriptorUid, uuidScopeMetadata_descriptor),
            value: booking.bookingId
        }));

        this.localOrder.order.transferredTo = UuidUtils.visualToScoped<UuidScopeIntegration_app>(this.reservit.app.uid);
        this.localOrderRepository.saveAndResync(this.localOrder);
        this.close();
    }

    close() {
        this.$emit('close');
    }

    getInitials(firstName: string|null, lastName: string|null): string {
        const firstInitial = firstName ? firstName.charAt(0).toUpperCase() : '';
        const lastInitial = lastName ? lastName.charAt(0).toUpperCase() : '';
        return `${firstInitial}${lastInitial}`;
    }

    formatDate(dateString: string): string {
        const date = new Date(dateString);
        const day = date.getDate().toString().padStart(2, '0');
        const month = (date.getMonth() + 1).toString().padStart(2, '0');
        const year = date.getFullYear();
        return `${day}/${month}/${year}`;
    }

    getBookingStatus(booking: ReservitApi_booking): string {
        // Get today's date at start of day
        const today = new Date();
        today.setHours(0, 0, 0, 0);

        // Parse booking dates
        const fromDate = new Date(booking.from);
        const toDate = new Date(booking.to);

        // Set times to start of day for proper comparison
        fromDate.setHours(0, 0, 0, 0);
        toDate.setHours(0, 0, 0, 0);

        if (today < fromDate) {
            return 'À venir';
        } else if (today > toDate) {
            return 'Terminé';
        } else {
            return 'En cours';
        }
    }

    getBookingStatusClass(booking: ReservitApi_booking): string {
        const status = this.getBookingStatus(booking);

        switch (status) {
            case 'À venir':
                return 'grey';
            case 'En cours':
                return 'green';
            case 'Terminé':
                return 'red';
            default:
                return '';
        }
    }
}
