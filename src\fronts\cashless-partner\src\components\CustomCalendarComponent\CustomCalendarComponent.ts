import {Component, Prop, Vue} from "vue-facing-decorator";
import DateUtils, {monthNames, Range} from "../../../../../shared/utils/DateUtils";
import {InputDateTimeComponent, InputHourComponent} from "@groupk/vue3-interface-sdk";

export const translations: {[value: string]: string} = {
	M: "matin",
	A: "après-midi",
	N: "nuit",
	MONDAY: "lundi",
	TUESDAY: "mardi",
	WEDNESDAY: "mercredi",
	THURSDAY: "jeudi",
	FRIDAY: "vendredi",
	SATURDAY: "samedi",
	SUNDAY: "dimanche",
	RECURRING: "Récurrente",
	OCCASIONAL: "Occasionelle",
};

@Component({
	components: {
		'hour-input': InputHourComponent,
		'date-time-input': InputDateTimeComponent,
	},
	emits: ["selected-period", "unselect"],
})
export default class CustomCalendarComponent extends Vue {
	monthNames = monthNames;

	@Prop() defaultSelection?: Range[];
	@Prop() defaultYear?: number;
	@Prop() defaultMonth?: number;
	@Prop() unselectable?: boolean;
	@Prop() small?: boolean;
	@Prop() viewType?: "NAVIGATION" | "SCROLL";
	@Prop() dataCallback?: (month: number, year: number) => Promise<Range[]>;
	@Prop() profilePictures?: {[date: string]: string[]};

	@Prop() disabledBefore?: Date;

	data: Range[] = [];
	selectedMonth: number = new Date().getMonth();
	selectedYear: number = new Date().getFullYear();
	loadedMonths: number = 5;

	startTime: string = '00:00';
	endTime: string = '23:59';

	rangeSelection: Range[] = [];

	mounted() {
		if (this.defaultSelection) {
			this.rangeSelection = this.defaultSelection;
			this.startTime = this.defaultSelection[0].start ? DateUtils.formatDateHour(this.defaultSelection[0].start?.toISOString(), ':') : '00:00';
			this.endTime = this.defaultSelection[0].end ? DateUtils.formatDateHour(this.defaultSelection[0].end?.toISOString(), ':') : '23:59';
		}

		if (this.defaultYear) this.selectedYear = this.defaultYear;
		if (this.defaultMonth) this.selectedMonth = this.defaultMonth;

		if (this.dataCallback) {
			if (this.getViewType() === "NAVIGATION") {
				this.dataCallback(this.selectedMonth, this.selectedYear).then((data) => {
					this.data = data;
				});
			} else {
				let month = this.selectedMonth;
				let year = this.selectedYear;
				for (let i = 1; i <= this.loadedMonths; i++) {
					if (this.dataCallback) {
						this.dataCallback(month, year).then((data) => {
							this.addDataToExisting(data);
						});
					}

					if (month === 11) {
						month = 0;
						year++;
					} else {
						month++;
					}
				}
			}
		}
	}

	public getViewType() {
		return this.viewType ?? "NAVIGATION";
	}

	getDisplayedMonth() {
		if (this.getViewType() === "NAVIGATION") {
			return [{month: this.selectedMonth, year: this.selectedYear}];
		} else if (this.getViewType() === "SCROLL") {
			let displayed: {month: number; year: number}[] = [];
			let month = this.selectedMonth;
			let year = this.selectedYear;
			for (let i = 1; i <= this.loadedMonths; i++) {
				displayed.push({
					month: month,
					year: year,
				});

				if (month === 11) {
					month = 0;
					year++;
				} else {
					month++;
				}
			}
			return displayed;
		}
	}

	public getChunkedMonthDays(month: number, year: number) {
		return this.chunkArrayInGroups(DateUtils.getFilledDaysInMonth(month, year), 7);
	}

	chunkArrayInGroups(arr: any[], size: number) {
		let myArray: any[] = [];
		for (let i = 0; i < arr.length; i += size) {
			myArray.push(arr.slice(i, i + size));
		}
		return myArray;
	}

	previousMonth() {
		if (this.selectedMonth === 0) {
			this.selectedYear--;
			this.selectedMonth = 11;
		} else {
			this.selectedMonth--;
		}
		if (this.dataCallback) {
			this.dataCallback(this.selectedMonth, this.selectedYear).then((data) => {
				this.addDataToExisting(data);
			});
		}
	}

	nextMonth() {
		if (this.selectedMonth === 11) {
			this.selectedYear++;
			this.selectedMonth = 0;
		} else {
			this.selectedMonth++;
		}
		if (this.dataCallback) {
			this.dataCallback(this.selectedMonth, this.selectedYear).then((data) => {
				this.addDataToExisting(data);
			});
		}
	}

	addDataToExisting(ranges: Range[]) {
		for (let range of ranges) {
			let isIn = false;
			for (let existingRange of this.data) {
				if (existingRange.start?.getTime() === range.start?.getTime() && existingRange.end?.getTime() === existingRange.end?.getTime()) {
					isIn = true;
					break;
				}
			}
			if (!isIn) {
				this.data.push(range);
			}
		}
	}

	loadMoreMonths() {
		for (let i = 0; i < 5; i++) {
			let currentDate = new Date(this.selectedYear, this.selectedMonth + this.loadedMonths, 1);
			if(this.dataCallback) this.dataCallback(currentDate.getMonth(), currentDate.getFullYear()).then((data) => {
				this.addDataToExisting(data);
			});
			this.loadedMonths++;
		}
	}

	selectDay(date: Date) {
		if (this.unselectable) {
			// Can only select enabled data ranges
			let isDayInEnabledRange = false;
			for (let range of this.data) {
				if (!range.disabled && DateUtils.isDateOverlappingRange(date, range)) {
					isDayInEnabledRange = true;
					break;
				}
			}
			if (!isDayInEnabledRange) {
				return;
			}
		}
		if (this.rangeSelection.length > 1 || this.rangeSelection.length === 0) {
			this.rangeSelection = [{start: null, end: null, color: "default", disabled: false, chained: true}];
		}
		if (this.rangeSelection[0].start === null) {
			// No start, we set start
			this.rangeSelection[0].start = date;

			if(this.rangeSelection[0].start) {
				this.setDateHours(this.rangeSelection[0].start, this.startTime, 0, 0);
			}

			const end = new Date(date);
			this.setDateHours(end, this.endTime, 59, 599);

			this.$emit("selected-period", [{start: date, end: end, color: "default", disabled: false}]);
		} else if (date.getTime() === this.rangeSelection[0].start.getTime()) {
			this.rangeSelection = [{start: null, end: null, color: "default", disabled: false, chained: true}];
			this.$emit("unselect");
		} else if (this.rangeSelection[0].end === null) {
			// No end, we set end
			// We set virtual start/end to verify queried selection is valid
			// if it is valid we will set range's start/end
			let virtualStart = this.rangeSelection[0].start;
			let virtualEnd = date;

			// If end is before start we invert them
			if (date.getTime() <= this.rangeSelection[0].start.getTime()) {
				virtualStart = date;
				virtualEnd = this.rangeSelection[0].start;
			}

			if(virtualStart) {
				this.setDateHours(virtualStart, this.startTime, 0, 0);
			}
			this.setDateHours(virtualEnd, this.endTime, 59, 599);

			if (this.unselectable) {
				this.rangeSelection = DateUtils.splitIntoOverlappingRanges(
					{
						start: virtualStart,
						end: virtualEnd,
						disabled: false,
						color: "default",
						chained: true,
					},
					this.data ?? []
				);
			} else {
				this.rangeSelection = DateUtils.splitIntoNotOverlappingRanges(
					{
						start: virtualStart,
						end: virtualEnd,
						disabled: false,
						color: "default",
						chained: true,
					},
					this.data ?? []
				);
			}

			this.$emit("selected-period", this.rangeSelection);
		} else {
			// Both start and end, we reset end and set start
			this.rangeSelection[0].start = date;
			this.rangeSelection[0].end = null;

			if(this.rangeSelection[0].start) {
				this.setDateHours(this.rangeSelection[0].start, this.startTime, 0, 0);
			}

			this.$emit("selected-period", [{start: date, end: date, color: "default", disabled: false}]);
		}
		this.$forceUpdate();
	}

	setDateHours(date: Date, hours: string, seconds: number, milliseconds: number) {
		const split = hours.split(':').map((value) => parseInt(value, 10));
		if(split.length !== 2) return;
		date.setHours(split[0]);
		date.setMinutes(split[1]);
		date.setSeconds(seconds);
		date.setMilliseconds(milliseconds);
	}

	updateStartDate(date: string) {
		const newDate = new Date(date);
		this.setDateHours(newDate, this.startTime, 0, 0);
		if(!this.rangeSelection[0]) this.rangeSelection = [{start: null, end: null, color: "default", disabled: false, chained: true}];
		this.rangeSelection[0].start = newDate;
		this.$emit("selected-period", this.rangeSelection);
	}

	updateEndDate(date: string) {
		const newDate = new Date(date);
		this.setDateHours(newDate, this.endTime, 59, 599);
		if(!this.rangeSelection[0]) this.rangeSelection = [{start: null, end: null, color: "default", disabled: false, chained: true}];
		this.rangeSelection[0].end = newDate;
		this.$emit("selected-period", this.rangeSelection);
	}

	updatedTime() {
		if(this.rangeSelection[0]) {
			if(this.rangeSelection[0].start) this.setDateHours(this.rangeSelection[0].start, this.startTime, 0, 0);
			if(this.rangeSelection[0].end) this.setDateHours(this.rangeSelection[0].end, this.endTime, 59, 599);
			this.$emit("selected-period", this.rangeSelection);
		}
	}

	getCurrentRange(date: Date): Range | null {
		let allRanges = this.rangeSelection.concat(this.data ?? []);

		for (let range of allRanges) {
			let dataRange = this.isInsideOfRange(date, range);
			if (dataRange) {
				return dataRange;
			}
		}
		return null;
	}

	isInsideOfRange(date: Date, range: Range): Range | null {
		if (range.start === null || range.end === null) return null;

		if (date.getTime() >= range.start.getTime() && date.getTime() <= range.end.getTime()) {
			return range;
		}

		return null;
	}

	isStartOfRange(date: Date, range: Range | null) {
		// If date is not in any data range we take first selection as comparator
		if (range === null) range = DateUtils.getRangesSmallerDate(this.rangeSelection);

		// If date is within a data range and is just after current selection end
		// then it should be marked as start of range
		if (range && range.color !== "default") {
			for (let selection of this.rangeSelection) {
				if (selection.end && date.getTime() - selection.end.getTime() === 86400000) {
					return true;
				}
			}
		}

		// If date is within a range and correspond to start of that range
		// then it should be marked as start of range
		return (
			range &&
			range.start &&
			range.start.getDate() === date.getDate() &&
			range.start.getMonth() === date.getMonth() &&
			range.start.getFullYear() === date.getFullYear()
		);
	}

	isEndOfRange(date: Date, range: Range | null) {
		// If date is not in any data range we take first selection as comparator
		if (range === null) range = DateUtils.getRangesBiggerDate(this.rangeSelection);

		// If date is within a data range and is just before current selection start
		// then it should be marked as end of range
		if (range && range.color !== "default") {
			for (let selection of this.rangeSelection) {
				if (selection.start && selection.end && selection.start.getTime() - date.getTime() === 86400000) {
					return true;
				}
			}
		}

		// If date is within a range and correspond to end of that range
		// then it should be marked as end of range
		return (
			range &&
			range.start &&
			range.end &&
			range.end.getDate() === date.getDate() &&
			range.end.getMonth() === date.getMonth() &&
			range.end.getFullYear() === date.getFullYear()
		);
	}

	getTdClass(data: {month: number; year: number}, day: Date) {
		if (day.getMonth() !== data.month) return "disabled";

		let classes: string[] = [];

		let today = Date.now();
		if (this.disabledBefore) {
			if (today < this.disabledBefore.getTime()) {
				today = this.disabledBefore.getTime();
			}
		}
		if (day.getTime() > today) classes.push("disabled");

		let currentRange = this.getCurrentRange(day);
		if (currentRange && !currentRange.chained) {
			return classes;
		}

		if (currentRange) {
			if (currentRange.chained) classes.push("in-range");

			classes.push(currentRange.color);
			if (currentRange.disabled) {
				classes.push("disabled");
			}
		}

		if (this.isStartOfRange(day, currentRange)) classes.push("range-start");

		if (this.isEndOfRange(day, currentRange)) classes.push("range-end");

		return classes.join(" ");
	}

	getDayClass(data: {month: number; year: number}, day: Date) {
		if (day.getMonth() !== data.month) return "hidden";

		let classes: string[] = [];
		let currentRange = this.getCurrentRange(day);
		if (currentRange) classes.push(currentRange.color);

		let today = new Date(new Date().setHours(23, 59, 59, 59)).getTime();
		if (day.getTime() > today) classes.push("fade");

		let now = new Date(Date.now());
		if (day.getDate() === now.getDate() && day.getMonth() === now.getMonth() && day.getFullYear() === now.getFullYear()) classes.push("today");

		if (this.isStartOfRange(day, null)) classes.push("selected");
		if (this.isEndOfRange(day, null)) classes.push("selected");

		if (this.isStartOfRange(day, currentRange)) classes.push("range-start");

		if (this.isEndOfRange(day, currentRange)) classes.push("range-end");

		return classes.join(" ");
	}

	getPicturesForDay(day: Date): {hidden: number; pictures: string[]} {
		if (!this.profilePictures) return {hidden: 0, pictures: []};
		if (this.profilePictures[DateUtils.formatDateToIsoString(day)] && this.profilePictures[DateUtils.formatDateToIsoString(day)].length > 3) {
			return {
				hidden: this.profilePictures[DateUtils.formatDateToIsoString(day)].length - 2,
				pictures: this.profilePictures[DateUtils.formatDateToIsoString(day)].slice(0, 2),
			};
		}
		return {hidden: 0, pictures: this.profilePictures[DateUtils.formatDateToIsoString(day)] ?? []};
	}
}
