import {Component, Prop, Vue} from "vue-facing-decorator";
import {AutoWired} from "@groupk/horizon2-core";
import {AppBus} from "../../config/AppBus";
import {OptionBuilder} from "vue-facing-decorator/dist/optionBuilder";
import {PosState} from "../../model/PosState";

export type ToastAction = {
	name: string,
	icon: string,
	callback?: Function,
	page?: string
};

export type Toast = {
	title: string,
	description: string,
	color: 'red'|'green'|'grey'|'orange',
	closable: boolean,
	duration: number, // Duration in ms
	action?: ToastAction|ToastAction[]
}

type ToastState = {
	toast: Toast,
	lifetime: number,
}

@Component({})
export default class ToastManagerComponent extends Vue {
	@Prop() posState!: PosState;

	max: number = 15; // Display maximum 15 toasts to avoid performances problems
	displayAll: boolean = false;

	state: ToastState[] = [];

	@AutoWired(AppBus) accessor appBus!: AppBus;

	constructor(optionBuilder: OptionBuilder, vueInstance: any) {
		super(optionBuilder, vueInstance);

		this.appBus.on('displayToast', this.displayToast);
	}

	beforeMount() {
		this.tick();
	}

	tick() {
		for (let i = this.state.length - 1; i >= 0; i--) {
			this.state[i].lifetime -= 100;
			if (this.state[i].lifetime < 0) {
				this.removeToast(i);
			}
		}
		setTimeout(() => this.tick(), 100);
	}

	toggleDisplayAll() {
		if(this.state.length === 1) this.displayAll = false;
		else this.displayAll = !this.displayAll;
	}

	removeToast(index: number) {
		this.state.splice(index, 1);
		if(this.state.length <= 1) {
			this.displayAll = false;
		}
	}

	displayToast(toast: Toast) {
		if(this.state.length === this.max) {
			this.state.splice(0, 1);
		}
		this.state.push({
			toast: toast,
			lifetime: toast.duration ?? 3000
		});
	}

	clickedAction(action: ToastAction, index: number, toast: Toast){
		if(action.page) {
			this.posState.currentPage = action.page;
		}
		if(action.callback) {
			action.callback();
		}
		this.removeToast(index);
	}
}