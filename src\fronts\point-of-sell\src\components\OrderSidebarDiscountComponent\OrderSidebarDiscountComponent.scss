.order-sidebar-discount-component {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    user-select: none;
    background: white;
    z-index: 500;
    box-sizing: border-box;
    padding: var(--safe-area-top) 0 var(--safe-area-bottom) 0;

    .quick-actions {
        display: flex;
        padding: 10px;
        align-items: stretch;
        justify-content: flex-start;
        gap: 10px;

        .action {
            display: flex;
            padding: 10px 20px;
            justify-content: center;
            align-items: center;
            gap: 10px;
            border-radius: 6px;
            background: #E8E8E8;
            cursor: pointer;

            font-family: Montserrat, sans-serif;
            font-size: 14px;
            font-weight: 500;

            &:hover {
                background: #cbcbcb;
            }

            &.disabled {
                opacity: 0.5;
                pointer-events: none;
            }

            .number {
                display: flex;
                width: 20px;
                height: 20px;
                justify-content: center;
                align-items: center;
                gap: 10px;
                flex-shrink: 0;
                border-radius: 50%;
                background: #FFF;
                box-sizing: border-box;
                font-size: 12px;
                font-weight: 500;
                line-height: 20px;
            }

            i {
                margin-top: 2px;
            }
        }
    }

    .hint {
        padding: 10px;
    }

    .purchases {
        flex-grow: 2;
        display: flex;
        flex-direction: column;
        gap: 10px;
        padding: 10px;
        overflow: auto;

        &::-webkit-scrollbar {
            display: none;
        }

        .purchase {
            display: flex;
            padding: 10px 15px;
            border-radius: 8px;
            background: #F2F4F7;
            gap: 10px;
            box-sizing: border-box;
            border: 2px solid transparent;

            &.selected {
                border: 2px solid var(--primary-color);

                i {
                    color: var(--primary-color);
                }
            }

            &.disabled {
                background: var(--secondary-hover-color);

                .quantity {
                    background: grey;
                }
            }

            .quantity {
                flex-shrink: 0;
                display: flex;
                width: 25px;
                height: 25px;
                justify-content: center;
                align-items: center;
                background: var(--primary-color);
                border-radius: 50%;
                color: var(--primary-text-color);

                font-family: Montserrat, sans-serif;
                font-size: 12px;
                font-weight: 700;
                line-height: 20px;
            }

            .data {
                flex-grow: 2;
                display: flex;
                flex-direction: column;
                gap: 4px;
                padding: 5px 10px;

                .name {
                    font-family: Montserrat, sans-serif;
                    font-size: 14px;
                    font-weight: 700;
                }

                .total {
                    font-family: Montserrat, sans-serif;
                    font-size: 13px;
                }
            }
        }

        .select {
            flex-shrink: 0;
            padding: 10px;
            cursor: pointer;

            &:hover {
                background: var(--primary-hover-color);
                border-radius: 4px;
            }

            i {
                font-size: 22px;
            }
        }
    }

    .bottom {
        padding: 10px;

        .add-discount {
            all: unset;
            display: flex;
            align-items: center;
            justify-content: center;
            height: 56px;
            width: 100%;
            border-radius: 8px;
            background: var(--primary-color);
            color: var(--primary-text-color);
            font-family: Montserrat, sans-serif;
            font-size: 15px;
            font-weight: 700;
            cursor: pointer;

            &:hover {
                background: var(--primary-hover-color);
            }

            &.disabled {
                opacity: 0.5;
                pointer-events: none;
            }
        }
    }

    .keypad-dimmer {
        position: absolute;
        z-index: 80;
        inset: 0;
        background: rgba(0, 0, 0, 0.16);
        backdrop-filter: blur(4px); // Performance problems ???
        opacity: 0;
        pointer-events: none;
        transition: opacity .3s cubic-bezier(0.22, 1, 0.36, 1);

        &.displayed {
            opacity: 1;
            pointer-events: all;
        }
    }

    .hiding-keypad {
        display: flex;
        flex-direction: column;
        gap: 10px;
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        background: white;
        padding: 10px;
        transform: translateY(100%);
        transition: transform .3s cubic-bezier(0.22, 1, 0.36, 1);
        z-index: 90;

        &.opened {
            transform: translateY(0);
        }

        .payment-methods {
            display: grid;
            grid-template-columns: 1fr 1fr;
            grid-gap: 10px;

            .payment-method {
                display: flex;
                align-items: center;
                gap: 10px;
                padding: 15px;
                border: 1px solid black;
                border-radius: 6px;
                cursor: pointer;

                .name {
                    white-space: nowrap;
                    font-size: 15px;
                }

                &.selected {
                    background: rgba(0, 102, 255, 0.15);
                    color: var(--primary-color);
                    border-color: var(--primary-color);
                    font-weight: 600;
                }
            }
        }

        .display {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
            padding: 20px;

            .amount {
                font-size: 26px;
                font-weight: bold;

                &.red {
                    color: #e83636;
                }
            }

            .left-to-pay {
                font-size: 14px;

                .striped {
                    text-decoration: line-through;
                }
            }

            .change-mode {
                margin-top: 10px;
                font-size: 14px;
                i {
                    font-size: 12px;
                }
            }
        }

        .actions {
            display: grid;
            grid-template-columns: 1fr 1fr;
            grid-gap: 10px;

            .cancel, .pay {
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                height: 56px;
                width: 100%;
                border-radius: 8px;
                background: #e83636;
                color: #FFF;
                font-family: Montserrat, sans-serif;
                font-size: 15px;
                font-weight: 700;
                cursor: pointer;
                text-overflow: ellipsis;
                overflow: hidden;
                white-space: nowrap;

                &.disabled {
                    pointer-events: none;
                    opacity: 0.7;
                }

                &:hover {
                    background: #cb1818;
                }
            }

            .pay {
                background: var(--primary-color);
                color: var(--primary-text-color);

                &:hover {
                    background: var(--primary-text-color);
                }

                .small {
                    font-size: 12px;
                    font-weight: 400;
                }
            }
        }
    }

    &.performance-mode {
        .keypad-dimmer {
            transition: none;
        }

        .hiding-keypad {
            transition: none;
        }
    }
}