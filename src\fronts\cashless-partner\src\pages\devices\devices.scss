#devices-page {
    .order-banner {
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 20px;
        padding: 20px;
        border-radius: 12px;
        background: var(--primary-hover-color);
        margin-bottom: 20px;

        @media screen and (max-width: 900px) {
            flex-direction: column;
            align-items: stretch;
        }

        .left {
            display: flex;
            flex-direction: column;
            gap: 4px;

            .title {
                font-weight: 600;
            }

            .description {
                font-size: 14px;
            }
        }
    }

    .checkbox {
        display: flex;
        align-items: center;
        justify-content: center;
        border: 1px solid black;
        border-radius: 3px;
        height: 18px;
        width: 18px;
        cursor: pointer;
        box-sizing: border-box;
        flex-shrink: 0;

        i {
            font-size: 11px;
            display: none;
        }

        &.selected {
            border: none;
            background: #1099FD;
            color: white !important;

            i {
                display: initial;
            }
        }

        &.half-selected {
            color: black;

            i {
                display: initial;
            }
        }
    }

    .right-view {
        display: flex;
        flex-direction: column;
        gap: 20px;
        padding: 40px;
    }

    .toggle-all {
       position: relative;

        .selected-number {
            position: absolute;
            top: 9px;
            left: 45px;
            font-weight: normal;
        }
    }

    .hotkeys {
        display: flex;
        align-items: center;
        gap: 20px;

        .hotkey {
            display: flex;
            align-items: center;
            font-size: 13px;
            gap: 5px;

            .key {
                padding: 5px;
                background: var(--secondary-hover-color);
                border-radius: 4px;
            }
        }
    }


    .table-scroll-wrapper {
        overflow: auto;

        .no-break {
            white-space: nowrap;
        }

        &.no-margin {
            margin-right: -40px;
            padding-right: 40px;
        }
    }

    .mobile-floating {
        position: fixed;
        bottom: 20px;
        right: 20px;

        @media (min-width: 1500px) {
            display: none;
        }
    }

    @media (max-width: 1500px) {
        .close {
            display: flex !important;
            gap: 10px;
            align-items: center;
        }
    }
}