<script lang="ts" src="./sidebar.ts" />

<style scoped lang="sass">
@use './sidebar.scss' as *
</style>

<template>
    <sidebar
        :opened="opened"
        :minimized="minimized"
        :hidden="hidden"
        :header="header"
        :menus="menus"
        :bottom="bottom"
        :selected-navigation="selectedNavigation"
        v-on:navigation-clicked="navigationClicked($event)"
        @close="opened = false"
    >
        <template v-slot:default>
            <div class="default-slot">
                <div class="logo-container">
                    <img v-if="!minimized" src="/img/logo-long.svg" class="platform-logo" />
                    <img v-else src="/img/logo.svg" class="platform-logo" />
                </div>

                <template v-if="appState.establishment">
                    <establishment-switch></establishment-switch>
                </template>
            </div>
        </template>
        <template v-slot:bottom>
            <div class="negative-space">
                <application-switcher
                    @open="loadPlatform()"
                    :platform-descriptor="platformDescriptor"
                    :config="{
                      title:'Cashless',
                      icon:'/img/logo.svg'
                    }"
                ></application-switcher>

                <div class="reseller" v-if="appState.establishment && appState.establishment.reseller" :class="{hidden: minimized}" @click="showContactInfos = true;">
                    <img :src="appState.requireEstablishment().reseller?.logoUrl">
                    <div class="data">
                        <span class="name"> {{ appState.requireEstablishment().reseller?.name }} </span>
                        <span class="info"> Informations de contact </span>
                    </div>
                </div>

                <div class="advanced" v-if="appState.advancedInterfaces">
                    Mode avancé
                </div>
            </div>
        </template>
    </sidebar>

    <modal-or-drawer :state="showContactInfos" @close="showContactInfos = false" :edit-secured="false">
        <div class="contact" v-if="appState.establishment">
            <div class="head">
                <span class="contact-name"> {{ appState.requireEstablishment().reseller?.contactName }}</span>
                <span class="contact-description"> {{ appState.requireEstablishment().reseller?.contactDescription }}</span>
            </div>

            <div class="grid">
                <div class="item">
                    <span class="method"> Téléphone </span>
                    <span class="how">
                        <i class="fa-regular fa-phone"></i>
                        {{ appState.requireEstablishment().reseller?.contactPhone }}
                    </span>
                </div>
                <div class="item">
                    <span class="method"> Email </span>
                    <span class="how">
                        <i class="fa-regular fa-envelope"></i>
                        {{ appState.requireEstablishment().reseller?.contactEmail }}
                    </span>
                </div>
            </div>
        </div>
    </modal-or-drawer>
</template>
