<script lang="ts" src="./SEPARefundFormComponent.ts">
</script>
<style lang="sass" scoped>
@use './SEPARefundFormComponent.scss' as *
</style>
<template>
  <form-modal-or-drawer
      :title="'Configuration SEPA pour les remboursements'"
      :loading="preprocessing"
      :state="true"
  >
    <template v-slot:content>
    <div class="content">
      <div class="input-group">
        <label for="orgName">Nom de l'organisme</label>
        <div class="ui input">
          <input v-model="refundBatch.sepaConfig.orgName" type="text" id="orgName" placeholder="Nom de l'organisme"/>
        </div>
      </div>

      <div class="input-group">
        <label for="orgIban">IBAN</label>
        <div class="ui input">
          <input type="text" placeholder="IBAN du compte à débiter" v-cleave="{obj: refundBatch.sepaConfig, key: 'orgIban', options: cleaveIban}" />
        </div>
      </div>

      <div class="input-group">
        <label for="orgBic">BIC</label>
        <div class="ui input">
          <input v-model="refundBatch.sepaConfig.orgBic" type="text" id="orgBic" placeholder="BIC / SWIFT"/>
        </div>
      </div>

      <div class="input-group checkbox">
        <input v-model="refundBatch.sepaConfig.batchBooking" type="checkbox" />
        <label>Traitement par lot (Batch Booking)</label>
      </div>

      <div class="summary-section" v-if="refunds && refunds.length > 0">
        <h3>Résumé des remboursements</h3>
        <div class="summary-item">
          <span>Nombre de remboursements : {{ filteredRefunds.legit.length }}</span>
        </div>
        <div class="summary-item">
          <span>Montant total : {{ totalRefundAmount(filteredRefunds.legit) }}</span>
        </div>
      </div>

      <div class="summary-section" v-if="refunds && refunds.length > 0">
        <h3>Remboursements ignorés</h3>
        <div class="summary-item">
          <span>Nombre de remboursements : {{ filteredRefunds.skipped.length }}</span>
        </div>
        <div class="summary-item">
          <span>Montant total : {{ totalRefundAmount(filteredRefunds.skipped) }}</span>
        </div>
      </div>
    </div>
    </template>
    <template v-slot:buttons>
      <div class="button" :class="{disabled: preprocessing, loading: preprocessing}" @click="save()"> Créer </div>
    </template>
  </form-modal-or-drawer>
</template>


