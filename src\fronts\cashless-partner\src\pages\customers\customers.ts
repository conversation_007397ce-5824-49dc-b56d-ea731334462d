import {Component, Vue} from "vue-facing-decorator";
import {
	CustomFilterComponent,
	DropdownButtonAction,
	DropdownButtonComponent,
	FilterParameters, FormModalOrDrawerComponent,
	LayoutContentWithRightPanelComponent,
	ForbiddenMessageComponent,
} from "@groupk/vue3-interface-sdk";
import {
	AutoWired,
	QueryFilter,
	QueryFilterGroup,
	QueryFilterGroupClause,
	QueryOperator,
	TypedQuerySearch,
	Uuid,
	UuidUtils,
	VisualScopedUuid
} from "@groupk/horizon2-core";
import SidebarStateListener from "../../../../../shared/utils/SidebarStateListener";
import {CustomerRepository} from "../../../../../shared/repositories/CustomerRepository";
import {
	CustomerApiOut,
	CustomerHttpCustomerContractSearchConfig, UuidScopeCustomer_customer, uuidScopeCustomer_customer,
	ApplicationPermission,
	EstablishmentAccountPermissionModel,
	CustomerHttpCustomerContract,
} from "@groupk/mastodon-core";
import CustomerFormComponent from "../../components/CustomerFormComponent/CustomerFormComponent.vue";
import {QueryFilterUtils} from "../../../../../shared/utils/QueryFilterUtils";
import {CustomerChipRepository} from "../../../../../shared/repositories/CustomerChipRepository";
import CustomerChipCreationFormComponent
	from "../../components/CustomerChipCreationFormComponent/CustomerChipCreationFormComponent.vue";
import CustomerChipDatesFormComponent
	from "../../components/CustomerChipDatesFormComponent/CustomerChipDatesFormComponent.vue";
import {
	CustomerChipApiIn,
	CustomerChipApiOut,
	WalletApiOut,
} from "@groupk/mastodon-core";
import {EstablishmentUrlBuilder} from "../../../../../shared/utils/EstablishmentUrlBuilder";
import CustomerImportModalComponent from "../../components/CustomerImportModalComponent/CustomerImportModalComponent.vue";
import {CustomerImportModalComponentHasRequiredPermissions} from "../../components/CustomerImportModalComponent/CustomerImportModalComponent";
import {TransactionsRepository} from "../../../../../shared/repositories/TransactionsRepository";
import {WalletsRepository} from "../../../../../shared/repositories/WalletsRepository";
import {WalletInactiveReason} from "@groupk/mastodon-core";
import WalletDeclareStateFormComponent
	from "../../components/WalletDeclareStateFormComponent/WalletDeclareStateFormComponent.vue";
import WalletEmptyBalanceFormComponent
	from "../../components/WalletNetworkTransactionFormComponent/WalletNetworkTransactionFormComponent.vue";
import CustomerTransactionsExportComponent
	from "../../components/CustomerTransactionsExportComponent/CustomerTransactionsExportComponent.vue";
import {AppState} from "../../../../../shared/AppState";
import {ComponentUtils} from "../../../../../shared/utils/ComponentUtils";
import {CustomerFormComponentHasRequiredPermissions} from "../../components/CustomerFormComponent/CustomerFormComponent";
import {WalletDeclareStateFormComponentHasRequiredPermissions} from "../../components/WalletDeclareStateFormComponent/WalletDeclareStateFormComponent";
import {CustomerTransactionsExportComponentHasRequiredPermissions} from "../../components/CustomerTransactionsExportComponent/CustomerTransactionsExportComponent";

@Component({
	components: {
		'layout': LayoutContentWithRightPanelComponent,
		'customer-form': CustomerFormComponent,
		'chip-creation-form': CustomerChipCreationFormComponent,
		'chip-dates-form': CustomerChipDatesFormComponent,
		'custom-filter': CustomFilterComponent,
		'dropdown-button': DropdownButtonComponent,
		'customer-import-modal': CustomerImportModalComponent,
		'form-modal-or-drawer': FormModalOrDrawerComponent,
		'wallet-declare-state-form': WalletDeclareStateFormComponent,
		'wallet-empty-balance-form': WalletEmptyBalanceFormComponent,
		'customer-transactions-export': CustomerTransactionsExportComponent,
		'forbidden-message': ForbiddenMessageComponent,
	}
})
export default class CustomersView extends Vue {
	customers: CustomerApiOut[] = [];
	selectedCustomer: CustomerApiOut|null = null;
	chipsPerCustomer: Record<VisualScopedUuid<UuidScopeCustomer_customer>, CustomerChipApiOut[]> = {};
	selectedChip: CustomerChipApiOut|null = null;
	forbidden: boolean = false;

	allowedFilters = CustomerHttpCustomerContractSearchConfig;
	appliedFilters: TypedQuerySearch<typeof CustomerHttpCustomerContractSearchConfig> = {};
	filters: TypedQuerySearch<typeof CustomerHttpCustomerContractSearchConfig> = {};
	filterParameters: { [filterName: string]: FilterParameters } = {
		'firstname': {
			type: 'UNKNOWN',
			translation: 'Prénom',
			validation: (value: unknown) => {
				return typeof value === 'string' && value.trim() !== '';
			}
		},
		'lastname': {
			type: 'UNKNOWN',
			translation: 'Nom de famille',
			validation: (value: unknown) => {
				return typeof value === 'string' && value.trim() !== '';
			}
		},
		'email': {
			type: 'UNKNOWN',
			translation: 'Email',
			validation: (value: unknown) => {
				return typeof value === 'string' && value.trim() !== '';
			}
		},
		'description': {
			type: 'UNKNOWN',
			translation: 'Description',
			validation: (value: unknown) => {
				return typeof value === 'string' && value.trim() !== '';
			}
		},
	};

	editingCustomer: CustomerApiOut|null = null;
	customerSearch: string = '';
	showEmptyChipConfirmationModal: boolean = false;
	showUnusableChipConfirmationModal: WalletInactiveReason|null = null;
	showCustomerForm: boolean = false;
	showChipCreationForm: boolean = false;
	showChipDatesForm: boolean = false;
	showCustomersImportModal: boolean = false;
	showCustomersTransactionsExportModal: boolean = false;
	showFilters: boolean = false;
	loadingChips: boolean = false;
	loading: boolean = false;
	disabledActions: string[] = [];

	@AutoWired(SidebarStateListener) accessor sidebarStateListener!: SidebarStateListener;
	@AutoWired(CustomerRepository) accessor customerRepository!: CustomerRepository;
	@AutoWired(CustomerChipRepository) accessor customerChipRepository!: CustomerChipRepository;
	@AutoWired(TransactionsRepository) accessor transactionsRepository!: TransactionsRepository;
	@AutoWired(WalletsRepository) accessor walletRepository!: WalletsRepository;
	@AutoWired(AppState) accessor appState!: AppState;

	beforeMount() {
		this.sidebarStateListener.setHiddenSidebar(false);
		this.sidebarStateListener.setMinimizedSidebar(true);

		if(!ComponentUtils.hasPermissions(CustomerImportModalComponentHasRequiredPermissions)) {
			this.disabledActions.push('import');
		}

		if(!ComponentUtils.hasPermissions(CustomerFormComponentHasRequiredPermissions)) {
			this.disabledActions.push('create-customer');
		}

		if(!ComponentUtils.hasPermissions(WalletDeclareStateFormComponentHasRequiredPermissions)) {
			this.disabledActions.push('lost');
			this.disabledActions.push('stolen');
		}

		if(!ComponentUtils.hasPermissions(CustomerTransactionsExportComponentHasRequiredPermissions)) {
			this.disabledActions.push('export');
		}

	}

	async mounted() {
		// Check for essential page functionality
		if(!ComponentUtils.hasPermissions((ownedPermissions: ApplicationPermission[]) => {
			return EstablishmentAccountPermissionModel.hasPermissionsForContracts(ownedPermissions, [
				CustomerHttpCustomerContract.search,
			])
		})) {
			this.forbidden = true;
			this.loading = false;
			return;
		}

		await this.searchCustomers({});

		const urlParams = new URLSearchParams(window.location.search);
		if(urlParams.has('uid')) {
			const visualScoped = UuidUtils.uuidToVisual(uuidScopeCustomer_customer, urlParams.get('uid') as Uuid);
			this.selectCustomer((await this.customerRepository.callContract('getOne', {establishmentUid: this.appState.requireUrlEstablishmentUid(), customerUid: visualScoped}, undefined)).success())
		}
	}

	async selectCustomer(customer: CustomerApiOut) {
		if(this.selectedCustomer && this.selectedCustomer.uid === customer.uid) {
			this.selectedCustomer = null;
			this.selectedChip = null;

			window.history.replaceState(null, '', window.location.pathname);
		} else {
			this.selectedCustomer = customer;
			this.selectedChip = null;
			window.history.replaceState(null, '', window.location.pathname + '?uid=' + UuidUtils.visualToUuid(customer.uid));

			if(!this.chipsPerCustomer[customer.uid]) {
				this.loadingChips = true;
				this.chipsPerCustomer[customer.uid] = (await this.customerChipRepository.callContract('listCustomerChips', {establishmentUid: this.appState.requireUrlEstablishmentUid(), customerUid: customer.uid}, undefined)).success();
				this.loadingChips = false;
			}
		}
	}

	searchTimeout: ReturnType<typeof setTimeout>|null = null;
	async search() {
		if(this.searchTimeout) {
			clearTimeout(this.searchTimeout);
			this.searchTimeout = null;
		}
		this.filters.filter = QueryFilterUtils.setFiltersKeepDisabled(this.appliedFilters.filter,  this.customerSearch !== '' ? {
			group: QueryFilterGroupClause.OR,
			filters: [{
				name: 'firstname',
				value: this.customerSearch,
				operator: QueryOperator.CONTAINS
			}, {
				name: 'lastname',
				value: this.customerSearch,
				operator: QueryOperator.CONTAINS
			}, {
				name: 'email',
				value: this.customerSearch,
				operator: QueryOperator.CONTAINS
			}]
		} : null, this.filterParameters) as any;

		this.searchTimeout = setTimeout(async () => {
			await this.searchCustomers(this.filters);
		}, 500);
	}


	async searchCustomers(filters: TypedQuerySearch<typeof CustomerHttpCustomerContractSearchConfig>, cursor: {after?: Uuid, before?: Uuid}|null = null) {
		this.loading = true;
		this.filters = {...filters};

		// if(!cursor) {
		// 	const data = (await this.customerRepository.callContract('searchCount', {establishmentUid: this.establishmentUid}, filters)).success();
		// 	this.pagination.totalResults = data.rows;
		// 	this.pagination.currentPage = 1;
		// 	this.pagination.estimateTotal = data.estimate;
		// }

		if(cursor && cursor.after) filters.cursorAfter = cursor.after;
		if(cursor && cursor.before) filters.cursorBefore = cursor.before;
		this.customers = (await this.customerRepository.callContract('search', {establishmentUid: this.appState.requireUrlEstablishmentUid()}, filters)).success();
		this.appliedFilters = filters;
		this.loading = false;
	}

	async loadMore() {
		await this.searchCustomers(this.filters, {after: this.customers[this.customers.length - 1].uid})
	}

	get groupedCustomers(): Record<string, CustomerApiOut[]> {
		const sortedCustomers = this.customers.sort((c1, c2) => {
			const c1FullName = (c1.lastname ?? '?') + (c1.firstname ?? '?');
			const c2FullName = (c2.lastname ?? '?') + (c2.firstname ?? '?');
			return c1FullName.localeCompare(c2FullName);
		})

		const grouped: Record<string, CustomerApiOut[]> = {};
		for(let customer of sortedCustomers) {
			const letter = (customer.lastname ? customer.lastname[0].toUpperCase() : '?');
			if(!grouped[letter]) {
				grouped[letter] = [];
			}
			grouped[letter].push(customer);
		}
		return grouped;
	}

	get dropdownActions() {
		const allActions = [{
			id: 'create',
			name: 'Créer un client',
			icon: 'fa-regular fa-circle-plus'
		}, {
			id: 'import',
			name: 'Importer des clients',
			icon: 'fa-regular fa-file-import'
		}];

		return allActions.filter(action => !this.disabledActions.includes(action.id));
	}

	get customerDropdownActions() {
		const allActions = [{
			id: 'edit',
			name: 'Modifier les informations',
			icon: 'fa-regular fa-pen-line fa-flip-horizontal'
		}, {
			id: 'bind',
			name: 'Lier une puce',
			icon: 'fa-regular fa-circle-plus'
		}, {
			id: 'export',
			name: 'Exporter les consos',
			icon: 'fa-regular fa-arrow-down-to-line'
		}];

		return allActions.filter(action => !this.disabledActions.includes(action.id));
	}

	get chipDropdownActions() {
		const allActions = [{
			id: 'edit',
			name: 'Modifier',
			icon: 'fa-regular fa-pen-line'
		}, {
			id: 'now',
			name: 'Rendre maintenant',
			icon: 'fa-regular fa-arrow-left'
		}, {
			id: 'lost',
			name: 'Déclarer perdue',
			icon: 'fa-regular fa-location-exclamation'
		}, {
			id: 'stolen',
			name: 'Déclarer volée',
			icon: 'fa-regular fa-location-exclamation'
		}, {
			id: 'empty',
			name: 'Vider le solde',
			icon: 'fa-regular fa-trash-alt'
		}];

		return allActions.filter(action => !this.disabledActions.includes(action.id));
	}

	editCustomer(customer: CustomerApiOut) {
		this.editingCustomer = customer;
		this.showCustomerForm = true;
	}

	createdCustomer(customer: CustomerApiOut) {
		this.customers.push(customer);
		this.editingCustomer = null;
		this.showCustomerForm = false;
	}

	updatedCustomer(updatedCustomer: CustomerApiOut) {
		const index = this.customers.findIndex((customer) => customer.uid === updatedCustomer.uid);
		if(index !== -1) {
			this.customers.splice(index, 1, updatedCustomer);
		}
		if(this.selectedCustomer && this.selectedCustomer.uid === updatedCustomer.uid) this.selectedCustomer = updatedCustomer;
		this.editingCustomer = null;
		this.showCustomerForm = false;
	}

	getFiltersCount(filter: QueryFilter | QueryFilterGroup | undefined) {
		if(!filter) return 0;
		if('group' in filter) {
			let filters = 0;
			for(let subFilter of filter.filters) filters += this.getFiltersCount(subFilter);
			return filters;
		} else {
			return 1;
		}
	}

	getEuroWalletAmount(wallets: WalletApiOut[]): number {
		const wallet = wallets.find((wallet) => wallet.currency === 0);
		if(!wallet) return 0;
		return wallet.balance;
	}

	createdChip(customerChip: CustomerChipApiOut) {
		if(!this.chipsPerCustomer[customerChip.customerUid]) this.chipsPerCustomer[customerChip.customerUid] = [];
		this.chipsPerCustomer[customerChip.customerUid].push(customerChip);
	}

	updatedChip(updatedCustomerChip: CustomerChipApiOut) {
		if(!this.chipsPerCustomer[updatedCustomerChip.customerUid]) this.chipsPerCustomer[updatedCustomerChip.customerUid] = [updatedCustomerChip];
		else {
			const index = this.chipsPerCustomer[updatedCustomerChip.customerUid].findIndex((customerChip) => customerChip.uid === updatedCustomerChip.uid);
			if(index !== -1) {
				this.chipsPerCustomer[updatedCustomerChip.customerUid].splice(index, 1, updatedCustomerChip)
			} else {
				this.chipsPerCustomer[updatedCustomerChip.customerUid].push(updatedCustomerChip);
			}
		}
		if(this.selectedChip && this.selectedChip.uid === updatedCustomerChip.uid) this.selectedChip = updatedCustomerChip;
	}

	goToChipTransactions(chip: CustomerChipApiOut) {
		window.open(EstablishmentUrlBuilder.buildUrl('/transactions?chip=' + chip.chipVisualId), '_blank');
	}

	dropdownClicked(action: DropdownButtonAction) {
		if(action.id === 'create') {
			this.showCustomerForm = true;
		} else if(action.id === 'import' && !this.disabledActions.includes('import')) {
			this.showCustomersImportModal = true;
		}
	}

	customerDropdownClicked(customer: CustomerApiOut, action: DropdownButtonAction) {
		if(action.id === 'edit') {
			this.editCustomer(customer);
		} else if(action.id === 'bind') {
			this.showChipCreationForm = true;
		} else if(action.id === 'export') {
			this.showCustomersTransactionsExportModal = true;
		}
	}

	async chipDropdownClicked(action: DropdownButtonAction) {
		if(action.id === 'edit') {
			this.showChipDatesForm = true
		} else if(action.id === 'now') {
			if(!this.selectedChip) return;
			const apiIn = new CustomerChipApiIn({
				chipVisualId: null,
				chipId: this.selectedChip.chipId,
				chipUid: this.selectedChip.chipUid,
				customerUid: this.selectedChip.customerUid,
				startingDatetime: this.selectedChip.startingDatetime,
				endingDatetime:	new Date().toISOString()
			});
			const response = await this.customerChipRepository.callContract('update', {establishmentUid: this.appState.requireUrlEstablishmentUid(), customerChipUid: this.selectedChip.uid}, apiIn);
			if(response.isSuccess()) {
				this.updatedChip(response.success());
			} else if(response.isError()) {} else {}
		} else if(action.id === 'empty') {
			this.showEmptyChipConfirmationModal = true;
		} else if(action.id === 'lost') {
			this.showUnusableChipConfirmationModal = WalletInactiveReason.LOST;
			// const apiIn = new WalletSetStateApiIn({
			// 	inactiveReason: WalletInactiveReason.LOST
			// });
			// this.walletRepository.request(
			// 	'setState',
			// 	{establishmentUid: this.appState.requireUrlEstablishmentUid()},
			//
			// )
		} else if(action.id === 'stolen') {
			this.showUnusableChipConfirmationModal = WalletInactiveReason.STOLEN;
		}
	}

	selectChip(chip: CustomerChipApiOut) {
		const selection = window.getSelection();
		if (selection && !selection.isCollapsed) {
			return;
		}

		this.selectedChip = chip;
	}

	resetFilters() {
		this.appliedFilters = {};
		this.filters = {};
	}
}
