import {Component, Prop, Vue} from "vue-facing-decorator";
import {MetadataDescriptorTemplateStringApiOut} from "@groupk/mastodon-core";

@Component({
    emits: ['validated']
})
export default class StringDescriptorComponent extends Vue {
    @Prop({required: true}) metadataDescriptorTemplate!: MetadataDescriptorTemplateStringApiOut;
    @Prop({default: null}) defaultValue!: string|null;

    value: string = '';

    beforeMount() {
        if(this.defaultValue) this.value = this.defaultValue;
    }

    validate() {
        this.$emit('validated', this.value);
    }
}