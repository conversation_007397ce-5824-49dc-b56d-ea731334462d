import {Component, Prop, Ref, Vue, Watch} from "vue-facing-decorator";
import {ChartData} from "chart.js/dist/types";
import {Chart, registerables} from "chart.js";
import {ExternalTooltipHandler} from "../../../../../../shared/utils/ExternalTooltipHandler";
import {VueIgnore} from "@groupk/horizon2-front";

@Component({})
export default class BarChartComponent extends Vue {
	@Prop() data!: ChartData;

	@VueIgnore chart: Chart|null = null;
	@Ref() canvas!: HTMLCanvasElement;

	@Watch('data', {deep: true})
	dataWatch() {
		if(this.chart){
			// for(let i = 0; i < chartJs.data.datasets.length; ++i){
			// 	const newData = JSON.parse(JSON.stringify(this.data.datasets[i].data));
			// 	console.log(newData);
			// 	chartJs.data.datasets[i].data = newData;
			// }
			this.chart.data = JSON.parse(JSON.stringify(this.data));
			this.chart.update();
		}
	}


	mounted() {
		Chart.register(...registerables);
		this.initChart();
	}

	initChart() {
		this.chart = new Chart(this.canvas, {
			type: 'bar',
			data: this.data,
			options: {
				maintainAspectRatio: false,
				scales: {
					x: {
						border: {
							display: false
						},
						grid: {
							color: 'rgba(0, 0, 0, 0)'
						}
					},
					y: {
						ticks: {
							display: false,
							count: 5
						},
						border: {
							display: false
						},
						grid: {
							color: '#f2f4f7',
						}
					}
				},
				plugins: {
					legend: {
						display: false // This hides all text in the legend and also the labels.
					},
					tooltip: {
						enabled: false,
						position: 'nearest',
						external: ExternalTooltipHandler.handler as never
					}
				}
			}
		});
	}
}