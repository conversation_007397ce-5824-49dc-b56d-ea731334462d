import type PrinterLanguageConverter from "./PrinterLanguageConverter";
import {ImageConversionStrategy, ImageUtils} from "../ImageUtils";
import type {FormArea} from "../CanvasRendererSDK";
import {PrinterConfig} from "../PrinterHelper";

// To add new features, see ZPL manual :
// https://www.zebra.com/content/dam/zebra/manuals/printers/common/programming/zpl-zbi2-pm-en.pdf

const ZPL_START = "^XA";
const ZPL_END = "^XZ";
// const ZPL_FIELDORIGIN = "^FO";
// const ZPL_FIELDSEPARATOR = "^FS";
// const ZPL_FIELDDATA = "^FD";
const ZPL_LABELLENGTH = "^LL";
// const ZPL_SCALABLEFONT = "^A";
// const ZPL_DRAWBOX = "^GB";
const ZPL_PRINT_RATE = "^PR"; //print speed [1inch to 14 inch] 3 values (print, slew, backfeed)
const ZPL_PPMM = 8;
// const ZPL_FONTS: {[s: number]: string} = {0: "0", 1: "A", 2: "B", 3: "C", 4: "D", 5: "E", 6: "F", 7: "G"};
// const ZPL_FONTS_SIZES: {[s: string]: number} = {"0": 15, A: 6, B: 9, C: 12, D: 12, E: 20, F: 16, G: 48};
// const ZPL_DIRECTIONS: {[s: number]: string} = {0: "N", 90: "R", 180: "I", 270: "B"};
const ZPL_MEDIATYPETAG: string = "^MN";

type AllowedMediaTypes = "continue" | "fanfold" | "blackmark" | "auto";

/*
• Continuous Media – this media has no physical characteristic (web, notch, perforation,
mark, et cetera) to separate labels. Label length is determined by the ^LL command.
• Non-continuous Media – this media has some type of physical characteristic (web, notch,
perforation, mark, et cetera) to separate the labels.
 */
/*
N = continuous media
Y = non-continuous media web sensing *
W = non-continuous media web sensing *
M = non-continuous media mark sensing
Default Value: a value must be entered or the command is ignored
 */
const ZPL_MEDIATYPE: {[s: string]: string} = {
	continue: "N",
	fanfold: "M",
	web: "W",
	mark: "M",
	blackmark: "M",
	auto: "A",
};

const QZ_CMDEND: string = "\n";
const ZPL_UTF8: string = "^CI28";

// https://support.zebra.com/cpws/docs/zpl/euro_symbol.htm
// redirects the $ decimal 36 to the euro character decimal 21
// const ZPL_USEEUR: string = "^CI7";

export default class ZplLanguageConverter implements PrinterLanguageConverter {
	async convertFromBase64(base64Str: string, formAreas?: FormArea[], printerRelayConfig?: PrinterConfig): Promise<number[]> {
		throw new Error('not_implemented');
	}

	getStart(): string {
		let printSpeed = 14;
		let slewSpeed = 14;
		let backfeedSpeed = 14;
		return ZPL_START + QZ_CMDEND + ZPL_UTF8 + QZ_CMDEND + ZPL_PRINT_RATE + printSpeed + "," + slewSpeed + "," + backfeedSpeed + QZ_CMDEND;
	}

	getLength(size: number): string {
		console.log("length:" + size * ZPL_PPMM);
		return ZPL_LABELLENGTH + size * ZPL_PPMM + QZ_CMDEND;
	}

	getMediaType(mediaType: AllowedMediaTypes, detectionOffset: number = 0): string {
		let convertedMediaType = ZPL_MEDIATYPE["continue"];
		if (typeof ZPL_MEDIATYPE[mediaType] !== "undefined") convertedMediaType = ZPL_MEDIATYPE[mediaType];

		/*
This sets the expected location of the media mark relative to the point of
separation between documents. If set to 0, the media mark is expected to
be found at the point of separation. (i.e., the perforation, cut point, etc.)
All values are listed in dots. This parameter is ignored unless the a
parameter is set to M. If this parameter is missing, the default value is used.
Values:
-80 to 283 for direct-thermal only printers
-240 to 566 for 600 dpi printers
-75 to 283 for KR403 printers
-120 to 283 for all other printers
Default: 0
		 */
		let formattedOffset = "";
		if (detectionOffset !== 0) {
			formattedOffset = "," + detectionOffset;
		}

		return ZPL_MEDIATYPETAG + convertedMediaType + formattedOffset + QZ_CMDEND;
	}

	getEnd(): string {
		return ZPL_END + QZ_CMDEND;
	}

	public static convertFromImage(image: Array<Array<number>>, outputMode: "hex" | "binary"): string {
		if(image.length === 0) throw new Error('missing_images');

		let convertedImage = ImageUtils.toRasterFormat(image, outputMode);
		let compressionType = "A"; //ASCII=HEX
		if (outputMode == "binary") {
			compressionType = "B";
		}

		let bytesPerRow = Math.ceil(image[0]!.length / 8);
		let binaryByteCount = bytesPerRow * image.length;
		let graphicByteCount = bytesPerRow * image.length;
		let command = "^GF" + compressionType + "," + binaryByteCount + "," + graphicByteCount + "," + bytesPerRow + ",";

		return command + convertedImage;
	}
}
