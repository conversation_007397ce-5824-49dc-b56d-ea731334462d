<script lang="ts" src="./ProfileDevicesComponents.ts">
</script>

<style lang="sass">
@use './ProfileDevicesComponents.scss' as *
</style>

<template>
    <div class="profile-devices-components">
        <div class="top-actions">
            <div class="grey button" @click="showDeviceSelectionModal = true">
                <i class="fa-regular fa-circle-plus"></i>
                Ajouter un terminal
            </div>
        </div>

        <div class="table-scroll-wrapper">
            <table class="data-table">
                <thead>
                <tr>
                    <td>
                        Appareils
                    </td>
                    <td></td>
                </tr>
                </thead>
                <tbody>
                <tr class="table-no-data" v-if="filteredDevices.length === 0">
                    <td colspan="100%">
                        Aucun appareil n'est relié à ce point de vente
                    </td>
                </tr>
                <tr v-else v-for="device in filteredDevices">
                    <td>
                        <div class="device-data">
                            <div class="device-icon">
                                <i v-if="device.brand.toLowerCase() === 'apple'" class="fa-brands fa-apple" />
                                <i v-if="device.brand.toLowerCase() === 'chrome'" class="fa-brands fa-chrome" />
                                <i v-else class="fa-regular fa-tablet" />
                            </div>

                            <div class="device-serial">
                                {{ device.brand }} - {{ device.model }}
                                <span class="ticket-description"> {{ device.hardwareId }} </span>
                            </div>
                        </div>
                    </td>

                    <td width="1px">
                        <dropdown-button
                            button-class="tertiary icon button"
                            icon="fa-regular fa-ellipsis-vertical"
                            alignment="RIGHT"
                            :actions="[{title: '', actions: getDropdownButtonActions(device)}]"
                            @clicked="dropdownClicked($event, device)"
                        ></dropdown-button>
                    </td>
                </tr>
                </tbody>
            </table>
        </div>

        <device-selection
            v-if="showDeviceSelectionModal"
            :devices="devices"
            :account-profiles="accountProfiles"
            @selected="bindDevices($event)"
            @close="showDeviceSelectionModal = false"
        ></device-selection>

        <device-state
            v-if="showDeviceStateModal"
            :device-state="showDeviceStateModal"
            @close="showDeviceStateModal = null"
        ></device-state>
    </div>
</template>