<script lang="ts" src="./ProfilePaymentMethodsEditorComponent.ts">
</script>

<style lang="sass" scoped>
@use './ProfilePaymentMethodsEditorComponent.scss' as *
</style>

<template>
    <div class="profile-payment-methods-editor-component">
        <form-modal-or-drawer
            :state="true"
            title="Glissez les méthodes autorisées"
            subtitle="Vous pouvez organiser les méthodes pour les mettre dans l'ordre souhaité"
            @close="done()"
        >
            <template v-slot:content>
                <div class="all-methods methods drag-container">
                    <div class="method og" :data-uid="method.uid" v-for="method of paymentMethods">
                        <span> {{ method.name }} </span>
                        <i class="fa-regular fa-grip-dots-vertical"></i>
                    </div>
                </div>

                <div class="dropzone final drag-container" :key="refreshKey">
                    <span> Méthode autorisées </span>
                    <div class="sortable methods">
                        <span v-if="(keypad.paymentMethods ?? []).length === 0" class="empty"> Aucune méthode de paiement autorisée </span>
                        <div v-for="methodUid of (keypad.paymentMethods ?? [])" class="method copy">
                            <span> {{ getMethodWithUid(methodUid).name }} </span>
                            <i class="fa-regular fa-grip-dots-vertical"></i>
                        </div>
                    </div>

                    <div class="delete-zone sortable" :class="'delete-zone-' + uid">
                        Glisser ici pour supprimer
                    </div>
                </div>
            </template>
            <template v-slot:buttons>
                <button class="primary button" @click="done()">
                    Valider
                </button>
            </template>
        </form-modal-or-drawer>
    </div>
</template>