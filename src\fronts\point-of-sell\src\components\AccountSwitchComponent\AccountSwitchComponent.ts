import {Component, Prop, Vue} from "vue-facing-decorator";
import {PosState} from "../../model/PosState";
import {PosProfile} from "../../model/PosProfile";
import {AutoWired, VisualScopedUuid} from "@groupk/horizon2-core";
import {UuidScopeEstablishmentAccount} from "@groupk/mastodon-core";
import {AppBus} from "../../config/AppBus";

@Component({})
export default class AccountSwitchComponent extends Vue {
	@Prop() posState!: PosState;
	@Prop() posProfile!: PosProfile;

	@AutoWired(AppBus) accessor appBus!: AppBus;

	changeAccount(establishmentAccountUid: VisualScopedUuid<UuidScopeEstablishmentAccount>|null) {
		this.appBus.emit('changeEstablishmentAccount', establishmentAccountUid);
		this.posState.currentPage = 'sell';
		this.$forceUpdate();
	}
}