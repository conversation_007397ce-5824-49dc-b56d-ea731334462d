# CLAUDE.md

This file provides guidance to Claude <PERSON> (claude.ai/code) when working with code in this repository.

## Important

Before writing code you MUST ensure all typings are correct by reviewing corresponding .d.ts files. 
You are allowed to read node_modules files but you are not allowed to modify them.

## Project Overview

This is a monorepo containing 11 Vue.js frontend applications for a comprehensive point-of-sale and ticketing system with cashless payment capabilities. Each frontend serves a specific purpose within the ecosystem.

## Project Structure

```
mastodon-fronts/
├── src/
│   ├── fronts/                        # Individual frontend applications
│   │   ├── cas/                       # Central Authentication Service (port 9700)
│   │   ├── cashless-front/            # Customer-facing cashless payment (port 9366)
│   │   ├── cashless-partner/          # Partner cashless management (port 9400)
│   │   ├── littl-front/               # Link shortening service (port 9555)
│   │   ├── mastodon-front/            # Main administrative frontend (port 9888)
│   │   ├── point-of-sell/             # Point of Sale application (port 9600)
│   │   ├── product-front/             # Product management (port 9201)
│   │   ├── ticket-delegate-front/     # Ticket delegation system (port 9211)
│   │   ├── ticket-ecommerce/          # E-commerce ticketing (port 9355)
│   │   ├── ticketing-partner/         # Partner ticketing management (port 9857)
│   │   ├── ticketing-simplified-partner/ # Simplified ticketing (port 9858)
│   │   └── widget-front/              # Embeddable widget (port 9812)
│   └── shared/                        # Shared components and utilities
│       ├── components/                # Reusable Vue components
│       ├── mastodonCoreFront/         # Core types/models
│       ├── printApiSdk/               # Printing SDK
│       ├── repositories/              # API repositories
│       └── utils/                     # Utility functions
├── docker/                            # Docker configuration
├── tests/                             # Playwright E2E tests
└── playwright/                        # Playwright utilities
```

## Essential Commands

### Development
```bash
# Run individual frontends (each on its own port)
npm run builddev-cas                    # Port 9700
npm run builddev-cashless-front         # Port 9366
npm run builddev-cashless-partner       # Port 9400
npm run builddev-littl-front            # Port 9555
npm run builddev-mastodon-front         # Port 9888
npm run builddev-point-of-sell          # Port 9600
npm run builddev-product-front          # Port 9201
npm run builddev-ticket-delegate-front  # Port 9211
npm run builddev-ticket-ecommerce       # Port 9355
npm run builddev-ticketing-partner      # Port 9857
npm run builddev-ticketing-simplified-partner # Port 9858
npm run builddev-widget-front           # Port 9812
```

### Production Build
```bash
# Build all frontends with type checking
npm run buildprod-all

# Build individual frontend with type checking
npm run buildprod-[frontend-name]
# Example: npm run buildprod-cashless-front
```

### Type Checking
```bash
# Type check all frontends
npm run typecheck-all

# Type check runs vue-tsc --noEmit for each frontend
```

### Testing
```bash
# Run Playwright E2E tests
npx playwright test

# Run specific test file
npx playwright test tests/[test-file].spec.ts

# Run with UI mode
npx playwright test --ui

# Run with headed browser
npx playwright test --headed
```

### Docker
```bash
# Build Docker images (via GitLab CI)
# Images are tagged with:
# - Git commit SHA
# - Date (YYYY-MM-DD)
# - Version from project.json

# Local Docker development
cd docker
docker-compose up
```

## Architecture Patterns

### Component Structure
Each component follows a consistent pattern:
- `ComponentName.vue` - Template
- `ComponentName.ts` - Logic using vue-facing-decorator
- `ComponentName.scss` - Styles

Example:
```typescript
@Component({
    name: "ComponentName",
    components: { /* imported components */ }
})
export default class ComponentName extends Vue {
    // Component logic
}
```

### Repository Pattern
All API communication uses repositories extending HttpAuthedRepository:
```typescript
export class SomeRepository extends HttpAuthedRepository {
    async getSomething(): Promise<ResponseType> {
        return await this.get<ResponseType>('/api/endpoint');
    }
}
```

### Routing
Routes use AuthedVueJsRoute for authenticated pages:
```typescript
const routes: RouteDefinition<AuthedVueJsRoute>[] = [
    {
        path: '/path',
        component: ComponentName,
        meta: { permissions: ['permission.name'] }
    }
];
```

### Event Communication
AppBus is used for cross-component communication:
```typescript
// Emit event
AppBus.$emit('event-name', data);

// Listen to event
AppBus.$on('event-name', (data) => {
    // Handle event
});
```

### State Management
Each frontend maintains its own state using:
- `AppState` - Application-wide state
- `AppBus` - Event-driven state updates
- Repository pattern for API data

## Tech Stack

- **Vue 3** with TypeScript and decorators (vue-facing-decorator)
- **Vite** for development and builds
- **SCSS** for styling
- **Horizon2 SDK** (@groupk/horizon2-core, @groupk/horizon2-front)
- **Chart.js** for data visualization
- **QR/Barcode** scanning capabilities
- **WebSocket** support for real-time features
- **Payment Integrations**: Stripe, MangoPay, physical payment terminals

## Important Configuration Files

- `vite.config.shared.ts` - Shared Vite configuration with manual chunking
- `tsconfig.json` - TypeScript config (strict mode, ES2021 target)
- Each frontend has:
  - `project.json` - Version tracking
  - `vite.config.ts` - App-specific config extending shared config
  - `index.html` - Entry HTML
  - `index.ts` - Application bootstrap

## Build Optimization

The build uses manual chunks to optimize loading:
- `vue` - Vue framework and related packages
- `horizon` - Horizon2 SDK packages
- `mastodon-core` - Core Mastodon packages

## CI/CD (GitLab)

- Builds trigger on master branch when `project.json` changes
- Each frontend builds independently as a Docker image
- Images are pushed to registry with multiple tags

## Security Considerations

- Authentication via CAS (Central Authentication Service)
- Permission-based route access
- Secure payment processing with multiple providers
- Native bridge integration for desktop/mobile apps

## Development Tips

1. **Before modifying**: Check if functionality exists in `src/shared/`
2. **API changes**: Update repository classes in `src/shared/repositories/`
3. **New components**: Follow the .vue/.ts/.scss pattern
4. **Styling**: Use SCSS variables from each app's `variables.scss`
5. **Type safety**: TypeScript strict mode is enabled - ensure proper typing
6. **Testing**: Add E2E tests for critical user flows

## Common Gotchas

- Each frontend loads config from `public/config.json`
- Build commands run type checking before building (can be slow)
- Some frontends depend on specific backend services being available
- The project uses custom @groupk packages that may have specific behaviors