import {Component, Vue} from "vue-facing-decorator";
import {SidebarComponent, ApplicationsSwitcherComponent} from "@groupk/vue3-interface-sdk";
import type {SidebarHeader, SidebarMenu} from "@groupk/vue3-interface-sdk";
import {randomUUID, Router} from "@groupk/horizon2-front";
import {AutoWired} from "@groupk/horizon2-core";
import {OptionBuilder} from "vue-facing-decorator/dist/optionBuilder";
import {PlatformRepository} from "../../repositories/PlatformRepository";
import {PlatformDescriptorApiOut} from "@groupk/mastodon-core";
import LimitUsageComponent from "../../components/LimitUsageComponent/LimitUsageComponent.vue";
import EstablishmentSwitchComponent
    from "../../../../../shared/components/EstablishmentSwitchComponent/EstablishmentSwitchComponent.vue";
import {EstablishmentUrlBuilder} from "../../../../../shared/utils/EstablishmentUrlBuilder";
import SidebarStateListener from "../../../../../shared/utils/SidebarStateListener";

@Component({
    components: {
        sidebar: SidebarComponent,
        'application-switcher': ApplicationsSwitcherComponent,
        'limit-usage': LimitUsageComponent,
        'establishment-switch': EstablishmentSwitchComponent
    },
})
export default class SidebarView extends Vue {
    opened: boolean = false;
    hiddenSidebar: boolean = false;
    minimized: boolean = false;
    hidden: boolean = false;
    openSwitchFamily: boolean = false;

    displayMobileMenu!: boolean;
    header: SidebarHeader | undefined = {
        title: "Littl",
        icon: "fa-square-c",
        subtitle: "Votre plateforme",
    };

    menus: SidebarMenu[] = [
        {
            separator: false,
            navigations: [
                {
                    icon: "fa-link",
                    title: "Liens",
                    url: EstablishmentUrlBuilder.buildUrl('/links'),
                    name: [EstablishmentUrlBuilder.buildUrl('/links'), EstablishmentUrlBuilder.buildUrl('/'), EstablishmentUrlBuilder.buildUrl('')]
                },
            ],
        },
    ];

    bottom: SidebarMenu[] = [
        {
            separator: false,
            navigations: [{
                icon: "fa-sign-out-alt", title: "Déconnexion",
                url: '/cas-redirect?disconnect=true',
                name: "disconnect"
            }],
        },
    ];

    platformDescriptor!: PlatformDescriptorApiOut|null;
    selectedNavigation: string | undefined = undefined;

    /** UUID **/
    uid: string = randomUUID();

    @AutoWired(SidebarStateListener) accessor sidebarStateListener!: SidebarStateListener;
    @AutoWired(PlatformRepository) accessor platformRepository!: PlatformRepository;
    @AutoWired(Router) accessor router!: Router;

    constructor(optionBuilder: OptionBuilder, vueInstance: any) {
        super(optionBuilder, vueInstance);

        this.opened = this.sidebarStateListener.openedSidebar;
        this.hidden = this.sidebarStateListener.hiddenSidebar;
        this.minimized = this.sidebarStateListener.minimizedSidebar;

        this.sidebarStateListener.listen(this.stateChanged);
    }

    stateChanged(event: {type: string; value: any}){
        if (event.type === "openedSidebar") {
            this.opened = event.value;
        } else if (event.type === "minimizedSidebar") {
            this.minimized = event.value;
        } else if (event.type === "hiddenSidebar") {
            this.hidden = event.value;
        }
    }

    mounted() {
        this.selectedNavigation = window.location.pathname;

        this.router.hookOnAsync('newPageLoaded', (data) => {
            if (data.newRoute.location !== null) {
                this.selectedNavigation = window.location.pathname;
            }
            return Promise.resolve(data);
        });
    }

    async loadPlatform() {
        if(!this.platformDescriptor) this.platformDescriptor = (await this.platformRepository.callContract('get', undefined, undefined)).success();
    }

    navigationClicked(_value: string) {}
}
