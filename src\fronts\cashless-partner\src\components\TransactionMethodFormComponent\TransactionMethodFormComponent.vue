<script lang="ts" src="./TransactionMethodFormComponent.ts">
</script>

<style lang="sass">
@use './TransactionMethodFormComponent.scss' as *
</style>

<template>
    <div class="transaction-method-form-component">
        <form-modal-or-drawer
            :state="opened"
            title="Changer le moyen de paiement de la transaction"
            subtitle="Le changement de moyen de paiement sera bien pris en compte dans les statistiques"
            @close="close()"
        >
            <template v-slot:content>
                <div v-if="loading" class="loading-container">
                    <div class="loader"></div>
                </div>
                <div v-else class="input-group">
                    <label> Choisir la méthode de paiement </label>
                    <dropdown
                        placeholder="Méthode de paiement"
                        :searchable="true"
                        :values="paymentMethodsDropdownValues"
                        @update="selectedPaymentMethod = $event"
                    ></dropdown>
                </div>

                <div class="form-error" v-if="error">
                    <i class="fa-solid fa-exclamation-circle"></i>
                    <div class="details">
                        <span class="title"> Une erreur est survenue </span>
                        <span class="description">
                            {{ error }}
                        </span>
                    </div>
                </div>
            </template>

            <template v-slot:buttons>
                <button class="white button" :class="{disabled: loading}" @click="close()"> Annuler </button>
                <button class="button" :class="{disabled: loading, loading: loading}" @click="validate()">
                    Modifier la méthode de paiement
                </button>
            </template>
        </form-modal-or-drawer>
    </div>
</template>