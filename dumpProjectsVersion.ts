import * as fs from 'fs';
import * as path from 'path';

function findProjectJsonFiles(dir: string): string[] {
    const files: string[] = [];
    const entries = fs.readdirSync(dir);

    for (const entry of entries) {
        const fullPath = path.join(dir, entry);
        const stat = fs.statSync(fullPath);

        if (stat.isDirectory()) {
            files.push(...findProjectJsonFiles(fullPath));
        } else if (entry === 'project.json') {
            files.push(fullPath);
        }
    }

    return files;
}

function logProjectVersions() {
    const frontsDir = path.join(process.cwd(), 'src', 'fronts');
    const projectFiles = findProjectJsonFiles(frontsDir);

    for (const file of projectFiles) {
        const content = fs.readFileSync(file, 'utf-8');
        const projectData = JSON.parse(content);
        const subfolderName = path.basename(path.dirname(file));
        console.log(`${subfolderName} - ${projectData.version}`);
    }
}

logProjectVersions();