<script lang="ts" src="./ProfileFormComponent.ts">
</script>

<style lang="sass" scoped>
@use './ProfileFormComponent.scss' as *
</style>

<template>
    <form-modal-or-drawer
        :state="true"
        :title="editingProfile ? 'Modifier le point de vente' : 'Créer un point de vente'"
        :subtitle="'Le point de vente pourra être associé a un terminal de vente'"
    >
        <template v-slot:content>

            <div class="loading-container" v-if="loading">
                <div class="loader"></div>
            </div>
            <template v-else>
                <div class="input-group">
                    <label for="title"> Nom </label>
                    <input v-model="profile.name" type="text" placeholder="Nom" />
                </div>

<!--                <div class="two-inputs">-->
<!--                    <div class="input-group" :class="{disabled: !appState.advancedInterfaces}">-->
<!--                        <label for="title"> ID de la puce de crédit </label>-->
<!--                        <div class="ui input">-->
<!--                            <input v-model="profile.creditChipId" type="number" />-->
<!--                        </div>-->
<!--                    </div>-->

<!--                    <div class="input-group" :class="{disabled: !appState.advancedInterfaces}">-->
<!--                        <label for="title"> ID de la puce de débit </label>-->
<!--                        <input v-model="profile.debitChipId" type="number" />-->
<!--                    </div>-->
<!--                </div>-->

<!--                <div class="two-inputs" v-if="appState.advancedInterfaces" style="align-items: flex-end">-->
<!--                    <div class="input-group">-->
<!--                        <label for="title"> Suppression des transactions (temps en secondes) </label>-->
<!--                        <div class="ui input">-->
<!--                            <input v-model="profile.txHistoryDelay" type="number" />-->
<!--                        </div>-->
<!--                    </div>-->

<!--                    <div class="input-group">-->
<!--                        <label for="title"> Mot de passe admin {{}} </label>-->
<!--                        <div class="ui input">-->
<!--                            <input v-model="profile.adminPassword" type="text" />-->
<!--                        </div>-->
<!--                    </div>-->
<!--                </div>-->

<!--                <div v-if="!profile.terminalKeypadDebit" class="sub-form">-->
<!--                    <div class="enable-credit-debit" @click="addDebitKeypad()">-->
<!--                        <i class="fa-regular fa-plus"></i>-->
<!--                        Autoriser le débit manuel-->

<!--                        <hoverable-info-->
<!--                            :data="{-->
<!--                                title: 'Permet de débiter manuellement',-->
<!--                                description: `Il sera possible de saisir un montant libre pour débiter un utilisateur.`,-->
<!--                                image: null-->
<!--                            }"-->
<!--                            :alignment="'LEFT'"-->
<!--                        ></hoverable-info>-->
<!--                    </div>-->

<!--                </div>-->
<!--                <div v-else class="sub-form">-->
<!--                    <div class="title-group">-->
<!--                        <span class="title"> Configuration débit </span>-->
<!--                        <span class="disable" @click="profile.terminalKeypadDebit = null">-->
<!--                            <i class="fa-regular fa-ban"></i>-->
<!--                            Désactiver le débit-->
<!--                        </span>-->
<!--                    </div>-->

<!--                    <profile-payment-editor-->
<!--                        v-if="showDebitEditModal && profile.terminalKeypadDebit"-->
<!--                        :keypad="profile.terminalKeypadDebit"-->
<!--                        :payment-methods="paymentMethods"-->
<!--                        @done="showDebitEditModal = false"-->
<!--                    ></profile-payment-editor>-->

<!--                    <div class="available-methods">-->
<!--                        <span v-if="(profile.terminalKeypadDebit.paymentMethods ?? []).length === 0"> Aucun moyen de paiement </span>-->

<!--                        <div class="method" v-for="methodUid of (profile.terminalKeypadDebit.paymentMethods ?? [])">-->
<!--                            <span> {{ getMethodWithUid(methodUid).name }} </span>-->
<!--                        </div>-->
<!--                        <div class="method action" @click="showDebitEditModal = true">-->
<!--                            <i class="fa-regular fa-cog"></i>-->
<!--                        </div>-->
<!--                    </div>-->

<!--                    <div class="toggle-input" v-if="appState.advancedInterfaces">-->
<!--                        <toggle :default-toggled-value="profile.terminalKeypadDebit.autoDecimals" @toggled="profile.terminalKeypadDebit.autoDecimals = $event"></toggle>-->
<!--                        <div class="infos">-->
<!--                            <span class="title"> Décimales automatiques </span>-->
<!--                        </div>-->
<!--                    </div>-->

<!--                    <div class="toggle-input">-->
<!--                        <toggle :default-toggled-value="profile.terminalKeypadDebit.requirePaymentMethod" @toggled="profile.terminalKeypadDebit.requirePaymentMethod = $event"></toggle>-->
<!--                        <div class="infos">-->
<!--                            <span class="title"> Requiert les méthodes de paiement </span>-->
<!--                        </div>-->

<!--                        <hoverable-info-->
<!--                            :data="{-->
<!--                                title: ' Requiert les méthodes de paiement ',-->
<!--                                description: `Cette fonction permet d'indiquer lors d'un débit ou un crédit, la méthode de paiement utilisé.<br/> <b>Exemple :</b> J'ai crédité mon client de 15,00€, et il a rechargé grace à un billet de 50€ (Liquide).`,-->
<!--                                image: null-->
<!--                            }"-->
<!--                            :alignment="'LEFT'"-->
<!--                        ></hoverable-info>-->
<!--                    </div>-->

<!--                    <div class="toggle-input" v-if="appState.advancedInterfaces">-->
<!--                        <toggle :default-toggled-value="profile.terminalKeypadDebit.requireAdminAuth" @toggled="profile.terminalKeypadDebit.requireAdminAuth = $event"></toggle>-->
<!--                        <div class="infos">-->
<!--                            <span class="title"> Requiert admin </span>-->
<!--                        </div>-->

<!--                    </div>-->
<!--                </div>-->

<!--                <div v-if="!profile.terminalKeypadCredit" class="sub-form">-->
<!--                    <div class="enable-credit-debit" @click="addCreditKeypad()">-->
<!--                          <i class="fa-regular fa-plus"></i>-->
<!--                        Autoriser le crédit manuel-->
<!--                      <hoverable-info-->
<!--                          :data="{-->
<!--                                title: 'Permet de créditer manuellement',-->
<!--                                description: `Il sera possible de saisir un montant libre pour créditer un utilisateur.`,-->
<!--                                image: null-->
<!--                            }"-->
<!--                          :alignment="'LEFT'"-->
<!--                      ></hoverable-info>-->
<!--                    </div>-->
<!--                </div>-->
<!--                <div v-else class="sub-form">-->
<!--                    <div class="title-group">-->
<!--                        <span class="title"> Configuration crédit </span>-->
<!--                        <span class="disable" @click="profile.terminalKeypadCredit = null">-->
<!--                            <i class="fa-regular fa-ban"></i>-->
<!--                            Désactiver le crédit-->
<!--                        </span>-->
<!--                    </div>-->

<!--                    <profile-payment-editor-->
<!--                        v-if="showCreditEditModal && profile.terminalKeypadCredit"-->
<!--                        :keypad="profile.terminalKeypadCredit"-->
<!--                        :payment-methods="paymentMethods"-->
<!--                        @done="showCreditEditModal = false"-->
<!--                    ></profile-payment-editor>-->

<!--                    <div class="available-methods">-->
<!--                        <span v-if="(profile.terminalKeypadCredit.paymentMethods ?? []).length === 0"> Aucun moyen de paiement </span>-->

<!--                        <div class="method" v-for="methodUid of (profile.terminalKeypadCredit.paymentMethods ?? [])">-->
<!--                            <span> {{ getMethodWithUid(methodUid).name }} </span>-->
<!--                        </div>-->

<!--                        <div class="method action" @click="showCreditEditModal = true">-->
<!--                            <i class="fa-regular fa-cog"></i>-->
<!--                        </div>-->
<!--                    </div>-->

<!--                    <div class="toggle-input" v-if="appState.advancedInterfaces">-->
<!--                        <toggle :default-toggled-value="profile.terminalKeypadCredit.autoDecimals" @toggled="profile.terminalKeypadCredit.autoDecimals = $event"></toggle>-->
<!--                        <div class="infos">-->
<!--                            <span class="title"> Décimales automatiques </span>-->
<!--                        </div>-->

<!--                    </div>-->

<!--                    <div class="toggle-input">-->
<!--                        <toggle :default-toggled-value="profile.terminalKeypadCredit.requirePaymentMethod" @toggled="profile.terminalKeypadCredit.requirePaymentMethod = $event"></toggle>-->
<!--                        <div class="infos">-->
<!--                            <span class="title"> Requiert les méthodes de paiement </span>-->
<!--                        </div>-->

<!--                      <hoverable-info-->
<!--                          :data="{-->
<!--                                title: ' Requiert les méthodes de paiement ',-->
<!--                                description: `Cette fonction permet d'indiquer lors d'un débit ou un crédit, la méthode de paiement utilisé.<br/> <b>Exemple :</b> J'ai crédité mon client de 15,00€, et il a rechargé grace à un billet de 50€ (Liquide).`,-->
<!--                                image: null-->
<!--                            }"-->
<!--                          :alignment="'LEFT'"-->
<!--                      ></hoverable-info>-->
<!--                    </div>-->

<!--                    <div class="toggle-input" v-if="appState.advancedInterfaces">-->
<!--                        <toggle :default-toggled-value="profile.terminalKeypadCredit.requireAdminAuth" @toggled="profile.terminalKeypadCredit.requireAdminAuth = $event"></toggle>-->
<!--                        <div class="infos">-->
<!--                            <span class="title"> Requiert admin </span>-->
<!--                        </div>-->
<!--                    </div>-->
<!--                </div>-->

<!--                <template v-if="appState.advancedInterfaces">-->
<!--                    <div v-if="!profile.pos" class="sub-form">-->
<!--                        <div class="enable-credit-debit" @click="addPos()">-->
<!--                            <i class="fa-regular fa-plus"></i>-->
<!--                            Autoriser la pos-->
<!--                        </div>-->
<!--                    </div>-->
<!--                    <div v-else class="sub-form">-->
<!--                        <div class="title-group">-->
<!--                            <span class="title"> Configuration pos </span>-->
<!--                            <span class="disable" @click="profile.pos = null">-->
<!--                                <i class="fa-regular fa-ban"></i>-->
<!--                                Désactiver la pos-->
<!--                            </span>-->
<!--                        </div>-->

<!--                        <div class="input-group">-->
<!--                            <label for="title"> listeningPort </label>-->
<!--                            <div class="ui input">-->
<!--                                <input v-model="profile.pos.listeningPort" type="number" />-->
<!--                            </div>-->
<!--                        </div>-->

<!--                        <div class="two-inputs">-->
<!--                            <div-->
<!--                                class="boolean-input"-->
<!--                                :class="{selected: profile.pos.allowReturn}"-->
<!--                                @click="profile.pos.allowReturn = !profile.pos.allowReturn"-->
<!--                            >-->
<!--                                <i class="fa-regular" :class="profile.pos.allowReturn ? 'fa-square-check' : 'fa-square'"></i>-->
<!--                                <div class="right">-->
<!--                                    <span class="title"> allowReturn </span>-->
<!--                                </div>-->
<!--                            </div>-->
<!--                        </div>-->
<!--                    </div>-->
<!--                </template>-->

<!--                <div v-if="!profile.kiosk && appState.advancedInterfaces" class="sub-form">-->
<!--                    <div class="enable-credit-debit" @click="addKiosk()">-->
<!--                        <i class="fa-regular fa-plus"></i>-->
<!--                        Autoriser le kiosk-->
<!--                    </div>-->
<!--                </div>-->

<!--                <div class="sub-form" v-if="profile.kiosk">-->
<!--                    <div class="title-group">-->
<!--                        <span class="title"> Configuration kiosk </span>-->
<!--                        <span v-if="appState.advancedInterfaces" class="disable" @click="profile.kiosk = null">-->
<!--                            <i class="fa-regular fa-ban"></i>-->
<!--                            Désactiver le kiosk-->
<!--                        </span>-->
<!--                    </div>-->

<!--                    <div class="input-group">-->
<!--                        <label for="title"> Montant fiat par défaut </label>-->
<!--                        <input class="white" v-model="profile.kiosk.defaultFiatAmount" type="number" />-->
<!--                    </div>-->

<!--                    <div class="two-inputs">-->
<!--                        <div class="input-group">-->
<!--                            <label for="title"> Fiat MIN </label>-->
<!--                            <div class="ui input">-->
<!--                                <input class="white" v-model="profile.kiosk.fiatMin" type="number" />-->
<!--                            </div>-->
<!--                        </div>-->

<!--                        <div class="input-group">-->
<!--                            <label for="title"> Fiat MAX </label>-->
<!--                            <input class="white" v-model="profile.kiosk.fiatMax" type="number" />-->
<!--                        </div>-->
<!--                    </div>-->

<!--                    <div class="two-inputs">-->
<!--                        <div class="input-group">-->
<!--                            <label for="title"> IP du terminal bancaire </label>-->
<!--                            <div class="ui input">-->
<!--                                <input class="white" v-model="profile.kiosk.bankTerminalIp" type="text" />-->
<!--                            </div>-->
<!--                        </div>-->

<!--                        <div class="input-group">-->
<!--                            <label for="title"> Port du terminal bancaire </label>-->
<!--                            <input class="white" v-model="profile.kiosk.bankTerminalPort" type="number" />-->
<!--                        </div>-->
<!--                    </div>-->

<!--                    <div class="input-group">-->
<!--                        <label for="title"> IP du terminal cashless </label>-->
<!--                        <input class="white" v-model="profile.kiosk.cashlessTerminalIp" type="text" />-->
<!--                    </div>-->

<!--                    <div class="input-group">-->
<!--                        <label for="title"> Méthode de paiement </label>-->
<!--                        <dropdown-->
<!--                            class="white"-->
<!--                            placeholder="Méthode de paiement"-->
<!--                            :values="getPaymentMethodsDropdownValues()"-->
<!--                            :default-selected="profile.kiosk.paymentMethodUid"-->
<!--                            @update="profile.kiosk.paymentMethodUid = $event"-->
<!--                        ></dropdown>-->
<!--                    </div>-->
<!--                </div>-->

<!--                <div class="sub-form">-->
<!--                    <div class="header">-->
<!--                        <span class="title"> Puces admin </span>-->
<!--                        <hoverable-info-->
<!--                            :data="{-->
<!--                                title: ' Débloquez les fonctionnalités avancées sur le terminal ',-->
<!--                                description: `Une puce admin permet de passer d'un point de vente à l'autre sur un terminal sans avoir besoin d'internet.`,-->
<!--                                image: null-->
<!--                            }"-->
<!--                            :alignment="'LEFT'"-->
<!--                        ></hoverable-info>-->
<!--                    </div>-->

<!--                    <div class="two-inputs">-->
<!--                        <span v-if="profile.adminPublicChipIds.length === 0" class="empty-sub-form"> Aucune puce admin </span>-->
<!--                        <div class="input-group" v-for="(chipId, index) in profile.adminPublicChipIds">-->
<!--                            <label for="title"> Numéro de puce </label>-->
<!--                            <div class="action-input">-->
<!--                                <input maxlength="8" class="white" :value="profile.adminPublicChipIds[index]" @input="setAdminPublicChip($event, index)"  type="text" />-->
<!--                                <i @click="profile.adminPublicChipIds.splice(index, 1)" class="fa-regular fa-trash-alt"></i>-->
<!--                            </div>-->
<!--                        </div>-->
<!--                    </div>-->

<!--                    <div>-->
<!--                        <button class="small transparent button" @click="profile.adminPublicChipIds.push('')">-->
<!--                            <i class="fa-regular fa-plus"></i>-->
<!--                            Ajouter une puce-->
<!--                        </button>-->
<!--                    </div>-->
<!--                </div>-->

<!--                <div class="toggles">-->
<!--                    <div class="toggle-input">-->
<!--                        <toggle :default-toggled-value="profile.allowBalanceReading" @toggled="profile.allowBalanceReading = $event"></toggle>-->
<!--                        <div class="infos">-->
<!--                            <span class="title"> Autoriser la lecture de solde </span>-->
<!--                        </div>-->

<!--                        <hoverable-info-->
<!--                            :data="{-->
<!--                                title: 'Autoriser la lecture de solde',-->
<!--                                description: `-->
<!--                                  Permet au terminal de lire le solde d'une carte sans faire d'opération.<br/>-->
<!--                                  <br/>-->
<!--                                  <b>Utilisation :</b> une personne vient vous voir car elle ne se souvient plus de son solde.-->
<!--                                `,-->
<!--                                image: null-->
<!--                            }"-->
<!--                            :alignment="'LEFT'"-->
<!--                        ></hoverable-info>-->
<!--                    </div>-->

<!--                    <div class="toggle-input">-->
<!--                        <toggle :default-toggled-value="profile.resetBalanceOptionOnBalanceReading" @toggled="profile.resetBalanceOptionOnBalanceReading = $event"></toggle>-->
<!--                        <div class="infos">-->
<!--                            <span class="title"> Bouton de réinitialisation de solde après lecture de solde  </span>-->
<!--                        </div>-->

<!--                      <hoverable-info-->
<!--                          :data="{-->
<!--                                title: 'Bouton de réinitialisation de solde après lecture de solde',-->
<!--                                description: `-->
<!--                                  Permet de lire le solde, puis cliquer sur le bouton et rescanner le bracelet pour le vider.<br/>-->
<!--                                  <br/>-->
<!--                                  <b>Information :</b> cela évite de lire le solde, puis d'aller dans terminal, entrer le montant à vider, et vider le bracelet.-->
<!--                                `,-->
<!--                                image: null-->
<!--                            }"-->
<!--                          :alignment="'LEFT'"-->
<!--                      ></hoverable-info>-->
<!--                    </div>-->

<!--                    <div class="toggle-input">-->
<!--                        <toggle :default-toggled-value="profile.allowDeviceHistoricReading" @toggled="profile.allowDeviceHistoricReading = $event"></toggle>-->
<!--                        <div class="infos">-->
<!--                            <span class="title"> Autoriser la lecture de l'historique </span>-->
<!--                        </div>-->

<!--                      <hoverable-info-->
<!--                          :data="{-->
<!--                                title: 'Autoriser la lecture de l\'historique',-->
<!--                                description: `-->
<!--                                  Permet de consulter le solde du terminal.<br/>-->
<!--                                  <br/>-->
<!--                                  <b>Information :</b> vous ne verrez pas l'historique des autres terminaux. Uniquement du terminal en question.-->
<!--                                `,-->
<!--                                image: null-->
<!--                            }"-->
<!--                          :alignment="'LEFT'"-->
<!--                      ></hoverable-info>-->
<!--                    </div>-->

<!--                    <div class="toggle-input">-->
<!--                        <toggle :default-toggled-value="profile.allowTerminalProduct" @toggled="profile.allowTerminalProduct = $event"></toggle>-->
<!--                        <div class="infos">-->
<!--                            <span class="title"> Activer la visualisation des produits </span>-->
<!--                        </div>-->

<!--                      <hoverable-info-->
<!--                          :data="{-->
<!--                                title: 'Activer la visualisation des produits',-->
<!--                                description: `-->
<!--                                  Vendez en sélectionnant des produits.<br/>-->
<!--                                  <br/>-->
<!--                                  <b>Information :</b> vous devez créer une catégorie avec des produits à l'intérieur et les associer à ce point de vente.-->
<!--                                `,-->
<!--                                image: null-->
<!--                            }"-->
<!--                          :alignment="'LEFT'"-->
<!--                      ></hoverable-info>-->
<!--                    </div>-->

<!--                    <div class="toggle-input">-->
<!--                        <toggle :default-toggled-value="profile.demoMode" @toggled="profile.demoMode = $event"></toggle>-->
<!--                        <div class="infos">-->
<!--                            <span class="title"> Mode démo </span>-->
<!--                            <span class="subtitle"> Permet d'exclure des statistiques les transactions. Les transactions sont quand même remontées. </span>-->
<!--                        </div>-->
<!--                    </div>-->

<!--                    <div class="toggle-input" v-if="appState.advancedInterfaces">-->
<!--                        <toggle :default-toggled-value="profile.allowDevicePosManaged" @toggled="profile.allowDevicePosManaged = $event"></toggle>-->
<!--                        <div class="infos">-->
<!--                            <span class="title"> allowDevicePosManaged </span>-->
<!--                        </div>-->
<!--                    </div>-->

<!--                    <div class="toggle-input" v-if="appState.advancedInterfaces">-->
<!--                        <toggle :default-toggled-value="profile.allowDeviceKiosk" @toggled="profile.allowDeviceKiosk = $event"></toggle>-->
<!--                        <div class="infos">-->
<!--                            <span class="title"> allowDeviceKiosk </span>-->
<!--                        </div>-->
<!--                    </div>-->
<!--                </div>-->

<!--                <div class="form-error" v-if="error">-->
<!--                    <i class="fa-solid fa-exclamation-circle"></i>-->
<!--                    <div class="details">-->
<!--                        <span class="title"> Erreur </span>-->
<!--                        <span class="description">{{ error }}</span>-->
<!--                    </div>-->
<!--                </div>-->
            </template>
        </template>


        <template v-slot:buttons>
            <button class="button" :class="{loading: saving, disabled: saving}" @click="save()">
                <i class="fa-regular fa-check"></i>
                Sauvegarder
            </button>
        </template>
    </form-modal-or-drawer>
</template>