import {Component, Prop, Ref, Vue} from "vue-facing-decorator";
import {PaymentManager, PendingPayment} from "../../class/payment/PaymentManager";
import {PosState} from "../../model/PosState";
import {OrderPaymentApiOut, OrderPaymentStatus, PaymentMethodDataStatus} from "@groupk/mastodon-core";
import {PhysicalPaymentProtocol} from "../../class/payment/processors/PhysicalPaymentProtocol";
import {NeptingPaymentProcessor} from "../../class/payment/processors/NeptingPaymentProcessor";
import NeptingProcessorComponent from "./NeptingProcessorComponent/NeptingProcessorComponent.vue";
import {NeptingBridge} from "../../class/NeptingBridge";
import {GetInstance} from "@groupk/horizon2-core";

@Component({
	components: {
		'nepting-processor': NeptingProcessorComponent
	},
	emits: ['close', 'paid']
})
export default class PaymentComponent extends Vue {
	@Prop() posState!: PosState;
	@Prop() pendingPaymentData!: PendingPayment;

	paymentManager!: PaymentManager;
	processor: PhysicalPaymentProtocol|null = null;
	currentPayment: OrderPaymentApiOut|null = null;

	NeptingPaymentProcessor = NeptingPaymentProcessor;

	error: 'CONNECTION'|null = null;

	paid: number = 0;
	refund: number = 0;

	@Ref() cardPaymentModal!: NeptingProcessorComponent;

	async mounted() {
		this.paymentManager = new PaymentManager(this.posState);

		try {
			if(this.pendingPaymentData.payment.status === OrderPaymentStatus.SUCCESS) {
				this.$emit('close');
			} else {
				this.processor = this.pendingPaymentData.processors[0];
				this.currentPayment = this.pendingPaymentData.payment;

				this.processor.on('totalPaidChanged', (paidAmount) => {
					this.paid = paidAmount;
				});

				this.processor.on('totalRefundChanged', (refundAmount) => {
					this.refund = refundAmount;
				});

				this.processor.on('done',async (paid) => {
					if(!this.posState.currentOrder) throw new Error('no_current_order');

					this.currentPayment = await this.paymentManager.updatePaymentState(this.posState.currentOrder, this.pendingPaymentData.payment, paid);

					if(paid) this.$emit('paid');
					if(paid && this.processor?.config.autoCloseOnSuccess) {
						this.close();
					}
					this.$forceUpdate();
				});

				if(this.processor instanceof NeptingPaymentProcessor) {
					try  {
						await GetInstance(NeptingBridge).init();
					} catch(err) {
						this.cardPaymentModal.internalError = 'Nepting n\'est pas disponible sur cet appareil';
						return;
					}

					this.processor.on('neptingResult', async (result) => {
						if(!this.posState.currentOrder) throw new Error('no_current_order');

						this.cardPaymentModal.setPaymentResponse(result, {refund: false});

						if(result.status === PaymentMethodDataStatus.SUCCESS) {
							this.currentPayment = await this.paymentManager.updatePaymentState(this.posState.currentOrder, this.pendingPaymentData.payment, true, result);
							this.$emit('paid');
						} else {
							this.currentPayment = await this.paymentManager.updatePaymentState(this.posState.currentOrder, this.pendingPaymentData.payment, false, result);
						}

						this.$forceUpdate();
					});

					this.processor.on('neptingError', async (err) => {
						if(!this.posState.currentOrder) throw new Error('no_current_order');

						this.cardPaymentModal.internalError = err;
						this.currentPayment = await this.paymentManager.updatePaymentState(this.posState.currentOrder, this.pendingPaymentData.payment, false);
					});
				}

				await this.processor.initiatePayment(this.pendingPaymentData.payment);
			}
		} catch(err) {
			console.log(err);
			const error = err as Record<string, undefined>
			if(typeof error === 'object' && 'message' in error && error.message === 'missing_configuration') {
				this.$emit('close');
			} else {
				this.error = 'CONNECTION';
			}
		}
	}

	beforeUnmount() {
		if(this.processor) this.processor.cancel();
	}

	cancel() {
		this.processor?.cancel();
		this.$emit('close');
	}

	close() {
		this.$emit('close');
	}
}