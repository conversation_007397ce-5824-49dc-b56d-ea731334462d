import {Component, Prop, Vue} from "vue-facing-decorator";
import {FormModalOrDrawerComponent} from "@groupk/vue3-interface-sdk";
import {AutoWired, SearchUtils, VisualScopedUuid} from "@groupk/horizon2-core";
import {EstablishmentAccountRepository} from "../../../../../shared/repositories/EstablishmentAccountRepository";
import {EstablishmentDeviceApiOut, UuidScopeIot_deviceApp} from "@groupk/mastodon-core";
import {EstablishmentAccountProfileApi} from "@groupk/mastodon-core";
import {CleaveDirective} from "../../../../../shared/directives/CleaveDirective";
import {AppState} from "../../../../../shared/AppState";

@Component({
	directives: {
		cleave: CleaveDirective,
	},
	components: {
		'form-modal-or-drawer': FormModalOrDrawerComponent,
	},
	emits: ['close', 'selected']
})
export default class DeviceSelectionComponent extends Vue {
	@Prop({default: []}) excludedDevices!: VisualScopedUuid<UuidScopeIot_deviceApp>[];
	@Prop() devices!: EstablishmentDeviceApiOut[];
	@Prop({required: true}) accountProfiles!: EstablishmentAccountProfileApi[];

	selectedEstablishmentDevices: VisualScopedUuid<UuidScopeIot_deviceApp>[] = [];

	opened: boolean = false;
	search: string = '';
	error: string|null = null;

	@AutoWired(EstablishmentAccountRepository) accessor establishmentAccountRepository!: EstablishmentAccountRepository;
	@AutoWired(AppState) accessor appState!: AppState;

	mounted() {
		setTimeout(() => this.opened = true, 0);
	}

	get filteredEstablishmentDevices() {
		return SearchUtils.searchInTab(this.devices, (device) => {
			return [device.model, (device.hardwareId ?? ''), device.brand];
		}, this.search).filter((device) => !this.excludedDevices.includes(device.uid)).sort((deviceA, deviceB) => {
			if(this.isLinkedWithProfile(deviceA) || deviceA.establishmentAccountUid === null) return 1;
			if(this.isLinkedWithProfile(deviceB) || deviceB.establishmentAccountUid === null) return -1;
			return 0
		});
	}

	isLinkedWithProfile(device: EstablishmentDeviceApiOut) {
		return this.accountProfiles.find(profile => profile.establishmentAccountUid === device.establishmentAccountUid) !== undefined;
	}

	toggleEstablishmentDevice(establishmentDevice: EstablishmentDeviceApiOut) {
		if(this.isLinkedWithProfile(establishmentDevice) || establishmentDevice.establishmentAccountUid === null) return;
		const index = this.selectedEstablishmentDevices.findIndex((deviceUid) => establishmentDevice.uid === deviceUid);
		if(index === -1) {
			this.selectedEstablishmentDevices.push(establishmentDevice.uid);
		} else {
			this.selectedEstablishmentDevices.splice(index, 1);
		}
	}

	select() {
		this.$emit('selected', this.selectedEstablishmentDevices);
		this.close();
	}

	close() {
		this.opened = false;
		setTimeout(() => this.$emit('close'), 300);
	}
}