import {GetInstance, VisualScopedUuid} from "@groupk/horizon2-core";
import {
    OrderApiOut,
    ProductApiOut,
    ProductRevisionsFetchApiIn,
    PurchaseApiOut,
    UuidScopeProductProductRevision
} from "@groupk/mastodon-core";
import {PosState} from "../model/PosState";
import {ProductRepository} from "../../../../shared/repositories/ProductRepository";
import {LocalOrderRepository} from "../repositories/LocalOrderRepository";

export class ProductUtils {
    /**
     * Gets missing product revisions from local orders and supplementary orders
     * @param suppOrdersToCheck Optional supplementary orders to check
     */
    public static async getMissingProductRevisions(
        suppOrdersToCheck: OrderApiOut[] = []
    ) {
        const posState = GetInstance(PosState);
        const productRepository = GetInstance(ProductRepository);
        const localOrderRepository = GetInstance(LocalOrderRepository);

        const missingRevisionUids: VisualScopedUuid<UuidScopeProductProductRevision>[] = [];
        const localOrders = await localOrderRepository.findAll();
        for(let localOrder of localOrders) {
            for(let purchase of localOrder.order.purchases) {
                missingRevisionUids.push(...this.getPurchaseMissingProductRevisions(purchase));
            }
        }

        for(let order of suppOrdersToCheck) {
            for(let purchase of order.purchases) {
                missingRevisionUids.push(...this.getPurchaseMissingProductRevisions(purchase));
            }
        }

        // New Set to remove duplicates
        const apiIn = new ProductRevisionsFetchApiIn({revisionsUid: [...new Set(missingRevisionUids)]});
        if (apiIn.revisionsUid.length > 0) {
            posState.orderExecutorModel.registerProductRevisions(
                (await productRepository.callContract('fetchMultipleRevisions', {establishmentUid: posState.establishmentUid}, apiIn)).success()
            );
        }
    }

    /**
     * Gets missing product revisions from a purchase
     * @param purchase The purchase to check
     * @returns Array of missing product revision UIDs
     */
    public static getPurchaseMissingProductRevisions(
        purchase: PurchaseApiOut
    ): VisualScopedUuid<UuidScopeProductProductRevision>[] {
        const posState = GetInstance(PosState);
        const missingRevisionUids: VisualScopedUuid<UuidScopeProductProductRevision>[] = [];
        if(!posState.orderExecutorModel.getProductRevision(purchase.productRevisionUid)) {
            missingRevisionUids.push(purchase.productRevisionUid);
        }
        for(let item of purchase.items) {
            for(let group of item.groups) {
                for(let childPurchase of group.purchases) {
                    missingRevisionUids.push(...this.getPurchaseMissingProductRevisions(childPurchase));
                }
            }
        }
        return missingRevisionUids;
    }

    public static doesProductNeedManualConfiguration(product: ProductApiOut) {
        for(let group of product.lastRevision.groups) {
            for(let item of group.items) {
                if(item.minQuantity !== item.maxQuantity) return true;
                if(item.product) {
                    const subProductNeeds = this.doesProductNeedManualConfiguration(item.product);
                    if(subProductNeeds) return true;
                }
            }
        }
        return false;
    }
}
