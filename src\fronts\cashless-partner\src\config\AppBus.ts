import {EventEmitter} from '@groupk/horizon2-core';
import {SimpleProductApiOut, SimpleProductCategoryApiOut, TransactionApiOut} from '@groupk/mastodon-core';
import {Toast} from "../components/ToastManagerComponent/ToastManagerComponent";

export class AppBus extends EventEmitter<{
	transactionReceived: TransactionApiOut,
	disconnectedFromRemote: undefined,
	connectedToRemote: undefined,
	'point-of-sale-product-clicked': { product: SimpleProductApiOut, isMobile: boolean },
	'selectedCategory': SimpleProductCategoryApiOut,
	'point-of-sale-add-filtered-products-clicked': SimpleProductApiOut[],
	'emit-toast': Toast
}>{

}
