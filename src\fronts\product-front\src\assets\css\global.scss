
:root {
    --primary-hover-color: #CEDEFD !important;
    --primary-hover-text-color: black !important;
    --primary-color: #031B49 !important;
    --primary-button-hover-color: #03163d !important;
    --primary-text-color: white !important;
    --desktop-padding: 40px !important;
    --mobile-padding: 20px !important;
}

html, body {
    height: 100%;
    margin: 0;
    padding: 0;
    overflow: hidden;
}

body.native {
    .page .layout-center-content, .page .layout-main-content-right-panel {
        padding-top: 40px !important;
    }
    .top-bar-component {
        padding-top: 70px;
    }
}

*:not(i, text) {
    font-family: 'Montserrat', sans-serif !important;
}

.page {
    overflow: hidden;
    height: 100%;
    width: 100%;
    box-sizing: border-box;
}

#mainRouterContainer, #oldRouterContainer {
    width: 100%;
    height: 100%;
}

// Computer
@media (min-width: 900px) {
    body, #mainRouterContainer, #oldRouterContainer {
        display: flex;
    }
}

// Mobile & Tablet
@media (max-width: 900px) {
    #mainRouterContainer .page, #oldRouterContainer .page {
        padding-top: 60px;
    }
}

.layout-main-content-right-panel {
    .close {
        display: none;
        margin-bottom: 20px;
        cursor: pointer;
        user-select: none;
    }

    @media screen and (max-width: 1400px) {
        .close {
            display: flex;
            gap: 10px;
            align-items: center;
        }
    }
}

.loading-container {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 20px;
    width: 100%;
}

.empty-right-panel {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 20px;
    justify-content: center;
    height: 100%;
    color: #636363;
    font-size: 14px;
    text-align: center;

    img {
        width: 150px;
    }
}

.text-align-center {
    text-align: center;
}

.checkbox {
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid black;
    border-radius: 3px;
    height: 18px;
    width: 18px;
    cursor: pointer;
    box-sizing: border-box;
    flex-shrink: 0;

    i {
        font-size: 11px;
        display: none;
    }

    &.selected {
        border: none;
        background: #1099FD;
        color: white !important;

        i {
            display: initial;
        }
    }

    &.disabled {
        opacity: 0.5;
        pointer-events: none;
    }

    &.half-selected {
        color: black;

        i {
            display: initial;
        }
    }
}

.table-scroll-wrapper {
    overflow: auto;

    .nowrap {
        white-space: nowrap
    }
}

.centered {
    display: flex;
    justify-content: center;
    width: 100%;
}