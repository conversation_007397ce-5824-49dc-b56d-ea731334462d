<script lang="ts" src="./CustomerTransactionsExportComponent.ts">
</script>

<style lang="sass">
@use './CustomerTransactionsExportComponent.scss' as *
</style>

<template>
    <div class="customer-transactions-export-component">
        <form-modal-or-drawer
            :title="'Exporter des données'"
            :subtitle="'Choisissez les données que vous souhaitez exporter'"
            :state="opened"
            @close="close()"
        >
            <template v-slot:content>
                <div class="two-inputs">
                    <div class="input-group">
                        <label for="title"> Réalisée après le </label>
                        <div class="ui input">
                            <date-input :value="exportDates.start" @change="exportDates.start = $event" ></date-input>
                        </div>
                    </div>

                    <div class="input-group">
                        <label for="title"> et avant le </label>
                        <div class="ui input color">
                            <date-input :value="exportDates.end" @change="exportDates.end = $event" ></date-input>
                        </div>
                    </div>
                </div>

                <div class="input-group">
                    <dropdown
                        :values="[{
                            image: '/img/excel.png',
                            name: 'Excel',
                            value: 'xlsx'
                        }, {
                            image: '/img/orb.png',
                            name: 'OpenOffice',
                            value: 'ods'
                        }, {
                            name: 'CSV',
                            value: 'csv'
                        }]"
                        :default-selected="selectedFileType"
                        @update="selectedFileType = $event"
                    ></dropdown>
                </div>

                <div class="form-error" v-if="exportError">
                    <i class="fa-solid fa-exclamation-circle"></i>
                    <div class="details">
                        <span class="title"> Erreur </span>
                        <span class="description">{{ exportError }}</span>
                    </div>
                </div>
            </template>

            <template v-slot:buttons>
                <button type="button" class="white button" @click="close()"> Annuler </button>
                <button type="button" class="button" :class="{disabled: exporting, loading: exporting}" @click="validate()">
                    <i class="fa-regular fa-arrow-down-to-line"></i>
                    Exporter
                </button>
            </template>

        </form-modal-or-drawer>
    </div>
</template>