<script lang="ts" src="./ProductRequiredMetadataListComponent.ts" />

<style lang="sass" scoped>
@import './ProductRequiredMetadataListComponent.scss'
</style>

<template>
    <div class="product-required-metadata-list-component">
        <metadata-descriptor-form
            :key="fillingMetadataDescriptor.uid"
            :metadata-descriptor="fillingMetadataDescriptor"
            @validated="validateFillingMetadata($event)"
            @close="cancel()"
        ></metadata-descriptor-form>
    </div>
</template>