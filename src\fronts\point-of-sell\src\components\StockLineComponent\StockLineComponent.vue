<script lang="ts" src="./StockLineComponent.ts">
</script>

<style lang="sass" scoped>
@import './StockLineComponent.scss'
</style>

<template>
    <tr v-if="remainingStock !== null" >
        <td> {{ product.lastRevision.name }} </td>
        <td>
            <div class="remaining-stock">
                <div v-if="remainingStock > 50" class="green indicator"></div>
                <div v-else-if="remainingStock > 10" class="orange indicator"></div>
                <div v-else class="red indicator"></div>
                {{ remainingStock }}
            </div>
        </td>
        <td @click="!favoriteProductUids.includes(product.uid) ? addToFavorites(product.uid) : removeFromFavorites(product.uid)">
            <i v-if="!favoriteProductUids.includes(product.uid)" class="fa-regular fa-star"></i>
            <i v-else class="fa-solid fa-star"></i>
        </td>
    </tr>
</template>