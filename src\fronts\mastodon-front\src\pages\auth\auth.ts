import {Component, Vue} from "vue-facing-decorator";
import {AutoWired, hydrate} from "@groupk/horizon2-core";
import {AuthCenter} from "../../../../../shared/repositories/AuthCenter";
import {AccountAuthTokenApiOut, EstablishmentAuthTokenApiOut} from "@groupk/mastodon-core";
import {Router} from "@groupk/horizon2-front";
import {AuthRepository} from "../../../../../shared/repositories/AuthRepository";
import {EstablishmentUrlBuilder} from "../../../../../shared/utils/EstablishmentUrlBuilder";

@Component({})
export default class AuthView extends Vue {
	@AutoWired(AuthCenter) accessor authCenter!: AuthCenter;
	@AutoWired(Router) accessor router!: Router;
	@AutoWired(AuthRepository) accessor authRepository!: AuthRepository;

	error!: string|null;

	async mounted() {
		const urlParams = new URLSearchParams(window.location.search);
		const rawToken = urlParams.get('token');
		const redirectAfter = urlParams.get('redirectAfter') ?? null;
		if(!rawToken) {
			this.error = 'Un problème est survenu lors de la tentative de connexion - err1'
		} else {
			try {
				const parsedToken = JSON.parse(atob(rawToken));
				const token =
					'accountUid' in parsedToken ?
						hydrate(AccountAuthTokenApiOut, parsedToken)
						: hydrate(EstablishmentAuthTokenApiOut, parsedToken);

				this.authCenter.storeConnectionToken(token);

				await this.refreshToken(token);

				window.location.href = EstablishmentUrlBuilder.buildUrl(redirectAfter ? redirectAfter : '/payment-methods');
			} catch(err) {
				console.error(err);
				this.error = 'Un problème est survenu lors de la tentative de connexion - err2'
			}
		}
	}

	async refreshToken(outdatedToken: AccountAuthTokenApiOut|EstablishmentAuthTokenApiOut) {
		if('establishmentAccounts' in outdatedToken) {
			const token = (await this.authRepository.callContract('account_state', undefined, undefined)).success();
			this.authCenter.storeConnectionToken(token);
		} else {
			const token = (await this.authRepository.callContract('establishment_state', {establishmentUid: outdatedToken.establishmentUid}, undefined)).success();
			this.authCenter.storeConnectionToken(token);
		}
	}
}
