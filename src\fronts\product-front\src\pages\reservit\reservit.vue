<script lang="ts" src="./reservit.ts"/>

<style lang="sass" scoped>
@import './reservit.scss'
</style>

<template>
    <div id="reservit-page" class="page">
        <forbidden-message v-if="forbidden"></forbidden-message>

        <filter-table-layout
            v-else
            :header-parameters="headerParameters"
            :allowed-filters="allowedFilters"
            :table-columns="tableColumns"
            :pagination="pagination"
            :filters="filters"
            :drawer-opened="selectedTransfer !== null"
            @changed-column-preferences="saveColumnPreferences($event)"
            @next="nextPage()"
            @previous="previousPage()"
            @filters-changed="search($event);"
            @save-filter="saveFilter($event)"
            @select-filter="selectFilter($event)"
            @delete-filter="deleteFilter($event)"
        >
            <template v-slot:table-data>
                <tr class="table-dimmer" :class="{relative: reservitTransfers.length === 0}" v-if="loading">
                    <td colspan="100%">
                        <div class="dimmer">
                            <div class="loader-container">
                                <div class="loader"></div>
                            </div>
                        </div>
                    </td>
                </tr>
                <tr class="table-no-data" v-else-if="reservitTransfers.length === 0">
                    <td colspan="100%">Aucune donnée</td>
                </tr>
                <tr v-for="reservitTransfer in reservitTransfers" :class="{selected: selectedTransfer && reservitTransfer.uid === selectedTransfer.uid}">
                    <td :class="{'mobile-hidden': column.mobileHidden}" v-for="column in tableColumns.filter((instance) => instance.displayed)" @click="toggleSelectedTransfer(reservitTransfer)">
                        <template v-if="column.name === 'uid'"> {{ reservitTransfer.uid }} </template>
                        <template v-if="column.name === 'success'">
                            <div v-if="reservitTransfer.success" class="green label"> Succès </div>
                            <div v-else class="red label"> Erreur </div>
                        </template>
                        <template v-if="column.name === 'executionDatetime'">
                            <span v-if="!reservitTransfer.executionDatetime"> - </span>
                            <template v-else>
                                {{ $filters.Date(reservitTransfer.executionDatetime) }}
                                {{ $filters.Hour(reservitTransfer.executionDatetime) }}
                            </template>
                        </template>
                        <template v-if="column.name === 'creationDatetime'">
                            {{ $filters.Date(reservitTransfer.creationDatetime) }}
                            {{ $filters.Hour(reservitTransfer.creationDatetime) }}
                        </template>
                    </td>
                </tr>
            </template>
            <template v-slot:right>
                <div v-if="!selectedTransfer" class="empty-right-panel">
                    <img :src="$assets.selectHint" />
                    Cliquez sur un produit pour <br/> le sélectionner
                </div>
                <div v-else class="selected-transfer">
                    <div class="close" @click="toggleSelectedTransfer(selectedTransfer)">
                        <i class="fa-regular fa-xmark"></i>
                        <span> Fermer </span>
                    </div>

                    <div class="header">
                        <div class="left">
                            <h2> {{ selectedTransfer.uid }} </h2>
                        </div>
                    </div>

                    <div class="transfer-details">
                        <div class="stack-trace">
                            {{ selectedTransfer.serverResultBody ?? '-' }}
                        </div>

                        <button class="button" :class="{loading: replayingTransfer === selectedTransfer.uid, disabled: replayingTransfer === selectedTransfer.uid}" @click="replayTransfer(selectedTransfer)">
                            <i class="fa-regular fa-arrows-rotate"></i>
                            Rejouer
                        </button>
                    </div>
                </div>
            </template>
        </filter-table-layout>

        <toast-manager></toast-manager>
    </div>
</template>