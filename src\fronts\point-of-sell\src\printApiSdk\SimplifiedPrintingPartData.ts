export type SimplifiedPrintingPart_Align = "left" | "center" | "right";

export interface SimplifiedPrintingPart_Text{
	type:'TEXT',
	text: string|string[],
	textAlign?: SimplifiedPrintingPart_Align|undefined;
	spaceBefore?: boolean|undefined;
	spaceAfter?: boolean|undefined;
	fontWeight?: number|undefined;
	baseLineMultiplier?: number|undefined;
}
export interface SimplifiedPrintingPart_Separator{
	type:'SEPARATOR',
}
export interface SimplifiedPrintingPart_Table{
	type:'TABLE',
	widthPerCell: number[], // sum must be 100
	marginBetweenCells: number,
	header?: string[]
	rows: string[][],
	textAlignPerCellRows: SimplifiedPrintingPart_Align[],
	textAlignPerCellHeader?: SimplifiedPrintingPart_Align[],
}

export type SimplifiedPrintingPart = SimplifiedPrintingPart_Text | SimplifiedPrintingPart_Separator | SimplifiedPrintingPart_Table;

export interface SimplifiedPrintingPartRendererConfig{
	margins: {top: number; right: number; bottom: number; left: number};
}
