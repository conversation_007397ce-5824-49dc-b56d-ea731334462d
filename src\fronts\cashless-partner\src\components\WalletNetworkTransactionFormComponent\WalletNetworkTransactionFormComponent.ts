import {Component, Prop, Vue} from "vue-facing-decorator";
import {DropdownComponent, FormModalOrDrawerComponent} from "@groupk/vue3-interface-sdk";
import {AutoWired, VisualScopedUuid} from "@groupk/horizon2-core";
import {CustomerRepository} from "../../../../../shared/repositories/CustomerRepository";
import {
	CurrencyApiOut,
	ProfileApiOut,
	TransactionNetworkApiIn, UuidScopeCashless_profile,
	ApplicationPermission,
	EstablishmentAccountPermissionModel,
	CashlessHttpTransactionContract, CashlessHttpCurrencyContract, CashlessHttpProfileContract, WalletApiOut
} from "@groupk/mastodon-core";
import {CurrencyRepository} from "../../../../../shared/repositories/CurrencyRepository";
import {ProfilesRepository} from "../../../../../shared/repositories/ProfilesRepository";
import {TransactionsRepository} from "../../../../../shared/repositories/TransactionsRepository";
import {WalletsRepository} from "../../../../../shared/repositories/WalletsRepository";
import {CustomerData} from "../../../../../shared/mastodonCoreFront/cashless/CustomerData";
import {AppState} from "../../../../../shared/AppState";
import {CustomerChipApiOut} from "@groupk/mastodon-core";

export function WalletNetworkTransactionFormComponentHasRequiredPermissions(ownedPermissions: ApplicationPermission[]): boolean {
	return EstablishmentAccountPermissionModel.hasPermissionsForContracts(ownedPermissions, [
		CashlessHttpTransactionContract.createNetwork,
		CashlessHttpCurrencyContract.list,
		CashlessHttpProfileContract.list,
	]);
}

@Component({
	components: {
		'form-modal-or-drawer': FormModalOrDrawerComponent,
		'dropdown': DropdownComponent
	},
	emits: ['close']
})
export default class WalletNetworkTransactionFormComponent extends Vue {
	@Prop({required: true}) wallet!: WalletApiOut|CustomerChipApiOut;
	@Prop({default: 'CLEAR'}) transactionType!: 'CLEAR'|'ENABLE';

	currencies: CurrencyApiOut[] = [];
	profiles: ProfileApiOut[] = [];

	selectedProfile: VisualScopedUuid<UuidScopeCashless_profile>|null = null;
	customer: CustomerData = new CustomerData();

	opened: boolean = false;
	loading: boolean = true;
	error: { title: string, description: string }|null = null;

	@AutoWired(CustomerRepository) accessor customerRepository!: CustomerRepository;
	@AutoWired(CurrencyRepository) accessor currencyRepository!: CurrencyRepository;
	@AutoWired(WalletsRepository) accessor walletsRepository!: WalletsRepository;
	@AutoWired(ProfilesRepository) accessor profilesRepository!: ProfilesRepository;
	@AutoWired(TransactionsRepository) accessor transactionsRepository!: TransactionsRepository;
	@AutoWired(AppState) accessor appState!: AppState;

	async mounted() {
		setTimeout(() => this.opened = true, 0);

		this.currencies = (await this.currencyRepository.callContract('list', {establishmentUid: this.appState.requireUrlEstablishmentUid()}, undefined)).success();
		this.profiles = (await this.profilesRepository.callContract('list', {establishmentUid: this.appState.requireUrlEstablishmentUid()}, undefined)).success();

		this.loading = false;
	}

	get profilesDropdownValues() {
		return this.profiles.map((profile) => {
			return {
				name: profile.name,
				value: profile.uid
			}
		})
	}

	close() {
		this.opened = false;
		setTimeout(() => this.$emit('close'), 300);
	}

	async validate() {
		this.error = null;
		if(!this.selectedProfile) {
			this.error = {
				title: 'Veuillez sélectionnez un point de vente',
				description: 'Le point de vente impactera les statistiques de remboursements, puisqu\'une transaction va vider la puce de son solde'
			};
			return;
		}

		this.loading = true;

		if(!this.wallet.chipUid && !this.wallet.chipId) {
			throw new Error('wallet_have_no_chip_id');
		}

		const apiIn = new TransactionNetworkApiIn({
			profileUid: this.selectedProfile,
			amount: 0,
			currency: this.currencies[0].id,
			clear: this.transactionType === 'CLEAR',
			disable: false,
			enable: this.transactionType === 'ENABLE',
			paymentMethod: null,
			targetChipId: undefined,
			targetChipIdPrivate: this.wallet.chipId ?? undefined,
			targetChipUid: this.wallet.chipUid ?? undefined
		});
		const response = await this.transactionsRepository.callContract(
			'createNetwork',
			{establishmentUid: this.appState.requireUrlEstablishmentUid()},
			apiIn
		);

		if (response.isSuccess()) {
			this.close();
		} else {
			const error = response.error();
			if (error && 'error' in error) {
				if(error.error === 'tx_creation_weird_state') {
					this.error = {
						title: 'Une erreur est survenue',
						description: 'Le statut actuel du support ne permet pas cette action'
					}
				}
				if(error.error === 'unknown_currency') {
					this.error = {
						title: 'Une erreur est survenue',
						description: 'La devise utilisée est inconnue'
					}
				}
				if(error.error === 'unknown_profile') {
					this.error = {
						title: 'Une erreur est survenue',
						description: 'Le point de vente utilisé est inconnue'
					}
				}
				if(error.error === 'invalid_data') {
					this.error = {
						title: 'Une erreur est survenue',
						description: 'Données invalides'
					}
				}
			} else {
				this.error = {
					title: 'Une erreur est survenue',
					description: 'Raison inconnue'
				}
			}
		}
		this.loading = false;
	}
}