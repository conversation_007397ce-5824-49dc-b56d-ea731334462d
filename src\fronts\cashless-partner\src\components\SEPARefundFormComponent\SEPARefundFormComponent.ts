import {Component, Prop, Vue} from "vue-facing-decorator";
import {FormModalOrDrawerComponent} from "@groupk/vue3-interface-sdk";
import {
    RefundStatus,
    UuidScopeCashless_refund,
    RefundApiOut, BankAccountUtils
} from "@groupk/mastodon-core";
import {AutoWired, UuidUtils} from "@groupk/horizon2-core";
import {RefundRepository} from "../../../../../shared/repositories/RefundRepository";
import {AppState} from "../../../../../shared/AppState";
import {AppBus} from "../../config/AppBus";
import {RefundBatchData} from "../../../../../shared/mastodonCoreFront/cashless/RefundBatchData";
import {CleaveDirective} from "../../../../../shared/directives/CleaveDirective";
import {MoneyFilter} from "../../../../../shared/filters/Money";

@Component({
    components: {
        'form-modal-or-drawer': FormModalOrDrawerComponent,
    },
    directives: {
        cleave: CleaveDirective,
    },
    emits: ['saved']
})
export default class SEPARefundFormComponent extends Vue {
    @Prop() refunds!: RefundApiOut[];

    @AutoWired(RefundRepository) accessor refundRepository!: RefundRepository;
    @AutoWired(AppState) accessor appState!: AppState;
    @AutoWired(AppBus) accessor appBus!: AppBus;

    preprocessing: boolean = false;
    refundBatch: RefundBatchData = new RefundBatchData();

    cleaveIban = {
        blocks: [4, 4, 4, 4, 4, 4, 4, 4, 4],
        delimiter: ' ',
        uppercase: true
    }

    filteredRefunds: {
        legit: RefundApiOut[],
        skipped: RefundApiOut[];
    } = {
        legit: [],
        skipped: []
    }

    // Initialisation des valeurs par défaut pour faciliter les tests
    beforeMount() {
        // Initialiser avec des valeurs par défaut pour faciliter les tests
        this.refundBatch.sepaConfig.orgBic = "";
        this.refundBatch.sepaConfig.orgIban = "";
        this.refundBatch.sepaConfig.orgName = "";
        this.refundBatch.sepaConfig.orgId = this.appState.requireUrlEstablishmentUid();
        this.refundBatch.sepaConfig.batchBooking = true;
    }

    mounted() {
        this.fillFilteredRefunds();
    }

    fillFilteredRefunds() {
        for(let refund of this.refunds) {
            if(refund.status === RefundStatus.PENDING
                && refund.plannedError === null
                && refund.plannedAmount !== null
                && refund.plannedAmount > 0
                && refund.chipVisualId !== ""
            ) {
                this.filteredRefunds.legit.push(refund);
            } else {
                this.filteredRefunds.skipped.push(refund);
            }
        }
    }


    totalRefundAmount(refunds: RefundApiOut[]) {
        return MoneyFilter(refunds
            .filter(refund => refund.status === RefundStatus.PENDING)
            .reduce((total, refund) => total + (refund.plannedAmount || 0), 0));
    }

    validateForm(): boolean {
        // Validation de base des champs requis
        if (!this.refundBatch.sepaConfig.orgName ||
            !this.refundBatch.sepaConfig.orgIban ||
            !this.refundBatch.sepaConfig.orgBic) {
            this.appBus.emit('emit-toast', {
                title: 'Formulaire incomplet',
                description: 'Veuillez remplir tous les champs obligatoires',
                duration: 3000,
                type: 'ERROR',
                closable: true
            });
            return false;
        }

        // Validation spécifique pour l'IBAN (format simplifié)
        if(!BankAccountUtils.extractInfoFromIban(this.refundBatch.sepaConfig.orgIban)){
            this.appBus.emit('emit-toast', {
                title: 'IBAN invalide',
                description: 'Le format de l\'IBAN est incorrect',
                duration: 3000,
                type: 'ERROR',
                closable: true
            });
            return false;
        }

        return true;
    }

    async save() {
        if (!this.validateForm()) {
            return;
        }

        this.preprocessing = true;

        try {
            // S'assurer que nous avons des remboursements à traiter
            if (!this.refunds || this.refunds.length === 0 || this.filteredRefunds.legit.length === 0) {
                throw new Error("Aucun remboursement à traiter");
            }

            // Filtrer les remboursements en attente
            const pendingRefunds =this.filteredRefunds.legit;

            if (pendingRefunds.length === 0) {
                throw new Error("Aucun remboursement en attente à traiter");
            }

            // Préparer les IDs de remboursements (en format scopé)
            const refundUids = pendingRefunds
                .map(refund => UuidUtils.visualToScoped<UuidScopeCashless_refund>(refund.uid));

            // Mettre à jour les UUIDs dans le modèle
            this.refundBatch.refundUidList = refundUids; // Prendre le premier pour tester

            const response = await this.refundRepository.callContract(
                'createBatch',
                {establishmentUid: this.appState.requireUrlEstablishmentUid()},
                this.refundBatch.toApiIn()
            );

            if (response.isSuccess()) {
                this.appBus.emit('emit-toast', {
                    title: 'Traitement réussi',
                    description: 'Les remboursements ont été traités avec succès',
                    duration: 3000,
                    type: 'SUCCESS',
                    closable: true
                });
                this.$emit('saved', true);
            } else {
                this.appBus.emit('emit-toast', {
                    title: 'Échec du traitement',
                    description: 'Une erreur est survenue lors du traitement des remboursements',
                    duration: 3000,
                    type: 'ERROR',
                    closable: true
                });
            }
        } catch (error) {
            console.error('Erreur lors du prétraitement des remboursements:', error);
            this.appBus.emit('emit-toast', {
                title: 'Erreur lors du traitement',
                description: 'Une erreur est survenue: ' + (error instanceof Error ? error.message : 'Erreur inconnue'),
                duration: 3000,
                type: 'ERROR',
                closable: true
            });
        } finally {
            this.preprocessing = false;
        }
    }
}