import {Component, Vue} from "vue-facing-decorator";
import {AutoWired} from "@groupk/horizon2-core";
import SidebarStateListener from "../../../../../shared/utils/SidebarStateListener";
import {
    ContentHeaderComponent,
    ContentHeaderParameters,
    LayoutContentWithRightPanelComponent
} from "@groupk/vue3-interface-sdk";
import {EcommerceConfigApiIn, EcommerceConfigApiOut} from "@groupk/mastodon-core";
import {EcommerceConfigRepository} from "../../../../../shared/repositories/EcommerceConfigRepository";
import {AppState} from "../../../../../shared/AppState";

@Component({
    components: {
        'content-header': ContentHeaderComponent,
        'layout': LayoutContentWithRightPanelComponent
    }
})
export default class ECommerceConfigView extends Vue {
    headerParameters: ContentHeaderParameters = {
        header: 'Ecommerce config',
        subtitle: '-',
        actions: [],
        hideSearch: true,
        searchPlaceholder: '',
    }

    ecommerceConfig!: EcommerceConfigApiOut;
    loading: boolean = true;

    @AutoWired(EcommerceConfigRepository) private accessor ecommerceConfigRepository!: EcommerceConfigRepository;
    @AutoWired(SidebarStateListener) private accessor sidebarStateListener!: SidebarStateListener;
    @AutoWired(AppState) private accessor appState!: AppState;

    async mounted() {
        this.sidebarStateListener.setHiddenSidebar(false);
        this.sidebarStateListener.setMinimizedSidebar(false);

        this.ecommerceConfig = (await this.ecommerceConfigRepository.callContract('get', {establishmentUid: this.appState.requireUrlEstablishmentUid()}, undefined)).success();

        this.loading = false;
    }

    addExternalCss() {
        if(this.ecommerceConfig.externalCssLinkList === undefined) {
            this.ecommerceConfig.externalCssLinkList = [];
        }
        this.ecommerceConfig.externalCssLinkList.push('');
    }

    async save() {
        await this.ecommerceConfigRepository.callContract(
            'update',
            {establishmentUid: this.appState.requireUrlEstablishmentUid()},
            new EcommerceConfigApiIn({
                sellerName: this.ecommerceConfig.sellerName,
                sellerTosLink: this.ecommerceConfig.sellerTosLink,
                contactName: this.ecommerceConfig.contactName,
                contactEmail: this.ecommerceConfig.contactEmail,
                contactLink: this.ecommerceConfig.contactLink,
                googleAnalyticsTagId: this.ecommerceConfig.googleAnalyticsTagId,
                facebookPixelId: this.ecommerceConfig.facebookPixelId,
                executionFlowUid: this.ecommerceConfig.executionFlowUid,
                paymentMethodsUid: this.ecommerceConfig.paymentMethodsUid,
                externalCssLinkList: this.ecommerceConfig.externalCssLinkList,
                customCss: this.ecommerceConfig.customCss,
            })
        );
    }
}