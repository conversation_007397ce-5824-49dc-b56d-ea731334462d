import {
	OrderApiOut,
	PurchaseApiOut,
	PurchaseItemApiOut, UuidScopeIot_deviceApp,
	uuidScopeProduct_order,
	UuidScopeProduct_order, uuidScopeProduct_orderTransfer,
	uuidScopeProductOrderPurchase
} from "@groupk/mastodon-core"
import {
	Bool<PERSON>ield, DatetimeField,
	Entity, EntityAsSimpleObject,
	EntityClass,
	EntityField,
	IntegerField, StringField, Uuid, UuidField,
	VisualScopedUuid, VisualScopedUuidField,
} from "@groupk/horizon2-core";
import {
	UuidScopeProduct_orderTransfer,
	UuidScopeProductOrderPurchase,
	UuidScopeProductProductRevision
} from "@groupk/mastodon-core";
import {OrderExtraDiningApiOut, uuidScopeIot_deviceApp} from "@groupk/mastodon-core";

@EntityClass()
export class PurchasesPerRound extends Entity {
	@StringField() name: string;
	@VisualScopedUuidField(uuidScopeProductOrderPurchase, {array: true}) purchaseUids: VisualScopedUuid<UuidScopeProductOrderPurchase>[];

	constructor({name, purchaseUids}: {
		name: string,
		purchaseUids: VisualScopedUuid<UuidScopeProductOrderPurchase>[],
	}) {
		super();
		this.name = name;
		this.purchaseUids = purchaseUids;
	}
}


// WARNING OFFLINE
// EVERY ADDED VALUE SHOULD BE UNDEFINABLE
@EntityClass()
export class LocalOrderTransfer extends Entity {
	@UuidField() uid: Uuid;
	@VisualScopedUuidField(uuidScopeProduct_orderTransfer, {nullable: true}) transferUid: VisualScopedUuid<UuidScopeProduct_orderTransfer>|null;
	@EntityField(OrderApiOut) order: OrderApiOut;
	@VisualScopedUuidField(uuidScopeIot_deviceApp) creatorIotDevice: VisualScopedUuid<UuidScopeIot_deviceApp>;
	@VisualScopedUuidField(uuidScopeIot_deviceApp, {undefinable: true}) originIotDevice: VisualScopedUuid<UuidScopeIot_deviceApp>|undefined;
	@VisualScopedUuidField(uuidScopeIot_deviceApp) targetIotDevice: VisualScopedUuid<UuidScopeIot_deviceApp>;
	@StringField({maxLength: 4}) unlockCodeOk: string;
	@StringField({maxLength: 4}) unlockCodeKo: string;
	@DatetimeField({nullable: true}) fetchedFromTargetDatetime: string|null;
	@DatetimeField({nullable: true}) confirmedFromTargetDatetime: string|null;
	@BoolField() canceled: boolean;

	constructor({uid,
				transferUid,
				order,
				creatorIotDevice,
				targetIotDevice,
				originIotDevice,
				unlockCodeOk,
				unlockCodeKo,
				canceled = false
	}: Omit<EntityAsSimpleObject<LocalOrderTransfer>, 'fetchedFromTargetDatetime' | 'confirmedFromTargetDatetime'>) {
		super();
		this.uid = uid;
		this.transferUid = transferUid;
		this.order = order;
		this.creatorIotDevice = creatorIotDevice;
		this.originIotDevice = originIotDevice;
		this.targetIotDevice = targetIotDevice;
		this.fetchedFromTargetDatetime = null;
		this.confirmedFromTargetDatetime = null;
		this.unlockCodeOk = unlockCodeOk;
		this.unlockCodeKo = unlockCodeKo;
		this.canceled = canceled;
	}
}


// WARNING OFFLINE
// EVERY ADDED VALUE SHOULD BE UNDEFINABLE
@EntityClass()
export class LocalOrder extends Entity {
	@VisualScopedUuidField(uuidScopeProduct_order) uid: VisualScopedUuid<UuidScopeProduct_order>;
	@EntityField(OrderApiOut) order: OrderApiOut;
	@BoolField() synced: boolean = false;
	@BoolField() transferred: boolean = false;
	@StringField({nullable: true}) error: string|null = null;
	@IntegerField({bigint: true}) lastLocalUpdateDate: number = new Date().getTime();

	@IntegerField() currentStep: number = 0;

	constructor({uid, order}: {
		uid: VisualScopedUuid<UuidScopeProduct_order>,
		order: OrderApiOut,
	}) {
		super();
		this.uid = uid;
		this.order = order;
	}

	get diningExtra(): OrderExtraDiningApiOut {
		if(!this.order.diningExtra) this.order.diningExtra = new OrderExtraDiningApiOut({
			guestCount: undefined,
			tableUid: undefined,
			purchaseItemSteps: []
		});
		return this.order.diningExtra;
	}

	getSteps() {
		const maxRoundInOrder = this.diningExtra.purchaseItemSteps
			.reduce((max, step) => Math.max(max, step.step), 0);

		const maxWithLocal = Math.max(this.currentStep, maxRoundInOrder);
		const steps: number[] = [];
		for(let i = 0; i <= maxWithLocal; i++) {
			steps.push(i);
		}
		return steps;
	}

	getPurchaseItemsRecursive(purchase: PurchaseApiOut) {
		let items: { item: PurchaseItemApiOut, productRevisionUid: VisualScopedUuid<UuidScopeProductProductRevision> }[] = [];
		for(let item of purchase.items) {
			items.push({
				item: item,
				productRevisionUid: purchase.productRevisionUid
			});

			for(let group of item.groups) {
				for(let purchase of group.purchases) {
					items = items.concat(this.getPurchaseItemsRecursive(purchase));
				}
			}
		}
		return items;
	}
}
