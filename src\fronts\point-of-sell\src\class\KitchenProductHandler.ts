import {LocalOrder} from "../model/LocalOrder";
import {GetInstance, UuidUtils, VisualScopedUuid} from "@groupk/horizon2-core";
import {PosState} from "../model/PosState";
import {
    MetadataDescriptorType,
    PrintingLocationApiOut,
    ProductRevisionApiOut,
    PurchaseApiOut,
    PurchaseItemApiOut,
    UuidScopeProductOrderPurchase,
    UuidScopeProductOrderPurchaseItem,
    UuidScopeProductProduct,
    UuidScopeProductProductRevision
} from "@groupk/mastodon-core";
import {SimplifiedPrintingPart} from "../printApiSdk/SimplifiedPrintingPartData";
import {HourFilter} from "../../../../shared/filters/DateFilter";
import {GlobalFilters} from "../../../../shared/filters/GlobalFilters";

export default class KitchenProductHandler {

    static getPrintingParts(
        printingStep: number,
        order: LocalOrder,
        itemsToPrint: VisualScopedUuid<UuidScopeProductOrderPurchaseItem>[],
        groupPurchaseItems: boolean = true
    ) {
        const posState = GetInstance(PosState);

        const ticketsToPrint: { printingLocation: PrintingLocationApiOut, parts: SimplifiedPrintingPart[]}[] = [];

        const headingParts : SimplifiedPrintingPart[] = [];

        const printingStepName = printingStep === 0 ? 'Direct' : `Suite ${printingStep}`;
        // ====================================
        headingParts.push({
            type: 'TEXT',
            text: `Commande table ${(order.order.diningExtra && order.diningExtra?.tableUid) ? posState.requireTableWithUid(order.diningExtra.tableUid).name : '?'}` + ' - ' + printingStepName,
            textAlign: 'center',
            fontWeight: 600,
        });
        headingParts.push({type: 'TEXT', text: `Heure de commande: ${HourFilter(order.order.creationDatetime)}`, textAlign: 'center'});
        headingParts.push({type: 'TEXT', text: `Heure d'impression: ${HourFilter((new Date()).toISOString())}`, textAlign: 'center'});
        headingParts.push({type: 'TEXT', text: `${UuidUtils.visualToScoped(order.order.uid)}`, textAlign: 'center'});

        headingParts.push({type: 'SEPARATOR'});

        const dataToPrint: {
            parentPurchase: PurchaseApiOut,
            parentProductRevision: ProductRevisionApiOut,
            purchaseItem: PurchaseItemApiOut,
            overrideQuantity: number,
            productRevision: ProductRevisionApiOut,
            printingLocation: PrintingLocationApiOut
        }[] = [];

        for(const purchaseItemStep of order.diningExtra.purchaseItemSteps) {
            if(purchaseItemStep.step === printingStep) {
                for(const purchase of order.order.purchases) {
                    for(const purchaseItem of order.getPurchaseItemsRecursive(purchase)) {
                        if(purchaseItem.item.uid === purchaseItemStep.purchaseItemUid && itemsToPrint.includes(purchaseItem.item.uid)) {
                            const printingLocation = KitchenProductHandler.findProductPrintingLocation(
                                posState.orderExecutorModel.requireProductWithRevision(purchase.productRevisionUid).uid
                            );

                            if(!printingLocation) break;

                            if(
                                !groupPurchaseItems ||
                                purchaseItem.item.metadataList.length > 0 ||
                                purchaseItem.item.groups.length > 0
                            ) {
                                dataToPrint.push({
                                    parentPurchase: purchase,
                                    parentProductRevision: posState.orderExecutorModel.requireProductRevision(purchase.productRevisionUid),
                                    purchaseItem: purchaseItem.item,
                                    productRevision: posState.orderExecutorModel.requireProductRevisionWithProduct(purchaseItem.productRevisionUid),
                                    printingLocation: printingLocation,
                                    overrideQuantity: purchaseItem.item.quantity
                                });
                            } else {
                                const sameProductInDataToPrint = dataToPrint.find((dataToPrint) => {
                                    return dataToPrint.productRevision.uid === purchaseItem.productRevisionUid &&
                                        dataToPrint.parentPurchase.uid === purchase.uid &&
                                        dataToPrint.purchaseItem.metadataList.length === 0 &&
                                        dataToPrint.purchaseItem.groups.length === 0;
                                });
                                if(sameProductInDataToPrint) sameProductInDataToPrint.overrideQuantity += purchaseItem.item.quantity;
                                else {
                                    dataToPrint.push({
                                        parentPurchase: purchase,
                                        parentProductRevision: posState.orderExecutorModel.requireProductRevision(purchase.productRevisionUid),
                                        purchaseItem: purchaseItem.item,
                                        productRevision: posState.orderExecutorModel.requireProductRevisionWithProduct(purchaseItem.productRevisionUid),
                                        printingLocation: printingLocation,
                                        overrideQuantity: purchaseItem.item.quantity
                                    });
                                }
                            }

                            break;
                        }
                    }
                }
            }
        }

        const allPrintingLocations = dataToPrint
            .map((data) => data.printingLocation)
            .filter((value, index, array) => array.indexOf(value) === index);

        console.log(allPrintingLocations);

        for(const printingLocationForTicket of allPrintingLocations) {
            let itemsParts: SimplifiedPrintingPart[] = [...headingParts];

            for (const printingLocation of [...allPrintingLocations].sort((pl) => pl.uid === printingLocationForTicket.uid ? -1 : 1)) {
                itemsParts.push({type: 'TEXT', text: ``, textAlign: 'left'});
                if (allPrintingLocations.length > 1) {
                    itemsParts.push({
                        type: 'TEXT',
                        text: `[${printingLocation.name.toUpperCase()}]`,
                        textAlign: 'left',
                        fontWeight: printingLocation.uid === printingLocationForTicket.uid ? 800 : 400,
                    });
                }

                const alreadyPrintedParents: VisualScopedUuid<UuidScopeProductProductRevision>[] = [];
                for (const data of dataToPrint) {
                    if(data.printingLocation.uid !== printingLocation.uid) continue;
                    if(alreadyPrintedParents.includes(data.parentProductRevision.uid)) continue;

                    if(data.parentProductRevision.uid !== data.productRevision.uid) {
                        itemsParts.push({
                            type: 'TEXT',
                            text: `${data.parentProductRevision.name}`,
                            textAlign: 'left',
                            fontWeight: printingLocation.uid === printingLocationForTicket.uid ? 800 : 400,
                        });

                        const dataWithSameParent = dataToPrint.filter((data2) => data2.parentProductRevision.uid === data.parentProductRevision.uid);
                        const alreadyPrintedParentPurchases: VisualScopedUuid<UuidScopeProductOrderPurchase>[] = [];

                        for(const sameParent of dataWithSameParent) {
                            if(!alreadyPrintedParentPurchases.includes(sameParent.parentPurchase.uid)) {
                                if(alreadyPrintedParentPurchases.length > 0) {
                                    itemsParts.push({type: 'TEXT', text: ``, textAlign: 'left'})
                                }
                                alreadyPrintedParentPurchases.push(sameParent.parentPurchase.uid);
                            }

                            itemsParts = itemsParts.concat(KitchenProductHandler.getPurchaseTicketParts(
                                sameParent,
                                groupPurchaseItems,
                                printingLocation.uid === printingLocationForTicket.uid,
                                2
                            ));
                        }

                        alreadyPrintedParents.push(data.parentProductRevision.uid);
                    } else {
                        itemsParts = itemsParts.concat(KitchenProductHandler.getPurchaseTicketParts(
                            data,
                            groupPurchaseItems,
                            printingLocation.uid === printingLocationForTicket.uid
                        ));
                    }
                }
            }

            itemsParts.push({type: 'TEXT', text: ``, textAlign: 'left'})
            itemsParts.push({type: 'TEXT', text: `Point de vente: ${posState.pointOfSale.name}`, textAlign: 'center'});

            ticketsToPrint.push({
                printingLocation: printingLocationForTicket,
                parts: itemsParts
            })
        }

        return ticketsToPrint;
    }

    static getPurchaseTicketParts(data: {
        parentProductRevision: ProductRevisionApiOut,
        purchaseItem: PurchaseItemApiOut,
        overrideQuantity: number,
        productRevision: ProductRevisionApiOut,
        printingLocation: PrintingLocationApiOut
    }, groupPurchaseItems: boolean, bold: boolean, depth: number = 1) {
        const posState = GetInstance(PosState);

        let parts: SimplifiedPrintingPart[] = [];

        let depthCharacters = '';
        for(let i = 1; i < depth; i++) depthCharacters += '    ';

        let metadataDepthCharacters = '';
        for(let i = 0; i < depth; i++) metadataDepthCharacters += '    ';

        parts.push({
            type: 'TEXT',
            text: `${depthCharacters}x${groupPurchaseItems ? data.overrideQuantity : data.purchaseItem.quantity} ${data.productRevision.name}`,
            textAlign: 'left',
            fontWeight: bold ? 800 : 400
        });

        if(bold) {
            for (const metadata of data.purchaseItem.metadataList) {
                if(metadata.type === MetadataDescriptorType.STRING) {
                    const splittedByLine = metadata.value.split('\n');
                    for(const line of splittedByLine) {
                        parts.push({
                            type: 'TEXT',
                            text: `${metadataDepthCharacters}${line}`,
                            textAlign: 'left',
                            fontWeight: bold ? 700 : 400,
                        });
                    }
                } else {
                    parts.push({
                        type: 'TEXT',
                        text: `${metadataDepthCharacters}${GlobalFilters.metadataValue(metadata.value)}`, // ${posState.requireMetadataDescriptorWithUid(metadata.descriptorUid).descriptorTemplate.name} :
                        textAlign: 'left',
                        fontWeight: bold ? 700 : 400,
                    });
                }
            }
        }

        for(const group of data.purchaseItem.groups) {
            for(const purchase of group.purchases) {
                for(const item of purchase.items) {
                    parts = parts.concat(KitchenProductHandler.getPurchaseTicketParts({
                        parentProductRevision: posState.orderExecutorModel.requireProductRevision(purchase.productRevisionUid),
                        purchaseItem: item,
                        overrideQuantity: item.quantity,
                        printingLocation: data.printingLocation,
                        productRevision: posState.orderExecutorModel.requireProductRevision(purchase.productRevisionUid)
                    }, true, bold, depth + 1));
                }
            }
        }

        return parts;
    }

    static findProductPrintingLocation(productUid: VisualScopedUuid<UuidScopeProductProduct>) {
        const posState = GetInstance(PosState);
        for(const printingLocation of posState.pointOfSaleConfiguration.printingLocationList) {
            if(printingLocation.productUidList && printingLocation.productUidList.includes(productUid)) {
                return printingLocation;
            }
            if(printingLocation.categoryUidList) {
                for(const categoryUid of printingLocation.categoryUidList) {
                    const category = posState.requireCategoryWithUid(categoryUid);
                    if(category.productsConfig.some((productConfig) => productConfig.productUid === productUid)) {
                        return printingLocation;
                    }
                }
            }
        }
        return null;
    }
}
