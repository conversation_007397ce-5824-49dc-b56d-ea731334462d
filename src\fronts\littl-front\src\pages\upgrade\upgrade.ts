import {Component, Vue} from "vue-facing-decorator";
import {AutoWired} from "@groupk/horizon2-core";
import {ContentHeaderComponent, ContentHeaderParameters} from "@groupk/vue3-interface-sdk";
import SidebarStateListener from "../../../../../shared/utils/SidebarStateListener";
import {AppState} from "../../../../../shared/AppState";
import {MainConfig} from "../../../../../shared/MainConfig";

@Component({
	components: {
		'content-header': ContentHeaderComponent
	}
})
export default class upgrade extends Vue {


	headerParameters: ContentHeaderParameters = {
		header: 'Modifier mon offre',
		subtitle: 'Débloquez plus de fonctionnalités avec les offres sans engagement',
		actions: [{
			type: 'SIMPLE_ACTION',
			name: 'G<PERSON>rer mon abonnement',
			icon: 'fa-regular fa-cog',
			callback: this.editSubscribePlan
		}],
		hideSearch: true,
		searchPlaceholder: 'Rechercher un lien'
	};

	@AutoWired(SidebarStateListener) accessor sidebarStateListener!: SidebarStateListener;
	@AutoWired(AppState) accessor appState!: AppState;
	@AutoWired(MainConfig) accessor mainConfig!: MainConfig;

	mounted() {
		this.sidebarStateListener.setHiddenSidebar(false);
		let scriptEle = document.createElement('script');
		scriptEle.setAttribute('src', 'https://js.stripe.com/v3/pricing-table.js');
		document.body.appendChild(scriptEle);
	}

	editSubscribePlan() {
		window.open("https://billing.stripe.com/p/login/5kAdSbf6Kcc03E4aEE", '_blank');
	}
}