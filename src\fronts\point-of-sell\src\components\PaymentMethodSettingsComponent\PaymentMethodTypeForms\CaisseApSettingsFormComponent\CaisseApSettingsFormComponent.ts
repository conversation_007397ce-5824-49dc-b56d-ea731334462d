import {Component, Prop, Vue} from "vue-facing-decorator";
import {AutoWired, B<PERSON>erUtils} from "@groupk/horizon2-core";
import {ActionStatus, createIdentificationRequest, decodeTagBundle} from "@groupk/caisse-protocol";
import {CONCERT_DESCRIPTORS} from "@groupk/caisse-protocol";
import {PaymentMethodSettingsCaisseAp} from "@groupk/mastodon-core";
import {SocketNative} from "@groupk/native-bridge";

@Component({
	emits: ['close', 'save', 'delete']
})
export default class CaisseApSettingsFormComponent extends Vue {
	@Prop() settings!: PaymentMethodSettingsCaisseAp;
	@Prop() editing!: boolean;

	connectionTest: 'LOADING'|'ERROR'|'SUCCESS'|null = null;

	@AutoWired(SocketNative) accessor nativeSocket!: SocketNative;

	async testConnection(){
		this.connectionTest = 'LOADING';

		try {
			let hasConnected: boolean = false;
			const tcpWrapper = await this.nativeSocket.tcpConnectWrapper(this.settings.ip, this.settings.port);

			tcpWrapper.on('connected', () => {
				hasConnected = true;
				const data = createIdentificationRequest();

				let payload: number[] = [];
				for (let i = 0; i < data.length; i++) {
					payload.push(data.charCodeAt(i));
				}
				tcpWrapper.send(payload);

				tcpWrapper.on('data', (bytes) => {
					// const decoded = decodePaymentResult(String.fromCharCode(...data));
					try {
						const decoded = decodeTagBundle(String.fromCharCode(...(typeof bytes === 'string' ? BufferUtils.hex2Buffer(bytes) : bytes)));
						decoded.getWithCode(CONCERT_DESCRIPTORS.AE.code)?.value === ActionStatus.OperationDone;

						this.connectionTest = 'SUCCESS';
					} catch(err) {
						this.connectionTest = 'ERROR';
					}

					tcpWrapper.close();
				});

				setTimeout(async () => {
					if(this.connectionTest === 'LOADING') {
						this.connectionTest = 'ERROR';
						tcpWrapper.close();
					}
				}, 10000);
			});

			tcpWrapper.on('closed', () => {
				if(!hasConnected) this.connectionTest = 'ERROR';
			});
		} catch(err) {
			this.connectionTest = 'ERROR';
		}
	}

	save() {
		this.$emit('save', this.settings);
	}

	deleteSettings() {
		this.$emit('delete', this.settings);
	}

	close() {
		this.$emit('close');
	}
}