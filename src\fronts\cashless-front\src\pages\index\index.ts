import {Component, Vue} from "vue-facing-decorator";
import {PublicTransactionApiOut, WalletApiOut} from "@groupk/mastodon-core";
import {AutoWired} from "@groupk/horizon2-core";
import CustomerInfoFormComponent from "../../components/CustomerInfoFormComponent/CustomerInfoFormComponent.vue";
import {CashlessManualDataLSKey} from "../../components/CustomerInfoFormComponent/CustomerInfoFormComponent";
import SpecificState from "../../SpecificState";
import {EstablishmentUrlBuilder} from "../../../../../shared/utils/EstablishmentUrlBuilder";
import {AppState} from "../../../../../shared/AppState";
import {AuthStateModel} from "../../../../../shared/AuthStateModel";
import {CashlessCustomerRepository} from "../../../../../shared/repositories/CashlessCustomerRepository";

@Component({
	components: {
		'customer-info-form': CustomerInfoFormComponent
	}
})
export default class index extends Vue {
	wallets: WalletApiOut[] = [];
	transactions: PublicTransactionApiOut[] = [];

	urlBuilder = EstablishmentUrlBuilder;

	@AutoWired(CashlessCustomerRepository) accessor cashlessCustomerRepository!: CashlessCustomerRepository;
	@AutoWired(AppState) accessor appState!: AppState;
	@AutoWired(AuthStateModel) accessor authStateModel!: AuthStateModel;
	@AutoWired(SpecificState) accessor specificState!: SpecificState;

	async mounted() {
		this.update();
	}

	async update() {
		this.$forceUpdate();

		if(this.specificState.currentWallet){
			this.transactions = (await this.cashlessCustomerRepository.callContract('listTransactions', {
				establishmentUid: this.appState.requireUrlEstablishmentUid(),
				walletUid: this.specificState.currentWallet.uid
			}, undefined)).success().list.sort((t1, t2) => {
				return t1.creationDatetime > t2.creationDatetime ? -1 : 1
			});
		}
	}

	logout() {
		localStorage.removeItem(CashlessManualDataLSKey);
		window.location.reload();
	}
}