import {Component, Prop, Vue} from "vue-facing-decorator";
import {Draggable, Sortable, SortableStopEvent} from "@shopify/draggable";
import {randomUUID} from "@groupk/horizon2-front";
import {Uuid, VisualScopedUuid} from "@groupk/horizon2-core";
import {PaymentMethodApiOut, UuidScopePayment_method} from "@groupk/mastodon-core";
import {FormModalOrDrawerComponent} from "@groupk/vue3-interface-sdk";
import {ProfileTerminalKeypadData} from "../../../../../shared/mastodonCoreFront/cashless/ProfileTerminalKeypadData";

@Component({
	components: {
		'form-modal-or-drawer': FormModalOrDrawerComponent
	},
	emits: ['done']
})
export default class ProfilePaymentMethodsEditorComponent extends Vue {
	@Prop({required: true}) keypad!: ProfileTerminalKeypadData;
	@Prop({required: true}) paymentMethods!: PaymentMethodApiOut[];

	uid: string = randomUUID();
	refreshKey: string = randomUUID();

	draggable: Draggable | undefined = undefined;
	sortable: Sortable | undefined = undefined;

	mounted() {
		setTimeout(() => {
			this.initSortable();
			this.initDraggable();
		}, 0)
	}

	getMethodWithUid(methodUid: Uuid): PaymentMethodApiOut {
		const method =  this.paymentMethods.find((method) => method.uid === methodUid);
		if(!method) throw new Error('missing_payment_method');
		return method;
	}

	initSortable() {
		this.sortable = new Sortable(document.querySelectorAll('.sortable'), {
			draggable: '.method.copy',
		});

		let currentContainerDeleteZone: Element|null = null;
		this.sortable.on('drag:over:container', (e) => {
			if (currentContainerDeleteZone) currentContainerDeleteZone.classList.remove('displayed');
			currentContainerDeleteZone = null;

			const deleteZone = document.querySelectorAll(`.delete-zone-${this.uid}`)[0];
			currentContainerDeleteZone = deleteZone;
			deleteZone.classList.add('displayed');
		});


		this.sortable.on('sortable:stop', async (e: SortableStopEvent) => {
			this.sortable?.destroy();
			this.sortable = undefined;

			if(!this.keypad.paymentMethods) this.keypad.paymentMethods = [];
			if(e.newContainer.classList.contains('delete-zone')) {
				this.keypad.paymentMethods.splice(e.oldIndex, 1);
			} else {
				const methodUid = this.keypad.paymentMethods.splice(e.oldIndex, 1)[0];
				if(!this.keypad.paymentMethods.includes(methodUid)) {
					if (e.newIndex >= this.keypad.paymentMethods.length) {
						this.keypad.paymentMethods.push(methodUid);
					} else {
						this.keypad.paymentMethods.splice(e.newIndex, 0, methodUid);
					}
				}
			}

			if(currentContainerDeleteZone) {
				currentContainerDeleteZone.classList.remove('displayed');
				currentContainerDeleteZone = null;
			}

			this.refreshKey = randomUUID();

			this.$nextTick(() => {
				this.initSortable();
				this.initDraggable();
			});
		});
	}

	initDraggable() {
		if (this.draggable) this.draggable.destroy();
		const dropzones = document.querySelectorAll('.drag-container');

		this.draggable = new Draggable(dropzones, {
			draggable: '.method.og',
		});

		let currentContainer: HTMLElement|null = null;
		this.draggable.on('drag:over:container', (e) => {
			currentContainer = e.overContainer;
			currentContainer.classList.add('hovered');
		});

		this.draggable.on('drag:out:container', (e) => {
			if(currentContainer) currentContainer.classList.remove('hovered');
			currentContainer = null;
		});

		this.draggable.on('drag:stop', async (e) => {
			if(!e.originalSource.dataset.uid) return;
			const methodUid = e.originalSource.dataset.uid as VisualScopedUuid<UuidScopePayment_method>;

			if(currentContainer && currentContainer.classList.contains('final')) {
				if(!this.keypad.paymentMethods) this.keypad.paymentMethods = [];
				if(!this.keypad.paymentMethods.includes(methodUid)) {
					this.keypad.paymentMethods.push(methodUid);
				}

				currentContainer.classList.remove('hovered');
				currentContainer = null;
			}

			this.$nextTick(() => {
				this.initDraggable();
			});
		});
	}

	done() {
		this.$emit('done')
	}
}