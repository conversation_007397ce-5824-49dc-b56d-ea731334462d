<script lang="ts" src="./OrderBuyerComponent.ts" />

<style lang="sass" scoped>
@import './OrderBuyerComponent.scss'
</style>

<template>
    <div class="order-buyer-component">
        <div class="buyer">
            <div class="head">
                <div class="infos">
                    <span class="order-name">
                        Commande n°{{order.uid.slice(-8)}}
                    </span>
                    <span class="tickets-quantity">
                        {{ getOrderTickets(order).length }} billet{{ getOrderTickets(order).length > 1 ? 's' : ''}}
                    </span>
                </div>

                <dropdown-button
                    button-class="grey button"
                    icon="fa-regular fa-ellipsis-vertical"
                    title=""
                    alignment="RIGHT"
                    @clicked="orderDropdownClicked(order, $event)"
                    :actions="[{title: '', actions: [{
                        id: 'cancel-all',
                        name: 'Annuler tout les billets',
                        color: 'red',
                        icon: 'fa-regular fa-trash-alt'
                    }]}]"
                ></dropdown-button>
            </div>

            <a target="_blank" :href="getCustomerUrl(order.customerUid)" class="top" v-if="order.customerUid">
                <div class="profile-picture">
                    {{ (requireCustomerWithUid(order.customerUid).firstname??'-')[0] }}{{ (requireCustomerWithUid(order.customerUid).lastname??'-')[0] }}
                </div>
                <div class="data">
                    <span class="name">
                        {{ requireCustomerWithUid(order.customerUid).firstname }}
                        {{ requireCustomerWithUid(order.customerUid).lastname }}
                    </span>
                    <span class="email">
                        {{ requireCustomerWithUid(order.customerUid).email }}
                    </span>

    <!--                                <span class="email">-->
    <!--                                    Commentaire de derezr erzzerzer zer zer-->
    <!--                                </span>-->

                </div>

                <i class="fa-regular fa-arrow-up-right-from-square"></i>
            </a>

            <div v-else class="top">
                <div class="data">
                    <span class="name">
                        Anonyme
                    </span>
                </div>
            </div>

            <div class="metadata" v-for="metadata of order.metadataList">
                <div class="name"> {{ requireMetadataDescriptorWithUid(metadata.descriptorUid).descriptorTemplate.name }}</div>
                <div class="value"> {{ $filters.metadataValue(metadata.value) }} </div>
            </div>

            <div class="purchases">
                <template v-for="ticket of getOrderTickets(order)">
                    <div class="purchase">
                        <div class="data">
                            <span class="name"> {{ requireTemplateTicketWithUid(ticket.templateTicketUid).name }} </span>

                            <div class="red label" v-if="ticket.disabled">
                                Désactivé
                            </div>
                            <div class="green label" v-else-if="ticket.lastValidUsageEventDatetime">
                                Contrôlé
                            </div>
                            <div class="grey label" v-else>
                                Valide
                            </div>
                        </div>

                        <ticket-dropdown-actions
                            :ticket="ticket"
                        ></ticket-dropdown-actions>
                    </div>
                </template>
            </div>

            <div class="toggle-canceled" @click="displayOtherTickets = !displayOtherTickets" v-if="getOrderTickets(order, false).length > 0">
                Afficher les tickets sur les autres horaires
                <i class="fa-regular fa-chevron-down" v-if="!displayOtherTickets"></i>
                <i class="fa-regular fa-chevron-up" v-else></i>
            </div>

            <div class="purchases" v-if="displayOtherTickets">
                <template v-for="ticket of getOrderTickets(order, false)">
                    <div class="purchase">
                        <div class="data">
                            <span class="name"> {{ requireTemplateTicketWithUid(ticket.templateTicketUid).name }} </span>

                            <div class="red label" v-if="ticket.disabled">
                                Désactivé
                            </div>
                            <div class="green label" v-else-if="ticket.lastValidUsageEventDatetime">
                                Contrôlé
                            </div>
                            <div class="grey label" v-else>
                                Valide
                            </div>
                        </div>

                        <ticket-dropdown-actions
                            :ticket="ticket"
                        ></ticket-dropdown-actions>
                    </div>
                </template>
            </div>
        </div>
    </div>
</template>
