import {Component, Prop, Vue} from "vue-facing-decorator";
import {PosState} from "../../model/PosState";
import {PosProfile} from "../../model/PosProfile";
import {AutoWired} from "@groupk/horizon2-core";
import {AppBus} from "../../config/AppBus";

@Component({})
export default class MobileNavigationComponent extends Vue {
	@Prop() posState!: PosState;
	@Prop() posProfile!: PosProfile;

	@AutoWired(AppBus) accessor appBus!: AppBus;


}