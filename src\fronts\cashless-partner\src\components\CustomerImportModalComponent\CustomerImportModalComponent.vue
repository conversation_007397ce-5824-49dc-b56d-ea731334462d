<script lang="ts" src="./CustomerImportModalComponent.ts">
</script>

<style lang="sass">
@use './CustomerImportModalComponent.scss' as *
</style>

<template>
    <div class="customer-import-modal-component">
        <modal-or-drawer
            :state="state"
            @close="close()"
        >
            <table-import
                v-if="state"
                :expected-columns="csvImportColumns"
                :entity-type="customerApiInEntity"
                :entity-builder="(a: any, b: any) => entityBuilder(a, b)"
                :entity-saver="(a: any) => entitySaver(a)"
                @close="close()"
            ></table-import>
        </modal-or-drawer>
    </div>
</template>