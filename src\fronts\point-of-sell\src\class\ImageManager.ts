import {GetInstance, VisualScopedUuid, buildHttpRoutePathWithArgs} from "@groupk/horizon2-core";
import {
    CategoryApiOut,
    ProductApiOut,
    MastodonHttpImagesContract,
    UuidScopeProductProduct
} from "@groupk/mastodon-core";
import {PosState} from "../model/PosState";
import {MainConfig} from "../../../../shared/MainConfig";

/**
 * Global image manager for point-of-sale application
 * Handles progressive preloading of product images with persistence across navigation
 */
export class ImageManager {
    // Image preloading state
    private preloadedImages: Set<string> = new Set();
    private preloadingInProgress: Set<string> = new Set();
    private preloadingQueue: string[] = [];
    private isBackgroundPreloading: boolean = false;
    private isInitialized: boolean = false;

    /**
     * Initialize the image manager and start preloading
     */
    public initialize(): void {
        if (this.isInitialized) return;
        this.isInitialized = true;
        this.startImagePreloading();
    }

    /**
     * Start progressive image preloading
     * Priority: Current category first, then other categories in background
     */
    private startImagePreloading(): void {
        const posState = GetInstance(PosState);
        
        // Find current category (first category if available)
        let currentCategory: CategoryApiOut | null = null;
        if (posState.pointOfSale.categoriesUid.length > 0) {
            currentCategory = posState.requireCategoryWithUid(posState.pointOfSale.categoriesUid[0]);
        }

        // First, preload current category images with high priority
        if (currentCategory) {
            this.preloadCategoryImages(currentCategory, true);
        }

        // Then start background preloading for other categories
        this.startBackgroundPreloading();
    }

    /**
     * Preload images for a specific category
     */
    public preloadCategoryImages(category: CategoryApiOut, immediate: boolean = false): void {
        const posState = GetInstance(PosState);
        const categoryProducts = posState.products.filter(product => 
            category.productsConfig.some(config => config.productUid === product.uid)
        );

        for (const product of categoryProducts) {
            const imageUrl = this.getProductImageUrl(product);
            if (imageUrl) {
                if (immediate) {
                    this.preloadImage(imageUrl);
                } else {
                    this.queueImageForPreloading(imageUrl);
                }
            }
        }
    }

    /**
     * Start background preloading for all categories
     */
    private startBackgroundPreloading(): void {
        if (this.isBackgroundPreloading) return;
        this.isBackgroundPreloading = true;

        const posState = GetInstance(PosState);

        // Queue all categories for background preloading
        for (const categoryUid of posState.pointOfSale.categoriesUid) {
            const category = posState.requireCategoryWithUid(categoryUid);
            this.preloadCategoryImages(category, false);
        }

        // Start processing the queue
        this.processPreloadingQueue();
    }

    /**
     * Add image URL to preloading queue
     */
    private queueImageForPreloading(imageUrl: string): void {
        if (!this.preloadedImages.has(imageUrl) && !this.preloadingInProgress.has(imageUrl)) {
            this.preloadingQueue.push(imageUrl);
        }
    }

    /**
     * Process the preloading queue with idle time callbacks
     */
    private processPreloadingQueue(): void {
        const processNext = () => {
            if (this.preloadingQueue.length === 0) {
                this.isBackgroundPreloading = false;
                return;
            }

            const imageUrl = this.preloadingQueue.shift();
            if (imageUrl) {
                this.preloadImage(imageUrl);
            }

            // Use requestIdleCallback if available, otherwise setTimeout
            if (window.requestIdleCallback) {
                window.requestIdleCallback(processNext, { timeout: 1000 });
            } else {
                setTimeout(processNext, 50);
            }
        };

        processNext();
    }

    /**
     * Preload a single image
     */
    private preloadImage(imageUrl: string): Promise<void> {
        return new Promise((resolve, reject) => {
            if (this.preloadedImages.has(imageUrl) || this.preloadingInProgress.has(imageUrl)) {
                resolve();
                return;
            }

            this.preloadingInProgress.add(imageUrl);

            const img = new Image();
            img.onload = () => {
                this.preloadedImages.add(imageUrl);
                this.preloadingInProgress.delete(imageUrl);
                resolve();
            };
            img.onerror = () => {
                this.preloadingInProgress.delete(imageUrl);
                reject(new Error(`Failed to preload image: ${imageUrl}`));
            };
            img.src = imageUrl;
        });
    }

    /**
     * Get product image URL (same logic as SellComponent)
     */
    private getProductImageUrl(product: ProductApiOut): string | null {
        if (!product.mainImageUpload) return null;
        
        const posState = GetInstance(PosState);
        const mainConfig = GetInstance(MainConfig);
        
        return mainConfig.configuration.mastodonApiEndpoint + buildHttpRoutePathWithArgs(MastodonHttpImagesContract.getPublicImage, {
            establishmentUid: posState.establishmentUid,
            imageId: product.mainImageUpload.uid,
            options: {
                quality: 100
            },
            dimensions: {width: 100},
            extension: 'png',
            resizeType: 'contain',
            revision: product.mainImageUpload.lastUpdateDatetime,
        });
    }

    /**
     * Check if an image is already preloaded
     */
    public isImagePreloaded(imageUrl: string): boolean {
        return this.preloadedImages.has(imageUrl);
    }

    /**
     * Get preloading statistics
     */
    public getPreloadingStats(): {
        preloaded: number;
        inProgress: number;
        queued: number;
        isBackgroundPreloading: boolean;
    } {
        return {
            preloaded: this.preloadedImages.size,
            inProgress: this.preloadingInProgress.size,
            queued: this.preloadingQueue.length,
            isBackgroundPreloading: this.isBackgroundPreloading
        };
    }

    /**
     * Clear all preloading state (for cleanup)
     */
    public clear(): void {
        this.preloadedImages.clear();
        this.preloadingInProgress.clear();
        this.preloadingQueue.length = 0;
        this.isBackgroundPreloading = false;
        this.isInitialized = false;
    }

    /**
     * Force preload images for a specific product list
     */
    public preloadProductImages(products: ProductApiOut[], immediate: boolean = false): void {
        for (const product of products) {
            const imageUrl = this.getProductImageUrl(product);
            if (imageUrl) {
                if (immediate) {
                    this.preloadImage(imageUrl);
                } else {
                    this.queueImageForPreloading(imageUrl);
                }
            }
        }

        if (!immediate && !this.isBackgroundPreloading) {
            this.processPreloadingQueue();
        }
    }
}
