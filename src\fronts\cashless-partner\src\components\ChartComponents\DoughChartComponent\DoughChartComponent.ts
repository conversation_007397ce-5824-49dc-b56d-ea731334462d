import {Component, Prop, Ref, Vue, Watch} from "vue-facing-decorator";
import {Chart, registerables, ChartData} from "chart.js";
import {ExternalTooltipHandler} from "../../../../../../shared/utils/ExternalTooltipHandler";
import {MoneyFilter} from "../../../../../../shared/filters/Money";
import {VueIgnore} from "@groupk/horizon2-front";

@Component({})
export default class DoughChartComponent extends Vue {
	@Prop() data!: ChartData;
	@Prop({default: 'NUMBER'}) dataFormat!: 'NUMBER'|'MONEY';

	@Ref() canvas!: HTMLCanvasElement;

	@VueIgnore chart: Chart|null = null;

	@Watch('data', {deep: true})
	dataWatch() {
		if(this.chart){
			// for(let i = 0; i < chartJs.data.datasets.length; ++i){
			// 	const newData = JSON.parse(JSON.stringify(this.data.datasets[i].data));
			// 	console.log(newData);
			// 	chartJs.data.datasets[i].data = newData;
			// }
			this.chart.data = JSON.parse(JSON.stringify(this.data));
			this.chart.update();
		}
	}

	mounted() {
		Chart.register(...registerables);
		this.initChart();
	}

	initChart() {
		this.chart = new Chart(this.canvas, {
			type: 'doughnut',
			data: this.data,
			options: {
				plugins: {
					legend: {
						display: false
					},
					tooltip: {
						callbacks: {
							label: (context : any) => {
								if(this.dataFormat === 'NUMBER') {
									return 'Commandes: ' + (context as any).raw;
								} else {
									return 'CA ' + MoneyFilter((context as any).raw * 100);
								}
							}
						},
						enabled: false,
						position: 'nearest',
						external: ExternalTooltipHandler.handler as never
					}
				}
			}
		});
	}
}