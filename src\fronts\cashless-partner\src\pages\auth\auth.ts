import {Component, Vue} from "vue-facing-decorator";
import {AutoWired} from "@groupk/horizon2-core";
import {Router} from "@groupk/horizon2-front";
import {EstablishmentUrlBuilder} from "../../../../../shared/utils/EstablishmentUrlBuilder";
import {AuthRepository} from "../../../../../shared/repositories/AuthRepository";
import {AuthStateModel} from "../../../../../shared/AuthStateModel";

@Component({})
export default class AuthView extends Vue {
	@AutoWired(AuthStateModel) accessor authStateModel!: AuthStateModel;
	@AutoWired(Router) accessor router!: Router;
	@AutoWired(AuthRepository) accessor authRepository!: AuthRepository;

	error!: string|null;

	async mounted() {
		const urlParams = new URLSearchParams(window.location.search);
		// const rawToken = urlParams.get('token');
		const redirectAfter = urlParams.get('redirectAfter') ?? null;
		// if(!rawToken) {
		// 	this.error = 'Un problème est survenu lors de la tentative de connexion - err1'
		// } else {
		// 	try {
		// 		const parsedToken = JSON.parse(atob(rawToken));
		// 		const token =
		// 			'accountUid' in parsedToken ?
		// 				hydrate(AccountAuthTokenApiOut, parsedToken)
		// 				: hydrate(EstablishmentAuthTokenApiOut, parsedToken);
		//
		// 		this.authStateModel.storeConnectionToken(token);
		//
		// 		await this.refreshToken(token);
		//
		// 	} catch(err) {
		// 		console.error(err);
		// 		this.error = 'Un problème est survenu lors de la tentative de connexion - err2'
		// 	}
		// }

		window.location.href = EstablishmentUrlBuilder.buildUrl(redirectAfter ? redirectAfter : '/');

	}
}
