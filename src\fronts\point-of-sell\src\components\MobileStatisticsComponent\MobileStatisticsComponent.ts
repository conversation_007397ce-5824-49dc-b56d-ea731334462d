import {Component, Prop, Vue} from "vue-facing-decorator";
import {AutoWired, Uuid, VisualScopedUuid} from "@groupk/horizon2-core";
import {randomUUID} from "@groupk/horizon2-front";
import {PosState} from "../../model/PosState";
import {LocalOrder} from "../../model/LocalOrder";
import {OrderExecutorModel, OrderStatsModel} from "@groupk/mastodon-core";
import {LocalOrderRepository} from "../../repositories/LocalOrderRepository";
import {LocalFavoriteStockRepository} from "../../repositories/LocalFavoriteStockRepository";
import {SearchUtils} from "../../class/SearchUtils";
import {UuidScopeEstablishmentAccount} from "@groupk/mastodon-core";

@Component({})
export default class MobileStatisticsComponent extends Vue {
	refreshKey: Uuid = randomUUID();

	@Prop() posState!: PosState;

	selectedEmployee: VisualScopedUuid<UuidScopeEstablishmentAccount>|'global' = 'global';

	stockSearch: string = '';
	orders: LocalOrder[] = [];

	@AutoWired(OrderExecutorModel) accessor orderExecutorModel !: OrderExecutorModel;
	@AutoWired(LocalOrderRepository) accessor localOrderRepository !: LocalOrderRepository;
	@AutoWired(LocalFavoriteStockRepository) accessor localFavoriteStockRepository !: LocalFavoriteStockRepository;

	async mounted() {
		this.orders = await this.localOrderRepository.findAll();
	}

	get ordersStatistics() {
		const statsModel = new OrderStatsModel(this.orderExecutorModel);
		return statsModel.compute(this.orders.map((localOrder) => localOrder.order));
	}

	get productStatistics(): { name: string, quantity: number, totalPriceWithTaxes: number }[] {
		const stats: Record<string, { name: string, quantity: number, totalPriceWithTaxes: number }> = {};
		for(let localOrder of this.orders) {
			for(let purchase of localOrder.order.purchases) {
				const name = this.posState.orderExecutorModel.requireProductRevision(purchase.productRevisionUid).name;
				const purchaseQuantity = this.posState.orderExecutorModel.getPurchaseQuantity(purchase);
				const purchasePrice = this.posState.orderExecutorModel.getPurchasePrice(localOrder.order, purchase);
				if(!('withTaxes' in purchasePrice)) throw new Error('');

				if(purchaseQuantity.notCanceled > 0) {
					if(!stats[purchase.productRevisionUid]) stats[purchase.productRevisionUid] = {name: name, quantity: 0, totalPriceWithTaxes: 0};
					stats[purchase.productRevisionUid].quantity += purchaseQuantity.notCanceled;
					stats[purchase.productRevisionUid].totalPriceWithTaxes += purchasePrice.withTaxes;
				}
			}
		}
		return Object.values(stats).sort((data1, data2) => {
			return data1.quantity > data2.quantity ? -1 : 1;
		});
	}

	get filteredProducts() {
		return SearchUtils.searchInTab(this.posState.products, (product) => {
			return [product.lastRevision.name, product.lastRevision.ean13 ?? ''];
		}, this.stockSearch);

	}

	async toggleAllFavorite() {
		const favoriteProductUids = await this.localFavoriteStockRepository.get();
		const productWithStock = this.posState.products.filter((product) => {
			return this.posState.virtualStockHandler.estimateRemainingStock(product.lastRevision) !== null;
		});
		if(favoriteProductUids.length === productWithStock.length) {
			this.localFavoriteStockRepository.save([]);
		} else {
			const uids = productWithStock.map((product) => product.uid);
			this.localFavoriteStockRepository.save(uids);
		}

		this.refreshKey = randomUUID();
	}
}