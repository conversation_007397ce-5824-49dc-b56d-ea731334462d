import {Component, Vue} from "vue-facing-decorator";
import {ImportColumn, ModalOrDrawerComponent, TableImportComponent} from "@groupk/vue3-interface-sdk";
import {AutoWired} from "@groupk/horizon2-core";
import {
	ProfileApiIn,
	ProfileApiOut,
	ApplicationPermission,
	EstablishmentAccountPermissionModel,
	CashlessHttpProfileContract
} from "@groupk/mastodon-core";
import {ProfileImportExportHelper} from "../../../../../shared/utils/ProfileImportExportHelper";
import {ProfilesRepository} from "../../../../../shared/repositories/ProfilesRepository";
import {AppState} from "../../../../../shared/AppState";

export function ImportProfileModalComponentHasRequiredPermissions(ownedPermissions: ApplicationPermission[]): boolean {
	return EstablishmentAccountPermissionModel.hasPermissionsForContracts(ownedPermissions, [
		CashlessHttpProfileContract.list,
	]);
}

@Component({
	components: {
		'modal-or-drawer': ModalOrDrawerComponent,
		'table-import': TableImportComponent,
	},
	emits: ['close', 'imported-profiles']
})
export default class ImportProfileModalComponent extends Vue {
	csvImportColumns: ImportColumn[] = ProfileImportExportHelper.csvImportColumns;

	csvImportEntityType = ProfileApiIn;

	profiles: ProfileApiOut[]|null = null;

	profileImportExportHelper!: ProfileImportExportHelper;

	loading: boolean = false;
	opened: boolean = false;

	@AutoWired(ProfilesRepository) accessor profilesRepository!: ProfilesRepository;
	@AutoWired(AppState) accessor appState!: AppState;

	async mounted(){
		this.loading = true;

		this.profileImportExportHelper = new ProfileImportExportHelper(this.appState.requireUrlEstablishmentUid());
		this.profiles = (await this.profilesRepository.callContract('list', {establishmentUid: this.appState.requireUrlEstablishmentUid()},{})).success();

		setTimeout(() => {
			this.opened = true;
		}, 0);

		this.loading = false;
	}

	emitImportedProfiles(profiles: ProfileApiOut[]) {
		this.$emit('imported-profiles', profiles);
	}

	close(){
		this.opened = false;
		setTimeout(() => {
			this.$emit('close');
		}, 500);
	}
}