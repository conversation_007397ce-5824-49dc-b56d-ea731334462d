import {Component, Prop, Vue} from "vue-facing-decorator";
import {FormModalOrDrawerComponent} from "@groupk/vue3-interface-sdk";
import {
    CustomerApiOut,
    EventApiOut,
    StockTemporalApiOut,
    StockTemporalUsageListApiOut,
    UuidScopeCustomer_customer
} from "@groupk/mastodon-core";
import {AutoWired, VisualScopedUuid} from "@groupk/horizon2-core";
import {StockTemporalRepository} from "../../../../../shared/repositories/StockTemporalRepository";
import {AppState} from "../../../../../shared/AppState";
import {MultiFormatExporter} from "../../../../../shared/utils/MultiFormatExporter";
import {DateFilter, HourFilter} from "../../../../../shared/filters/DateFilter";
import DateUtils from "../../../../../shared/utils/DateUtils";

@Component({
    components: {
        'form-modal-or-drawer': FormModalOrDrawerComponent
    }
})
export default class CalendarExportComponent extends Vue {
    @Prop() event!: EventApiOut;
    @Prop() stockTemporal!: StockTemporalApiOut;
    @Prop({ required: true }) customers!: CustomerApiOut[];

    usages!: StockTemporalUsageListApiOut;
    monthStats!: {
        orderTicketQuantity: number,
        orderQuantity: number,
        batchTicketQuantity: number,
        batchQuantity: number
    };
    detailedStats!: {
        date: string,
        orderTicketQuantity: number,
        orderQuantity: number,
        batchTicketQuantity: number,
        batchQuantity: number
    }[];

    selectedMonth: Date = new Date();
    exporting: boolean = false;
    opened: boolean = false;
    loading: boolean = true;

    monthNames: string[] = ['Janvier', 'Février', 'Mars', 'Avril', 'Mai', 'Juin', 'Juillet', 'Août', 'Septembre', 'Octobre', 'Novembre', 'Décembre'];

    @AutoWired(StockTemporalRepository) accessor stockTemporalRepository!: StockTemporalRepository;
    @AutoWired(AppState) accessor appState!: AppState;

    async mounted() {
        setTimeout(() => this.opened = true, 0);

        this.loadDataForMonth();
    }

    async loadDataForMonth() {
        this.loading = true;

        this.usages = (await this.stockTemporalRepository.callContract('getUsageList', {
            establishmentUid: this.appState.requireUrlEstablishmentUid(),
            stockUid: this.stockTemporal.uid,
            startDatetime: new Date(this.selectedMonth.getFullYear(), this.selectedMonth.getMonth(), 1).toISOString(),
            endDatetime: new Date(this.selectedMonth.getFullYear(), this.selectedMonth.getMonth() + 1, 0).toISOString(),
        }, undefined)).success();

        this.monthStats = {
            orderTicketQuantity: 0,
            orderQuantity: 0,
            batchTicketQuantity: 0,
            batchQuantity: 0
        };

        // Initialize detailed stats map for daily statistics
        const dailyStatsMap: Map<string, {
            orderTicketQuantity: number,
            orderQuantity: number,
            batchTicketQuantity: number,
            batchQuantity: number
        }> = new Map();

        // Pre-populate all dates in the current month with zero values
        const startOfMonth = new Date(this.selectedMonth.getFullYear(), this.selectedMonth.getMonth(), 1);
        const endOfMonth = new Date(this.selectedMonth.getFullYear(), this.selectedMonth.getMonth() + 1, 0);

        for(let date = new Date(startOfMonth); date <= endOfMonth; date.setDate(date.getDate() + 1)) {
            const dateKey = DateUtils.formatDateIsoFormat(date.toISOString());
            dailyStatsMap.set(dateKey, {
                orderTicketQuantity: 0,
                orderQuantity: 0,
                batchTicketQuantity: 0,
                batchQuantity: 0
            });
        }

        // Process orders and their tickets
        for(const order of this.usages.orderList) {
            this.monthStats.orderQuantity++;

            // Track which dates this order was used
            const orderUsageDates = new Set<string>();

            for(const ticketPurchaseLink of (order.ticketPurchaseLinkList ?? [])) {
                const ticket = this.usages.ticketList.find((ticket) => ticket.uid === ticketPurchaseLink.ticketUid);
                if(!ticket) continue;

                if(ticket.groupUid === order.uid) {
                    this.monthStats.orderTicketQuantity++;

                    // Find usage date for this ticket
                    const usage = this.usages.list.find((usage) => usage.targetUid === ticketPurchaseLink.purchaseItemUid);
                    if(usage && usage.datetime) {
                        const usageDate = DateUtils.formatDateIsoFormat(usage.datetime);

                        // Get daily stats (should already exist from pre-population)
                        const dayStats = dailyStatsMap.get(usageDate);
                        if(dayStats) {
                            dayStats.orderTicketQuantity++;
                            orderUsageDates.add(usageDate);
                        }
                    }
                }
            }

            // Count the order once per day it was used
            for(const usageDate of orderUsageDates) {
                const dayStats = dailyStatsMap.get(usageDate);
                if(dayStats) {
                    dayStats.orderQuantity++;
                }
            }
        }

        // Process batches and their tickets
        for(const batch of this.usages.ticketBatchList) {
            this.monthStats.batchQuantity++;

            // Track which dates this batch was used
            const batchUsageDates = new Set<string>();

            for(const ticket of this.usages.ticketList) {
                if(ticket.groupUid === batch.uid) {
                    this.monthStats.batchTicketQuantity++;

                    // Find usage date for this ticket
                    const usage = this.usages.list.find((usage) => usage.targetUid === ticket.uid);
                    if(usage && usage.datetime) {
                        const usageDate = DateUtils.formatDateIsoFormat(usage.datetime);

                        // Get daily stats (should already exist from pre-population)
                        const dayStats = dailyStatsMap.get(usageDate);
                        if(dayStats) {
                            dayStats.batchTicketQuantity++;
                            batchUsageDates.add(usageDate);
                        }
                    }
                }
            }

            // Count the batch once per day it was used
            for(const usageDate of batchUsageDates) {
                const dayStats = dailyStatsMap.get(usageDate);
                if(dayStats) {
                    dayStats.batchQuantity++;
                }
            }
        }

        // Convert map to array and sort by date
        this.detailedStats = Array.from(dailyStatsMap.entries())
            .map(([date, stats]) => ({
                date,
                ...stats
            }))
            .sort((a, b) => a.date.localeCompare(b.date));

        this.loading = false;
    }

    previousMonth() {
        this.selectedMonth = new Date(this.selectedMonth.getFullYear(), this.selectedMonth.getMonth() - 1, 1);
        this.loadDataForMonth();
    }

    nextMonth() {
        this.selectedMonth = new Date(this.selectedMonth.getFullYear(), this.selectedMonth.getMonth() + 1, 1);
        this.loadDataForMonth();
    }

    exportUsages() {
        this.exporting = true;

        const jsonData: Record<string, string|number>[] = [];

        for(const order of this.usages.orderList) {
            const customer: CustomerApiOut|null = order.customerUid ? this.requireCustomerWithUid(order.customerUid) : null;

            for(const ticketPurchaseLink of (order.ticketPurchaseLinkList ?? [])) {
                const ticket = this.usages.ticketList.find((ticket) => ticket.uid === ticketPurchaseLink.ticketUid);
                if(!ticket) throw new Error('missing_ticket');

                const date = this.usages.list.find((usage) => usage.targetUid === ticketPurchaseLink.purchaseItemUid)?.datetime;
                jsonData.push({
                    'Date d\'achat': DateUtils.formatDateIsoFormat(ticket.creationDatetime),
                    'Heure d\'achat': DateUtils.formatDateHour(ticket.creationDatetime, ':'),
                    'Date de venue': date ? DateUtils.formatDateIsoFormat(date) : '',
                    'Heure de venue': date ? DateUtils.formatDateHour(date, ':') : '',
                    'Nom': customer ? customer.lastname ?? '' : '',
                    'Prenom': customer ? customer.firstname ?? '' : '',
                    'Email': customer ? customer.email ?? '' : '',
                    'Billet': ticket.templateTicketName
                });
            }
        }

        for(const batch of this.usages.ticketBatchList) {
            for(const ticket of this.usages.ticketList) {
                const customer = ticket.ownerCustomerUid ? this.requireCustomerWithUid(ticket.ownerCustomerUid) : null;
                if(ticket.groupUid === batch.uid) {
                    const date = this.usages.list.find((usage) => usage.targetUid === ticket.uid)?.datetime;
                    jsonData.push({
                        'Date d\'achat': DateUtils.formatDateIsoFormat(ticket.creationDatetime),
                        'Heure d\'achat': DateUtils.formatDateHour(ticket.creationDatetime, ':'),
                        'Date de venue': date ? DateUtils.formatDateIsoFormat(date) : '',
                        'Heure de venue': date ? DateUtils.formatDateHour(date, ':') : '',
                        'Nom': customer ? customer.lastname ?? '' : '',
                        'Prenom': customer ? customer.firstname ?? '' : '',
                        'Email': customer ? customer.email ?? '' : '',
                        'Billet': ticket.templateTicketName
                    });
                }
            }
        }

        MultiFormatExporter.downloadData([
            {name: 'Date', type: 'AUTO'},
            {name: 'Heure', type: 'AUTO'},
            {name: 'Nom', type: 'AUTO'},
            {name: 'Prenom', type: 'AUTO'},
            {name: 'Email', type: 'AUTO'},
            {name: 'Billet', type: 'AUTO'},
        ], jsonData, 'csv');

        this.exporting = false;
    }

    close() {
        this.$emit('close');
        this.opened = false;
    }

    requireCustomerWithUid(customerUid: VisualScopedUuid<UuidScopeCustomer_customer>): CustomerApiOut {
        const customer = this.customers.find((customer) => customer.uid === customerUid);
        if (!customer) throw new Error('missing_customer');
        return customer;
    }
}