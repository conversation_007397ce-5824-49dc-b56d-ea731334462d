<script lang="ts" src="./WebQrCodeReaderComponent.ts">
</script>

<style lang="sass">
@use './WebQrCodeReaderComponent.scss' as *
</style>

<template>
    <div class="web-qr-code-reader-component">
        <div class="browser-camera-dimmer" :class="{opened: opened}" @click="done()"></div>
        <div class="browser-camera-drawer" :class="{opened: opened}">
            <video :id="'video-' + uuid" class="video-frame"></video>
        </div>

        <form-modal-or-drawer
            title="Choisir une caméra"
            subtitle="Quelle caméra souhaitez-vous utiliser pour scanner les QR codes ?"
            :state="selectedVideoDeviceId === null"
        >
            <template v-slot:content>
                <div class="loader-container" v-if="loading">
                    <div class="loader"></div>
                </div>
                <template v-else>
                    <div class="no-device" v-if="availableVideoDevices.length === 0">
                        Aucune caméra n'a été détectée, impossible d'utiliser cette interface
                        <button class="button" @click="back()">
                            Retour
                        </button>
                    </div>
                    <div class="devices">
                        <div class="video-device" v-for="device of availableVideoDevices" @click="selectedVideoDeviceId = device.deviceId">
                            {{ getDeviceName(device) }}
                            <i class="fa-regular fa-chevron-right"></i>
                        </div>
                    </div>
                </template>
            </template>
        </form-modal-or-drawer>
    </div>
</template>