#categories-page {
    box-sizing: border-box;

    tr.hovered {
        background: #EEF2FF;
    }

    .page-content {
        display: flex;
        gap: 20px;
        box-sizing: border-box;

        .category-group {
            font-weight: 600;
        }

        .categories {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr)) ;
            grid-gap: 20px;

            .category {
                display: flex;
                flex-direction: column;
                padding: 20px;
                border-radius: 8px;
                border: #eeeeee 1px solid;
                background: #fafafa;
                gap: 15px;
                box-sizing: border-box;

                &.hovered {
                    background: #EEF2FF;
                    border: #4F46E5;
                }

                .empty-category {
                    font-size: 14px;
                    color: #7e828c;
                }

                .category-header {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;

                    .category-buttons {
                        display: flex;
                        align-items: center;
                        gap: 5px;

                        .edit-button {
                            padding: 5px 8px;
                            border-radius: 4px;
                            cursor: pointer;

                            &:hover {
                                background: #E8E8E8;
                            }
                        }
                    }

                }

                .title {
                    font-weight: 600;
                    font-size: 14px;
                }

                .products {
                    display: flex;
                    flex-direction: column;
                    gap: 5px;
                    min-height: 80px;

                    .product {
                        display: flex;
                        align-items: center;
                        gap: 8px;
                        justify-content: space-between;
                        background: white;
                        border: #eeeeee 1px solid;
                        padding: 10px 15px;
                        border-radius: 6px;
                        cursor: grab;
                        font-size: 14px;

                        &.draggable-mirror {
                            position: relative;
                            min-width: 300px;
                            max-width: 100%;
                            z-index: 9;
                        }

                        &.draggable-source--is-dragging {
                            background: #E2E2E2;
                            opacity: 0.5;
                            z-index: 0;
                        }

                        img {
                            height: 15px;
                        }
                    }
                }

                .delete-zone {
                    padding: 10px 15px;
                    border-radius: 6px;
                    font-size: 14px;
                    color: #7e828c;
                    border: 1px dashed #cbcbcb;
                    display: none;

                    &.displayed {
                        display: flex;
                        align-items: center;
                        gap: 10px;
                    }

                    .draggable-source--is-dragging {
                        display: none !important;
                    }

                    &.draggable-container--over {
                        background: #f64e4e;
                        color: white;
                    }
                }
            }

            .new-category {
                display: flex;
                flex-direction: column;
                justify-content: center;
                padding: 20px;
                align-items: center;
                gap: 10px;
                border: 1px dashed var(--border-color);
                border-radius: 8px;
                background: #fafafa;
                cursor: pointer;
                height: 100px;
                font-size: 14px;

                i {
                    font-size: 26px;
                    color: #696969;
                }
            }
        }
    }


    .right-view {
        display: flex;
        flex-direction: column;
        gap: 20px;
        padding: 40px;

        @media screen and (max-width: 900px){
            padding: 16px 20px;
        }

        .products {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            grid-gap: 20px;

            .product {
                display: flex;
                flex-direction: column;
                align-items: stretch;
                height: 120px;
                width: 100%;
                max-width: 120px;
                box-sizing: border-box;
                cursor: grab;
                border-radius: 6px;
                background: #FFF;
                border: #eeeeee 1px solid;
                text-align: center;
                font-size: 14px;

                &.selected {
                    background: #EEF2FF;
                    border: 1px solid #C7D2FE;

                    .bottom {
                        background: #4F46E5;
                        color: white;
                    }
                }

                .top {
                    flex-grow: 2;
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;

                    img {
                        height: 30px;
                        padding: 5px;
                    }
                }

                .bottom {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    flex-shrink: 0;
                    padding: 5px;
                    background: #E8E8E8;
                    font-size: 14px;
                    border-radius: 0 0 6px 6px;
                }
            }
        }
    }
}