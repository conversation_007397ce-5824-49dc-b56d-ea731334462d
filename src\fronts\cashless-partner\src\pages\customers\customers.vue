<script lang="ts" src="./customers.ts">
</script>

<style scoped lang="sass">
@use './customers.scss' as *
</style>

<template>
    <div id="customers-page" class="page">
        <forbidden-message v-if="forbidden"></forbidden-message>
        <template v-else>
            <div class="mobile-content-dimmer" :class="{displayed: selectedCustomer}"></div>
            <div class="customers-list" :class="{opened: selectedCustomer}">
            <div class="search">
                <div class="head">
                    <h1> Clients </h1>

                    <div class="action-head">
                        <div class="action-group mobile-hidden">
                            <button class="small white button" @click="showFilters = true">
                                <i class="fa-regular fa-bars-filter"></i>
                                Filtrer

                                <div v-if="getFiltersCount(appliedFilters.filter) > 0" class="active-filter" @click.stop="customerSearch = ''; searchCustomers({})">
                                    {{ getFiltersCount(appliedFilters.filter) }} filtre{{ getFiltersCount(appliedFilters.filter) === 1 ? '' : 's'}}
                                    <i class="fa-light fa-times"></i>
                                </div>
                            </button>

                            <div class="modal-custom-filter-dimmer" v-if="showFilters" @click="showFilters = false"></div>
                            <div class="custom-filter-modal" v-if="showFilters">
                                <custom-filter
                                    ref="filtersModal"
                                    v-if="showFilters"
                                    :filters="filters"
                                    :parameters="filterParameters"
                                    :allowed-filters="allowedFilters.filters"
                                    @apply-filters="searchCustomers($event); showFilters = false"
                                    @close="showFilters = false"
                                ></custom-filter>
                            </div>
                        </div>

                        <dropdown-button
                            button-class="small white button"
                            icon="fa-regular fa-chevron-down"
                            title="Plus"
                            alignment="RIGHT"
                            @clicked="dropdownClicked($event)"
                            :actions="[{title: 'Actions', actions: dropdownActions}]"
                        ></dropdown-button>
                    </div>

                </div>
                <div class="input-group">
                    <input v-model="customerSearch" type="text" placeholder="Rechercher..." @input="search()" />
                </div>
            </div>

            <div class="empty-customers" v-if="Object.keys(groupedCustomers).length === 0">
                Aucun client
            </div>

            <template v-for="(customers, letter) of groupedCustomers">
                <div class="letter-separator"> {{ letter }} </div>
                <div class="letter-group">
                    <div
                        class="customer"
                        :class="{active: selectedCustomer && selectedCustomer.uid === customer.uid }"
                        v-for="customer in customers"
                        @click="selectCustomer(customer)"
                    >
                        <div class="profile-picture" :style="`background-image: url(/no-pp.svg)`"></div>
                        <div class="infos">
                            <span class="name"> {{ customer.lastname }} {{ customer.firstname }} </span>
                            <span class="email"> {{ customer.email }} </span>
                        </div>
                    </div>
                </div>
            </template>

            <div class="load-more">
                <button v-if="customers.length === 50" class="small white button" :class="{ loading: loading, disabled: loading }" @click="loadMore()">
                    Voir plus...
                </button>
            </div>
        </div>
        <div class="customer-details" :class="{opened: selectedCustomer}">
            <layout :drawer-opened="selectedChip !== null">
                <template v-slot:content>

                    <div class="empty-state" v-if="!selectedCustomer">
                        <img :src="$assets.selectHint" />
                        Cliquez sur un client pour <br/> le sélectionner

                        <button class="small white button" :class="{disabled: disabledActions.includes('create-customer')}" @click="showCustomerForm = true">
                            <i class="fa-regular fa-plus"></i>
                            Créer un client
                        </button>
                    </div>

                    <div class="selected-customer" v-if="selectedCustomer">
                        <div class="profile-data-container">
                            <div class="profile-data">
                                <div class="close-area">
                                    <div class="small white button" @click="selectCustomer(selectedCustomer)">
                                        <i class="fa-regular fa-xmark"></i>
                                        <span> Fermer </span>
                                    </div>
                                </div>

                                <div class="header">
                                    <div class="profile-picture" :style="`background-image: url(/no-pp.svg)`"></div>
                                    <div class="infos">
                                        <span class="name"> {{ selectedCustomer.lastname }} {{ selectedCustomer.firstname }} </span>
                                        <span class="email"> {{ selectedCustomer.email }} </span>
                                    </div>
                                </div>

                                <div class="various-data">
                                    <div class="data">
                                        <div class="title"> Description </div>
                                        <div class="content"> {{ selectedCustomer.description ? selectedCustomer.description : 'Aucune description' }} </div>
                                    </div>
                                    <!--                                <div class="data">-->
                                    <!--                                    <div class="title"> Date de naissance </div>-->
                                    <!--                                    <div class="content"> 20/10/1998 </div>-->
                                    <!--                                </div>-->
                                    <!--                                <div class="data">-->
                                    <!--                                    <div class="title"> BDE </div>-->
                                    <!--                                    <div class="content"> Polytech’Tours </div>-->
                                    <!--                                </div>-->
                                </div>

                                <div class="actions mobile-hidden">
                                    <div class="action" @click="editCustomer(selectedCustomer)">
                                        <i class="fa-regular fa-pen-line fa-flip-horizontal"></i>
                                        Modifier les informations
                                    </div>

                                    <div class="action" @click="showChipCreationForm = true">
                                        <i class="fa-regular fa-circle-plus"></i>
                                        Lier une puce
                                    </div>

                                    <div class="action" :class="{disabled: disabledActions.includes('export')}" @click="showCustomersTransactionsExportModal = true">
                                        <i class="fa-regular fa-arrow-down-to-line"></i>
                                        Exporter les consos
                                    </div>
                                </div>

                                <dropdown-button
                                    class="mobile-only"
                                    button-class="tertiary button"
                                    icon="fa-regular fa-ellipsis-vertical"
                                    title="Actions"
                                    alignment="LEFT"
                                    @clicked="customerDropdownClicked(selectedCustomer, $event)"
                                    :actions="[{title: '', actions: customerDropdownActions}]"
                                ></dropdown-button>
                            </div>

<!--                            <div class="qr-code">-->

<!--                            </div>-->
                        </div>



                        <div class="customer-content">
                            <div class="table-scroll-wrapper">
                                <table class="data-table">
                                    <thead>
                                    <tr>
                                        <td> ID public </td>
                                        <td> Date de liaison </td>
                                        <td> Fin de liaison </td>
                                        <td> Solde (€) </td>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <div class="table-dimmer" v-if="loadingChips">
                                        <div class="loader-container">
                                            <div class="loader"></div>
                                        </div>
                                    </div>
                                    <tr v-else-if="chipsPerCustomer[selectedCustomer.uid].length === 0">
                                        <td class="empty" colspan="100%"> Aucune puce liée </td>
                                    </tr>
                                    <tr v-else v-for="chip of chipsPerCustomer[selectedCustomer.uid]" @click="selectChip(chip)">
                                        <td> {{ $filters.Chip(chip.chipVisualId) }} </td>
                                        <td> {{ $filters.Date(chip.startingDatetime) }} </td>
                                        <td> {{ chip.endingDatetime ? $filters.Date(chip.endingDatetime) : '-' }} </td>
                                        <td> {{ $filters.Money(getEuroWalletAmount(chip.wallets)) }} </td>
                                    </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </template>
                <template v-slot:right>
                    <template v-if="selectedCustomer">
                        <div v-if="!selectedChip" class="empty-right-panel">
                            <img :src="$assets.selectHint" />
                            Cliquer sur une puce <br/> la sélectionner
                        </div>
                        <div class="right-view" v-else>
                            <div class="close" @click="selectedChip = null">
                                <i class="fa-regular fa-xmark"></i>
                                <span> Fermer </span>
                            </div>

                            <div class="header">
                                <div class="left">
                                    <h2> {{ $filters.Chip(selectedChip.chipVisualId) }} </h2>
                                </div>

                                <dropdown-button
                                    button-class="grey button"
                                    icon="fa-regular fa-ellipsis-vertical"
                                    title=""
                                    alignment="RIGHT"
                                    @clicked="chipDropdownClicked($event)"
                                    :actions="[{title: 'Actions', actions: chipDropdownActions}]"
                                ></dropdown-button>
                            </div>

                            <div class="chip-details">
                                <div class="properties-table">
                                    <div class="row">
                                        <span class="title"> Liée le </span>
                                        <span class="value"> {{ $filters.Date(selectedChip.startingDatetime) }} à {{ $filters.Hour(selectedChip.startingDatetime) }} </span>
                                    </div>
                                    <div class="row">
                                        <span class="title"> Rendue le </span>
                                        <span class="value">
                                        {{ selectedChip.endingDatetime ? ($filters.Date(selectedChip.endingDatetime) + ' à ' + $filters.Hour(selectedChip.endingDatetime)) : '-' }}
                                    </span>
                                    </div>
                                    <div class="row">
                                        <span class="title"> Solde (€) </span>
                                        <span class="value"> {{ $filters.Money(getEuroWalletAmount(selectedChip.wallets)) }} </span>
                                    </div>
                                </div>
                            </div>

                            <button class="small white button" @click="goToChipTransactions(selectedChip)">
                                <i class="fa-regular fa-arrow-up-right-from-square"></i>
                                Voir les transactions liées
                            </button>
                        </div>
                    </template>
                </template>
            </layout>
        </div>

        <customer-form
            v-if="showCustomerForm"
            :establishment-uid="appState.requireUrlEstablishmentUid()"
            :editing-customer="editingCustomer"
            @created="createdCustomer($event)"
            @updated="updatedCustomer($event)"
            @close="editingCustomer = null; showCustomerForm = false;"
        ></customer-form>

        <chip-creation-form
            v-if="showChipCreationForm && selectedCustomer"
            :establishment-uid="appState.requireUrlEstablishmentUid()"
            :customer="selectedCustomer"
            @created="createdChip($event)"
            @close="showChipCreationForm = false"
        ></chip-creation-form>

        <chip-dates-form
            v-if="showChipDatesForm && selectedChip"
            :establishment-uid="appState.requireUrlEstablishmentUid()"
            :customer-chip="selectedChip"
            @updated="updatedChip($event)"
            @close="showChipDatesForm = false"
        ></chip-dates-form>

        <customer-import-modal
            :state="showCustomersImportModal"
            @close="showCustomersImportModal = false"
        ></customer-import-modal>

        <wallet-declare-state-form
            v-if="showUnusableChipConfirmationModal"
            :reason=showUnusableChipConfirmationModal
            :wallet-uid="selectedChip?.wallets[0].uid"
            @close="showUnusableChipConfirmationModal = null"
        ></wallet-declare-state-form>

        <wallet-empty-balance-form
            v-if="showEmptyChipConfirmationModal"
            :wallet="selectedChip"
            @close="showEmptyChipConfirmationModal = false"
        ></wallet-empty-balance-form>

        <customer-transactions-export
            v-if="showCustomersTransactionsExportModal"
            :customer-uid="selectedCustomer?.uid"
            @close="showCustomersTransactionsExportModal = false"
        ></customer-transactions-export>
        </template>
    </div>
</template>