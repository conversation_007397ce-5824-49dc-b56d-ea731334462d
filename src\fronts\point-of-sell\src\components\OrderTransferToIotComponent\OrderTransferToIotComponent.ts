import {Component, Prop, Vue} from "vue-facing-decorator";
import RightModalComponent from "../RightModalComponent/RightModalComponent.vue";
import {PosState} from "../../model/PosState";
import {
    EstablishmentDeviceKnownService,
    OrderTransferApiIn,
    uuidScopeIot_deviceApp
} from "@groupk/mastodon-core";
import {OrderRepository} from "../../../../../shared/repositories/OrderRepository";
import {AutoWired, ScopedUuid, UuidUtils} from "@groupk/horizon2-core";
import {LocalOrder, LocalOrderTransfer} from "../../model/LocalOrder";
import {randomUUID} from "@groupk/horizon2-front";
import {LocalOrderTransferRepository} from "../../repositories/LocalOrderTransferRepository";
import {translateResponseError} from "../../../../../shared/RepositoryExtensions";
import {UuidScopeIot_deviceApp} from "@groupk/mastodon-core";

@Component({
    components: {
        'right-modal': RightModalComponent
    },
    emits: ['close']
})
export default class OrderTransferToIotComponent extends Vue {
    @Prop({required: true}) posState!: PosState;
    @Prop({required: true}) localOrder!: LocalOrder;

    favorites: ScopedUuid<UuidScopeIot_deviceApp>[] = [];
    creatingTransfer: ScopedUuid<UuidScopeIot_deviceApp>|null = null;
    error: string|null = null;

    @AutoWired(OrderRepository) accessor orderRepository!: OrderRepository;
    @AutoWired(LocalOrderTransferRepository) accessor localOrderTransferRepository!: LocalOrderTransferRepository;

    beforeMount() {
        this.favorites = localStorage.getItem('order-transfer-favorites') ? JSON.parse(localStorage.getItem('order-transfer-favorites')!) : [];
    }

    get posDevices() {
        return this.posState.iotDeviceApps.filter((device) => device.settings.type === EstablishmentDeviceKnownService.POINT_OF_SALE).sort((a, b) => {
            if(this.favorites.includes(a.uid) && !this.favorites.includes(b.uid)) return -1;
            if(!this.favorites.includes(a.uid) && this.favorites.includes(b.uid)) return 1;
            return 0;
        });
    }

    async transferToIot(iotUid: ScopedUuid<UuidScopeIot_deviceApp>) {
        this.creatingTransfer = iotUid;

        const codes = OrderTransferApiIn.generateUnlockCodes();
        const pendingTransfer = new LocalOrderTransfer({
            uid: randomUUID(),
            transferUid: null,
            order: this.localOrder.order,
            creatorIotDevice: UuidUtils.scopedToVisual(this.posState.iotDevice.uid, uuidScopeIot_deviceApp),
            originIotDevice: UuidUtils.scopedToVisual(this.posState.iotDevice.uid, uuidScopeIot_deviceApp),
            targetIotDevice: UuidUtils.scopedToVisual(iotUid, uuidScopeIot_deviceApp),
            unlockCodeOk: codes.ok,
            unlockCodeKo: codes.ko,
            canceled: false
        });

        try {
            if(this.posState.currentOrder && this.posState.currentOrder.uid === pendingTransfer.order.uid) {
                this.posState.currentOrder = null;
            }

            await this.localOrderTransferRepository.save(pendingTransfer);

            const response = await this.orderRepository.callContract('createTransfer', {establishmentUid: this.posState.establishmentUid}, new OrderTransferApiIn({
                order: this.localOrder.order,
                targetIotDevice: UuidUtils.scopedToVisual(iotUid, uuidScopeIot_deviceApp),
                creatorIotDevice: UuidUtils.scopedToVisual(this.posState.iotDevice.uid, uuidScopeIot_deviceApp),
                unlockCodeOk: pendingTransfer.unlockCodeOk,
                unlockCodeKo: pendingTransfer.unlockCodeKo
            }));

            if(response.isSuccess()) {
                const data = response.success();
                pendingTransfer.transferUid = data.item.uid;
                await this.localOrderTransferRepository.save(pendingTransfer);

                this.close();
            } else if(response.isError()) {
                this.error = translateResponseError(response, {});
                pendingTransfer.canceled = true;
                await this.localOrderTransferRepository.save(pendingTransfer);
            } else {
                this.close();
            }
        } catch(err) {}

        this.creatingTransfer = null;
    }

    toggleFavorite(iotUid: ScopedUuid<UuidScopeIot_deviceApp>) {
        const index = this.favorites.findIndex((favorite) => favorite === iotUid);
        if(index === -1) {
            this.favorites.push(iotUid);
        } else {
            this.favorites.splice(index, 1);
        }
        localStorage.setItem('order-transfer-favorites', JSON.stringify(this.favorites));
    }

    close() {
        this.$emit('close');
    }
}