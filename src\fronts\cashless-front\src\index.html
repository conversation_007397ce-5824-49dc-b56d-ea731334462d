<!DOCTYPE html>
<html lang="fr">
<head>
	<title>Cashless</title>
	<meta charset="UTF-8"/>
	<link rel="icon" href="/img/favicon.ico" />
	<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no"/>
	<link rel="preconnect" href="https://fonts.googleapis.com">
	<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
	<link href="https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap" rel="stylesheet">


	<style>
        #oldRouterContainer {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            overflow-y: hidden;
        }

        #mainRouterContainer {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            overflow-y: hidden;
        }

        .animationSlideFromRightToLeft {
            animation: slideFromRightToLeft 0.5s cubic-bezier(0.22, 1, 0.36, 1) forwards;
        }
        .animationSlideFromRightToLeftBackwards {
            animation: slideFromRightToLeftBackwards 0.5s cubic-bezier(0.22, 1, 0.36, 1) forwards;
        }
        .animationSlideFromRightToLeft25Percent {
            animation: slideFromRightToLeft25Percent 0.5s cubic-bezier(0.22, 1, 0.36, 1) forwards;
        }
        .animationSlideFromRightToLeft25PercentBackwards {
            animation: slideFromRightToLeft25PercentBackwards 0.5s cubic-bezier(0.22, 1, 0.36, 1) forwards;
        }

        .pageOnTop {
            z-index: 1;
        }
        .pageOnLower {
            z-index: 0;
        }

        @keyframes slideFromRightToLeft {
            from {
                transform: translateX(100vw);
            }
            to {
                transform: translateX(0);
            }
        }
        @keyframes slideFromRightToLeftBackwards {
            from {
                transform: translateX(0);
            }
            to {
                transform: translateX(100vw);
            }
        }
        @keyframes slideFromRightToLeft25Percent {
            from {
                transform: translateX(0);
                filter: brightness(1);
            }
            to {
                transform: translateX(-25vw);
                filter: brightness(0.75);
            }
        }
        @keyframes slideFromRightToLeft25PercentBackwards {
            from {
                transform: translateX(-25vw);
                filter: brightness(0.75);
            }
            to {
                transform: translateX(0);
                filter: brightness(1);
            }
        }
	</style>
</head>
<body>
<div id="top-bar" v-cloak></div>
<div id="sidebar" v-cloak></div>
<div id="oldRouterContainer"></div>
<div id="mainRouterContainer"></div>

<script type="module" src="./index.ts"></script>

<script id="config" type="application/json">
	"__INJECTED_CONFIG__"
</script>

<script type="text/javascript"></script>

</body>
</html>
