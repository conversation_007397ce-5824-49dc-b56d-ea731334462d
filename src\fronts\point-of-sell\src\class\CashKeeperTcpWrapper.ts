import {PaymentMethodSettingsCashKeeper} from "@groupk/mastodon-core";
import {CashkeeperConnection} from "@groupk/cashkeeper-protocol";
import {SocketNative, TcpSocketWrapper} from "@groupk/native-bridge";
import {BufferUtils, GetInstance} from "@groupk/horizon2-core";
import {<PERSON><PERSON>r<PERSON>and<PERSON>} from "./ErrorHandler";

export type CashKeeperTcpConnection = {tcpWrapper: TcpSocketWrapper, cashKeeperConnection: CashkeeperConnection}

export class CashKeeperTcpWrapper {
	static async getCashKeeperConnection(settings: PaymentMethodSettingsCashKeeper, onError: Function|null = null): Promise<CashKeeperTcpConnection> {
		const tcpWrapper = await GetInstance(SocketNative).tcpConnectWrapper(settings.address, settings.masterPort);

		try {
			const cashKeeperConnection = new CashkeeperConnection((data) => {
				if(!tcpWrapper) throw new Error('connection_have_been_closed');

				let payload: number[] = [];
				for (let i = 0; i < data.length; i++) {
					payload.push(data.charCodeAt(i));
				}
				tcpWrapper.send(payload);
			}, {officeMode: false, securitySeed: settings.securitySeed});

			tcpWrapper.on('data', async (bytes) => {
				if(!cashKeeperConnection) return;

				let data: string = String.fromCharCode(...(typeof bytes === 'string' ? BufferUtils.hex2Buffer(bytes) : bytes));
				try {
					await cashKeeperConnection.pushData(data);
				} catch(err) {
					if(onError !== null) onError();
					await this.closeConnection({tcpWrapper: tcpWrapper, cashKeeperConnection: cashKeeperConnection});
				}
			});

			tcpWrapper.on('closed', ()=>{
				console.warn('CONNECTION CLOSED');
			});

			await new Promise((resolve, reject) => {
				tcpWrapper.on('connected', resolve);
				setTimeout(reject, 10000);
			});

			return {tcpWrapper: tcpWrapper, cashKeeperConnection: cashKeeperConnection};
		} catch(err) {
			GetInstance(ErrorHandler).logToToastManager({
				title: 'Connection impossible',
				description: 'L\'adresse renseigné semble ne pas correspondre à un CashKeeper, vérifiez la configuration du CashKeeper'
			});
			await tcpWrapper.close();

			throw err;
		}
	}

	static async closeConnection(data: CashKeeperTcpConnection) {
		await data.cashKeeperConnection.disconnect();
		await data.tcpWrapper.close();
	}
}