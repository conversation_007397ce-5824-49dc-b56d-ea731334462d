import {Component, Prop, Vue} from "vue-facing-decorator";
import {DropdownComponent, FormModalOrDrawerComponent} from "@groupk/vue3-interface-sdk";
import {AutoWired} from "@groupk/horizon2-core";
import {PaymentMethodsRepository} from "../../../../../shared/repositories/PaymentMethodsRepository";
import {CashlessAppId, CashlessPermissions} from "@groupk/mastodon-core";
import {ProductAppId, ProductPermissions} from "@groupk/mastodon-core";
import {CustomerAppId, CustomerPermissions} from "@groupk/mastodon-core";
import {ApplicationFrontBackPermission} from "@groupk/mastodon-core";
import {ApplicationPermission} from "@groupk/mastodon-core";
import {PaymentAppId, PaymentPermissions} from "@groupk/mastodon-core";
import {MastodonBaseAppId, MastodonPermissions} from "@groupk/mastodon-core";
import {AppState} from "../../../../../shared/AppState";

@Component({
	components: {
		'form-modal-or-drawer': FormModalOrDrawerComponent,
		'dropdown': DropdownComponent,
	},
	emits: ['edit-permissions']
})
export default class PermissionGroupFormComponent extends Vue {
	@Prop({required: true}) currentPermissions!: ApplicationFrontBackPermission[];
	@Prop({default: null}) currentFrontPermissions!: string[]|null;
	@Prop({required: true}) availableFrontPermissions!: string[];

	toggledPermissions: ApplicationFrontBackPermission[] = [];
	toggledFrontPermissions: string[] = [];

	opened: boolean = false;
	loading: boolean = false;
	error: string | null = null;

	@AutoWired(PaymentMethodsRepository) accessor paymentMethodsRepository!: PaymentMethodsRepository;
	@AutoWired(AppState) accessor appState!: AppState;


	mounted() {
		setTimeout(() => this.opened = true, 0);
		this.toggledPermissions = [...this.currentPermissions];
		if(this.currentFrontPermissions) this.toggledFrontPermissions = [...this.currentFrontPermissions];
	}

	close() {
		this.opened = false;
		setTimeout(() => this.$emit('close'), 300);
	}


	isToggled(permission: ApplicationPermission) {
		return this.toggledPermissions.find((toggled) => toggled.applicationId === permission.applicationId && toggled.id === permission.id);
	}

	toggle(permission: ApplicationPermission) {
		const index = this.toggledPermissions.findIndex((toggled) => toggled.applicationId === permission.applicationId && toggled.id === permission.id);
		if(index === -1) this.toggledPermissions.push(permission);
		else this.toggledPermissions.splice(index, 1);
		console.log(this.toggledPermissions);
	}

	isToggledFront(permission: string) {
		return this.toggledFrontPermissions.includes(permission);
	}

	toggleFront(permission: string) {
		const index = this.toggledFrontPermissions.indexOf(permission);
		if(index === -1) this.toggledFrontPermissions.push(permission);
		else this.toggledFrontPermissions.splice(index, 1);
	}

	get allPermissions(): ApplicationPermission[] {
		let permissions: ApplicationPermission[] = [];

		permissions = permissions.concat(MastodonPermissions.permissions.map((permission) => {
			return {
				applicationId: MastodonBaseAppId,
				id: permission
			}
		}));

		permissions = permissions.concat(CashlessPermissions.permissions.map((permission) => {
			return {
				applicationId: CashlessAppId,
				id: permission
			}
		}));

		permissions = permissions.concat(ProductPermissions.permissions.map((permission) => {
			return {
				applicationId: ProductAppId,
				id: permission
			}
		}));

		permissions = permissions.concat(CustomerPermissions.permissions.map((permission) => {
			return {
				applicationId: CustomerAppId,
				id: permission
			}
		}));

		permissions = permissions.concat(PaymentPermissions.permissions.map((permission) => {
			return {
				applicationId: PaymentAppId,
				id: permission
			}
		}));

		return permissions;
	}

	save() {
		this.$emit('edit-permissions', {
			permissions: this.toggledPermissions,
			frontPermissions: this.toggledFrontPermissions
		});
	}
}