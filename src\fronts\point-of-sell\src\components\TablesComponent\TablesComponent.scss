.tables-component {
    display: flex;
    height: 100%;
    width: 100%;
    user-select: none;

    > .left {
        height: 100%;
        width: 100%;
        box-sizing: border-box;
        overflow: auto;

        @media (max-width: 900px) {
            padding-bottom: 160px;
        }

        .content {
            position: relative;
            display: flex;
            flex-direction: column;
            gap: 20px;
            flex-grow: 2;
            padding: 20px;
            box-sizing: border-box;
            min-height: 100%;
        }


        .head {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            justify-content: space-between;
            gap: 20px;

            .top {
                display: flex;
                align-items: center;
                gap: 10px;
                width: 100%;

                .right-container {
                    flex-grow: 2;
                    display: flex;
                    justify-content: flex-end;
                    gap: 10px;
                }
            }

            @media (max-width: 900px) {
               flex-direction: column;
            }

            .white.button {
                border: none;
            }

            h2 {
                font-size: 20px;
                font-weight: 600;
            }

            .red.label {
                margin-top: 10px;
                font-size: 16px;
                border-radius: 6px;
                padding: 8px 10px;
                background: rgba(255, 124, 124, 0.2);
                color: #fd1d1d;

                i {
                    font-size: 16px;
                }
            }

            .buttons {
                margin: 0;
            }

            .button-group {
                display: flex;
                flex-wrap: wrap;
                gap: 10px;

                button {
                    all: unset;
                    display: flex;
                    align-items: center;
                    gap: 10px;
                    background: white;
                    padding: 12px;
                    border-radius: 8px;

                    &.selected {
                        background: var(--primary-color);
                        color: var(--primary-text-color);
                    }

                }
            }
        }

        &::-webkit-scrollbar {
            display: none;
        }
    }

    .form-error {
        .red.button {
            flex-shrink: 0;
        }
    }

    .right {
        flex-shrink: 0;
        width: var(--sidebar-width);
        background: white;
    }


    .button-group {
        display: flex;

        button {
            all: unset;
            display: flex;
            align-items: center;
            gap: 10px;
            background: white;
            padding: 12px;

            &.selected {
                background: var(--primary-color);
                color: var(--primary-text-color);
            }

            &:first-child {
                border-radius: 8px 0 0 8px;
            }

            &:last-child {
                border-radius: 0 8px 8px 0;
            }
        }
    }

    .tables-dimmer {
        position: absolute;
        inset: 0;
        background: rgba(0, 0, 0, 0.7);
        z-index: 3;
    }

    .tables-area {
        display: flex;
        flex-direction: column;
        gap: 20px;
        background: white;
        border-radius: 12px;
        padding: 20px;

        .tables-group {
            display: flex;
            flex-direction: column;
            gap: 10px;

            .tables {
                display: grid;
                grid-template-columns: 1fr 1fr 1fr 1fr 1fr;
                grid-gap: 10px;

                @media (max-width: 1279px) {
                    grid-template-columns: 1fr 1fr 1fr 1fr;
                }

                @media (max-width: 1150px) {
                    grid-template-columns: 1fr 1fr 1fr;
                }

                @media (max-width: 1000px) {
                    grid-template-columns: 1fr 1fr 1fr;
                }

                .table {
                    position: relative;
                    display: flex;
                    flex-direction: column;
                    gap: 15px;
                    align-items: center;
                    justify-content: center;
                    background: #F2F4F7;
                    padding: 10px;
                    border-radius: 8px;

                    .name {
                        font-weight: 500;
                        padding: 6px 12px;
                        background: white;
                        border-radius: 8px;
                        word-break: break-all;
                    }

                    .orders-count {
                        position: absolute;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        top: -6px;
                        right: -6px;
                        height: 25px;
                        width: 25px;
                        background: white;
                        border-radius: 50%;
                        font-size: 14px;
                        color: black;
                        font-weight: 600;
                    }

                    &.blue {
                        background: #e5efff;

                        .name {
                            background: #06F;
                            color: white;
                        }

                        .orders-count {
                            background: #06F;
                            color: white;

                            &.orange {
                                background: #f37901;
                            }
                        }
                    }

                    .guests {
                        display: flex;
                        align-items: center;
                        gap: 5px;

                        i {
                            font-size: 18px;
                        }

                        .infos {
                            display: flex;
                            flex-direction: column;
                            align-items: center;

                            .number {
                            }
                        }
                    }

                    .orders {
                        position: absolute;
                        top: 100%;
                        z-index: 30;
                        padding: 10px 0;
                        display: grid;
                        grid-template-columns: 1fr 1fr 1fr;
                        grid-gap: 10px;
                        pointer-events: none;

                        &.align-right {
                            right: 0;
                        }

                        &.align-left {
                            left: 0;
                        }

                        &.align-bottom {
                            top: auto;
                            bottom: 100%;
                        }

                        .order {
                            display: flex;
                            flex-direction: column;
                            gap: 10px;

                            .head {
                                display: flex;
                                align-items: center;
                                justify-content: center;
                                gap: 20px;

                                .time {
                                    font-weight: 600;
                                }
                            }

                            padding: 15px;
                            background: white;
                            white-space: nowrap;
                            border-radius: 6px;
                            opacity: 0;
                            transform: translateY(-50%);
                            transition: transform .3s cubic-bezier(0.22, 1, 0.36, 1), opacity .3s cubic-bezier(0.22, 1, 0.36, 1);
                        }
                    }


                    &.opened {
                        z-index: 4;

                        .orders {
                            pointer-events: all;

                            .order {
                                justify-content: flex-end;
                                opacity: 1;
                                transform: none;


                                @for $i from 1 through 10 {
                                    &:nth-child(#{$i}n) {
                                        transition-delay: #{($i - 1) * .04}s;
                                    }
                                }
                            }
                        }
                    }

                }
            }
        }
    }

    .legend-container {
        display: flex;
        justify-content: flex-end;
        gap: 20px;
        background: white;

        .sessions {
            flex-grow: 2;

            .grey.label {
                font-size: 15px;
                font-weight: 500;
            }

            i {
                font-size: 15px;
                margin-right: 5px;
            }
        }

        .legend {
            display: flex;
            align-items: center;
            gap: 10px;

            .color {
                height: 8px;
                width: 8px;
                border-radius: 50%;
                background: #F2F4F7;

                &.blue {
                    background: #06F;
                }
            }
        }
    }

    @media (max-width: 900px) {
        .tables {
            grid-template-columns: 1fr 1fr !important;

            .table {
                .orders {
                    grid-template-columns: 1fr 1fr !important;
                }
            }
        }

        .right {
            position: absolute;
            inset: 0;
            width: auto;
            z-index: 550;
            display: none;

            &.opened {
                display: initial;
            }
        }

        .group-container {
            position: fixed;
            inset: 20px !important;
            transform: none !important;
        }
    }

    .right-modal-dimmer {
        position: fixed;
        inset: 0;
        background: rgba(0, 0, 0, 0.4);
    }

    .table-long-press-modal {
        display: flex;
        flex-direction: column;
        gap: 20px;
        padding: 20px;
        height: 100%;
        overflow: auto;
        padding-bottom: 80px;

        .head {
            display: flex;
            flex-direction: column;
            gap: 4px;

            .title {
                font-size: 20px;
                font-weight: 600;
            }

            .subtitle {
                font-size: 15px;
            }
        }

        .orders {
            display: flex;
            flex-direction: column;
            gap: 10px;

            .order {
                background: var(--secondary-hover-color);
                padding: 15px;
                border-radius: 12px;
            }
        }
    }
}