.export-choice-component {
    .export-choice {
        display: flex;
        align-items: center;
        gap: 15px;
        user-select: none;

        .export-data {
            display: flex;
            flex-direction: column;
            gap: 2px;

            .title {
                font-weight: 600;
                font-size: 14px;
            }
        }
    }

    .text-align-right {
        text-align: right;
    }

    .radio {
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;
        height: 18px;
        width: 18px;
        border-radius: 50%;
        border: 1px solid black;
        box-sizing: border-box;

        &.selected {
            border: none;
            background: #1099FD;

            .center {
                height: 6px;
                width: 6px;
                background: white;
                border-radius: 50%;
            }
        }
    }

    .toggle-input {
        display: flex;
        align-items: center;
        gap: 15px;

        .infos {
            display: flex;
            flex-direction: column;
            gap: 4px;
            flex-grow: 2;

            .title {
                font-size: 15px;
                font-weight: 500;
            }

            .subtitle {
                font-size: 14px;
            }
        }
    }

}