import {AutoWired} from "@groupk/horizon2-core";
import {LocalOrderRepository} from "../repositories/LocalOrderRepository";
import {OrderRepository} from "../../../../shared/repositories/OrderRepository";
import {PosState} from "../model/PosState";
import {AppBus} from "../config/AppBus";
import {ErrorHandler} from "./ErrorHandler";
import {LocalWorkClockRepository} from "../repositories/LocalWorkClockRepository";
import {WorkClockRepository} from "../../../../shared/repositories/WorkClockRepository";

export class PosWorker {
	private posState: PosState;
	private nextSyncTimeout: ReturnType<typeof window.setTimeout> | null = null;
	private isSyncing: boolean = false;

	@AutoWired(LocalOrderRepository) accessor localOrderRepository!: LocalOrderRepository
	@AutoWired(LocalWorkClockRepository) accessor localWorkClockRepository!: LocalWorkClockRepository
	@AutoWired(WorkClockRepository) accessor workClockRepository!: WorkClockRepository
	@AutoWired(OrderRepository) accessor orderRepository!: OrderRepository
	@AutoWired(AppBus) accessor appBus!: AppBus
	@AutoWired(ErrorHandler) accessor errorHandler!: ErrorHandler

	constructor(posState: PosState) {
		this.posState = posState;

		this.appBus.on('orderSaved', async (order) => {
			if (order.synced) return; // prevent loop when saving sync state
			this.scheduleNextSync(0);
		});

		this.appBus.on('workClockSaved', async (workClock) => {
			if (workClock.synced) return; // prevent loop when saving sync state
			this.scheduleNextSync(0);
		});
	}

	scheduleNextSync(delay: number = 3 * 1000) {
		if (this.isSyncing && this.nextSyncTimeout !== null) {
			clearTimeout(this.nextSyncTimeout);
			this.nextSyncTimeout = null;
		}

		if (this.nextSyncTimeout === null) {
			this.nextSyncTimeout = setTimeout(async () => {
				this.nextSyncTimeout = null;
				try {
					await this.sync();
				} catch (e) {
					console.error(e);
					this.isSyncing = false;
				}
				this.scheduleNextSync();
			}, delay);
		}
	}

	private async sync(forceAll: boolean = false) : Promise<boolean> {
		if (this.isSyncing) return false;
		this.isSyncing = true;

		let allSync = true;
		if(!await this.syncOrders(forceAll)) allSync = false;
		if(!await this.syncWorkClocks()) allSync = false;

		this.isSyncing = false;

		return false;
	}

	private async syncOrders(forceAll : boolean){
		const ordersCursor = forceAll ? await this.localOrderRepository.findAllCursor() : await this.localOrderRepository.findAllNotSyncedCursor();
		let allSync = true;

		for await(const localOrder of ordersCursor.generator()) {
			if(localOrder.error !== null) continue;
			const syncResult = await this.orderRepository.callContract('create_update_offline', {establishmentUid: this.posState.establishmentUid}, localOrder.order);
			if (syncResult.isSuccess()) {
				const syncedOrder = syncResult.success();
				const localOrder2 = await this.localOrderRepository.findOne(syncedOrder.uid);
				if (localOrder2 && new Date(localOrder2.lastLocalUpdateDate).getTime() === new Date(localOrder.lastLocalUpdateDate).getTime()) {
					localOrder.synced = true;
					await this.localOrderRepository.save(localOrder);
				}
			} else {
				allSync = false;
				if(syncResult.isError()) {
					const error = syncResult.error();
					let errorText = error ? JSON.stringify(error) : 'Synchronisation impossible';
					if(syncResult.rawResponse?.status === 403) errorText += ' (raison 403)';

					localOrder.error = errorText;
					if(syncResult.rawResponse?.status !== 403) {
						this.errorHandler.logToSentry({
							title: 'Order is refused by server',
							description: errorText,
							order: localOrder
						});
					}
					await this.localOrderRepository.save(localOrder);
				} else if(syncResult.isServerError()) {
					localOrder.error = JSON.stringify(syncResult.serverError() ?? 'Erreur inconnue');
					this.errorHandler.logToSentry({
						title: 'Order is causing server crash',
						description: JSON.stringify(syncResult.serverError()),
						order: localOrder
					});
					await this.localOrderRepository.save(localOrder);
				} else if(syncResult.isNetworkError()) {
					break;
				} else {
					try {
						this.errorHandler.logToSentry({
							title: 'Unknown saving order error',
							description: 'Sync result is neither isError, isServerError or isNetworkError',
							order: localOrder,
							error: new Error(JSON.stringify(syncResult))
						});
					} catch(err) {}
				}
			}
		}
		return allSync;
	}

	private async syncWorkClocks(){
		const workClocksCursor = await this.localWorkClockRepository.findAllNotSyncedCursor();
		let allSync = true;

		for await(const localWorkClock of workClocksCursor.generator()) {
			const syncResult = await this.workClockRepository.callContract('createOrUpdate', {establishmentUid: this.posState.establishmentUid}, localWorkClock);
			if (syncResult.isSuccess()) {
				const syncedWorkClock = syncResult.success();
				const localWorkClock2 = await this.localWorkClockRepository.findOne(syncedWorkClock.uid);
				if (localWorkClock2 && new Date(localWorkClock2.lastLocalUpdateDate).getTime() === new Date(localWorkClock.lastLocalUpdateDate).getTime()) {
					localWorkClock.synced = true;
					await this.localWorkClockRepository.save(localWorkClock);
				}
			} else {
				allSync = false;
				if(syncResult.isError()) {

				} if(syncResult.isServerError()) {

				} if(syncResult.isNetworkError()) {
					break;
				}
			}
		}
		return allSync;
	}

}