.reservit-integration-component {
    .button-group {
        border-radius: 8px;
        display: flex;
        border: 1px solid #E2E2E2;

        &.small {
            div {
                font-size: 12px;
                font-weight: 500;
                padding: 8px 15px;
            }
        }

        div {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            padding: 10px 15px;
            border-right: 1px solid #E2E2E2;
            cursor: pointer;
            font-size: 14px;
            user-select: none;
            width: 100%;

            i {
                margin-top: 2px;
            }

            &:first-child {
                border-radius: 8px 0 0 8px;
            }

            &:last-child {
                border: none;
                border-radius: 0 8px 8px 0;
            }

            &:hover {
                background: var(--secondary-hover-color);
            }

            &.active {
                background: var(--secondary-hover-color);
                font-weight: 500;
            }
        }
    }
}