<script lang="ts" src="./OrderSidebarDiscountComponent.ts">
</script>

<style lang="sass" scoped>
@import './OrderSidebarDiscountComponent.scss'
</style>

<template>
    <div class="order-sidebar-discount-component">
        <div class="quick-actions">
            <div class="action" @click="close()">
                <i class="fa-regular fa-arrow-left"></i>
                Retour
            </div>
        </div>

        <div class="hint">
            Sélectionnez les produits que vous souhaitez inclure dans la réduction
        </div>

        <div class="purchases">
            <template v-for="purchase in localOrder.order.purchases" >
                <div v-if="getPurchaseQuantity(purchase) > 0" class="purchase" :class="{selected: isPurchaseSelected(purchase), disabled: havePurchaseAlreadyBeenRefund(purchase)}" @click="togglePurchase(purchase)">
                    <div class="top">
                        <div class="quantity"> {{ getPurchaseQuantity(purchase) }} </div>
                        <div class="data">
                            <span class="name"> {{ $filters.Length(posState.orderExecutorModel.requireProductRevision(purchase.productRevisionUid).name, 35) }} </span>
                            <span class="total">
                                {{ $filters.Money(getPurchaseTotals(purchase).withTaxes) }}
                            </span>
                        </div>
                        <div class="select" v-if="!havePurchaseAlreadyBeenRefund(purchase)">
                            <i v-if="!isPurchaseSelected(purchase)" class="fa-regular fa-square"></i>
                            <i v-else class="fa-regular fa-square-check"></i>
                        </div>

                        <div class="select" v-else>
                            <i class="fa-regular fa-xmark"></i>
                        </div>
                    </div>
                </div>
            </template>
        </div>

        <div class="bottom">
            <div class="add-discount" :class="{disabled: selectedPurchasesTotalPrice === 0}" @click="showKeypad = true">
                Définir une réduction
            </div>
        </div>

        <div class="keypad-dimmer" :class="{displayed: showKeypad}" @click="showKeypad = false"></div>
        <div class="hiding-keypad" :class="{opened: showKeypad}">
            <keypad
                @clicked-key="updatePaymentAmount($event)"
            >
                <template v-slot:display>
                    <div class="display" @click="changeMode()">
                        <span class="left-to-pay">
                             Réduction de
                        </span>

                        <template v-if="mode === 'AMOUNT'">
                            <span class="amount" :class="{red: currentDiscountAmount > selectedPurchasesTotalPrice}">
                                {{ $filters.Money(currentDiscountAmount) }}
                            </span>
                        </template>
                        <template v-else>
                            <span class="amount" :class="{red: currentDiscountAmount > 100}">
                                {{ currentDiscountAmount }}%
                            </span>
                        </template>

                        <span class="left-to-pay">
                            <span class="striped">{{ $filters.Money(selectedPurchasesTotalPrice) }}</span>&nbsp;
                            <span v-if="mode === 'AMOUNT'"> {{ $filters.Money(selectedPurchasesTotalPrice - currentDiscountAmount) }}</span>
                            <span v-if="mode === 'PERCENT'"> {{ $filters.Money(selectedPurchasesTotalPrice - (selectedPurchasesTotalPrice * currentDiscountAmount / 100)) }}</span>
                        </span>

                        <span class="change-mode">
                            <i class="fa-regular fa-arrows-repeat"></i>
                            <template v-if="mode === 'PERCENT'"> Vers le mode montant </template>
                            <template v-else> Vers le mode pourcentage </template>
                        </span>
                    </div>
                </template>
            </keypad>
            <div class="actions">
                <div class="cancel" @click="showKeypad = false">
                    Annuler
                </div>
                <div class="pay" :class="{disabled: currentDiscountAmount > selectedPurchasesTotalPrice || currentDiscountAmount === 0}" @click="addDiscount()">
                    Ajouter
                </div>
            </div>
        </div>
    </div>
</template>