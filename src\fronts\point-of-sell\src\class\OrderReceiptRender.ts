import {AutoWired, UuidUtils, VisualScopedUuid} from "@groupk/horizon2-core";
import {
	CashStatementSessionApiOut, Constants,
	OrderApiOut,
	OrderExecutorModel, OrderPaymentApiOut,
	OrderReceiptApiOut, OrderStatsModel,
	ProductModel, PurchaseApiOut, UuidScopeProduct_order,
	UuidScopeProductOrder
} from "@groupk/mastodon-core";
import {
	SimplifiedPrintingPart,
	SimplifiedPrintingPart_Table,
	SimplifiedPrintingPart_Text
} from "../printApiSdk/SimplifiedPrintingPartData";
import {MoneyFilter} from "../../../../shared/filters/Money";
import {LengthFilter} from "../../../../shared/filters/StringFilter";
import {PaymentMethodApiOut, UuidScopePayment_method} from "@groupk/mastodon-core";
import {DateFilter, HourFilter} from "../../../../shared/filters/DateFilter";
import {PosState} from "../model/PosState";
import {Toast} from "../components/ToastManagerComponent/ToastManagerComponent";
import {AppBus} from "../config/AppBus";
import {OrderPaymentStatus} from "@groupk/mastodon-core";
import {UuidScopeProductOrderPayment} from "@groupk/mastodon-core";

const OrderNotPaidReceiptErrorToast: Toast = {
	title: 'Impossible d\'imprimer le reçu',
	description: 'La commande n\'est pas entièrement payée',
	color: 'red',
	closable: true,
	duration: 5000,
};

export interface OrderReceiptRenderConfig{
	companyName: string,
	companyAddress: string,
	identifiers: {siren: string, vatNumber?: string},
	posVersion: string,
	freeLinesTop?: string[],
	freeLinesBottom?: string[],
}

export class OrderReceiptRender {
	@AutoWired(OrderExecutorModel) accessor orderExecutorModel !: OrderExecutorModel;
	@AutoWired(ProductModel) accessor productModel !: ProductModel;
	@AutoWired(PosState) accessor posState !: PosState;
	@AutoWired(AppBus) accessor appBus !: AppBus;

	getReceiptConfig() {
		const receiptConfig: OrderReceiptRenderConfig = {
			companyAddress: '',
			companyName: 'Caisse by Weecop',
			posVersion: '1.0.32',
			identifiers: {siren: '', vatNumber: ''}
		};

		if(this.posState.productEstablishment && this.posState.productEstablishment.receipt) {
			receiptConfig.companyAddress = this.posState.productEstablishment.receipt.companyAddress;
			receiptConfig.companyName = this.posState.productEstablishment.receipt.companyName;
			receiptConfig.identifiers.vatNumber = this.posState.productEstablishment.receipt.vatNumber ?? undefined;
			receiptConfig.identifiers.siren = this.posState.productEstablishment.receipt.siren ?? '';
			receiptConfig.freeLinesTop = this.posState.productEstablishment.receipt.freeAreaTop;
			receiptConfig.freeLinesBottom = this.posState.productEstablishment.receipt.freeAreaBottom;
		}
		return receiptConfig;
	}

	renderOrderReceipt(order: OrderApiOut) {
		const totals = this.posState.orderExecutorModel.getOrderTotals(order);
		if(totals.leftToPay !== 0) {
			this.appBus.emit('displayToast', OrderNotPaidReceiptErrorToast);
			return null;
		}
		const receipt = this.posState.orderExecutorModel.logPhysicalReceiptPrinting(order);
		return this.render(order, receipt, this.getReceiptConfig());
	}

	render(order : OrderApiOut,
		   receipt : OrderReceiptApiOut|null,
		   config : OrderReceiptRenderConfig){
		const orderTotals = this.orderExecutorModel.getOrderTotals(order);

		const renderingParts : SimplifiedPrintingPart[] = [];

		// ====================================
		renderingParts.push({type: 'TEXT', text:config.companyName, textAlign:'center'});
		renderingParts.push({type: 'TEXT', text:config.companyAddress, textAlign:'center'});
		renderingParts.push({type: 'SEPARATOR'});

		if(receipt) {
			// ====================================
			renderingParts.push({
				type: 'TEXT',
				text: 'Imprimé le ' + DateFilter(receipt.creationDatetime) + ' ' + HourFilter(receipt.creationDatetime),
				textAlign: 'center'
			});
			if (order.receipts.length > 1) {
				let countReprintToThatReceipt = 0;
				for (const orderReceipt of order.receipts) {
					if (orderReceipt === receipt) break;
					++countReprintToThatReceipt;
				}

				renderingParts.push({
					type: 'TEXT',
					text: 'Ticket réimprimé ' + (countReprintToThatReceipt - 1) + ' fois',
					textAlign: 'center'
				});
			}
		} else {
			renderingParts.push({
				type: 'TEXT',
				text: 'justificatif non valable pour encaissement',
				textAlign: 'center'
			});
		}

		renderingParts.push({type: 'TEXT', text:'Caisse Weecop '+config.posVersion, textAlign:'center'});
		renderingParts.push({type: 'SEPARATOR'});

		for(const line of config.freeLinesTop ?? [])
			renderingParts.push({type: 'TEXT', text:line, textAlign: 'center'});

		if((config.freeLinesTop ?? []).length > 0) renderingParts.push({type: 'TEXT', text:'', textAlign: 'center'})

		// ====================================
		let purchasesTabPart : SimplifiedPrintingPart_Table;
		if(order.roundPricesAtEnd){
			purchasesTabPart = {
				header:['Qté', 'Designation', 'PU HT'],
				rows: [],
				type: 'TABLE',
				widthPerCell: [11, 73, 16],
				marginBetweenCells: 4,
				textAlignPerCellHeader: ['left', 'center', 'right'],
				textAlignPerCellRows: ['left', 'left', 'right'],
			};
		}else{
			purchasesTabPart = {
				header:['Designation', 'Qté x P.U.', 'Total'],
				rows: [],
				type: 'TABLE',
				widthPerCell: [55, 25, 20],
				marginBetweenCells: 3,
				textAlignPerCellHeader: ['left', 'right', 'right'],
				textAlignPerCellRows: ['left', 'right', 'right'],
			};
		}

		for(const purchase of order.purchases){
			const purchaseQuantity = this.orderExecutorModel.getPurchaseQuantity(purchase);
			if(purchaseQuantity.notCanceled <= 0) continue;

			const productRevision = this.orderExecutorModel.requireProductRevision(purchase.productRevisionUid);
			const purchasePrices = this.orderExecutorModel.getPurchasePrice(order, purchase);

			if(order.roundPricesAtEnd) {
				purchasesTabPart.rows.push([
					purchaseQuantity.notCanceled+'x',
					LengthFilter(productRevision.name, 25),
					''+MoneyFilter(purchasePrices.withoutTaxes),
				]);
			} else {
				if(purchasePrices.prices.length > 1) {
					const unitPriceWithTax = purchasePrices.prices.reduce((total, price) => total + price.priceWithTaxes, 0);
					purchasesTabPart.rows.push([
						LengthFilter(productRevision.name, 15),
						purchaseQuantity.notCanceled+'x '+MoneyFilter(unitPriceWithTax),
						''+MoneyFilter(unitPriceWithTax * purchaseQuantity.notCanceled),
					]);
				} else {
					const price = purchasePrices.prices[0];
					purchasesTabPart.rows.push([
						LengthFilter(productRevision.name, 15),
						purchaseQuantity.notCanceled+'x '+MoneyFilter(price.priceWithTaxes / purchaseQuantity.notCanceled),
						''+MoneyFilter(price.priceWithTaxes),
					]);
				}
			}

			for(const item of purchase.items) {
				for (let group of item.groups) {
					for (let subPurchase of group.purchases) {
						purchasesTabPart.rows = purchasesTabPart.rows.concat(this.getPurchaseRenderingParts(subPurchase));
					}
				}
			}
		}

		renderingParts.push(purchasesTabPart);

		// ====================================
		if(orderTotals.purchases.discountDetails.length > 0) {
			renderingParts.push({type: 'TEXT', text: 'Total', textAlign: 'left', fontWeight: 600});
		}

		let discountTabPart : SimplifiedPrintingPart_Table = {
			type:'TABLE',
			rows: [],
			widthPerCell: [75, 25],
			textAlignPerCellRows: ['left', 'right'],
			marginBetweenCells: 3,
		};

		if(orderTotals.purchases.discountDetails.length > 0) {
			discountTabPart.rows.push(['Total TTC avant remises', MoneyFilter(orderTotals.purchases.withTaxesBeforeDiscount)]);
		}

		for(const discount of orderTotals.purchases.discountDetails){
			if(!('amountWithTaxes' in discount)) continue;
			discountTabPart.rows.push([discount.name, MoneyFilter(discount.amountWithTaxes)]);
		}
		renderingParts.push(discountTabPart);

		discountTabPart.rows.push(['Total HT', MoneyFilter(orderTotals.purchases.withoutTaxesAfterDiscount)]);
		discountTabPart.rows.push(['Total TTC à payer', MoneyFilter(orderTotals.purchases.withTaxesAfterDiscount)]);

		// ====================================
		let totalsTabPart : SimplifiedPrintingPart_Table = {
			type:'TABLE',
			rows: [],
			widthPerCell: [75, 25],
			textAlignPerCellRows: ['left', 'right'],
			marginBetweenCells: 3,
		};

		totalsTabPart.rows.push(['Total HT', MoneyFilter(orderTotals.purchases.withoutTaxesAfterDiscount)]);

		for(const taxDetail of orderTotals.purchases.taxDetails){
			totalsTabPart.rows.push([taxDetail.descriptor.name+' ('+(taxDetail.descriptor.percent/1000)+'%)', MoneyFilter(taxDetail.amount)]);
		}

		totalsTabPart.rows.push(['Total TTC', MoneyFilter(orderTotals.purchases.withTaxesAfterDiscount)]);


		// ====================================
		if(order.payments.length > 0) {
			renderingParts.push({type: 'TEXT', text: 'Paiements', textAlign: 'left', fontWeight: 600});
		}

		let paymentTabPart : SimplifiedPrintingPart_Table = {
			type:'TABLE',
			rows: [],
			widthPerCell: [75, 25],
			textAlignPerCellRows: ['left', 'right'],
			marginBetweenCells: 3,
		};
		for(const payment of order.payments){
			if(payment.status === OrderPaymentStatus.SUCCESS) {
				const paymentMethod : PaymentMethodApiOut = this.orderExecutorModel.requirePaymentMethod(payment.method);
				paymentTabPart.rows.push([paymentMethod.name, MoneyFilter(payment.amount)]);
			}
		}
		renderingParts.push(paymentTabPart);

		if(orderTotals.purchases.taxDetails.length > 0) {
			let taxesTabPart : SimplifiedPrintingPart_Table = {
				type:'TABLE',
				rows: [],
				header: ['Taxe', 'Total produits TTC', 'Dont taxe'],
				widthPerCell: [25, 50, 25],
				textAlignPerCellHeader: ['left', 'right', 'right'],
				textAlignPerCellRows: ['left', 'right', 'right'],
				marginBetweenCells: 3,
			};

			for(const tax of orderTotals.purchases.taxDetails){
				taxesTabPart.rows.push([
					(tax.descriptor.percent / Constants.PERCENT_MULTIPLIER) * 100 + '%',
					MoneyFilter(tax.baseAmount + tax.amount),
					MoneyFilter(tax.amount)
				]);
			}
			renderingParts.push(taxesTabPart);
		}


		// ====================================
		for(let line of config.freeLinesBottom ?? []){
			let baseLineMultiplier: number|undefined = undefined;
			while(line.startsWith('#')){
				if(baseLineMultiplier === undefined) baseLineMultiplier = 2;
				else ++baseLineMultiplier;
				line = line.substring(1);
			}

			if(line.indexOf('{{totalCountRootPurchaseItem}}') !== -1){
				let totalCountRootPurchaseItems = 0;
				for(const purchase of order.purchases){
					totalCountRootPurchaseItems += this.orderExecutorModel.getPurchaseQuantity(purchase).notCanceled;
				}
				line = line.replaceAll('{{totalCountRootPurchaseItem}}', ''+totalCountRootPurchaseItems);
			}

			renderingParts.push({type: 'TEXT', text:line, textAlign: 'center', baseLineMultiplier} satisfies SimplifiedPrintingPart_Text);
		}
		if((config.freeLinesBottom ?? []).length) renderingParts.push({type: 'SEPARATOR'});
		// ====================================

		renderingParts.push({type: 'TEXT', text:'Siren: '+config.identifiers.siren});
		if(config.identifiers.vatNumber) renderingParts.push({type: 'TEXT', text:'TVA: '+config.identifiers.vatNumber});

		renderingParts.push({type: 'TEXT', text: 'Numéro de ticket: '});
		renderingParts.push({type: 'TEXT', text: UuidUtils.visualToScoped<UuidScopeProductOrder>(order.uid)});


		return renderingParts;

		// https://www.legalstart.fr/fiches-pratiques/facturation/ticket-caisse-mention-obligatoire/
		// X le nom du commerce ;
		// X l’adresse du commerce ;
		// mention légales: siret, num tva (semble pas "100% obligatoire")

		// X le numéro du ticket de caisse ;
		// X ’horodatage de l’émission du ticket, à savoir le jour, le mois et l’année ainsi que l’heure exacte (heure et minutes) ;
		// le libellé des articles ou prestations ;
		// le prix unitaire ;
		// la quantité ;
		// le montant HT ; + taux TVA
		// X les réductions éventuellement applicables ;
		// X le montant total TTC ;
		// X le(s) moyen(s) de paiement.

		// vu sur d'autres tickets
		//	- numéro de caisse
		// X - mention duplicata si 2e impression + "ticket ré-imprimé 1 fois"
		//  - nom du logiciel/version

	}

	getPurchaseRenderingParts(purchase: PurchaseApiOut, depth: number = 1): string[][] {
		let parts: string[][] = [];

		const revision = this.posState.orderExecutorModel.requireProductRevision(purchase.productRevisionUid);

		let spaces: string = '';
		for(let i = 0; i < depth; i++) {
			spaces += '    ';
		}

		parts.push([
			spaces + 'x' + this.orderExecutorModel.getPurchaseQuantity(purchase).notCanceled + ' ' + revision.name,
			'',
			'',
		]);

		for(const item of purchase.items) {
			for(let group of item.groups) {
				for(let purchase of group.purchases) {
					parts = parts.concat(this.getPurchaseRenderingParts(purchase, depth + 1));
				}
			}
		}

		return parts;
	}

	renderCashStatement(orders: OrderApiOut[], cashStatementSession: CashStatementSessionApiOut) {
		const config = this.getReceiptConfig();
		const statsModel = new OrderStatsModel(this.orderExecutorModel);
		const stats = statsModel.compute(orders);

		const renderingParts : SimplifiedPrintingPart[] = [];

		// ====================================
		renderingParts.push({type: 'TEXT', text:config.companyName, textAlign:'center'});
		renderingParts.push({type: 'TEXT', text:config.companyAddress, textAlign:'center'});
		renderingParts.push({type: 'SEPARATOR'});

		renderingParts.push({type: 'TEXT', text: 'Résumé du Ticket Z', textAlign: 'center'});
		renderingParts.push({
			type: 'TEXT',
			text: DateFilter(cashStatementSession.creationDatetime) + ' ' + HourFilter(cashStatementSession.creationDatetime)
				+ ' - ' + (cashStatementSession.closingDatetime ? (DateFilter(cashStatementSession.closingDatetime) + ' ' + HourFilter(cashStatementSession.closingDatetime)) : ''),
			textAlign:'center'
		});

		renderingParts.push({
			type: 'TEXT',
			text: `${orders.length} commandes / ${orders.reduce((acc, order) => acc + (order.diningExtra?.guestCount ?? 0), 0)} couverts`,
			textAlign:'center'
		});
		renderingParts.push({type: 'SEPARATOR'});

		let totalsTab: SimplifiedPrintingPart_Table = {
			header:['', ''],
			rows: [],
			type: 'TABLE',
			widthPerCell: [60, 40],
			marginBetweenCells: 4,
			textAlignPerCellHeader: ['left', 'right'],
			textAlignPerCellRows: ['left', 'right'],
		};

		totalsTab.rows.push([
			'CA TOTAL TTC', MoneyFilter(stats.totals.withTaxes)
		]);

		totalsTab.rows.push([
			'CA TOTAL HT', MoneyFilter(stats.totals.withoutTaxes)
		]);

		for (const [taxName, data] of Object.entries(stats.perTaxes)) {
			totalsTab.rows.push([
				'', ''
			]);

			totalsTab.rows.push([
				'CA HT ' + taxName, MoneyFilter(data.withoutTaxes)
			]);

			totalsTab.rows.push([
				taxName + ' collecté', MoneyFilter(data.collected)
			]);

			totalsTab.rows.push([
				'CA TTC ' + taxName, MoneyFilter(data.withTaxes)
			]);
		}

		renderingParts.push(totalsTab);

		renderingParts.push({type: 'TEXT', text: 'Moyens de paiement', textAlign:'left', fontWeight: 600});

		let methodsTabPart: SimplifiedPrintingPart_Table = {
			rows: [],
			type: 'TABLE',
			widthPerCell: [60, 40],
			marginBetweenCells: 4,
			textAlignPerCellHeader: ['left', 'right'],
			textAlignPerCellRows: ['left', 'right'],
		};

		for (const [methodUid, data] of Object.entries(stats.perPaymentMethod)) {
			const method = this.orderExecutorModel.requirePaymentMethod(methodUid as VisualScopedUuid<UuidScopePayment_method>);
			methodsTabPart.rows.push([method.name, MoneyFilter(data.success)]);
		}

		renderingParts.push(methodsTabPart);


		renderingParts.push({type: 'TEXT', text: 'N°' + UuidUtils.visualToUuid(cashStatementSession.uid), textAlign:'left'});

		return renderingParts;
	}

	renderPaymentNote(order: OrderApiOut, payment: OrderPaymentApiOut, config: OrderReceiptRenderConfig, mealCount: number) {
		const orderTotals = this.orderExecutorModel.getOrderTotals(order);

		const renderingParts : SimplifiedPrintingPart[] = [];

		renderingParts.push({type: 'TEXT', text:'* JUSTIFICATIF DE PAIEMENT *', textAlign:'center'});
		renderingParts.push({type: 'TEXT', text:'', textAlign:'center'});

		// ====================================
		renderingParts.push({type: 'TEXT', text:config.companyName, textAlign:'center'});
		renderingParts.push({type: 'TEXT', text:config.companyAddress, textAlign:'center'});
		renderingParts.push({type: 'SEPARATOR'});

		renderingParts.push({
			type: 'TEXT',
			text: 'justificatif non valable pour encaissement',
			textAlign: 'center'
		});

		renderingParts.push({type: 'TEXT', text:'Caisse Weecop '+config.posVersion, textAlign:'center'});
		renderingParts.push({type: 'SEPARATOR'});

		for(const line of config.freeLinesTop ?? [])
			renderingParts.push({type: 'TEXT', text:line, textAlign: 'center'});

		if((config.freeLinesTop ?? []).length > 0) renderingParts.push({type: 'TEXT', text:'', textAlign: 'center'})

		// ====================================

		let taxesTabPart : SimplifiedPrintingPart_Table = {
			type:'TABLE',
			rows: [],
			header: ['Taxe', 'Total TTC', 'Dont taxe'],
			widthPerCell: [60, 20, 20],
			textAlignPerCellHeader: ['left', 'right', 'right'],
			textAlignPerCellRows: ['left', 'right', 'right'],
			marginBetweenCells: 3,
		};

		const ratio = payment.amount / orderTotals.purchases.withTaxesAfterDiscount;

		for(const tax of orderTotals.purchases.taxDetails){
			taxesTabPart.rows.push([
				`x${mealCount} Repas complet (${tax.descriptor.name+' '+(tax.descriptor.percent/1000)+'%'})`,
				MoneyFilter((tax.baseAmount + tax.amount) * ratio),
				MoneyFilter(tax.amount * ratio)
			]);
		}
		renderingParts.push(taxesTabPart);
		renderingParts.push({type: 'SEPARATOR'});

		// ====================================
		let totalsTabPart : SimplifiedPrintingPart_Table = {
			type:'TABLE',
			rows: [],
			widthPerCell: [75, 25],
			textAlignPerCellRows: ['left', 'right'],
			marginBetweenCells: 3,
		};
		totalsTabPart.rows.push(['Total', MoneyFilter(payment.amount)]);

		renderingParts.push(totalsTabPart);

		// ====================================
		for(let line of config.freeLinesBottom ?? []){
			let baseLineMultiplier: number|undefined = undefined;
			while(line.startsWith('#')){
				if(baseLineMultiplier === undefined) baseLineMultiplier = 2;
				else ++baseLineMultiplier;
				line = line.substring(1);
			}

			if(line.indexOf('{{totalCountRootPurchaseItem}}') !== -1){
				let totalCountRootPurchaseItems = 0;
				for(const purchase of order.purchases){
					totalCountRootPurchaseItems += this.orderExecutorModel.getPurchaseQuantity(purchase).notCanceled;
				}
				line = line.replaceAll('{{totalCountRootPurchaseItem}}', ''+totalCountRootPurchaseItems);
			}

			renderingParts.push({type: 'TEXT', text:line, textAlign: 'center', baseLineMultiplier} satisfies SimplifiedPrintingPart_Text);
		}
		if((config.freeLinesBottom ?? []).length) renderingParts.push({type: 'SEPARATOR'});
		// ====================================

		renderingParts.push({type: 'TEXT', text:'Siren: '+config.identifiers.siren});
		if(config.identifiers.vatNumber) renderingParts.push({type: 'TEXT', text:'TVA: '+config.identifiers.vatNumber});

		renderingParts.push({type: 'TEXT', text: 'Numéro de ticket: '});
		renderingParts.push({type: 'TEXT', text: UuidUtils.visualToScoped<UuidScopeProduct_order>(order.uid)});
		renderingParts.push({type: 'TEXT', text: 'Paiement: '});
		renderingParts.push({type: 'TEXT', text: UuidUtils.visualToScoped<UuidScopeProductOrderPayment>(payment.uid)});

		return renderingParts;

	}
}
