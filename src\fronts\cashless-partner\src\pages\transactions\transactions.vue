<script lang="ts" src="./transactions.ts">
</script>

<style scoped lang="sass">
@use './transactions.scss' as *
</style>

<template>
    <div id="transactions-page" class="page">
        <forbidden-message v-if="forbidden"></forbidden-message>
        <filter-table-layout
            v-else
            :header-parameters="headerParameters"
            :allowed-filters="allowedFilters"
            :table-columns="tableColumns"
            :filters="filters"
            :applied-filters="appliedFilters"
            :drawer-opened="selectedTransaction !== null"
            :pagination="pagination"
            :filter-parameters="filterParameters"
            :saved-filters="savedFilters"
            @search="search($event)"
            @filters-changed="searchTransactions($event);"
            @changed-column-preferences="saveColumnPreferences($event)"
            @next="nextPage()"
            @previous="previousPage()"
            @save-filter="saveFilter($event)"
            @select-filter="selectFilter($event)"
            @delete-filter="deleteFilter($event)"
        >
            <template v-slot:actions-right>
                <div class="new-data">
                    <div class="cursor-pointer label">
                        Afficher 2 nouvelles transactions
                    </div>
                </div>
            </template>

            <template v-slot:actions-left>
                <div class="new-data" v-if="newTransactions.length > 0" @click="searchTransactions(filters)">
                    <div class="cursor-pointer label">
                        Afficher {{ newTransactions.length }} nouvelle{{newTransactions.length === 1 ? '' : 's'}} transaction{{newTransactions.length === 1 ? '' : 's'}}
                    </div>
                </div>
            </template>

            <template v-slot:table-data>
                <tr class="table-dimmer" :class="{relative: transactions.length === 0}" v-if="loading">
                    <td colspan="100%">
                        <div class="dimmer">
                            <div class="loader-container">
                                <div class="loader"></div>
                            </div>
                        </div>
                    </td>
                </tr>
                <tr class="table-no-data" v-else-if="transactions.length === 0">
                    <td colspan="100%">Aucune donnée</td>
                </tr>
                <tr v-for="transaction in transactions" :class="{selected: selectedTransaction && transaction.uid === selectedTransaction.uid}" @click="selectTransaction(transaction)">
                    <td :class="{'mobile-hidden': column.mobileHidden}" v-for="column in tableColumns.filter((column) => column.displayed)">
                        <template v-if="column.name === 'uid'"> {{ transaction.uid }} </template>
                        <template v-if="column.name === 'chip'">
                            {{ $filters.Chip(transaction.chipVisualId ?? '') }}
                        </template>
                        <template v-if="column.name === 'amount'"> {{ $filters.Money(transaction.signedAmount) }} </template>
                        <template v-if="column.name === 'profileUid'"> {{ requireProfileWithUid(transaction.profileUid).name }} </template>
                        <template v-if="column.name === 'currency'"> {{ transaction.currency }} </template>
                        <template v-if="column.name === 'balanceOnChipBeforeTx'"> {{ $filters.Money(transaction.balanceOnChipBeforeTx) }} </template>
                        <template v-if="column.name === 'paymentMethod'">
                          <template v-if="transaction.paymentMethod">{{ $filters.PaymentMethod(transaction.paymentMethod,paymentMethods)?.name }} </template>
                          <template v-else>-</template>
                        </template>
                        <template v-if="column.name === 'status'">
                            <span class="label" :class="{red: transaction.status === TransactionStatus.CANCELED, 'grey': transaction.status === TransactionStatus.PENDING}"> {{ $filters.TransactionStatus(transaction.status) }} </span>
                        </template>
                        <template v-if="column.name === 'creationDatetime'"> {{ $filters.Date(transaction.creationDatetime) }} {{ $filters.Hour(transaction.creationDatetime) }} </template>
                        <template v-if="column.name === 'action'">
                            <div class="table-action">
                                <dropdown-button
                                    button-class="tertiary button"
                                    icon="fa-regular fa-ellipsis-vertical"
                                    title=""
                                    alignment="RIGHT"
                                    @clicked="transactionDropdownClicked($event, transaction)"
                                    :actions="[{title: '', actions: [{
                                        id: 'change-method',
                                        name: 'Changer le moyen de paiement',
                                        icon: 'fa-regular fa-arrow-right-arrow-left'
                                    }]}]"
                                ></dropdown-button>
                            </div>
                        </template>
                    </td>
                </tr>
            </template>
            <template v-slot:right>
                <div v-if="!selectedTransaction" class="empty-right-panel">
                    <img src="../../assets/img/select-hint.svg" />
                    Cliquer sur une transaction pour <br/> la sélectionner
                </div>
                <div v-else class="selected-transaction">
                    <div class="close" @click="selectedTransaction = null">
                        <i class="fa-regular fa-xmark"></i>
                        <span> Fermer </span>
                    </div>

                    <div class="header">
                        <div class="left">
                            <h2> {{ $filters.Chip(selectedTransaction.chipVisualId ?? '')}} </h2>
                        </div>
                        <dropdown-button v-if="appState && appState.advancedInterfaces"
                            title="Actions"
                            icon="fa-regular fa-sparkles"
                            alignment="RIGHT"
                            button-class="black"
                            :actions="[{title: '', actions: transactionDropdownActions }]"
                            @clicked="dropdownClicked($event)"
                        ></dropdown-button>

                        <dropdown-button
                            v-else
                            button-class="grey button"
                            icon="fa-regular fa-ellipsis-vertical"
                            title=""
                            alignment="RIGHT"
                            @clicked="transactionDropdownClicked($event, selectedTransaction)"
                            :actions="[{title: '', actions: transactionSimpleDropdownActions}]"
                        ></dropdown-button>
                    </div>

                    <div class="transaction-details">
                        <div class="labels">
                            <div v-if="selectedTransaction.network" class="label"> Réseau </div>
                            <div v-if="selectedTransaction.refund" class="label"> Remboursée </div>
                            <div v-if="selectedTransaction.clear" class="label"> Vidage </div>
                            <div v-if="selectedTransaction.repaired" class="label"> Réparée </div>
                            <div v-if="selectedTransaction.disable" class="label"> Désactivation </div>
                        </div>

                        <h3> Informations générales </h3>

                        <div class="properties-table">
                            <div class="row">
                                <span class="title"> Méthode de paiement </span>
                                <span class="value" v-if="selectedTransaction.rectifiedPaymentMethod"> {{ requirePaymentMethodWithUid(selectedTransaction.rectifiedPaymentMethod).name }} </span>
                                <span class="value" v-else-if="selectedTransaction.paymentMethod"> {{ requirePaymentMethodWithUid(selectedTransaction.paymentMethod).name }} </span>
                                <span class="value" v-else> Aucune </span>
                            </div>
                            <div class="row">
                                <span class="title"> Date de création </span>
                                <span class="value">
                                    {{ $filters.Date(selectedTransaction.creationDatetime) }} à
                                    {{ $filters.Hour(selectedTransaction.creationDatetime) }}
                                </span>
                            </div>
                            <div class="row">
                                <span class="title"> Date de création serveur </span>
                                <span class="value">
                                    {{ $filters.Date(selectedTransaction.creationDatetimeOnServer) }} à
                                    {{ $filters.Hour(selectedTransaction.creationDatetimeOnServer) }}
                                </span>
                            </div>
                            <div class="row">
                                <span class="title"> Appareil </span>
                                <span class="value"> {{ requireDeviceNameWithUid(selectedTransaction.creationDeviceUid) }} </span>
                            </div>
                            <div class="row">
                                <span class="title"> Solde avant transaction </span>
                                <span class="value"> {{ $filters.Money(selectedTransaction.balanceOnChipBeforeTx) }} </span>
                            </div>
                            <div class="row">
                                <span class="title"> Point de vente </span>
                                <span class="value"> {{ requireProfileWithUid(selectedTransaction.profileUid).name }} </span>
                            </div>
                            <div class="row">
                                <span class="title"> Compteur de la puce </span>
                                <span class="value"> {{ selectedTransaction.chipChronologicalCount }} </span>
                            </div>
                            <div class="row">
                                <span class="title"> Heure réseau de la puce </span>
                                <span class="value"> {{ selectedTransaction.networkTime ?? '' }} </span>
                            </div>
                            <div class="row">
                                <span class="title"> Raison de l'erreur </span>
                                <span class="value"> {{ selectedTransaction.refusedReason ?? '' }} </span>
                            </div>
                            <div class="row">
                                <span class="title"> Méthode de paiement (originale) </span>
                                <span class="value" v-if="selectedTransaction.paymentMethod"> {{ requirePaymentMethodWithUid(selectedTransaction.paymentMethod).name }} </span>
                                <span class="value" v-else> Aucune </span>
                            </div>
                            <div class="row">
                                <span class="title"> Utilisateur lié </span>
                                <a :href="getCustomerUrl(selectedTransactionCustomerChip.customerUid)" target="_blank" class="value" v-if="selectedTransactionCustomerChip">
                                    Voir l'utilisateur
                                    <i class="fa-regular fa-arrow-up-right-from-square"></i>
                                </a>
                                <span class="value" v-else> Aucun </span>
                            </div>
                        </div>

                        <template v-if="selectedTransaction.network">
                            <h3> Informations réseau </h3>

                            <div class="properties-table" >
                                <div class="row">
                                    <span class="title"> Date finale </span>
                                    {{ $filters.Date(selectedTransaction.finalStateDatetime) }} à
                                    {{ $filters.Hour(selectedTransaction.finalStateDatetime) }}
                                </div>
                                <div class="row">
                                    <span class="title"> Date finale serveur </span>
                                    {{ $filters.Date(selectedTransaction.finalStateDatetimeOnServer) }} à
                                    {{ $filters.Hour(selectedTransaction.finalStateDatetimeOnServer) }}
                                </div>
                                <div class="row">
                                    <span class="title"> Appareil final </span>
                                    {{ requireDeviceNameWithUid(selectedTransaction.finalStateDeviceUid) }}
                                </div>
                            </div>
                        </template>

                        <h3> Produits </h3>

                        <div class="empty" v-if="!selectedTransaction.products || selectedTransaction.products.length === 0"> Aucun produit </div>
                        <div class="products" v-for="product of selectedTransaction.products">
                            <div class="product">
                                <img v-if="requireProductWithUid(product.id).img" :src="requireProductWithUid(product.id).img" />
                                <span class="name"> x{{ product.qty }} {{ requireProductWithUid(product.id).name }} </span>
                                {{ $filters.Money(product.price) }}
                            </div>
                        </div>
                    </div>
                </div>
            </template>
        </filter-table-layout>

        <form-modal-or-drawer
            :state="showExportModal"
            :title="'Exporter les transactions'"
            :subtitle="'Exporter les transactions au format CSV'"
            @close="showExportModal=false"
        >
            <template v-slot:content>

                <div class="two-inputs">
                    <div class="input-group">
                        <label for="title"> Réalisée après le </label>
                        <div class="ui input">
                            <date-input :value="exportDates.start" @change="exportDates.start = $event" ></date-input>
                        </div>
                    </div>

                    <div class="input-group">
                        <label for="title"> et avant le </label>
                        <div class="ui input color">
                            <date-input :value="exportDates.end" @change="exportDates.end = $event" ></date-input>
                        </div>
                    </div>
                </div>

                <div class="input-group">
                    <label> Format </label>

                    <div class="input">
                        <dropdown
                            :values="[{
                                image: '/img/excel.png',
                                name: 'Excel',
                                value: 'xlsx'
                            }, {
                                image: '/img/orb.png',
                                name: 'OpenOffice',
                                value: 'ods'
                            }, {
                                name: 'CSV',
                                value: 'csv'
                            }]"
                            :default-selected="selectedFileType"
                            @update="selectedFileType = $event"
                        ></dropdown>
                    </div>
                </div>

                <div class="form-error" v-if="exportError">
                    <i class="fa-solid fa-exclamation-circle"></i>
                    <div class="details">
                        <span class="title"> Erreur </span>
                        <span class="description">{{ exportError }}</span>
                    </div>
                </div>
            </template>

            <template v-slot:buttons>
                <button class="white button" @click="showExportModal = false" >
                    Annuler
                </button>
                <button class="button" :class="{loading: exporting, disabled: exporting}" @click="exportTransactions()">
                    <i class="fa-regular fa-arrow-down-to-line"></i>
                    Exporter
                </button>
            </template>
        </form-modal-or-drawer>

        <transaction-method-form
            v-if="showPaymentMethodFormModal"
            :transaction="showPaymentMethodFormModal"
            @updated="updatedTransaction($event)"
            @close="showPaymentMethodFormModal = null"
        ></transaction-method-form>
    </div>
</template>