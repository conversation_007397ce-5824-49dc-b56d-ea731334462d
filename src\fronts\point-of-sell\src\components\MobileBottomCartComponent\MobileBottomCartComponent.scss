.mobile-bottom-cart-component {
    display: none;
    position: fixed;
    top: calc(100% - (85px + var(--safe-area-bottom) + 70px));
    right: 10px;
    left: 10px;
    bottom: calc(85px + var(--safe-area-bottom));
    z-index: 500;
    align-items: center;
    gap: 15px;
    padding: 15px 22px;
    border-radius: 8px;
    background: var(--primary-color);
    color: var(--primary-text-color);

    @media (max-width: 900px) {
        display: flex !important;
    }

    //transition:
    //        top cubic-bezier(0.22, 1, 0.36, 1) .3s,
    //        bottom cubic-bezier(0.22, 1, 0.36, 1) .3s,
    //        right cubic-bezier(0.22, 1, 0.36, 1) .3s,
    //        left cubic-bezier(0.22, 1, 0.36, 1) .3s,
    //        background-color cubic-bezier(0.22, 1, 0.36, 1) .3s;

    &.opened {
        top: 0;
        bottom: 0;
        left: 0;
        right: 0;
        border-radius: 0;
        background: white;

        * {
            display: none !important;
        }
    }

    .quantity {
        flex-shrink: 0;
        display: flex;
        justify-content: center;
        align-items: center;
        height: 28px;
        width: 28px;
        background: white;
        border-radius: 50%;
        color: var(--primary-color);
        background: var(--primary-text-color);
        font-weight: bold;
    }

    .center {
        flex-grow: 2;
        display: flex;
        flex-direction: column;
        gap: 2px;

        .amount {
            font-size: 18px;
            font-weight: bold;
        }

        .title {
            font-size: 13px;
        }
    }

    .actions {
        display: flex;
        align-items: center;
        gap: 10px;

        .action {
            padding: 5px 10px;

            i {
                font-size: 20px;
            }
        }
    }
}