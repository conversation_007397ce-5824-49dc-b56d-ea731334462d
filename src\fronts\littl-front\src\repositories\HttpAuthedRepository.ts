import {
	AbstractHttpDescriptorRepository, AbstractHttpDescriptorRepositoryOptions,
	AutoWired, DefaultAbstractHttpDescriptorRepositoryOptions,
	GetInstance,
	HttpRouteDescriptorAggregate
} from '@groupk/horizon2-core';
import {MainConfig} from "../../../../shared/MainConfig";
import {AuthStateModel} from "../../../../shared/AuthStateModel";

export abstract class HttpAuthedRepository<ContractAggregate extends HttpRouteDescriptorAggregate> extends AbstractHttpDescriptorRepository<ContractAggregate>{

	@AutoWired(AuthStateModel) accessor authStateModel!: AuthStateModel;

	constructor(contractAggregate: ContractAggregate, options : Partial<AbstractHttpDescriptorRepositoryOptions> = DefaultAbstractHttpDescriptorRepositoryOptions) {
		const mainConfig = GetInstance(MainConfig);
		super(mainConfig.configuration.mastodonApiEndpoint, contractAggregate, options);
	}

}