import {Component, Vue} from "vue-facing-decorator";
import {AutoWired} from "@groupk/horizon2-core";
import {PosPrinter, PrinterRepository} from "../../repositories/PrinterRepository";
import {FormModalOrDrawerComponent} from "@groupk/vue3-interface-sdk";
import PrinterHelper, {PrinterBrand} from "../../printApiSdk/PrinterHelper";
import {randomUUID} from "@groupk/horizon2-front";
import {PrinterConfigData, PrinterType, SelectedPrinter, TcpPrinter, UsbPrinter} from "../../model/PrinterTypes";
import {NativeInterface, UsbNative} from "@groupk/native-bridge";
import {SocketNative} from "@groupk/native-bridge";

@Component({
	components: {
		'form-modal-or-drawer': FormModalOrDrawerComponent
	},
	emits: []
})
export default class PrintSettingsComponent extends Vue {
	usbPrinters: UsbPrinter[] = [];
	tcpPrinters: TcpPrinter[] = [];
	selectedPrinter: SelectedPrinter|null = null;

	isNative: boolean = false;

	editingConfig: PrinterConfigData|null = null;
	editingTcpPrinter: TcpPrinter|null = null;
	editingTcpPrinterError: string|null = null;
	connectionTest: 'LOADING'|'ERROR'|'SUCCESS'|null = null;

	refreshing: boolean = false;
	error: boolean = false;

	@AutoWired(PrinterRepository) accessor printerRepository!: PrinterRepository;
	@AutoWired(UsbNative) accessor usbNative!: UsbNative;
	@AutoWired(SocketNative) accessor socketNative!: SocketNative;

	beforeMount() {
		this.isNative = NativeInterface.instance.available();
		this.usbNative.on('deviceAttached', () => {
			this.detectUsbPrinters();
		});
	}

	mounted() {
		this.tcpPrinters = this.printerRepository.getTcpPrinters();
		this.detectUsbPrinters();
		this.selectedPrinter = this.printerRepository.getReceiptPrinter();
	}

	async refreshDevicesList() {
		this.refreshing = true;

		// Add 1s timeout to give user feedback of refreshing state
		await Promise.all([
			this.detectUsbPrinters(),
			new Promise((resolve) => {
				setTimeout(() => resolve(null), 1000);
			})
		]);

		this.refreshing = false;
	}

	detectUsbPrinters() {
		return new Promise(async (resolve) => {
			this.error = false;

			try {
				let devices = await this.usbNative.getDevices()
				this.usbPrinters = [];
				for(let device of devices) {
					try {
						await this.usbNative.requestPermission(device.id);
					} catch(err) {}
				}
				devices = await this.usbNative.getDevices()
				for(let device of devices) {
					this.usbPrinters.push(new UsbPrinter(device))
				}
			} catch(err) {
				this.error = true;
				resolve(null);
			}
		});
	}

	editPrinterCustomConfig(printer: PosPrinter){
		this.editingConfig = new PrinterConfigData(printer.getId(), printer.getType())
		const existingCustomConfig = this.printerRepository.getCustomConfigForPrinter(printer.getId());
		if(existingCustomConfig) {
			this.editingConfig.fillFromConfig(existingCustomConfig);
		}
	}

	async selectPrinter(printer: PosPrinter) {
		if(printer.getType() === PrinterType.USB) {
			const usbPrinter = printer as UsbPrinter;
			if (!usbPrinter.usbDevice.connected) {
				const updatedDevice = await this.printerRepository.connectToUsbPrinter(usbPrinter.usbDevice.id);
				if (!updatedDevice) return;
				if (!updatedDevice.serialNumber) return;

				usbPrinter.usbDevice.connected = true;
				usbPrinter.usbDevice.serialNumber = updatedDevice.serialNumber;
			}
		}

		const existingCustomConfig = this.printerRepository.getCustomConfigForPrinter(printer.getId());
		const productName = printer instanceof UsbPrinter ? printer.usbDevice.productName : null;
		const descriptor = PrinterHelper.getDescriptorForBrand((printer.getBrand() ?? '').toUpperCase() as PrinterBrand, {product: productName ?? undefined});
		if(!existingCustomConfig && descriptor.maxSize.width === null) {
			// No default or known device config found
			// user have to configure printer by himself
			this.editingConfig = new PrinterConfigData(printer.getId(), printer.getType());
		} else {
			this.saveDeviceAsDefaultPrinter(printer.getId(), printer.getType());
		}
	}

	saveDeviceAsDefaultPrinter(printerId: string, printerType: PrinterType) {
		const selectedPrinter: SelectedPrinter = {
			id: printerId,
			type: printerType
		};

		this.printerRepository.setReceiptPrinter(selectedPrinter);
		this.selectedPrinter = selectedPrinter;
	}

	savePrinterCustomConfig() {
		if(!this.editingConfig) return;
		this.printerRepository.saveCustomConfig(this.editingConfig.printerId, this.editingConfig.toPrinterConfig());
		this.saveDeviceAsDefaultPrinter(this.editingConfig.printerId, this.editingConfig.printerType);
		this.editingConfig = null;
	}

	isPrinterSelected(printer: PosPrinter) {
		if(!this.selectedPrinter) return false;
		return printer.getId() === this.selectedPrinter.id;
	}

	testTcpPrinterConnection() {
		if(!this.editingTcpPrinter) return;

		this.connectionTest = 'LOADING';

		const timeout = setTimeout(() => {
			if(this.connectionTest === 'LOADING') this.connectionTest = 'ERROR';
		}, 5000);

		this.socketNative.tcpConnectWrapper(this.editingTcpPrinter.ip, parseInt(this.editingTcpPrinter.port, 10)).then((wrapper) => {
			this.connectionTest = 'SUCCESS';
			clearTimeout(timeout);
			wrapper.close();
		}).catch(() => {
			this.connectionTest = 'ERROR';
			clearTimeout(timeout);
		});
	}

	saveTcpPrinter() {
		this.editingTcpPrinterError = null;
		if(!this.editingTcpPrinter) return;

		const splitIp = this.editingTcpPrinter.ip.split('.');
		if(splitIp.length !== 4) {
			this.editingTcpPrinterError = 'Adresse IP invalide';
			return;
		}
		for(let part of splitIp) if(part.length > 3) {
			this.editingTcpPrinterError = 'Adresse IP invalide';
			return;
		}

		if(this.editingTcpPrinter.port === '') {
			this.editingTcpPrinterError = 'Port invalide';
			return;
		}

		this.printerRepository.saveTcpPrinter(this.editingTcpPrinter);
		this.editingTcpPrinter = null;

		this.tcpPrinters = this.printerRepository.getTcpPrinters();
	}

	showTcpPrinterCreationModal() {
		this.editingTcpPrinter = new TcpPrinter(randomUUID(), '', '', PrinterBrand.BROTHER, '');
	}

	requireTcpPrinterWithUid(printerId: string) {
		const printer = this.tcpPrinters.find((printer) => printer.getId() === printerId);
		if(!printer) throw new Error('missing_tcp_printer');
		return printer;
	}
}