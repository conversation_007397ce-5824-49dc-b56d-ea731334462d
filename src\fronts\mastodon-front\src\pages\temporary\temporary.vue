<script lang="ts" src="./temporary.ts">
</script>

<style lang="sass">
@import './temporary.scss'
</style>

<template>
    <div class="temporary-component">
        <div class="mastodon-pricing-table">
            <div class="headers">
                <div></div>
                <div class="header">
                    <div class="title-group">
                        <div class="title"> Terminal 1 </div>
                        <div class="subtitle"> Le plus adapté pour </div>
                    </div>

                    <div class="price"> 9,99 € </div>
                    <button class="button"> Choisir </button>
                </div>
                <div class="header">
                    <div class="title-group">
                        <div class="title"> Terminal 2 </div>
                        <div class="subtitle"> Le plus adapté pour </div>
                    </div>

                    <div class="price"> 16,99 € </div>
                    <button class="button"> Choisir </button>
                </div>
                <div class="header">
                    <div class="title-group">
                        <div class="title"> Terminal 3 </div>
                        <div class="subtitle"> Le plus adapté pour </div>
                    </div>

                    <div class="price"> 19,99 € </div>
                    <button class="button"> Choisir </button>
                </div>
            </div>

            <div class="rows">
                <div class="row" v-for="i in 10">
                    <div class="description"> Acces internet un peu long mais ca passe franchement bien et puis c'est possible en vrai que ca soit long comme ca </div>
                    <div class="state">
                        -
                    </div>
                    <div class="state">
                        <i class="fa-regular fa-circle-check"></i>
                    </div>
                    <div class="state">
                        <i class="fa-regular fa-circle-check"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>