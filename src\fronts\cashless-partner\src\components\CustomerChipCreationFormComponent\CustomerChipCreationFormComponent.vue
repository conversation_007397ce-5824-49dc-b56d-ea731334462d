
<script lang="ts" src="./CustomerChipCreationFormComponent.ts">
</script>

<style lang="sass">
@use './CustomerChipCreationFormComponent.scss' as *
</style>

<template>
    <div class="customer-chip-creation-form-component">
        <form-modal-or-drawer
            :state="opened"
            :title="'Lier une puce'"
            :subtitle="'Lier une puce à ' + customer.lastname + ' ' + customer.firstname"
            @close="close()"
        >
            <template v-slot:content>
                <div class="input-group">
                    <label> ID de la puce </label>

                    <div class="fluid input">
                        <input v-cleave="{obj: data, key: 'chipId', options: cleaveChip}" class="fluid" placeholder="ID de la puce" />
                    </div>
                </div>

                <div class="form-error" v-if="error && error.message">
                    <i class="fa-solid fa-exclamation-circle"></i>
                    <div class="details">
                        <span class="title"> Erreur </span>
                        <span class="description">
                            {{ error.message }}.
                            <a v-if="error.customer" :href="getCustomerUrl(error.customer)" target="_blank" class="value">
                                Voir l'utilisateur
                                <i class="fa-regular fa-arrow-up-right-from-square"></i>
                            </a>
                        </span>
                    </div>
                </div>
            </template>

            <template v-slot:buttons>
                <button type="button" class="white button" @click="close()"> Annuler </button>
                <button type="button" class="button" @click="create()">
                    <i class="fa-regular fa-plus"></i>
                    Valider
                </button>
            </template>
        </form-modal-or-drawer>
    </div>
</template>