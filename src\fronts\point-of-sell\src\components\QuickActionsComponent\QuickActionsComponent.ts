import {Component, Prop, Vue} from "vue-facing-decorator";
import {PosState} from "../../model/PosState";
import {AutoWired} from "@groupk/horizon2-core";
import {WorkClock} from "../../class/WorkClock";

@Component({
	emits: ['close']
})
export default class QuickActionsComponent extends Vue {
	@Prop() posState!: PosState;

	@AutoWired(WorkClock) accessor workClock!: WorkClock;

	closeCashStatement() {
		this.posState.showCashStatementModal = true;
		this.close();
	}

	cashKeeper() {
		this.posState.showCashKeeperActions = true;
		this.close();
	}

	async clockIn() {
		if(!this.posState.isClockedIn()) {
			await this.workClock.clockInOut();
			this.$forceUpdate();
			this.close();
		}
	}

	async clockOut() {
		if(this.posState.isClockedIn()) {
			await this.workClock.clockInOut();
			this.$forceUpdate();
			this.close();
		}
	}

	close() {
		this.$emit('close');
	}
}