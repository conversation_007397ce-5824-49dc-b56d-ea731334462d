.nepting-processor-component {
    position: fixed;
    inset: 0;
    background: white;
    z-index: 1000;
    padding: 12px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 20px;

    img {
        width: 40vw;
    }


    &.error {
        background: red;
    }

    .header {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 4px;

        .title {
            font-size: 20px;
            font-weight: 600;
            text-align: center;
        }

        .subtitle {
            font-size: 18px;
            text-align: center;
        }
    }

    .top {
        flex-grow: 2;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        color: #49a300;

        .title {
            margin: 20px;
            font-size: 26px;
            font-weight: bold;
            //color: #2f5392;

            &.red{
                color: red;
            }
        }

        .subtitle {
            font-size: 20px;
            text-align: center;
            margin-top: 5px;
            color: black;
        }

        .big-result-icon {
            font-size: 90px;

            &.red{
                color: red;
            }
        }
    }

    .buttons {
        display: flex;
        flex-direction: column;
        gap: 10px;
        width: 100%;
        box-sizing: border-box;

        .description {
            color: black;
            font-size: 20px;

            &.label {
                padding: 15px;
                border-radius: 8px;
                background: orange;
                font-size: 16px;
                color: white;

                &.major{
                    background: red;
                }
            }
        }

        .button {
            width: 100%;
            box-sizing: border-box;

            &.big {
                flex-shrink: 0;
                display: flex;
                align-items: center;
                justify-content: center;
                height: 56px;
                font-size: 15px;
                font-weight: 700;
            }
        }
    }

    .receipt-page {
        flex-grow: 2;
        display: flex;
        flex-direction: column;
        gap: 10px;
        width: 100%;
        max-height: 100%;

        .option {
            flex-grow: 2;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            gap: 5px;
            background: #2f5392;
            color: white;
            width: 100%;
            border-radius: 12px;
            font-size: 22px;

            i {
                font-size: 24px;
            }
        }

        .raw-receipt {
            flex-grow: 2;
            padding: 15px;
            background: white;
            border-radius: 8px;
            overflow: scroll;
            white-space: pre-wrap;
            box-sizing: border-box;
            margin-bottom: 0;
        }

        .qr-code-container {
            flex-grow: 2;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0;
            background: white;
            border-radius: 8px;
            image-rendering: pixelated;

            img {
                width: 100%;
                max-width: 100%;
            }
        }
    }

    &.error {
        .top {
            color: white;
        }

        .buttons {
            .description {
                color: white;
            }

            .red {
                background: rgba(255, 255, 255, 0.32)
            }
        }

        .receipt-page {
            .option {
                background: white;
                color: black;
            }

            .button {
                &.blue {
                    background: white;
                    color: black;
                }

                &.grey  {
                    background: rgba(255, 255, 255, 0.32);
                    color: white;
                }

            }
        }
    }



    .spinner {
        width: 56px;
        height: 56px;
        border-radius: 50%;
        background: conic-gradient(#0000 10%,black);
        -webkit-mask: radial-gradient(farthest-side,#0000 calc(100% - 9px),#000 0);
        animation: spinner-zp9dbg 1s infinite linear;
    }

    @keyframes spinner-zp9dbg {
        to {
            transform: rotate(1turn);
        }
    }
}