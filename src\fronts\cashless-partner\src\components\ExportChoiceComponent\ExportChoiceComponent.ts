import {Component, Prop, Vue} from "vue-facing-decorator";
import {
	DropdownComponent, FormModalOrDrawerComponent, ToggleComponent,
} from "@groupk/vue3-interface-sdk";
import {AutoWired} from "@groupk/horizon2-core";
import {CustomerRepository} from "../../../../../shared/repositories/CustomerRepository";
import {ExportFileType} from "../../../../../shared/utils/MultiFormatExporter";

export type ExportChoice = {
	id: string,
	title: string,
	description: string,
	selected?: boolean
}

export type ChosenExport = {
	exportId: string,
	format: ExportFileType,
	exportZeroLines: boolean
}


@Component({
	components: {
		dropdown: DropdownComponent,
		'form-modal-or-drawer': FormModalOrDrawerComponent,
		'toggle': ToggleComponent
	},
	emits: ['close', 'chose-export']
})
export default class ExportChoiceComponent extends Vue {
	@Prop() exportChoices: ExportChoice[] = [];
	@Prop({default:true}) askExportZeroLines: boolean=true;

	selectedChoice: string | null = null;
	selectedFileType: ExportFileType = 'xlsx';
	exportZeroLines: boolean = false;

	opened: boolean = false;

	@AutoWired(CustomerRepository) accessor customerRepository!: CustomerRepository;

	mounted() {
		setTimeout(() => {
			this.opened = true;
			this.selectedChoice = this.exportChoices.find((choice) => choice.selected)?.id ?? null;
		}, 0);
	}

	close() {
		this.opened = false;
		setTimeout(() => this.$emit('close'), 300);
	}

	validate() {
		if (!this.selectedChoice) return;
		this.$emit('chose-export', {
			exportId: this.selectedChoice,
			format: this.selectedFileType,
			exportZeroLines: this.exportZeroLines
		} satisfies ChosenExport);
		this.close();
	}
}