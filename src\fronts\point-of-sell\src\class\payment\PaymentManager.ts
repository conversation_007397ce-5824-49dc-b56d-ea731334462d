import {
	allowedPaymentProtocolsPerPaymentMethod,
	PaymentMethodApiOut,
	PaymentMethodType,
	PaymentProtocol,
	UuidScopePayment_method
} from "@groupk/mastodon-core";
import {PosState} from "../../model/PosState";
import {AutoWired, UuidUtils, VisualScopedUuid} from "@groupk/horizon2-core";
import {ErrorHandler} from "../ErrorHandler";
import {PhysicalPaymentProtocol} from "./processors/PhysicalPaymentProtocol";
import {LocalOrder} from "../../model/LocalOrder";
import {OrderPaymentApiOut, OrderPaymentStatus, PaymentMethodSettings_type} from "@groupk/mastodon-core";
import {LocalOrderRepository} from "../../repositories/LocalOrderRepository";
import {CashKeeperPaymentProcessor} from "./processors/CashKeeperPaymentProcessor";
import {CaisseApPaymentProcessor} from "./processors/CaisseApPaymentProcessor";
import {PosProfile} from "../../model/PosProfile";
import {NeptingBridge} from "../NeptingBridge";
import {UuidScopeProductOrderPurchase,PaymentMethodData_type} from "@groupk/mastodon-core";
import {NeptingPaymentProcessor} from "./processors/NeptingPaymentProcessor";

export type PendingPayment = {payment: OrderPaymentApiOut, processors: PhysicalPaymentProtocol[]};

export class PaymentManager {
	private posState: PosState;

	@AutoWired(LocalOrderRepository) accessor localOrderRepository!: LocalOrderRepository;
	@AutoWired(PosProfile) accessor posProfile!: PosProfile;
	@AutoWired(ErrorHandler) accessor errorHandler!: ErrorHandler;
	@AutoWired(NeptingBridge)  accessor neptingBridge!: NeptingBridge;

	constructor(posState: PosState) {
		this.posState = posState;
	}

	willPaymentBeAutomatic(paymentMethod: PaymentMethodApiOut) {
		const availableProcessors = this.handlePayment(paymentMethod);
		return availableProcessors.length === 0 && allowedPaymentProtocolsPerPaymentMethod[paymentMethod.type].includes(PaymentProtocol.AUTOMATIC);
	}

	async addPaymentToCurrentOrder(amount: number, paymentMethod: PaymentMethodApiOut, purchases: VisualScopedUuid<UuidScopeProductOrderPurchase>[] = []): Promise<PendingPayment> {
		if(!this.posState.currentOrder) throw new Error('no_current_order');

		const payment = this.posState.orderExecutorModel.createPaymentForOrder(this.posState.currentOrder.order, amount, paymentMethod.uid, purchases);
		const availableProcessors = this.handlePayment(paymentMethod);
		if(availableProcessors.length === 0 && allowedPaymentProtocolsPerPaymentMethod[paymentMethod.type].includes(PaymentProtocol.AUTOMATIC)) {
			const overpaid = amount - this.posState.orderExecutorModel.getOrderTotals(this.posState.currentOrder.order).leftToPay;

			const updatedPayment = await this.updatePaymentState(this.posState.currentOrder, payment, true);

			if(overpaid > 0) {
				const refund = this.posState.orderExecutorModel.refundPayment(this.posState.currentOrder.order, updatedPayment, paymentMethod.uid, overpaid);
				refund.status = OrderPaymentStatus.SUCCESS;
				await this.localOrderRepository.saveAndResync(this.posState.currentOrder);
			}
		} else if(availableProcessors.length === 0) {
			throw new Error('not_configured_payment_method');
		} else {
			await this.localOrderRepository.saveAndResync(this.posState.currentOrder);
		}

		return {payment: payment, processors: this.handlePayment(paymentMethod)};
	}

	async updatePaymentState(localOrder: LocalOrder, payment: OrderPaymentApiOut, paid: boolean, methodData: PaymentMethodData_type|null = null): Promise<OrderPaymentApiOut> {
		const safePayment = localOrder.order.payments.find((safePayment) => safePayment.uid === payment.uid);
		if(!safePayment) throw new Error('payment_not_found_for_order');

		if(paid) {
			this.posState.orderExecutorModel.setPaymentStatus(localOrder.order, safePayment, OrderPaymentStatus.SUCCESS, methodData);
			await this.localOrderRepository.saveAndResync(localOrder);
		} else {
			this.posState.orderExecutorModel.setPaymentStatus(localOrder.order, safePayment, OrderPaymentStatus.ERROR, methodData);
			await this.localOrderRepository.saveAndResync(localOrder);
		}

		return safePayment;
	}

	private handlePayment(paymentMethod: PaymentMethodApiOut): PhysicalPaymentProtocol[] {
		console.log('paymentMethod', paymentMethod);
		const availableProcessors: PhysicalPaymentProtocol[] = [];

		const paymentMethodSettings = this.getSettingsForPaymentMethod(paymentMethod.uid).map((data) => data.settings);
		console.log('paymentMethodSettings', paymentMethodSettings);
		for(let settings of paymentMethodSettings) {
			if(settings.protocol === PaymentProtocol.PHYSICAL_CASHKEEPER) {
				availableProcessors.push(new CashKeeperPaymentProcessor(settings));
			} else if(settings.protocol === PaymentProtocol.PHYSICAL_CAISEAP) {
				availableProcessors.push(new CaisseApPaymentProcessor(settings));
			} else if(settings.protocol === PaymentProtocol.PHYSICAL_NEPTING_APP2APP) {
				availableProcessors.push(new NeptingPaymentProcessor());
			}
		}
		return availableProcessors;
	}

	getSettingsForPaymentMethod(paymentMethodUid : VisualScopedUuid<UuidScopePayment_method>): {settings: PaymentMethodSettings_type, index: number}[] {
		const settings: {settings: PaymentMethodSettings_type, index: number}[] = [];
		let index: number = 0;
		for(let config of this.posProfile.paymentMethodConfigs ?? []) {
			if(config.paymentMethodUid === UuidUtils.visualToScoped(paymentMethodUid)) settings.push({settings: config.settings, index: index});
		}
		return settings;
	}
}