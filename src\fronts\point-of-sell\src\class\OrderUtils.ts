import {DropdownButtonAction} from "@groupk/vue3-interface-sdk";
import {GetInstance, VisualScopedUuid} from "@groupk/horizon2-core";
import {PosState} from "../model/PosState";
import {
    OrderApiOut,
    PurchaseApiOut,
    PurchaseItemApiOut,
    PurchaseStatusModel, UuidScopeProductOrderPurchase, UuidScopeProductOrderPurchaseItem,
    UuidScopeProductPurchaseStatus
} from "@groupk/mastodon-core";
import {AppBus} from "../config/AppBus";
import {LocalOrderRepository} from "../repositories/LocalOrderRepository";
import {PrinterRepository} from "../repositories/PrinterRepository";
import {LocalOrder} from "../model/LocalOrder";
import {OrderReceiptRender} from "./OrderReceiptRender";
import ShareUtils from "./ShareUtils";

export default class OrderUtils {
    static getOrderDropdownActions(localOrder: LocalOrder): DropdownButtonAction[] {
        const posState = GetInstance(PosState);

        if(localOrder.order.transferredTo) return [{
            name: 'Forcer la synchro',
            id: 'force-sync',
            icon: 'fa-regular fa-arrows-rotate'
        }, {
            name: 'Debug',
            id: 'show-debug',
            icon: 'fa-regular fa-flag'
        }];

        const dropdownValues: DropdownButtonAction[] = [{
            name: 'Imprimer la note',
            id: 'note',
            icon: 'fa-regular fa-receipt'
        }, {
            name: 'Afficher le reçu',
            id: 'receipt',
            icon: 'fa-regular fa-receipt'
        }, {
            name: 'Envoyer le reçu',
            id: 'send-receipt',
            icon: 'fa-regular fa-envelope'
        }, {
            name: 'Transférer la commande',
            id: 'transfer-order',
            icon: 'fa-regular fa-paper-plane'
        }, {
            name: 'Lier à un client',
            id: 'customer-link',
            icon: 'fa-regular fa-circle-user'
        }, {
            name: 'Forcer la synchro',
            id: 'force-sync',
            icon: 'fa-regular fa-arrows-rotate'
        }, {
            name: 'Debug',
            id: 'show-debug',
            icon: 'fa-regular fa-flag'
        }];

        if(posState.reservit) {
            dropdownValues.push({
                name: 'Envoyer sur Reservit',
                id: 'transfer-reservit',
                icon: 'fa-regular fa-paper-plane'
            })
        }

        for(let purchaseStatus of posState.purchaseStatuses) {
            if(!purchaseStatus.cancel && !purchaseStatus.system) {
                dropdownValues.unshift({
                    icon: 'fa-regular fa-send',
                    name: purchaseStatus.name,
                    id: purchaseStatus.uid
                });
            }
        }

        return dropdownValues;
    }

    static async addPurchaseStatus(statusUid: VisualScopedUuid<UuidScopeProductPurchaseStatus>, onlyStep: number|null) {

        // TODO a modifier quand on gerera d'autres actions que print
        const posState = GetInstance(PosState);
        const appBus = GetInstance(AppBus);
        const localOrderRepository = GetInstance(LocalOrderRepository);
        const printerRepository = GetInstance(PrinterRepository);

        if(!posState.currentOrder) return;

        const stepsToPrint: number[] = onlyStep ? [onlyStep] : posState.currentOrder.getSteps();

        for(let step of stepsToPrint) {
            const purchaseItems: { purchase: PurchaseApiOut, item: PurchaseItemApiOut }[] = [];
            for(const stepApiOut of posState.currentOrder.diningExtra.purchaseItemSteps) {
                if(stepApiOut.step === step) {
                    for(const purchase of posState.currentOrder.order.purchases) {
                        for(const purchaseItem of posState.currentOrder.getPurchaseItemsRecursive(purchase)) {
                            if(purchaseItem.item.uid === stepApiOut.purchaseItemUid) {
                                purchaseItems.push({
                                    purchase: purchase,
                                    item: purchaseItem.item
                                });
                                break;
                            }
                        }
                    }
                }
            }

            const itemsToPrint: { purchase: PurchaseApiOut, item: PurchaseItemApiOut }[] =  [];

            for(const data of purchaseItems) {
                if(PurchaseStatusModel.canExecuteAfter(
                    posState.pointOfSaleConfiguration.executionFlow,
                    posState.orderExecutorModel.requireStatus(statusUid),
                    posState.orderExecutorModel.requireStatus(data.item.statusUpdates[data.item.statusUpdates.length - 1].statusUid),
                    data.item.statusUpdates[data.item.statusUpdates.length - 1].status,
                )) {
                    itemsToPrint.push(data);
                }
            }

            // TODO Was there, but dont really know why. Seems useless since nothing in order is updated in this function
            // localOrderRepository.saveAndResync(posState.currentOrder);

            try {
                await printerRepository.printToKitchen(step, posState.currentOrder, itemsToPrint.map((data) => data.item.uid), {
                    successCallback: async () => {
                        if(!posState.currentOrder) return;

                        for(const data of itemsToPrint) {
                            posState.orderExecutorModel.addStatusToPurchaseItem(
                                posState.currentOrder.order,
                                OrderUtils.requireOrderPurchaseWithUid(posState.currentOrder.order, data.purchase.uid),
                                OrderUtils.requireOrderPurchaseItemWithUid(posState.currentOrder.order, data.item.uid),
                                statusUid
                            );
                        }

                        await posState.orderExecutorModel.processPendingPurchaseItemStatusUpdateActions(posState.currentOrder.order);
                        localOrderRepository.saveAndResync(posState.currentOrder);
                    }
                });
            } catch(err) {
                appBus.emit('displayToast', {
                    title: 'L\'impression du ticket a échoué',
                    description: 'Vérifiez que l\'imprimante est connectée et a encore du papier.',
                    color: 'red',
                    closable: true,
                    duration: 3000,
                });
            }
        }
    }

    static async printReceipt(localOrder: LocalOrder, sendType: 'SEND'|'DISPLAY'|'PRINT' = 'PRINT') {
        const posState = GetInstance(PosState);
        const localOrderRepository = GetInstance(LocalOrderRepository);
        const printerRepository = GetInstance(PrinterRepository);

        const orderReceiptRender = new OrderReceiptRender();
        const parts = orderReceiptRender.renderOrderReceipt(localOrder.order);
        await localOrderRepository.saveAndResync(localOrder);

        if(parts) {
            if(sendType === 'PRINT') {
                printerRepository.printToSelectedPrinter(parts).catch((err) => {
                    console.log(err);
                });
            } else if(sendType === 'DISPLAY') {
                posState.displayedReceipt = await printerRepository.printToBase64(parts);
            } else if(sendType === 'SEND') {
                const base64 = await printerRepository.printToBase64(parts);
                await ShareUtils.share(base64, 'image/png');
            }
        }
    }

    static async printNote(order: LocalOrder|null = null) {
        const posState = GetInstance(PosState);
        const printerRepository = GetInstance(PrinterRepository);

        const orderToPrint = order ?? posState.currentOrder;
        if(!orderToPrint) throw new Error('no_current_order');

        const orderReceiptRender = new OrderReceiptRender();
        const parts = orderReceiptRender.render(orderToPrint.order, null, orderReceiptRender.getReceiptConfig());

        printerRepository.printToSelectedPrinter(parts).catch((err) => {
            console.log(err);
        });
    }

    static requireOrderPurchaseWithUid(order: OrderApiOut, purchaseUid: VisualScopedUuid<UuidScopeProductOrderPurchase>): PurchaseApiOut {
        // First check top-level purchases
        for (const purchase of order.purchases) {
            if (purchase.uid === purchaseUid) {
                return purchase;
            }
        }

        // Then recursively search through nested purchases
        for (const purchase of order.purchases) {
            const found = this.searchPurchaseRecursive(purchase, purchaseUid);
            if (found) return found;
        }

        throw new Error('missing_purchase(' + purchaseUid + ')');
    }

    private static searchPurchaseRecursive(purchase: PurchaseApiOut, targetUid: VisualScopedUuid<UuidScopeProductOrderPurchase>): PurchaseApiOut | null {
        for (const item of purchase.items) {
            for (const group of item.groups) {
                for (const nestedPurchase of group.purchases) {
                    if (nestedPurchase.uid === targetUid) {
                        return nestedPurchase;
                    }
                    const found = this.searchPurchaseRecursive(nestedPurchase, targetUid);
                    if (found) return found;
                }
            }
        }
        return null;
    }

    static requireOrderPurchaseItemWithUid(order: OrderApiOut, purchaseItemUid: VisualScopedUuid<UuidScopeProductOrderPurchaseItem>): PurchaseItemApiOut {
        for (const purchase of order.purchases) {
            const found = this.searchPurchaseItemRecursive(purchase, purchaseItemUid);
            if (found) return found;
        }

        throw new Error('missing_purchase_item(' + purchaseItemUid + ')');
    }

    private static searchPurchaseItemRecursive(purchase: PurchaseApiOut, targetUid: VisualScopedUuid<UuidScopeProductOrderPurchaseItem>): PurchaseItemApiOut | null {
        for (const item of purchase.items) {
            if (item.uid === targetUid) {
                return item;
            }

            for (const group of item.groups) {
                for (const nestedPurchase of group.purchases) {
                    const found = this.searchPurchaseItemRecursive(nestedPurchase, targetUid);
                    if (found) return found;
                }
            }
        }
        return null;
    }
}