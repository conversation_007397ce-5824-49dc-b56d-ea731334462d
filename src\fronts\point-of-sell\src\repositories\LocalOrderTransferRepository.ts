import {EntityIndexeddbRepository} from "@groupk/horizon2-front";
import {LocalOrderTransfer} from "../model/LocalOrder";
import {AutoWired} from "@groupk/horizon2-core";
import {AppBus} from "../config/AppBus";

export class LocalOrderTransferRepository extends EntityIndexeddbRepository<LocalOrderTransfer> {
    protected static syncedIndexName = 'canceled';
    protected static fetchedFromTargetIndexName = 'fetched_from_target';

    @AutoWired(AppBus) accessor appBus!: AppBus;

    constructor() {
        super(LocalOrderTransfer, 'uid', 'pos_order_transfers', 'pos_order_transfers', 2);
        this.addEntityBooleanIndex('canceled');
    }

    protected onUpgrade(db: IDBDatabase, transaction: IDBTransaction | null, oldVersion: number, newVersion: number | null) {
        const objectStore = super.onUpgrade(db, transaction, oldVersion, newVersion);
        this.putEntityIndex(objectStore, LocalOrderTransferRepository.syncedIndexName, 'canceled');
        this.putEntityIndex(objectStore, LocalOrderTransferRepository.fetchedFromTargetIndexName, 'fetchedFromTargetDatetime');
        return objectStore;
    }

    async findAllNotDone(): Promise<LocalOrderTransfer[]> {
        const localOrderTransfers: LocalOrderTransfer[] = [];
        for await(let localOrderTransfer of (await this._findAllCursor()).generator()) {
            if(!localOrderTransfer.canceled && localOrderTransfer.fetchedFromTargetDatetime === null) {
                localOrderTransfers.push(localOrderTransfer);
            }
        }
        return localOrderTransfers;
    }

    async save(object: LocalOrderTransfer): Promise<void> {
        await super.save(object);
        this.appBus.emit('orderTransferSaved', object);
    }

    async clear() {
        await this._clear();
    }

}