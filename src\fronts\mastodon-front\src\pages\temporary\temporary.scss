

.temporary-component {
    height: 100%;
    overflow: auto;
    box-sizing: border-box;

    background: white;
    padding: 50px;
    max-width: 1140px;
    margin: auto;

    @media (max-width: 900px) {
        padding: 12px;
    }
}

.mastodon-pricing-table {
    display: flex;
    flex-direction: column;
    gap: 10px;
    width: 100%;
    position: relative;
    margin-bottom: 8000px;
    margin-top: 8000px;

    .headers {
        display: grid;
        grid-template-columns: 1fr 1fr 1fr 1fr;
        position: sticky;
        top: 0;
        margin-bottom: 10px;

        @media (max-width: 900px) {
            margin-bottom: 0;
        }

        .header {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            gap: 15px;
            background: var(--secondary-hover-color);
            padding: 15px;
            border-radius: 12px;
            margin: 5px;

            .title-group {
                .title {
                    font-size: 18px;
                    font-weight: 600;

                    @media (max-width: 900px) {
                        font-size: 14px;
                    }
                }

                .subtitle {
                    font-size: 16px;

                    @media (max-width: 900px) {
                        display: none;
                    }
                }

                margin: 0;
            }


            .price {
                font-size: 24px;
                font-weight: bold;

                @media (max-width: 900px) {
                    font-size: 18px;
                }
            }

            button  {
                width: 100%;
                box-sizing: border-box;

                @media (max-width: 900px) {
                    display: none;
                }
            }
        }

        @media (max-width: 900px) {
            grid-template-columns: 1fr 1fr 1fr;

            >:first-child {
                display: none;
            }
        }
    }

    .row {
        display: grid;
        grid-template-columns: 1fr 1fr 1fr 1fr;
        background: white;
        border: 1px solid var(--border-color);
        border-bottom: none;
        overflow: hidden;

        >div {
            padding: 20px;

            &:nth-child(even) {
                background: #FAFAFA;
            }
        }

        &.header {
            text-align: center;
        }

        .state {
            display: flex;
            align-items: center;
            justify-content: center;

            i {
                font-size: 24px;
                color: #6A43C7;
            }
        }

        &:first-child {
            border-radius: 12px 12px 0 0;
        }

        &:last-child {
            border-bottom: 1px solid var(--border-color);
            border-radius: 0 0 12px 12px;
        }

        @media (max-width: 900px) {
            grid-template-columns: 1fr 1fr 1fr;

            .description {
                grid-column: 1/4;
                text-align: center;
            }

            >div {
                padding: 20px;

                &:nth-child(even) {
                    background: white;
                }
            }
        }
    }
}