import {Component, Vue} from "vue-facing-decorator";
import {AutoWired, EmailUtils, ScopedUuid, UuidUtils} from "@groupk/horizon2-core";
import SidebarStateListener from "../../../../../shared/utils/SidebarStateListener";
import {DropdownComponent} from "@groupk/vue3-interface-sdk";
import {
	uuidScopeEstablishment,
	UuidScopeEstablishment
} from "@groupk/mastodon-core";
import {Router} from "@groupk/horizon2-front";
import {CashlessEstablishmentRepository} from "../../../../../shared/repositories/CashlessEstablishmentRepository";
import {RefundRepository} from "../../../../../shared/repositories/RefundRepository";
import {PublicRefundApiIn, PublicCashlessEstablishmentApiOut, BankAccountUtils} from "@groupk/mastodon-core";
import {CleaveDirective} from "../../../../../shared/directives/CleaveDirective";

@Component({
	directives: {
		cleave: CleaveDirective,
	},
	components: {
		dropdown: DropdownComponent
	}
})
export default class askRefund extends Vue {
	publicEstablishment!: PublicCashlessEstablishmentApiOut;

	data: {
		firstname: string,
		lastname: string,
		email: string,
		phone: string,
		chipPublicId: string,
		iban: string
		bic: string
	} = {
		firstname: '',
		lastname: '',
		email: '',
		phone: '',
		chipPublicId: '',
		iban: '',
		bic: ''
	};


	PublicRefundApiInDefinition = PublicRefundApiIn.__entityDefinition;
	cleaveChip = {
		blocks: [4, 4],
		delimiter: '-',
		uppercase: true
	}

	cleaveIban = {
		blocks: [4, 4, 4, 4, 4, 4, 4, 4, 4],
		delimiter: ' ',
		uppercase: true
	}

	cleavePhone = {
		blocks: [2, 2, 2, 2, 2],
		delimiter: ' ',
	}

	error: string|null = null;

	loading: boolean = true;
	areRefundsAvailable: boolean = false;
	creatingRefund: boolean = false;
	successRefundRequest: boolean = false;

	@AutoWired(CashlessEstablishmentRepository) accessor cashlessEstablishmentRepository!: CashlessEstablishmentRepository;
	@AutoWired(RefundRepository) accessor refundRepository!: RefundRepository;
	@AutoWired(SidebarStateListener) accessor sidebarStateListener!: SidebarStateListener;
	@AutoWired(Router) accessor router!: Router;

	beforeMount() {
		this.sidebarStateListener.setHiddenSidebar(true);
		this.sidebarStateListener.setHiddenTopBar(true);
	}

	async mounted() {
		let regexMatch = this.router.lastRouteRegexMatches;
		if (regexMatch && regexMatch[1]) {
			const establishmentUid = UuidUtils.scopedToVisual(regexMatch[1] as ScopedUuid<UuidScopeEstablishment>, uuidScopeEstablishment);

			this.publicEstablishment = (await this.cashlessEstablishmentRepository.callContract('getPublic', {establishmentUid: establishmentUid}, undefined)).success()

			if(this.publicEstablishment.refunds) {
				const startDate = new Date(this.publicEstablishment.refunds.startingDatetime);
				const endDate = new Date(this.publicEstablishment.refunds.endingDatetime);
				const now = new Date();

				if(now.getTime() >= startDate.getTime() && now.getTime() < endDate.getTime()) this.areRefundsAvailable = true;
			}
		}

		this.loading = false;
	}

	async sendRefundRequest() {
		this.error = null;

		if(!EmailUtils.isEmailValid(this.data.email)) {
			this.error = 'Veuillez renseigner un email valide';
			return;
		}

		const phoneRegex = /^(?:\d{10}|\d{3}[\s.-]?\d{3}[\s.-]?\d{4}|\(\d{3}\)[\s.-]?\d{3}[\s.-]?\d{4}|\d{2}[\s.-]?\d{3}[\s.-]?\d{3}|\d{4}[\s.-]?\d{3}[\s.-]?\d{3})$/;
		if(!phoneRegex.test(this.data.phone.replaceAll(' ', ''))) {
			this.error = 'Veuillez renseigner un numéro de tél valide';
			return;
		}

		// if(this.data.chipPublicId.length !== 9) {
		// 	this.error = 'Veuillez renseigner un numéro de puce valide';
		// 	return;
		// }

		const ibanInfos = BankAccountUtils.extractInfoFromIban(this.data.iban);
		if(!ibanInfos) {
			this.error = 'Veuillez renseigner un IBAN valide';
			return;
		}

		const bicInfos = BankAccountUtils.extractInfoFromBic(this.data.bic);
		if(!bicInfos) {
			this.error = 'Veuillez renseigner un BIC valide';
			return;
		}

		this.creatingRefund = true;

		const response = await this.refundRepository.callContract(
			'createPublic',
			{establishmentUid: this.publicEstablishment.uid},
			new PublicRefundApiIn({
				firstname: this.data.firstname,
				lastname: this.data.lastname,
				email: this.data.email,
				bic: bicInfos.sanitized,
				phoneNumber: this.data.phone,
				publicChipId: this.data.chipPublicId,
				iban: ibanInfos.sanitized
			})
		);

		if(response.isSuccess()) {
			this.successRefundRequest = true;
		} else {
			try {
				const error = response.error();
				if(!error) {
					this.error = 'Une erreur inconnue est survenue';
				} else if(error.error === 'refunds_creation_not_yet_allowed') {
					this.error = 'Les remboursement ne sont pas encore actifs pour cet établissement';
				} else if(error.error === 'refunds_not_setup') {
					this.error = 'Les remboursement ne sont pas encore actifs pour cet établissement';
				} else if(error.error === 'invalid_chip_id') {
					this.error = 'Le numéro de puce donné est invalide';
				}
			} catch(err) {
				this.error = 'Le serveur n\'a pas pu répondre correctement à la requête';
			}
		}

		this.creatingRefund = false;
	}

	isValidIBAN(iban: string): boolean {
		// Remove spaces and convert to uppercase
		iban = iban.replace(/\s+/g, '').toUpperCase();

		// Basic format check: 2 letters, 2 digits, followed by alphanumeric characters (up to 34 total characters)
		const ibanRegex = /^[A-Z]{2}[0-9]{2}[A-Z0-9]{11,30}$/;
		if (!ibanRegex.test(iban)) {
			return false; // Fails structure check
		}

		// Rearrange: Move the first 4 characters to the end
		const rearrangedIban = iban.slice(4) + iban.slice(0, 4);

		// Convert letters to numbers (A = 10, B = 11, ..., Z = 35)
		const numericIban = rearrangedIban
			.split('')
			.map(char => {
				const code = char.charCodeAt(0);
				return code >= 65 && code <= 90 ? (code - 55).toString() : char; // Convert A-Z to 10-35
			})
			.join('');

		// Perform MOD-97 operation
		let remainder = '';
		for (let i = 0; i < numericIban.length; i += 9) {
			remainder = (parseInt(remainder + numericIban.substring(i, i + 9), 10) % 97).toString();
		}

		// Valid IBANs must have a remainder of 1
		return remainder === '1';
	}

}