import {Component, Vue} from "vue-facing-decorator";
import {
	AutoWired, buildHttpRoutePathWithArgs,
	QueryFilterGroupClause,
	QueryOperator,
	TypedQuerySearch,
	VisualScopedUuid
} from "@groupk/horizon2-core";
import {StatsRepository} from "../../../../../shared/repositories/StatsRepository";
import {
	CashlessHttpTransactionContractSearchConfig, CustomerChipApiOut, MastodonHttpImagesContract,
	ProfileApiOut,
	TransactionApiOut,
	TransactionStats,
	TransactionStatus, UuidScopeCashless_profile, UuidScopeCashless_transaction,
} from "@groupk/mastodon-core";
import {PaymentMethodsRepository} from "../../../../../shared/repositories/PaymentMethodsRepository";
import {PaymentMethodApiOut} from "@groupk/mastodon-core";
import {UuidScopePayment_method} from "@groupk/mastodon-core";
import {ProfilesRepository} from "../../../../../shared/repositories/ProfilesRepository";
import {TransactionsRepository} from "../../../../../shared/repositories/TransactionsRepository";
import {CustomerChipRepository} from "../../../../../shared/repositories/CustomerChipRepository";
import {CustomerRepository} from "../../../../../shared/repositories/CustomerRepository";
import {CustomerApiOut, UuidScopeCustomer_customer} from "@groupk/mastodon-core";
import {AppState} from "../../../../../shared/AppState";
import {MainConfig} from "../../../../../shared/MainConfig";

@Component({})
export default class sampleReport extends Vue {
	paymentMethods!: PaymentMethodApiOut[];
	profiles!: ProfileApiOut[];
	transactions: TransactionApiOut[] = [];
	statistics!: TransactionStats;
	customerChipsPerTransaction: Record<VisualScopedUuid<UuidScopeCashless_transaction>, CustomerChipApiOut|null> = {};
	customers: CustomerApiOut[] = [];
	afterDate: string|null = null;
	beforeDate: string|null = null;
	excludedPaymentMethods: VisualScopedUuid<UuidScopePayment_method>[] = [];

	loading: boolean = false;

	@AutoWired(StatsRepository) accessor statsRepository!: StatsRepository;
	@AutoWired(PaymentMethodsRepository) accessor paymentMethodsRepository!: PaymentMethodsRepository;
	@AutoWired(ProfilesRepository) accessor profilesRepository!: ProfilesRepository;
	@AutoWired(CustomerChipRepository) accessor customerChipRepository!: CustomerChipRepository;
	@AutoWired(CustomerRepository) accessor customerRepository!: CustomerRepository;
	@AutoWired(TransactionsRepository) accessor transactionsRepository!: TransactionsRepository;
	@AutoWired(AppState) accessor appState!: AppState;
	@AutoWired(MainConfig) accessor mainConfig!: MainConfig;

	beforeMount() {
		this.loading = true;
	}

	async mounted() {
		const urlParams = new URLSearchParams(window.location.search);
		this.afterDate = urlParams.get('after');
		this.beforeDate = urlParams.get('before');
		if(!this.afterDate || !this.beforeDate) {
			throw new Error('missing_date_range');
		}

		const rawExcludedPaymentMethods = urlParams.get('excludedPaymentMethods');
		if(rawExcludedPaymentMethods) {
			this.excludedPaymentMethods = JSON.parse(rawExcludedPaymentMethods) as VisualScopedUuid<UuidScopePayment_method>[];
		}

		this.paymentMethods = (await this.paymentMethodsRepository.callContract('list', {establishmentUid: this.appState.requireUrlEstablishmentUid()}, {})).success();
		this.profiles = (await this.profilesRepository.callContract('list', {establishmentUid: this.appState.requireUrlEstablishmentUid()}, {})).success();
		this.statistics = (await this.statsRepository.callContract('fetch', {establishmentUid: this.appState.requireUrlEstablishmentUid()}, {
			filter: {
				group: QueryFilterGroupClause.AND,
				filters: [{
					name: 'creationDatetime',
					operator: QueryOperator.MORE_OR_EQUAL,
					value: this.afterDate
				}, {
					name: 'creationDatetime',
					operator: QueryOperator.LESS_OR_EQUAL,
					value: this.beforeDate
				}]
			}
		})).success();
		await this.searchAllTransactions(this.afterDate, this.beforeDate);

		setTimeout(() => {
			window.parent.postMessage('print-iframe-ready', '*');
		}, 1000); // Add 1s to ensure images load, got to find better way

		this.loading = false;
	}

	get visiblePaymentMethods() {
		return this.paymentMethods.filter((paymentMethod) => !this.excludedPaymentMethods.includes(paymentMethod.uid));
	}

	async searchAllTransactions(afterDate: string, beforeDate: string) {
		let transactions: TransactionApiOut[] = [];
		do {
			let params: TypedQuerySearch<typeof CashlessHttpTransactionContractSearchConfig> = {
				filter: {
					group: QueryFilterGroupClause.AND,
					filters: [{
						name: 'creationDatetime',
						operator: QueryOperator.MORE_OR_EQUAL,
						value: afterDate
					}, {
						name: 'creationDatetime',
						operator: QueryOperator.LESS_OR_EQUAL,
						value: beforeDate
					}]
				}
			}

			if(transactions[49]) {
				params.cursorAfter = transactions[49].uid
			}

			transactions = (await this.transactionsRepository.callContract('search', {establishmentUid: this.appState.requireUrlEstablishmentUid()}, params)).success();
			this.transactions = this.transactions.concat(transactions);
		} while(transactions.length === 50);

		for(let transaction of this.transactions) {
			if(transaction.paymentMethod !== null) {
				const customerChip = ((await this.customerChipRepository.callContract('search', {establishmentUid: this.appState.requireUrlEstablishmentUid()}, {
					filter: {
						group: QueryFilterGroupClause.AND,
						filters: [{
							name: 'publicChipId',
							value: transaction.chipVisualId ?? '',
							operator: QueryOperator.EQUAL
						}, {
							name: 'startingDatetime',
							value: transaction.creationDatetime,
							operator: QueryOperator.LESS_OR_EQUAL
						}, {
							group: QueryFilterGroupClause.OR,
							filters: [{
								name: 'endingDatetime',
								value: null,
								operator: QueryOperator.EQUAL
							}, {
								name: 'endingDatetime',
								value: transaction.creationDatetime,
								operator: QueryOperator.MORE_OR_EQUAL
							}]
						}]
					}
				})).success()[0] ?? null);

				this.customerChipsPerTransaction[transaction.uid] = customerChip;
				if(customerChip) this.customers.push((await this.customerRepository.callContract('getOne', {establishmentUid: this.appState.requireUrlEstablishmentUid(), customerUid: customerChip.customerUid}, undefined)).success())
			}
		}
	}

	getRefillsForPaymentMethod(paymentMethodUid: VisualScopedUuid<UuidScopePayment_method>|null) {
		let total = 0;
		for(let point of this.statistics.points) {
			for (let perProfile of point.perProfile) {
				for(let perEstablishmentAccount of perProfile.perEstablishmentAccount) {
					for(let perPaymentMethod of perEstablishmentAccount.perPaymentMethod) {

						if(
							!perPaymentMethod.paymentMethodUid ||
							(paymentMethodUid && perPaymentMethod.paymentMethodUid !== paymentMethodUid)||
							!this.visiblePaymentMethods.find((paymentMethod)=> perPaymentMethod.paymentMethodUid === paymentMethod.uid)
						) continue;

						for(let perProduct of perPaymentMethod.perProduct) {
							for(let perPrice of perProduct.perPrice) {
								for(let perCurrency of perPrice.perCurrency) {
									if(perCurrency.currency === 0) {
										if(perCurrency.totalAmount > 0) {
											total += perCurrency.totalAmount;
										}
									}
								}
							}
						}
					}
				}
			}
		}
		return total;
	}

	getRefundsForPaymentMethod(paymentMethodUid: VisualScopedUuid<UuidScopePayment_method>|null) {
		let total = 0;
		for(let point of this.statistics.points) {
			for (let perProfile of point.perProfile) {
				for(let perEstablishmentAccount of perProfile.perEstablishmentAccount) {
					for(let perPaymentMethod of perEstablishmentAccount.perPaymentMethod) {

						if(
							!perPaymentMethod.paymentMethodUid ||
							(paymentMethodUid && perPaymentMethod.paymentMethodUid !== paymentMethodUid) ||
							!this.visiblePaymentMethods.find((paymentMethod)=> perPaymentMethod.paymentMethodUid===paymentMethod.uid)
						) continue;

						for(let perProduct of perPaymentMethod.perProduct) {
							for(let perPrice of perProduct.perPrice) {
								for(let perCurrency of perPrice.perCurrency) {
									if(perCurrency.currency === 0) {
										if(perCurrency.totalAmount < 0) {
											total += perCurrency.totalAmount;
										}
									}
								}
							}
						}
					}
				}
			}
		}
		return total;
	}

	getPaymentsForProfile(profileUid: VisualScopedUuid<UuidScopeCashless_profile>|null) {
		let total = 0;
		for(let point of this.statistics.points) {
			for (let perProfile of point.perProfile) {

				if(profileUid && perProfile.profileUid !== profileUid) continue;

				for(let perEstablishmentAccount of perProfile.perEstablishmentAccount) {
					for(let perPaymentMethod of perEstablishmentAccount.perPaymentMethod) {

						if(perPaymentMethod.paymentMethodUid) continue;

						for(let perProduct of perPaymentMethod.perProduct) {
							for(let perPrice of perProduct.perPrice) {
								for(let perCurrency of perPrice.perCurrency) {
									if(perCurrency.currency === 0) {
										if(perCurrency.totalAmount < 0) {
											total += perCurrency.totalAmount;
										}
									}
								}
							}
						}
					}
				}
			}
		}
		return total;
	}


	getEstablishmentImageUrl() {
		if(!this.appState.establishment || !this.appState.establishment.mainImageUpload) return '';
		return this.mainConfig.configuration.mastodonApiEndpoint + buildHttpRoutePathWithArgs(MastodonHttpImagesContract.getPublicImage, {
			establishmentUid: this.appState.establishment.uid,
			imageId: this.appState.establishment.mainImageUpload.uid,
			options: {},
			dimensions: {width: 128},
			extension: 'png',
			resizeType: 'contain',
			revision: this.appState.establishment.mainImageUpload.lastUpdateDatetime
		});
	}

	getRefillsTransactionsForPaymentMethod(paymentMethodUid: VisualScopedUuid<UuidScopePayment_method>) {
		return this.transactions.filter((transaction) => {
			const transactionAmount = transaction.signedAmount;
			return transaction.paymentMethod === paymentMethodUid
				&& transactionAmount > 0
				&& (transaction.status === TransactionStatus.WRITTEN || transaction.status === TransactionStatus.WRITTEN_NETWORK);
		});
	}

	getRefundTransactionsForPaymentMethod(paymentMethodUid: VisualScopedUuid<UuidScopePayment_method>) {
		return this.transactions.filter((transaction) => {
			const transactionAmount = transaction.signedAmount;
			return (transaction.rectifiedPaymentMethod ?? transaction.paymentMethod) === paymentMethodUid
				&& transactionAmount < 0
				&& (transaction.status === TransactionStatus.WRITTEN || transaction.status === TransactionStatus.WRITTEN_NETWORK);
		});
	}

	requireCustomerWithUid(customerUid: VisualScopedUuid<UuidScopeCustomer_customer>|null) {
		const customer = this.customers.find((customer) => customer.uid === customerUid);
		if(!customer) throw new Error('missing_customer');
		return customer.lastname + ' ' + customer.firstname;
	}

	requireTransactionCustomerUid(transactionUid: VisualScopedUuid<UuidScopeCashless_transaction>): VisualScopedUuid<UuidScopeCustomer_customer> {
		const customerChip = this.customerChipsPerTransaction[transactionUid];
		if(!customerChip) throw new Error('missing_data');
		return customerChip.customerUid;
	}
}