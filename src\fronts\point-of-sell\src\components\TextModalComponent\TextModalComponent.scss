.text-modal-component {
    position: fixed;
    inset: 0;
    display: flex;
    align-items: flex-start;
    justify-content: center;
    background: rgba(0, 0, 0, 0.7);
    padding: 60px 16px;
    z-index: 500;

    .modal {
        display: flex;
        flex-direction: column;
        gap: 10px;
        width: 100%;
        padding: 20px;
        max-width: max(40%, 300px);
        background: #F2F4F7;
        border-radius: 12px;

        .head {
            display: flex;
            align-items: center;
            justify-content: space-between;

            .title {
                font-size: 18px;
                font-weight: bold;
            }

            .close {
                display: flex;
                align-items: center;
                justify-content: center;
                height: 42px;
                width: 42px;
                background: #E8E8E8;
                border-radius: 50%;
            }
        }

        textarea {
            height: 200px;
            padding: 20px;
            font-size: 18px;
            font-weight: 500;
            resize: none;
        }

        .button {
            all: unset;
            display: flex;
            align-items: center;
            justify-content: center;
            height: 56px;
            width: 100%;
            border-radius: 8px;
            background: var(--primary-color);
            color: var(--primary-text-color);
            font-family: Mont<PERSON>rat, sans-serif;
            font-size: 15px;
            font-weight: 700;
            cursor: pointer;

            &:hover {
                background: var(--primary-button-hover-color);
                color: var(--primary-text-color);
            }

            &.disabled {
                opacity: 0.5;
                pointer-events: none;
            }
        }
    }
}