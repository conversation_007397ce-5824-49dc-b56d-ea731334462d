import {Component, Prop, Vue} from "vue-facing-decorator";
import {PosState} from "../../model/PosState";
import {OrderPaymentStatus, PaymentMethodApiOut, PaymentMethodType, PurchaseApiOut} from "@groupk/mastodon-core";
import KeypadComponent from "../KeypadComponent/KeypadComponent.vue";
import {KeypadKey} from "../KeypadComponent/KeypadComponent";
import {AutoWired, Uuid} from "@groupk/horizon2-core";
import {randomUUID} from "@groupk/horizon2-front";
import {PaymentManager, PendingPayment} from "../../class/payment/PaymentManager";
import {ErrorHandler} from "../../class/ErrorHandler";
import {LocalOrderRepository} from "../../repositories/LocalOrderRepository";
import PaymentComponent from "../PaymentComponent/PaymentComponent.vue";

type OrderSplit = {
    uid: Uuid,
    amount: number,
    payed: boolean
}

@Component({
    components: {
        'keypad': KeypadComponent,
        'payment': PaymentComponent,
    },
    emits: ['close']
})
export default class OrderAdvancedPaymentInterfaceComponent extends Vue {
    @Prop() posState!: PosState;

    selectedPaymentMethod!: PaymentMethodApiOut;
    currentPayment: {
        amount: number,
        purchases: PurchaseApiOut[],
        splits: OrderSplit[]
    } = {
        amount: 0,
        splits: [],
        purchases: []
    };

    partsAmount: number = 0;
    pendingPaymentData: PendingPayment|null = null;
    splits: OrderSplit[]|null = null;

    mobileShowNext: boolean = false;

    showKeypad: boolean = false;
    activeTab: 'PRODUCTS'|'CUSTOM'|'SPLIT' = 'PRODUCTS';

    @AutoWired(ErrorHandler) accessor errorHandler!: ErrorHandler;
    @AutoWired(LocalOrderRepository) accessor localOrderRepository!: LocalOrderRepository;

    beforeMount() {
        this.selectedPaymentMethod = this.posState.paymentMethods.filter((method) => method.type !== PaymentMethodType.WEB)[0];
    }

    changeActiveTab(tab: 'PRODUCTS'|'CUSTOM'|'SPLIT') {
        if(this.activeTab === tab) return;
        this.activeTab = tab;
        this.mobileShowNext = false;
        this.currentPayment = {
            amount: 0,
            splits: [],
            purchases: []
        };
    }

    areAllToggled(): boolean {
        if (!this.posState.currentOrder) return false;
        return this.posState.currentOrder.order.purchases.filter((purchase) => !this.isPurchasePayed(purchase)).every(p => this.isPurchaseToggled(p));
    }

    toggleAll() {
        if (!this.posState.currentOrder) return;
        const allPurchases = this.posState.currentOrder.order.purchases.filter((purchase) => !this.isPurchasePayed(purchase));

        if (this.areAllToggled()) {
            this.currentPayment.purchases = [];
            this.currentPayment.amount = 0;
        } else {
            this.currentPayment.purchases = [...allPurchases];
            this.currentPayment.amount = allPurchases.reduce((acc, purchase) => {
                return acc + this.getOrderTotals.purchases.perRootPurchase[purchase.uid].withTaxes
            }, 0);
        }
    }

    togglePurchase(purchase: PurchaseApiOut) {
        const index = this.currentPayment.purchases.findIndex((existingPurchase) => existingPurchase.uid === purchase.uid);
        if(index === -1) this.currentPayment.purchases.push(purchase);
        else this.currentPayment.purchases.splice(index, 1);

        this.currentPayment.amount = this.currentPayment.purchases.reduce((acc, purchase) => {
            return acc + this.getOrderTotals.purchases.perRootPurchase[purchase.uid].withTaxes
        }, 0);
    }

    isPurchaseToggled(purchase: PurchaseApiOut) {
        return this.currentPayment.purchases.findIndex((existingPurchase) => existingPurchase.uid === purchase.uid) !== -1;
    }

    isPurchasePayed(purchase: PurchaseApiOut) {
        if(!this.posState.currentOrder) return false;
        return this.posState.currentOrder.order.payments.find((payment) => payment.purchases.includes(purchase.uid)) !== undefined;
    }

    toggleSplit(split: OrderSplit) {
        const index = this.currentPayment.splits.findIndex((existingSplit) => existingSplit.uid === split.uid);
        if(index === -1) this.currentPayment.splits.push(split);
        else this.currentPayment.splits.splice(index, 1);

        this.currentPayment.amount = this.currentPayment.splits.reduce((acc, split) => acc + split.amount, 0);
    }

    isSplitToggled(split: OrderSplit) {
        return this.currentPayment.splits.findIndex((existingSplit) => existingSplit.uid === split.uid) !== -1;
    }


    get getOrderTotals(){
        if(!this.posState.currentOrder) throw new Error('no_current_order');
        return this.posState.orderExecutorModel.getOrderTotals(this.posState.currentOrder.order);
    }

    updatePartsAmount(key: KeypadKey) {
        if (key < 10) {
            if (this.partsAmount === 0) {
                this.partsAmount = key;
            } else {
                this.partsAmount = parseInt(this.partsAmount + '' + key);
            }
        } else {
            if (key === KeypadKey.BACKSPACE) {
                if ((this.partsAmount + "").length === 1) {
                    this.partsAmount = 0;
                } else {
                    this.partsAmount = parseInt((this.partsAmount + "").slice(0, -1));
                }
            } else if (key === KeypadKey.TRASH) {
                this.partsAmount = 0;
            } else if (key === KeypadKey.ZERO_ZERO) {
                this.updatePaymentAmount(KeypadKey.ZERO);
                this.updatePaymentAmount(KeypadKey.ZERO);
            }
        }
    }

    updatePaymentAmount(key: KeypadKey) {
        if(!this.posState.currentOrder) return;

        if (key < 10) {
            if (this.currentPayment.amount === 0) {
                this.currentPayment.amount = key;
            } else {
                this.currentPayment.amount = parseInt(this.currentPayment.amount + '' + key);
            }
        } else {
            const leftToPay = this.posState.orderExecutorModel.getOrderTotals(this.posState.currentOrder.order).leftToPay;

            if (key === KeypadKey.MAX) {
                this.currentPayment.amount = leftToPay;
            } else if (key === KeypadKey.QUARTER) {
                this.currentPayment.amount = Math.round(leftToPay * 0.25);
            } else if (key === KeypadKey.HALF) {
                this.currentPayment.amount = Math.round(leftToPay * 0.5);
            } else if (key === KeypadKey.BACKSPACE) {
                if ((this.currentPayment.amount + "").length === 1) {
                    this.currentPayment.amount = 0;
                } else {
                    this.currentPayment.amount = parseInt((this.currentPayment.amount + "").slice(0, -1));
                }
            } else if (key === KeypadKey.TRASH) {
                this.currentPayment.amount = 0;
            } else if (key === KeypadKey.ZERO_ZERO) {
                this.updatePaymentAmount(KeypadKey.ZERO);
                this.updatePaymentAmount(KeypadKey.ZERO);
            }
        }
    }

    generateSplits() {
        if(!this.posState.currentOrder) return;
        const orderLeftToPay = this.posState.orderExecutorModel.getOrderTotals(this.posState.currentOrder.order).leftToPay;

        if (this.partsAmount < 1) {
            throw new Error('Number of parts must be at least 1');
        }
        if (orderLeftToPay < 0) {
            throw new Error('Amount must be non-negative');
        }

        // Convert to cents to avoid floating point precision issues

        // Calculate the base amount per part (in cents)
        const baseAmountInCents = Math.floor(orderLeftToPay / this.partsAmount);

        // Calculate remaining cents after even distribution
        const remainingCents = orderLeftToPay % this.partsAmount;

        // Create array of parts
        const result: number[] = new Array(this.partsAmount).fill(baseAmountInCents);

        // Distribute remaining cents one by one to the first N parts
        for (let i = 0; i < remainingCents; i++) {
            result[i] += 1;
        }

        // Convert back to decimal currency
       this.splits = result.map(amount => {
            return {
                uid: randomUUID(),
                amount: amount,
                payed: false
            }
        });
    }

    get willPaymentBeAutomatic() {
        if(!this.selectedPaymentMethod) return false;
        const paymentManager = new PaymentManager(this.posState);
        return paymentManager.willPaymentBeAutomatic(this.selectedPaymentMethod);
    }

    async pay() {
        if(!this.posState.currentOrder) return;
        if(!this.selectedPaymentMethod) return;

        const paymentManager = new PaymentManager(this.posState);
        const data = await paymentManager.addPaymentToCurrentOrder(
            this.currentPayment.amount,
            this.selectedPaymentMethod,
            this.currentPayment.purchases.map((purchase) => purchase.uid)
        );

        if(data.payment.status === OrderPaymentStatus.SUCCESS) {
            this.paymentSuccessHandler();
        } else {
            this.pendingPaymentData = data;
        }
    }

    paymentSuccessHandler() {
        for(let split of this.currentPayment.splits) {
            split.payed = true;
        }

        this.currentPayment = {
            amount: 0,
            splits: [],
            purchases: []
        };

        if(this.getOrderTotals.leftToPay === 0) this.close();
        else this.mobileShowNext = false;

        this.showKeypad = false;
    }

    close() {
        this.$emit('close');
    }
}