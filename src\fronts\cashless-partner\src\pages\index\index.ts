import {Component, Vue} from "vue-facing-decorator";
import {
	ContentHeaderComponent,
	ContentHeaderParameters, ForbiddenMessageComponent,
	LayoutContentWithRightPanelComponent
} from "@groupk/vue3-interface-sdk";
import {AutoWired, SearchUtils, UuidUtils} from "@groupk/horizon2-core";
import SidebarStateListener from "../../../../../shared/utils/SidebarStateListener";
import {ProductsRepository} from "../../../../../shared/repositories/ProductsRepository";
import {CategoriesRepository} from "../../../../../shared/repositories/CategoriesRepository";
import {ProfilesRepository} from "../../../../../shared/repositories/ProfilesRepository";
import {
	ApplicationPermission, CashlessHttpProfileContract, CashlessHttpSimpleProductCategoryContract,
	CashlessHttpSimpleProductContract,
	CashlessHttpTransactionContract,
	EstablishmentAccountPermissionModel,
	ProfileApiOut,
	SimpleProductApiOut,
	SimpleProductCategoryApiOut
} from "@groupk/mastodon-core";
import {EstablishmentUrlBuilder} from "../../../../../shared/utils/EstablishmentUrlBuilder";
import {AppState} from "../../../../../shared/AppState";
import {ComponentUtils} from "../../../../../shared/utils/ComponentUtils";

@Component({
	components: {
		'content-header': ContentHeaderComponent,
		'layout': LayoutContentWithRightPanelComponent,
		'forbidden-message': ForbiddenMessageComponent,
	}
})
export default class IndexView extends Vue {
	products: SimpleProductApiOut[] = [];
	categories: SimpleProductCategoryApiOut[] = [];
	profiles: ProfileApiOut[] = [];
	forbidden: boolean = false;

	headerParameters: ContentHeaderParameters = {
		header: 'Bien commencer',
		subtitle: 'Configurez votre événement Cashless facilement en suivant ces trois étapes',
		actions: [{
			type: 'SIMPLE_ACTION',
			name: 'Interface de rechargement',
			icon: 'fa-regular fa-arrow-up-right-from-square',
			callback: this.getRefillInterfaceUrl
		}, {
			type: 'SIMPLE_ACTION',
			name: 'Formulaire de remboursement',
			icon: 'fa-regular fa-arrow-up-right-from-square',
			callback: () => {
				window.open(this.buildUrl('/ask-refund'), '_blank');
			}
		}],
		hideSearch: true,
		searchPlaceholder: 'Rechercher un appareil'
	}

	faq: {id: string, question: string, response: string, opened: boolean}[] = [{
		id: 'one',
		question: 'À quoi servent les catégories ?',
		response: 'Les catégories permettent de faciliter l\'utilisation des terminaux Cashless en rangeant vos produits par catégorie. Créez autant de catégorie qu\'il vous paraît nécessaire pour une utilisation efficace des terminaux.',
		opened: false
	}, {
		id: 'one',
		question: 'À quoi servent les points de vente ?',
		response: 'Un point de vente correspond à un lieu spécifique de vente qui regroupe des catégories de produits. Par exemple il est possible d\'imaginer un point de vente "bar" dans lequel on vend les produits de la catégorie "alcool" ainsi qu\'un point de vente "restauration" ou l\'on vend des produits de la catégorie "food". Vous pourrez par la suite spécifier pour chaque terminal Cashless dans quel point de vente il va être utilisé pour n\'afficher que les catégories concernées.',
		opened: false
	}];
	faqSearch: string = '';

	loading: boolean = true;

	@AutoWired(ProductsRepository) accessor productsRepository!: ProductsRepository;
	@AutoWired(CategoriesRepository) accessor categoriesRepository!: CategoriesRepository;
	@AutoWired(ProfilesRepository) accessor profilesRepository!: ProfilesRepository;
	@AutoWired(SidebarStateListener) accessor sidebarStateListener!: SidebarStateListener;
	@AutoWired(AppState) accessor appState!: AppState;

	beforeMount() {
		this.sidebarStateListener.setHiddenSidebar(false);
		this.sidebarStateListener.setMinimizedSidebar(false);
	}

	async mounted() {
		if(!ComponentUtils.hasPermissions((ownedPermissions: ApplicationPermission[]) => {
			return EstablishmentAccountPermissionModel.hasPermissionsForContracts(ownedPermissions, [
				CashlessHttpSimpleProductContract.list,
				CashlessHttpSimpleProductCategoryContract.list,
				CashlessHttpProfileContract.list,
			])
		})) {
			this.forbidden = true;
			this.loading = false;
			return;
		}

		this.products = (await this.productsRepository.callContract('list', {establishmentUid: this.appState.requireUrlEstablishmentUid()}, undefined)).success();
		const categories = (await this.categoriesRepository.callContract('list', {establishmentUid: this.appState.requireUrlEstablishmentUid()}, undefined)).success();
		this.categories = categories.filter((category) => category.parent !== null);
		this.profiles = (await this.profilesRepository.callContract('list', {establishmentUid: this.appState.requireUrlEstablishmentUid()}, undefined)).success();

		this.loading = false;
	}

	getRefillInterfaceUrl() {
		return window.open('https://cashless.weecop.fr/establishment/' + UuidUtils.visualToScoped(this.appState.requireUrlEstablishmentUid()) + '/', '_blank');
	}

	get filteredFaq() {
		return SearchUtils.searchInTab(this.faq, (faq) => {
			return [faq.question, faq.response];
		}, this.faqSearch);
	}

	buildUrl(path: string) {
		return EstablishmentUrlBuilder.buildUrl(path);
	}
}