#index-page {

    .selected-link {
        padding: 40px;

        @media screen and (max-width: 900px) {
            padding: 20px;
        }

        .header {
            display: flex;
            flex-direction: column;

            .action {
                display: flex;
                justify-content: flex-end;
                width: 100%;
            }

            h2 {
                .copy-id {
                    padding: 8px;
                    border-radius: 8px;
                    font-size: 20px;
                    cursor: pointer;

                    &:hover {
                        background: var(--secondary-hover-color);
                    }

                    &.copied {
                        background: #5CB85C;
                        color: white;
                    }
                }
            }

            .label-area {
                position: relative;
                flex-shrink: 0;
                display: flex;
                gap: 5px;
                width: 100%;

                .labels {
                    display: flex;
                    gap: 5px;
                }

                .add-label {
                    cursor: pointer;
                    user-select: none;
                }
            }
        }
    }

    .link-id-column {
        @media screen and (max-width: 900px) {
            word-break: break-word;
        }
    }

    .target-uri {
        width: 240px;
        overflow: hidden;
        display: inline-block;
        white-space: nowrap;
        text-overflow: ellipsis;
    }

    .column-header-targetUri {
        width: 240px;
    }

    .platforms {
        display: flex;
        gap: 5px;
    }

    .no-data-container {
        display: flex;
        justify-content: center;
        align-items: center;

        .first-link-button {
            padding: 10px 15px;
            border-radius: 8px;

            &:hover {
                background: var(--secondary-hover-color);
            }
        }
    }


    .link-td {
        @media (hover: hover) {
            &:hover {
                .link-id-column {
                    i {
                        opacity: 1;
                    }
                }
            }
        }

        .link-id-column {
            display: flex;
            align-items: center;
            gap: 5px;
            position: relative;
            height: 100%;

            i {
                position: absolute;
                right: -40px;
                z-index: 99;
                opacity: 0;
                border: 1px solid var(--border-color);
                padding: 8px;
                border-radius: 8px;

                &:hover {
                    background: var(--secondary-hover-color);
                }
            }
        }
    }

    .tags {
        display: flex;
        gap: 5px;
        align-items: center;
    }

    .qr-code-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 10px;
        margin-top: 20px;

        .qrcode {
            width: 50%;
        }
    }


    .target-groups {
        display: flex;
        flex-direction: column;
        gap: 10px;
        margin-top: 20px;
        width: 100%;

        .target-group {
            border-radius: 8px;
            border: 1px solid #E2E2E2;
            background: #FFF;
            font-size: 14px;

            .top {
                display: flex;
                justify-content: space-between;
                align-items: center;

                border-bottom: 1px solid #E2E2E2;
                font-weight: bold;

                .actions {
                    display: flex;
                    align-items: center;
                    gap: 5px;
                    margin: -10px 0;

                    .action {
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        height: 32px;
                        width: 32px;
                        border-radius: 4px;
                        cursor: pointer;

                        &:hover {
                            background: #EBEBEB;
                        }

                        &.red:hover {
                            background: #ff4b4b;
                            color: white;
                        }
                    }
                }
            }

            .top, .content {
                padding: 15px 20px;
            }

            .content {
                display: flex;
                justify-content: space-between;
                word-break: break-all;

                .grid {
                    display: grid;
                    grid-template-columns: 100px 1fr;
                    grid-gap: 20px;

                    i {
                        margin-right: 4px;
                    }

                    a {
                        color: black;
                        font-weight: 500;
                    }
                }
            }

            .title {
                font-weight: 500;
            }

            .left, .right {
                display: flex;
                flex-direction: column;
                gap: 5px;

                .actions {
                    display: flex;
                    gap: 5px;
                }
            }
        }
    }

    .error {
        margin-top: 15px;
        text-align: right;
        color: red;
    }

    .link-form {
        h3 {
            margin: 0;
        }
    }

    .empty-right-panel {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 20px;
        justify-content: center;
        height: 100%;
        color: #636363;
        font-size: 14px;
        text-align: center;

        img {
            width: 150px;
        }
    }

    .close {
        display: none;
        margin-bottom: 20px;
        cursor: pointer;
        user-select: none;
    }

    @media screen and (max-width: 1400px) {
        .close {
            display: flex;
            gap: 10px;
            align-items: center;
        }
    }

    .bottom-actions {
        display: flex;
        flex-direction: column;
        gap: 20px;
        margin-top: 20px;
    }

    .fa-android {
        color: #2EDF85;
    }

    .fa-windows {
        color: #00AFEF;
    }

    .content.qr {
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 40px 0;

        img {
            height: 200px;
            image-rendering: pixelated;
        }
    }

    .no-wrap {
        white-space: nowrap;
    }

    .table-scroll-wrapper {
        overflow: auto;

        .nowrap {
            white-space: nowrap
        }
    }
}