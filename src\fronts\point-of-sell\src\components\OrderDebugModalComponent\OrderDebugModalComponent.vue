<script lang="ts" src="./OrderDebugModalComponent.ts" />

<style lang="sass" scoped>
@import './OrderDebugModalComponent.scss'
</style>

<template>
    <div class="order-debug-modal-component">
        <div class="modal-dimmer" @click.stop="close()">
            <div class="modal" @click.stop>
                <div class="group">
                    <span class="title"> UID de la commande </span>
                    {{ getFormattedUid() }}
                </div>

                <div class="group" v-if="'transferred' in localOrder">
                    <span class="title"> Transféré </span>
                    {{ localOrder.transferred ? 'Oui' : 'Non'}}
                </div>

                <div class="group" v-if="'lastLocalUpdateDate' in localOrder">
                    <span class="title"> lastLocalUpdateDate </span>
                    {{ $filters.Date(getLastLocalUpdateDate()) }}
                    {{ $filters.Hour(getLastLocalUpdateDate()) }}
                </div>

                <div class="group" v-if="'toCountry' in localOrder">
                    <span class="title"> Commande brute </span>
                    {{ localOrder }}
                </div>

                <button class="big grey button" @click="close()">
                    Fermer
                </button>
            </div>
        </div>
    </div>
</template>