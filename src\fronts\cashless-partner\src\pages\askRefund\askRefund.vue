<script lang="ts" src="./askRefund.ts">
</script>

<style lang="sass">
@use './askRefund.scss' as *
</style>

<template>
    <div id="ask-refund-page">
        <div class="loading-container" v-if="loading">
            <div class="loader"></div>
        </div>

        <div class="success" v-else-if="!areRefundsAvailable">
            <div class="form-error">
                <i class="fa-solid fa-circle-xmark"></i>
                <div class="details">
                    <span class="title"> Les remboursements ne sont pas actifs </span>
                    <span class="description"> L'établissement n'autorise pas les remboursements pour le moment </span>
                </div>
            </div>
        </div>

        <div class="success" v-else-if="successRefundRequest">
            <div class="form-success">
                <i class="fa-solid fa-circle-check"></i>
                <div class="details">
                    <span class="title"> C'est tout bon ! </span>
                    <span class="description"> Votre demande de remboursement a bien été prise en compte. </span>
                </div>
            </div>
        </div>

        <div class="form" v-else>
            <div class="header">
                <span class="title"> Demande de remboursement </span>
                <span class="subtitle"> Vous pouvez demander le remboursement des fonds restants sur vos supports Cashless en renseignant l'IBAN sur lequel vous souhaitez être remboursé </span>
            </div>

            <div class="input-group">
                <label>
                    Prénom
                    <span class="characters"> {{ data.firstname.length }}/{{ PublicRefundApiInDefinition.getFieldString('firstname').maxLength }} </span>
                </label>
                <input type="text" v-model="data.firstname" placeholder="Prénom" :maxlength="PublicRefundApiInDefinition.getFieldString('firstname').maxLength ?? '999'" />
            </div>

            <div class="input-group">
                <label>
                    Nom
                    <span class="characters"> {{ data.lastname.length }}/{{ PublicRefundApiInDefinition.getFieldString('lastname').maxLength }} </span>
                </label>
                <input type="text" v-model="data.lastname" placeholder="Nom" :maxlength="PublicRefundApiInDefinition.getFieldString('lastname').maxLength ?? '999'" />
            </div>

            <div class="input-group">
                <label> Email </label>
                <input type="text" v-model="data.email" placeholder="Email de contact" />
            </div>

            <div class="input-group">
                <label> Numéro de tél. </label>
                <input type="text" placeholder="Numéro de tél. de contact" v-cleave="{obj: data, key: 'phone', options: cleavePhone}" />
            </div>

            <div class="input-group">
                <label> Code affiché sur le support Cashless </label>
                <input type="text" v-model="data.chipPublicId" />
            </div>

            <div class="input-group">
                <label> IBAN </label>
                <input type="text" placeholder="IBAN du compte à créditer" v-cleave="{obj: data, key: 'iban', options: cleaveIban}" />
            </div>

            <div class="input-group">
                <label> BIC </label>
                <input type="text" placeholder="BIC de votre banque" v-model="data.bic" />
            </div>


            <div class="form-error" v-if="error">
                <i class="fa-solid fa-exclamation-circle"></i>
                <div class="details">
                    <span class="title"> Erreur </span>
                    <span class="description">{{ error }}</span>
                </div>
            </div>

            <div class="buttons">
                <button class="button" @click="sendRefundRequest()"> Valider ma demande </button>
            </div>

            <div class="credits">
                Cashless par <a href="https://pro.weecop.fr/" target="_blank"> Weecop </a>
            </div>
        </div>
    </div>
</template>