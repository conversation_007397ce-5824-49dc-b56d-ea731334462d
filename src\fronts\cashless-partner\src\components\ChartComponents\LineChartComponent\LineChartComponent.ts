import {Component, Prop, Ref, Vue, Watch} from "vue-facing-decorator";
import {ChartConfiguration, ChartData} from "chart.js/dist/types";
import {MoneyFilter} from "../../../../../../shared/filters/Money";
import {Chart, registerables} from "chart.js";
import {ExternalTooltipHandler} from "../../../../../../shared/utils/ExternalTooltipHandler";
import {VueIgnore} from "@groupk/horizon2-front";

@Component({})
export default class LineChartComponent extends Vue {
	@Prop() data!: ChartData;
	@Prop({default: []}) lineAtIndex!: number[];

	@VueIgnore chart: Chart|null = null;

	@Ref() canvas!: HTMLCanvasElement;

	@Watch('data', {deep: true})
	dataWatch() {
		if(this.chart){
			this.addGradient();
			this.chart.data = this.data;
			this.chart.update();
		}

	}

	@Watch('lineAtIndex', {deep: true})
	lineAtIndexWatch() {
		if(this.chart) {
			const plugins = this.chart.options.plugins as Record<string, unknown>;
			if(plugins) plugins['verticalLinePlugin'] = {
				lineAtIndex: this.lineAtIndex
			};
			this.chart?.update();
		}
	}

	mounted() {
		Chart.register(...registerables);
		this.initChart();
	}

	unmount(){
		if(this.chart) this.chart.destroy();
	}

	addGradient() {
		const context = this.canvas.getContext('2d');
		if(!context) return;

		const gradient = context.createLinearGradient(0, 0, 0, 100);
		gradient.addColorStop(0, 'rgba(0,61,255,0.1)');
		gradient.addColorStop(1, 'rgba(0,61,255,0)');

		for(let dataset of this.data.datasets) {
			dataset.backgroundColor = gradient;
		}
	}

	initChart() {
		this.addGradient();

		try {
			const config = {
				type: 'line',
				data: this.data,
				options: {
					maintainAspectRatio: false,
					elements: {
						point:{
							radius: 0
						}
					},
					scales: {
						x: {
							border: {
								display: false
							},
							grid: {
								color: 'rgba(0, 0, 0, 0)'
							}
						},
						y: {
							ticks: {
								display: false,
								count: 5
							},
							border: {
								display: false
							},
							grid: {
								color: '#f2f4f7',
							}
						}
					},
					interaction: {
						intersect: false,
						mode: 'index',
					},
					plugins: {
						legend: {
							display: false // This hides all text in the legend and also the labels.
						},
						verticalLinePlugin: {
							lineAtIndex: this.lineAtIndex
						},
						tooltip: {
							callbacks: {
								label: function(context : any) {
									return (context as any).dataset.label + ' ' + MoneyFilter((context as any).raw * 100);
								}
							},
							enabled: false,
							position: 'nearest',
							external: ExternalTooltipHandler.handler as never
						}
					}
				},
				plugins: [this.verticalLinePlugin]
			} as ChartConfiguration;

			this.chart = new Chart(this.canvas, config);
		} catch(err) {
			console.log(err);
		}
	}

	verticalLinePlugin = {
		id: 'verticalLinePlugin',
		beforeDatasetsDraw: (chart: Chart, args: Record<string, any>, options: Record<string, any>) => {
			const xScale = chart.scales['x'];
			const yScale = chart.scales['y'];

			for(let index of options.lineAtIndex) {
				const xPos = xScale.getPixelForValue(index);

				// Draw the line
				chart.ctx.beginPath();
				chart.ctx.moveTo(xPos, yScale.bottom);
				chart.ctx.strokeStyle = '#ff0000';
				chart.ctx.lineTo(xPos, yScale.top);
				chart.ctx.stroke();
			}
		},
		defaults: {
			lineAtIndex: []
		}
	};
}