<script lang="ts" src="./StringListDescriptorComponent.ts" />

<style lang="sass" scoped>
@import './StringListDescriptorComponent.scss'
</style>

<template>
    <div class="string-list-descriptor-component">
        <div class="choices">
            <div
                v-for="choice of metadataDescriptorTemplate.availableValueList"
                class="choice"
                :class="{selected: values.includes(choice)}"
                @click="toggleValue(choice)">
                {{ choice }}
            </div>
        </div>

        <button class="primary button" :class="{disabled: values.length < metadataDescriptorTemplate.minimumValueCount}" @click="validate()"> Valider </button>
    </div>
</template>