import {Component, Prop, Vue, Watch} from "vue-facing-decorator";
import {PosState} from "../../model/PosState";
import {PosProfile} from "../../model/PosProfile";
import {AutoWired, VisualScopedUuid} from "@groupk/horizon2-core";
import {AppBus} from "../../config/AppBus";
import {UuidScopeEstablishmentAccount} from "@groupk/mastodon-core";

@Component({})
export default class SidebarComponent extends Vue {
	@Prop() posState!: PosState;
	@Prop() posProfile!: PosProfile;

	@AutoWired(AppBus) accessor appBus!: AppBus;

	changeAccount(establishmentAccountUid: VisualScopedUuid<UuidScopeEstablishmentAccount>|null) {
		this.appBus.emit('changeEstablishmentAccount', establishmentAccountUid);
		this.$forceUpdate();
	}
}