.order-buyer-component {
    .buyer {
        all: unset;
        display: flex;
        flex-direction: column;
        gap: 15px;
        background: var(--secondary-hover-color);
        padding: 15px;
        border-radius: 12px;

        .head {
            display: flex;
            align-items: center;
            justify-content: space-between;
            gap: 15px;
            width: 100%;

            .infos {
                display: flex;
                flex-direction: column;
                gap: 4px;
            }
        }

        .order-name {
            font-size: 14px;
            font-weight: 600;
        }

        .tickets-quantity {
            font-size: 14px;
            font-weight: 500;
        }

        .top {
            all: unset;
            display: flex;
            align-items: center;
            gap: 15px;
            cursor: pointer;

            .profile-picture {
                flex-shrink: 0;
                display: flex;
                align-items: center;
                justify-content: center;
                height: 38px;
                width: 38px;
                background: #D9D9D9;
                border-radius: 50%;
                text-transform: uppercase;
                font-weight: 500;
                font-size: 13px;
            }

            .data {
                flex-grow: 2;
                display: flex;
                flex-direction: column;
                gap: 4px;

                .name {
                    font-size: 14px;
                    font-weight: 500;
                }

                .email {
                    font-size: 14px;
                    word-break: break-all;
                }
            }

            i {
                font-size: 18px;
                margin-right: 5px;
            }
        }

        .metadata {
            display: flex;
            flex-direction: column;
            gap: 4px;

            .name {
                display: flex;
                align-items: center;
                gap: 10px;
                font-size: 14px;
                font-weight: 500;

                i {
                    font-size: 12px;
                }
            }

            .value {
                font-size: 14px;
            }
        }

        .purchases {
            display: flex;
            flex-direction: column;
            gap: 10px;

            .purchase {
                display: flex;
                align-items: center;
                padding: 10px 5px 10px 15px;
                background: white;
                border-radius: 8px;

                .data {
                    flex-grow: 2;
                    display: flex;
                    gap: 10px;

                    .name {
                        font-size: 15px;
                        font-weight: 500;
                    }

                    .quantity {
                        font-size: 15px;
                    }
                }
            }
        }
    }
}
