import {Component, Vue} from "vue-facing-decorator";
import {TopBarComponent} from "@groupk/vue3-interface-sdk";
import SidebarStateListener from "../../../../../shared/utils/SidebarStateListener";
import {AutoWired} from "@groupk/horizon2-core";
import {OptionBuilder} from "vue-facing-decorator/dist/optionBuilder";

@Component({
	components: {
		'top-bar': TopBarComponent
	}
})
export default class TopBarView extends Vue {
	@AutoWired(SidebarStateListener) accessor sidebarStateListener!: SidebarStateListener;

	hidden: boolean = false;

	constructor(optionBuilder: OptionBuilder, vueInstance: any) {
		super(optionBuilder, vueInstance);

		this.hidden = this.sidebarStateListener.hiddenTopBar;
		this.sidebarStateListener.listen(this.stateChanged);
	}

	stateChanged(event: {type: string; value: any}){
		if (event.type === "hiddenTopBar") {
			this.hidden = event.value;
		}
	}

	showMenu() {
		this.sidebarStateListener.setOpenedSidebar(true);
	}
}
