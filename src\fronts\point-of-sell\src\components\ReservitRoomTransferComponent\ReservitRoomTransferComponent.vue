<script lang="ts" src="./ReservitRoomTransferComponent.ts" />

<style lang="sass">
@import './ReservitRoomTransferComponent.scss'
</style>

<template>
    <div class="right-modal-dimmer"></div>

    <right-modal
        bottom-button-text="Fermer"
        @bottom-button-clicked="close()"
    >
        <div class="reservit-room-transfer-component">
            <div class="loading-bookings" v-if="loadingBookings">
                <div class="loader"></div>
            </div>

            <div class="scroll-area" v-if="!selectedRoomBookings">
                <div class="top">
                    <div class="head">
                        <span class="title"> Envoyer sur Reservit </span>
                        <span class="description"> La note pourra être réglée plus tard lors de la remise de la chambre </span>
                    </div>

                    <div class="input-group">
                        <input v-model="roomNumber" placeholder="Rechercher..." type="text" @input="search()"/>
                    </div>
                </div>

                <div class="rooms">
                    <div class="form-error" v-if="!reservit.configuration">
                        <i class="fa-solid fa-exclamation-circle"></i>
                        <div class="details">
                            <span class="title"> Reservit indisponible </span>
                            <span class="description">
                                Le serveur de Reservit ne répond pas correctement aux requêtes. Veuillez contacter le support technique.
                            </span>
                        </div>
                    </div>

                    <div class="room" v-for="room of filteredRooms" @click="findBookings(room)">
                        <div class="data">
                            <div class="name"> {{ getRoomName(room).name }} </div>
                            <div class="id"> {{ room.id }} </div>
                        </div>

                        <i class="fa-regular fa-chevron-right"></i>
                    </div>
                </div>
            </div>

            <div class="scroll-area" v-else>
                <div class="top vertical">
                    <button class="grey button" @click="selectedRoomBookings = null">
                        <i class="fa-regular fa-arrow-left"></i>
                    </button>
                    <span class="room-name">
                        {{ getRoomName(selectedRoomBookings.room).name }}
                    </span>
                </div>

                <div class="bookings">
                    <div v-if="selectedRoomBookings.bookings.length === 0" class="no-bookings">
                        <i class="fa-solid fa-calendar-xmark"></i>
                        <span> Aucune réservation pour cette chambre </span>
                    </div>
                    <div class="booking" v-for="booking of selectedRoomBookings.bookings" :key="booking.bookingId">
                        <div class="booking-header">
                            <div class="guest-info">
                                <div class="guest-avatar">
                                    {{ getInitials(booking.paxFirstName, booking.paxLastName) }}
                                </div>
                                <div class="data">
                                    <div class="guest-name">
                                        {{ booking.paxFirstName }} {{ booking.paxLastName }}
                                    </div>

                                    <div class="booking-info" v-if="booking.bookingId">
                                        <i class="fa-regular fa-hashtag"></i>
                                        <span> {{ booking.bookingId }} </span>
                                    </div>
                                </div>
                            </div>
                            <div class="label" :class="getBookingStatusClass(booking)">
                                {{ getBookingStatus(booking) }}
                            </div>
                        </div>
                        <div class="booking-details">
                            <div class="booking-dates">
                                <div class="date-range">
                                    <div class="date-item from">
                                        <div class="date-label">Du</div>
                                        <div class="date-value">{{ formatDate(booking.from) }}</div>
                                    </div>
                                    <div class="date-separator">
                                        <i class="fa-regular fa-arrow-right"></i>
                                    </div>
                                    <div class="date-item to">
                                        <div class="date-label">Au</div>
                                        <div class="date-value">{{ formatDate(booking.to) }}</div>
                                    </div>
                                </div>
                            </div>
                            <div class="booking-actions">
                                <button class="button" @click="sendOrderToBooking(booking)">
                                    <i class="fa-regular fa-paper-plane"></i>
                                    Envoyer
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </div>

    </right-modal>





<!--        <div class="keypad-container">-->
<!--            <div class="keypad" v-if="step === 1">-->
<!--                <div class="head">-->
<!--                    <span class="title">-->
<!--                        Numéro de chambre-->
<!--                    </span>-->

<!--                    <div class="close" @click="close()">-->
<!--                        <i class="fa-regular fa-xmark"></i>-->
<!--                    </div>-->
<!--                </div>-->

<!--                <keypad-->
<!--                    :simple-mode="true"-->
<!--                    @clicked-key="updateRoomNumber($event)"-->
<!--                >-->
<!--                    <template v-slot:display>-->
<!--                        <input type="text" v-model="roomNumber" />-->

<!--                        <div class="form-error">-->
<!--                            <i class="fa-regular fa-circle-exclamation"></i>-->
<!--                            <div class="details">-->
<!--                                Aucune réservation pour ce numéro de chambre-->
<!--                            </div>-->
<!--                        </div>-->
<!--                    </template>-->
<!--                </keypad>-->

<!--                <button class="primary button" @click="findBookings()">-->
<!--                    Valider-->
<!--                </button>-->
<!--            </div>-->

<!--            <div class="keypad" v-if="step === 2">-->
<!--                <div class="head">-->
<!--                    <span class="title">-->
<!--                        Clients trouvés-->
<!--                    </span>-->

<!--                    <div class="close" @click="close()">-->
<!--                        <i class="fa-regular fa-xmark"></i>-->
<!--                    </div>-->
<!--                </div>-->

<!--                <div class="customers">-->
<!--                    <div class="customer">-->
<!--                        <div class="profile-picture">-->
<!--                            LL-->
<!--                        </div>-->
<!--                        <div class="infos">-->
<!--                            <span class="name"> Lelaidier Lucas </span>-->
<!--                            <span class="email"> Info supplémentaire </span>-->
<!--                        </div>-->
<!--                        <i class="fa-regular fa-arrow-right"></i>-->
<!--                    </div>-->

<!--                    <div class="customer">-->
<!--                        <div class="profile-picture">-->
<!--                            LL-->
<!--                        </div>-->
<!--                        <div class="infos">-->
<!--                            <span class="name"> Miguel Del Piero</span>-->
<!--                            <span class="email"> Info supplémentaire </span>-->
<!--                        </div>-->
<!--                        <i class="fa-regular fa-arrow-right"></i>-->
<!--                    </div>-->
<!--                </div>-->
<!--            </div>-->
<!--        </div>-->
</template>
