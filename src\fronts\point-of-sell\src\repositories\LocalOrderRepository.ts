import {LocalOrder} from "../model/LocalOrder";
import {AutoWired, Cursor} from "@groupk/horizon2-core";
import {AppBus} from "../config/AppBus";
import {EntityIndexeddbRepository} from "@groupk/horizon2-front";
import {<PERSON>rror<PERSON>and<PERSON>} from "../class/ErrorHandler";
import {PosState} from "../model/PosState";

export class LocalOrderRepository extends EntityIndexeddbRepository<LocalOrder> {
	protected static timestampIndexName = 'lastLocalUpdateDate';
	protected static syncedIndexName = 'synced';

	@AutoWired(AppBus) private accessor appBus !: AppBus;
	@AutoWired(PosState) private accessor posState !: PosState;
	@AutoWired(ErrorHandler) private accessor errorHandler !: ErrorHandler;

	constructor() {
		super(LocalOrder, 'uid', 'pos_orders', 'pos_orders', 1);
		this.addEntityBooleanIndex('synced');
	}

	protected onUpgrade(db: IDBDatabase, transaction: IDBTransaction | null, oldVersion: number, newVersion: number | null) {
		let objectStore = super.onUpgrade(db, transaction, oldVersion, newVersion);
		this.putEntityIndex(objectStore, LocalOrderRepository.timestampIndexName, 'lastLocalUpdateDate');
		this.putEntityIndex(objectStore, LocalOrderRepository.syncedIndexName, 'synced');
		return objectStore;
	}

	async findAllNotSyncedCursor(): Promise<Cursor<LocalOrder>> {
		return await this._findAllBooleanIndexCursor('synced', false);
	}

	async saveAndResync(object: LocalOrder) {
		object.synced = false;
		object.lastLocalUpdateDate = new Date().getTime();
		object.error = null;

		try {
			await this.save(object);
		} catch(err) {
			console.log(err);
			this.errorHandler.logEverywhere({
				error: err as Error,
				title: 'Une erreur inconnue est survenue',
				description: 'La commande n\'a pa pu être sauvegardée',
			});
		}

		this.posState.notifyCustomerScreen();
	}

	async save(object: LocalOrder): Promise<void> {
		await super.save(object);
		this.appBus.emit('orderSaved', object);
	}

	async clear(){
		await this._clear();
	}

	override formatObjectAfterFetch(object: any): LocalOrder{
		for(const discount of object.order.discounts) {
			discount.amount = Math.floor(discount.amount)
		}

		return super.formatObjectAfterFetch(object);
	}

}