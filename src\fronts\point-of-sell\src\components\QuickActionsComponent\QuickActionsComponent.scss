.quick-actions-component {
    position: fixed;
    inset: 0;
    padding: 80px;
    background: rgba(0, 0, 0, 0.4);

    .actions {
        display: grid;
        grid-template-columns: repeat(6, 1fr);
        grid-gap: 20px;

        .action {
            min-height: 85px;
            width: 100%;
            border-radius: 8px;
            background: white;
            display: flex;
            align-items: center;
            padding: 15px 20px;
            box-sizing: border-box;
            gap: 15px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;

            &:hover {
                background: var(--secondary-hover-color);
            }

            &.disabled {
                filter: brightness(80%);
            }

            i {
                font-size: 25px;
            }

            img {
                width: 25px;
            }

            &.red {
                background: var(--error-color);
                color: white;

                &:hover {
                    background: var(--error-hover-color);
                }
            }
        }
    }

    @media (max-width: 900px) {
        all: unset;

        .actions {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            padding: 20px;

            .action {
                all: unset;
                display: flex !important;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                font-size: 16px;
                gap: 10px;
                text-align: center;
                background: white;
                border-radius: 8px;
                height: 100px;
                padding: 10px;
                box-sizing: border-box;

                &.disabled {
                    opacity: 0.5;
                    filter: none;
                    pointer-events: none;
                }

                i {
                    font-size: 22px;
                }
            }
        }
    }
}