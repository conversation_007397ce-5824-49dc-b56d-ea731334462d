import {Component, Prop, Vue} from "vue-facing-decorator";
import {DropdownValue, SelectMultipleDropdownComponent} from "@groupk/vue3-interface-sdk";
import {OrderStat, OrderStatOrderStatus, ProductApiOut, UuidScopeProductProduct} from "@groupk/mastodon-core";
import {VisualScopedUuid} from "@groupk/horizon2-core";
import LineChartComponent from "../../../components/ChartComponents/LineChartComponent/LineChartComponent.vue";

@Component({
    components: {
        'line-chart': LineChartComponent,
        'select-multiple-dropdown': SelectMultipleDropdownComponent
    }
})
export default class ProductChartWithComparisonComponent extends Vue {
    @Prop({required: true}) selectedProduct!: ProductApiOut;
    @Prop({required: true}) stats!: OrderStat;
    @Prop({required: true}) labelsForRangeAndGroupBy!: string[];
    @Prop({required: true}) cashStatementsDataIndex!: {x: number, y: number|null}[];
    @Prop({required: true}) getDataIndex!: (dataDatetime: string) => number|null;

    compareWithProducts: VisualScopedUuid<UuidScopeProductProduct>[] = [];

    generateRandomColor(): string {
        const colors = [
            '#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#feca57',
            '#ff9ff3', '#54a0ff', '#5f27cd', '#00d2d3', '#ff9f43',
            '#10ac84', '#ee5a24', '#0984e3', '#6c5ce7', '#a29bfe',
            '#fd79a8', '#e17055', '#00b894', '#00cec9', '#e84393'
        ];
        return colors[Math.floor(Math.random() * colors.length)];
    }

    getProductChartData(product: ProductApiOut): number[] {
        const labels = this.labelsForRangeAndGroupBy;
        const data = new Array(labels.length).fill(0);

        for(let point of this.stats.points) {
            if(point.datetime === null) continue;
            const index = this.getDataIndex(point.datetime);
            if(index !== null) {
                for(const perOrderStatus of point.perOrderStatus) {
                    if (perOrderStatus.status !== OrderStatOrderStatus.FULLY_PAID) continue;
                    for (let perProduct of perOrderStatus.perProduct) {
                        if (perProduct.productUid === product.uid) {
                            data[index] += perProduct.soldAloneCount;
                        }
                    }
                }
            }
        }
        return data;
    }

    getSelectedProductChartData() {
        if(!this.selectedProduct) return;
        const labels = this.labelsForRangeAndGroupBy;
        const datasets = [];

        // Add selected product dataset
        const selectedProductData = this.getProductChartData(this.selectedProduct);
        datasets.push({
            label: this.selectedProduct.lastRevision.name,
            data: selectedProductData,
            borderColor: '#003dff',
            tension: 0.3,
            fill: true
        });

        // Add comparison products datasets with random colors
        for(let compareProductUid of this.compareWithProducts) {
            const compareProduct = this.stats.productList.find(p => p.uid === compareProductUid);
            if(compareProduct) {
                const compareProductData = this.getProductChartData(compareProduct);
                datasets.push({
                    label: compareProduct.lastRevision.name,
                    data: compareProductData,
                    borderColor: this.generateRandomColor(),
                    tension: 0.3,
                    fill: false
                });
            }
        }

        return {
            labels: labels,
            datasets: datasets
        };
    }

    get productComparisonDropdownValues(): DropdownValue[] {
        return this.stats.productList.filter((product) => product.uid !== this.selectedProduct.uid).map((product) => {
            return {
                name: product.lastRevision.name,
                value: product.uid
            }
        });
    }

}