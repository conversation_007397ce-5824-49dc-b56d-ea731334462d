<script lang="ts" src="./BluetoothDeviceComponent.ts" />

<style lang="sass" scoped>
@import './BluetoothDeviceComponent.scss'
</style>

<template>
    <div class="bluetooth-device-component">
        <div class="separator">
            Appareils détectés

            <button class="tertiary button" @click="bluetoothManager.refreshDevicesList()">
                <i class="fa-regular fa-arrows-rotate" :class="{'fa-spin': bluetoothManager.refreshing}"></i>
                Rafraîchir la liste
            </button>
        </div>

        <div class="no-device" v-if="!isNative">
            Bluetooth non disponible sur cette plateforme
        </div>

        <template v-else>
            <div class="no-device" v-if="bluetoothManager.bluetoothDevices.length === 0">
                Aucun appareil bluetooth détecté
                <span class="hint"> Assurez vous d'avoir connecté l'appareil dans les paramètres système de la caisse </span>
            </div>

            <div
                class="bluetooth-device"
                :class="{ connected: bluetoothManager.isConnected(device) }"
                v-for="device of bluetoothManager.bluetoothDevices" @click="bluetoothManager.connectDevice(device)"
            >
                {{ device.name }}
                <i v-if="bluetoothManager.isConnected(device)" class="fa-regular fa-check"></i>
                <i v-else-if="bluetoothManager.isConnecting(device)" class="fa-regular fa-loader fa-spin"></i>
                <i v-else class="fa-regular fa-bluetooth"></i>
            </div>
        </template>
    </div>
</template>