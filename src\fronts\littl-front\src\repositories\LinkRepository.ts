import {LinkApiIn, LittlLinkTargetContract} from '@groupk/mastodon-core';
import {HttpAuthedRepository} from "./HttpAuthedRepository";
import {VisualScopedUuid} from '@groupk/horizon2-core';
import {UuidScopeEstablishment} from '@groupk/mastodon-core';

export class LinkRepository extends HttpAuthedRepository<typeof LittlLinkTargetContract> {
	constructor() {
		super(LittlLinkTargetContract);
	}

	async create(establishmentUid: VisualScopedUuid<UuidScopeEstablishment>, linkId: string, linkApi: LinkApiIn) {
		return await this.callContract(
			'update',
			{establishmentUid: establishmentUid, linkId: linkId},
			linkApi
		);
	}
}