import {Component, Emit, Vue} from 'vue-facing-decorator';
import {
	NeptingApp2AppLoginResponse,
	NeptingApp2AppParserModel,
	PaymentMethodDataPhysicalApp2AppCommon,
	PaymentMethodDataStatus,
} from '@groupk/mastodon-core';
import {AutoWired} from '@groupk/horizon2-core';
import {
	UsbDeviceDescriptor,
	UsbDeviceInterfaceDescriptor,
	UsbKnownClass,
	UsbNative,
	WeecopUsbDevicePrinterDetails,
	WeecopUsbVirtualDeviceIds,
	WeecopUsbVirtualVendorId
} from '@groupk/native-bridge';
import * as QRCode from 'qrcode';
import {NeptingBridge} from "../../../class/NeptingBridge";
import {Encoding} from "../../../../../../shared/utils/Encoding";

export interface CardPaymentModalComponent_offlineState{
	offline: boolean;
	stuckTransactions: number;
	lastToggleDate: string;
}

interface CardPaymentModalComponentResult{
	status: PaymentMethodDataStatus,
	type: 'PAYMENT'|'LOGIN',
	paymentType?: 'debit'|'credit',
	ticket?: string|undefined,
	merchantTicket?: string|undefined,
	signatureRequired?: boolean|undefined,
	errorDetails?: string|undefined,
	offlineState?: CardPaymentModalComponent_offlineState|undefined,
}

export interface CardPaymentModalComponent_options {
	showOfflineTransactions: boolean;
	offlineUpdater?: undefined|(()=>Promise<CardPaymentModalComponent_offlineState>);
}
export const CardPaymentModalComponent_displayOptionsDefault : CardPaymentModalComponent_options = {
	showOfflineTransactions: true,
	offlineUpdater: undefined,
}

@Component({})
export default class NeptingProcessorComponent extends Vue {
	@AutoWired(UsbNative) private accessor usbNative!: UsbNative;

	state: 'NOTHING'|'COMMUNICATING'|'RESPONSE'|'RESPONSE_BUTTONS'|'RESPONSE_QR'|'RESPONSE_RAW' = 'NOTHING';
	options:CardPaymentModalComponent_options = CardPaymentModalComponent_displayOptionsDefault;
	// paymentResponse: PaymentMethodDataPhysicalNeptingApp2App|null = null;
	result: CardPaymentModalComponentResult|null = null;
	PaymentMethodDataStatus : typeof PaymentMethodDataStatus = PaymentMethodDataStatus;

	internalError: unknown|undefined = undefined;
	qrCodeImgBase64: string|undefined = undefined;

	offlineState: CardPaymentModalComponent_offlineState|undefined = undefined;

	bestPrinter : UsbDeviceDescriptor|null = null;

	mounted(){
		this.bestPrinter = null;
		this.usbNative.getDevices().then(async (usbDevices)=>{
			const potentialPrinters : UsbDeviceDescriptor[] = usbDevices.filter(
				device=>device.deviceClass === UsbKnownClass.PRINTER || device.deviceClass === UsbKnownClass.PER_INTERFACE_DESCRIPTOR
			);

			const potentialTextPrinters : UsbDeviceDescriptor[] = [];
			for(const printer of potentialPrinters){
				if(printer.vendorId === WeecopUsbVirtualVendorId && printer.productId === WeecopUsbVirtualDeviceIds.PRINTER_TEXT){
					potentialTextPrinters.push(printer);
					break;
				}
			}

			if(potentialTextPrinters.length > 0){
				this.bestPrinter = potentialTextPrinters[0]!;
			}
		});

		this.startCommunication();
	}

	async printReceipt(textToPrint?: string|undefined){
		if(this.bestPrinter === null) return;

		if(textToPrint === undefined){
			textToPrint = this.result?.ticket;
		}

		if(textToPrint === undefined) return;

		if(!this.bestPrinter.connected)
			await this.usbNative.requestPermission(this.bestPrinter.id);

		const printerDetails : WeecopUsbDevicePrinterDetails = this.bestPrinter.details;
		if(printerDetails.status){
			if(printerDetails.status !== 'ready'){
				if(printerDetails.status === 'out_of_paper')
					alert('Plus de papier');
				else
					alert('Erreur: '+printerDetails.status);
				return;
			}
		}

		const printerRawInterface = this.bestPrinter.interfaces.filter(i=>i.interfaceClass === UsbKnownClass.PRINTER);
		let selectedPrinterInterfaceId : UsbDeviceInterfaceDescriptor|null = null;
		for(const usbInterface of printerRawInterface){
			for(const endpoint of usbInterface.endpoints){
				if(endpoint.direction == 'out' && endpoint.type === 'BULK'){
					selectedPrinterInterfaceId = usbInterface;
					break;
				}
			}
		}
		console.debug('selectedPrinterInterfaceId', selectedPrinterInterfaceId);

		if(selectedPrinterInterfaceId !== null) {
			const connectionToPrinter = await this.usbNative.bulkConnection(this.bestPrinter.id, selectedPrinterInterfaceId.id);
			console.log('OPENNING', connectionToPrinter);
			connectionToPrinter.on('connected', async ()=>{
				console.debug('connection opened');
				try {
					connectionToPrinter.send(Encoding.strUtf8ToHex(textToPrint+'\n\n'));
					console.debug('text sent to printer');
				}catch (e){
					throw e;
				}finally {
					connectionToPrinter.close();
				}
			});
			connectionToPrinter.on('closed', ()=>{
				console.debug('connection closed');
			});
		}
	}

	reset(){
		this.state = 'NOTHING';
		this.result = null;
		this.internalError = undefined;
		this.qrCodeImgBase64 = undefined;
		this.offlineState = undefined;
	}

	startCommunication(
		displayOptions?:Partial<CardPaymentModalComponent_options>
	){
		this.reset();
		this.state = 'COMMUNICATING';
		this.options = {
			...CardPaymentModalComponent_displayOptionsDefault,
			...displayOptions,
		};
	}


	setPaymentResponse(paymentResponse : PaymentMethodDataPhysicalApp2AppCommon, more: {
		signatureRequired?: boolean|undefined,
		refund: boolean,
		errorDetails?: string|undefined,
		offlineState?: CardPaymentModalComponent_offlineState|undefined,
	}){
		console.debug('CardPaymentModal>SetPaymentResponse');
		this.setResult({
			status: paymentResponse.status,
			ticket: paymentResponse.holderTicket,
			merchantTicket: paymentResponse.merchantTicket,
			type: 'PAYMENT',
			paymentType: more.refund ? 'credit' : 'debit',
			signatureRequired: more?.signatureRequired ?? false,
			errorDetails: more?.errorDetails,
			offlineState: more?.offlineState,
		});
	}
	setLoginResult(result: NeptingApp2AppLoginResponse, more?: {
		offlineState?: CardPaymentModalComponent_offlineState|undefined,
	}){
		this.setResult({
			status: result.GLOBAL_STATUS,
			type:'LOGIN',
			errorDetails: NeptingBridge.computeErrorFromExtendedResult(NeptingApp2AppParserModel.parseExtendedResultText(result.EXTENDED_RESULT_TEXT)),
			offlineState: more?.offlineState,
		});
	}
	setResult(result : CardPaymentModalComponentResult){
		console.debug('CardPaymentModal>SetResult', result);
		this.result = result;

		this.offlineState = result.offlineState;

		// auto-print only in this case
		if(this.result.signatureRequired){
			this.printReceipt(this.result.merchantTicket);
		}

		if(this.result.ticket){
			// if(this.result.type === 'PAYMENT' && this.result.status !== NeptingApp2AppGlobalStatus.SUCCESS){
			// 	this.printReceipt();
			// }

			QRCode.toDataURL(this.result.ticket, {
				errorCorrectionLevel: 'low',
			}).then((base64)=>{
				this.qrCodeImgBase64 = base64;
				console.debug('qr code', base64)
			});
		}else{
			this.closeEvent();
		}
	}

	forceNeptingSync(){
		if(this.options.offlineUpdater){
			this.options.offlineUpdater().then((newState)=>{
				this.offlineState = newState;
			})
		}
	}

	isNeptingOfflineStateReallyOld(): boolean{
		if(this.offlineState === undefined) return false;
		return (Date.now() - new Date(this.offlineState.lastToggleDate).getTime()) >= 1000;
	}

	getPaymentTypeName(){
		return this.result?.paymentType === 'credit' ? 'Remboursement' : 'Paiement';
	}

	@Emit('close')
	closeEvent() : void{}
}