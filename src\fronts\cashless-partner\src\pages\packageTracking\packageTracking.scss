#package-tracking-page {
    .selected-tracking {
        display: flex;
        flex-direction: column;
        gap: 20px;
        padding: 40px;

        @media screen and (max-width: 900px) {
            padding: 20px;
        }
    }

    .updates {
        display: flex;
        flex-direction: column;

        .update {
            display: flex;
            align-items: center;
            gap: 20px;
            border-left: 2px solid green;
            margin-left: 6px;
            padding: 10px 0;

            .point {
                height: 12px;
                width: 12px;
                margin: -7px;
                background: green;
                border-radius: 50%;
            }

            .datetime {
                font-size: 15px;
            }

            .data {
                display: flex;
                flex-direction: column;
                gap: 4px;

                .status {
                    font-weight: 500;
                }

                .comment {
                    font-size: 14px;
                }
            }
        }
    }
}