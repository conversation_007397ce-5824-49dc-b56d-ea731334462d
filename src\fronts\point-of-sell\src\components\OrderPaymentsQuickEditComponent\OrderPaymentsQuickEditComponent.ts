import {Component, Prop, Vue} from "vue-facing-decorator";
import {LocalOrder} from "../../model/LocalOrder";

@Component({
})
export default class OrderPaymentsQuickEditComponent extends Vue {
	@Prop() localOrder!: LocalOrder;

	get amountPerMethod(): {[method: string]: number} {
		const indexed: {[method: string]: number} = {};

		for(let payment of this.localOrder.order.payments) {
			// Need payment data
		}

		return indexed;
	}
}