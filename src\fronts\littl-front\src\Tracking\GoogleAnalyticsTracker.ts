import {AutoWired} from "@groupk/horizon2-core";
import {AppBus} from "../AppBus";
declare global {
	interface Window {
		gtag: (arg1: string, arg2: string, arg3?: unknown, arg4?: unknown) => void;
	}
}

export class GoogleAnalyticsTracker {
	gtagId: string;

	@AutoWired(AppBus) accessor appBus!: AppBus;

	constructor(gtagId: string) {
		this.gtagId = gtagId;

		let scriptEleSrc = document.createElement('script');
		scriptEleSrc.setAttribute('src', `https://www.googletagmanager.com/gtag/js?id=${gtagId}`);
		scriptEleSrc.async = true;
		document.head.appendChild(scriptEleSrc);

		let gtagConfig = `gtag('config', '${gtagId}');`;

		const url = new URL(window.location.href);
		const trackingIds = url.searchParams.get(gtagId) ?? null;
		if(trackingIds) {
			const [sessionId, clientId] = trackingIds.split('-');
			gtagConfig = `gtag('config', '${gtagId}', {
                'client_id': ${clientId},
                'session_id': ${sessionId}
            });`;
		}

		let scriptEle = document.createElement('script');
		scriptEle.innerHTML = `
			window.dataLayer = window.dataLayer || [];
			function gtag(){dataLayer.push(arguments);}
			gtag('js', new Date());
			${gtagConfig}
		`;
		document.head.appendChild(scriptEle);

		this.appBus.on('page-view', this.propagatePageViewEvent);
		this.appBus.on('login', this.propagateLoginEvent);
		this.appBus.on('sign-up', this.propagateSignupEvent);
	}

	unbind() {
		this.appBus.off('page-view', this.propagatePageViewEvent);
		this.appBus.off('login', this.propagateLoginEvent);
		this.appBus.off('sign-up', this.propagateSignupEvent);
	}

	propagatePageViewEvent = () => {
		window.gtag('event', 'page_view');
	}

	propagateLoginEvent = () => {
		window.gtag('event', 'login');
	}

	propagateSignupEvent = () => {
		window.gtag('event', 'sign_up');
	}
}