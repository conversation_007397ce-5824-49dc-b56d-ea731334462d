.payment-method-settings-component {
    display: flex;
    flex-direction: column;
    gap: 20px;

    .payment-method {
        display: flex;
        flex-direction: column;
        gap: 10px;

        .separator {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 20px;
            background: #F2F4F7;
            width: 100%;
            margin: 0 -20px;

            .tertiary.button {
                padding: 0;
                font-weight: normal;
            }

            .infos {
                display: flex;
                align-items: center;
                gap: 10px;

                .name {
                    font-size: 16px;
                    font-weight: 600;
                }

                .type {
                    font-size: 15px;
                }
            }
        }


        .configured-protocols {
            display: flex;
            flex-direction: column;
            gap: 10px;
            padding: 10px 0;

            .empty {
                width: 100%;
                text-align: center;
                grid-column: span 2;
            }
        }

        .action {
            display: flex;
            justify-content: center;
            align-items: center;
        }
    }

    .linked-device {
        display: flex;
        align-items: center;
        justify-content: space-between;
        border: 1px solid black;
        padding: 15px;
        font-size: 14px;
        font-weight: 600;
        width: 100%;
        border-radius: 8px;
        box-sizing: border-box;

        i {
            font-size: 18px;
        }
    }

}