<script lang="ts" src="./signup.ts">
</script>

<style scoped lang="sass">
@use './signup.scss' as *
</style>

<template>
    <div id="signup-page">
        <div class="left">
            <div class="loading-container" v-if="loading">
                <div class="loader"></div>
            </div>
            <form v-else class="form" @submit.prevent="signup()">

                <div class="reseller" v-if="reseller">
                    <img class="reseller-logo" alt="Logo revendeur" :src="reseller.logoUrl" />
                </div>

                <div class="title-group">
                    <img v-if="currentPlatform" class="logo" :src="currentPlatform.url + 'img/logo-long.svg'" >

                    <h3 v-if="!establishmentJoining"> S’inscrire sur {{ currentPlatform.name }} </h3>
                    <h3 v-else> S’inscrire et rejoindre {{ establishmentJoining.establishment.name }} </h3>
                    <span>
                        Nous avons besoin des informations suivantes pour
                        configurer votre nouveau compte.
                    </span>
                </div>

                <div class="fluid input-group" v-if="!establishmentJoining">
                    <label for="title">
                        Nom de votre établissement
                        <span class="characters"> {{ signupData.establishmentName.length }}/{{ EstablishmentCreationDefinition.getFieldString('name').maxLength }} </span>
                    </label>
                    <div class="ui input">
                        <input v-model="signupData.establishmentName" type="text" placeholder="Nom de votre établissement" />
                    </div>
                </div>

                <template v-if="!createdAccount">
                    <div class="input-group">
                        <label for="title"> Prénom </label>
                        <div class="ui input">
                            <input autocomplete="given-name" v-model="signupData.firstname" type="text" placeholder="Prénom" />
                        </div>
                    </div>

                    <div class="input-group">
                        <label for="title"> Nom </label>
                        <div class="ui input">
                            <input autocomplete="family-name" v-model="signupData.lastname" type="text" placeholder="Nom" />
                        </div>
                    </div>

                    <div class="input-group">
                        <label for="title"> Email </label>
                        <div class="ui input">
                            <input autocomplete="email" v-model="signupData.email" type="email" placeholder="Email" />
                        </div>
                    </div>

                    <div class="fluid input-group">
                        <label for="title"> Mot de passe </label>
                        <div class="ui input">
                            <input autocomplete="new-password" class="password-input" v-model="signupData.password" type="password" placeholder="Mot de passe" />
                        </div>
                    </div>
                </template>

                <div class="form-error" v-if="error">
                    <i class="fa-regular fa-circle-exclamation"></i>
                    <div class="details">
                        {{ error }}
                    </div>
                </div>

                <div class="spaced buttons">
                    <button type="button" class="tertiary button"  @click="goToLogin()">
                        J'ai déjà un compte
                    </button>
                    <button class="ui black button" :class="{ loading: creatingAccount, disabled: creatingAccount }">
                        <i class="fa-regular fa-user-plus"></i>
                        Créer mon compte
                    </button>
                </div>

            </form>
        </div>
        <div class="right" :style="currentPlatform ? `background-image: url('${currentPlatform.url + 'img/login.png'}')` : ''">

        </div>

        <form-modal-or-drawer
            :state="joiningError"
            title="Invitation invalide"
            :subtitle="joiningError"
            @close="joiningError = null"
        >
            <template v-slot:content></template>
            <template v-slot:buttons>
                <div class="button" @click="joiningError = null"> Fermer </div>
            </template>
        </form-modal-or-drawer>
    </div>
</template>