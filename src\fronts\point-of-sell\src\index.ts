import "@groupk/vue3-interface-sdk/dist/style.css";
import "./assets/css/global.scss";
import "@groupk/font-awesome-sdk/src/V6/css/pro-v4-font-face.min.css";
import "@groupk/font-awesome-sdk/src/V6/css/pro-v4-shims.min.css";
import "@groupk/font-awesome-sdk/src/V6/css/pro.min.css";
import {AnimatedRoute, AnimationBetweenRouteChange} from "./AnimatedRouter";
import {
	Router,
	RouterRoute,
	RouterStateConfigVersionModifier,
	VueRouteFactory,
	VueRouteOptions
} from "@groupk/horizon2-front";
import {GetInstance, SetInstance} from "@groupk/horizon2-core";
import {VueAuthedRouteFactory} from "./AuthedVueJsRoute";
import {BluetoothNative, NativeHelper, SocketNative, SystemInfoNative, UsbNative} from "@groupk/native-bridge";
import {setExecutionContext} from "@groupk/horizon2-core";
import {ErrorHandler} from "./class/ErrorHandler";
import {GlobalFilters} from "../../../shared/filters/GlobalFilters";
import {Configuration, MainConfig} from "../../../shared/MainConfig";
import {AuthStateModel} from "../../../shared/AuthStateModel";
import assetsPlugin from "../../../shared/routing/assetsPlugin";

let mainConfig: MainConfig = GetInstance(MainConfig);

mainConfig.init().then(async function(configFromFile: Configuration) {
	if(configFromFile.develop) {
		setExecutionContext({
			debug: true
		});
	}

	const authStateModel = new AuthStateModel(configFromFile.mastodonApiEndpoint, false);
	try {
		await authStateModel.getState(true);
	} catch(err){}
	SetInstance(AuthStateModel, authStateModel);

	startApp(configFromFile);
});

async function startApp(config: Configuration) {
	let router = new Router<AnimatedRoute | RouterRoute>({
		prefix: "/",
		registerGlobalInterceptor: true,
		container: "mainRouterContainer",
		oldContainer: "oldRouterContainer",
		disableRouteTriggerIfIdentical: true,
	});
	if (window.innerWidth < 900) {
		new AnimationBetweenRouteChange(router);
	}

	let root = document.documentElement;
	if(window.innerWidth <= 900) {
		root.style.setProperty("--safe-area-top", "10px");
		root.style.setProperty("--safe-area-bottom", "10px");
	}

	try {
		const nativeHelper = NativeHelper.getInstance();
		await nativeHelper.load();
		const tcpNative = GetInstance(SocketNative);
		if(nativeHelper.isModuleAvailable(SocketNative)) {
			tcpNative.tcpKillAll().catch();
		}
		const bluetoothNative = GetInstance(BluetoothNative);
		if(nativeHelper.isModuleAvailable(BluetoothNative)) {
			bluetoothNative.killAll().catch();
		}
		const usbNative = GetInstance(UsbNative);
		if(nativeHelper.isModuleAvailable(UsbNative)) {
			usbNative.killAll().catch();
		}

		const systemInfo = GetInstance(SystemInfoNative);
		systemInfo
			.getSafeArea()
			.then((info) => {
				root.style.setProperty("--safe-area-top", info.top + 12 + "px");
				root.style.setProperty("--safe-area-bottom", info.bottom + 6 + "px");
			})
			.catch(() => {});

	} catch(e) {}

	if(!config.develop) {
		const sentryConfig = {
			dsn: 'https://<EMAIL>/4506670538555392'
		};
		const errorHandler = new ErrorHandler(sentryConfig);
		SetInstance(ErrorHandler, errorHandler);
	}

	router.addHook(new RouterStateConfigVersionModifier(13));

	let uuidRegex = /([0-9a-f]{8}\-[0-9a-f]{4}\-[0-9a-f]{4}\-[0-9a-f]{4}\-[0-9a-f]{12})/;
	const vueRouteOptions: Partial<VueRouteOptions> = {
		filters: GlobalFilters,
		hookAppCreated(app) {
			app.use(assetsPlugin);
		},
	};

	router.addRoute({regex: new RegExp(/establishment\//.source + uuidRegex.source + /\/pos/.source), loader: () => import('./pages/pos/pos.vue').then((vue) => VueAuthedRouteFactory(vue.default, vueRouteOptions))});
	router.addRoute({regex: new RegExp(/establishment\//.source + uuidRegex.source + /\/auth/.source), loader: () => import('./pages/auth/auth.vue').then((vue) => VueRouteFactory(vue.default, vueRouteOptions))});
	router.addRoute({regex: new RegExp(/establishment\//.source + uuidRegex.source + /\/cash-statement/.source), loader: () => import('./pages/cashStatement/cashStatement.vue').then((vue) => VueAuthedRouteFactory(vue.default, vueRouteOptions))});
	router.addRoute({regex: new RegExp(/establishment\//.source + uuidRegex.source + /\/customer-display/.source), loader: () => import('./pages/customerDisplay/customerDisplay.vue').then((vue) => VueAuthedRouteFactory(vue.default, vueRouteOptions))});
	router.addRoute({regex: new RegExp(/establishment\//.source + uuidRegex.source + /\/iot-onboarding/.source), loader: () => import('./pages/iotOnboarding/iotOnboarding.vue').then((vue) => VueAuthedRouteFactory(vue.default, vueRouteOptions))});
	router.addRoute({regex: new RegExp(/establishment\//.source + uuidRegex.source + /.*/.source), loader: () => import('./pages/pos/pos.vue').then((vue) => VueAuthedRouteFactory(vue.default, vueRouteOptions))});
	router.addRoute({regex: new RegExp(/.*/.source), loader: () => import('./pages/index/index.vue').then((vue) => VueRouteFactory(vue.default, vueRouteOptions))});


	SetInstance(Router, router);
	router.updateCurrentPageFromCurrentLocation();
}
