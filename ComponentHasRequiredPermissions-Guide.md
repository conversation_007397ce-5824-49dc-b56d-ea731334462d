# ComponentHasRequiredPermissions Implementation Guide

## Overview
ComponentHasRequiredPermissions is a permission checking pattern used throughout the mastodon-fronts project to control component access based on user permissions. Each component that requires permissions exports a function that validates whether a user has the necessary permissions to use that component.

## Naming Convention
```typescript
export function {ComponentName}HasRequiredPermissions(ownedPermissions: ApplicationPermission[]): boolean
```

**Examples:**
- `ProductFormComponentComponentHasRequiredPermissions`
- `EventFormComponentHasRequiredPermissions` 
- `PointOfSaleFormComponentHasRequiredPermissions`
- `IotSelectionComponentHasRequiredPermissions`

## Function Structure Template
```typescript
import { ApplicationPermission, EstablishmentAccountPermissionModel } from "@groupk/mastodon-core";

export function {ComponentName}HasRequiredPermissions(ownedPermissions: ApplicationPermission[]): boolean {
    return EstablishmentAccountPermissionModel.hasPermissionsForContracts(ownedPermissions, [
        // List only the contracts that this component actually calls using callContract()
        ContractName.operationName,
        AnotherContract.anotherOperation,
    ]);
}
```

**IMPORTANT:** Place this function at the **top** of the component file, right after imports and before the @Component decorator.

## How to Determine Required Permissions

### CRITICAL RULE
**Only include contracts that the component actually calls using `callContract()` method.**
- Do NOT base on operation type (create/update/delete)
- Do NOT include generic CRUD permissions
- ONLY include specific contract calls the component makes

### Step-by-Step Process:
1. **Analyze component code** - Find all `callContract()` calls
2. **Identify contracts** - Note the contract and operation for each call
3. **List contracts** - Add each contract.operation to the permissions array
4. **Verify completeness** - Ensure all contract calls are covered

### Example Analysis:
```typescript
// Component makes these calls:
await this.productRepository.callContract('create', params, data);
await this.productRepository.callContract('update', params, data);
await this.billingRepository.callContract('set', params, data);

// Therefore permissions should include:
return EstablishmentAccountPermissionModel.hasPermissionsForContracts(ownedPermissions, [
    ProductHttpProductContract.create,
    ProductHttpProductContract.update,
    ProductBillingAccountHttpContract.set,
]);
```

## Parent Component Usage Patterns

### 1. Disable Actions in beforeMount() (PREFERRED)
```typescript
beforeMount() {
    if(!ComponentUtils.hasPermissions(ChildComponentHasRequiredPermissions)) {
        ComponentUtils.disableActions(this.headerParameters, ['create', 'edit']);
    }
}
```

**IMPORTANT:** The action identifiers passed to `disableActions()` must match the `id` property of the actions in `headerParameters.actions`. Make sure your actions have an `id` property:

```typescript
headerParameters: ContentHeaderParameters = {
    actions: [{
        id: 'edit',  // This id is used by disableActions()
        type: 'SIMPLE_ACTION',
        name: 'Edit Item',
        icon: 'fa-regular fa-pen',
        callback: this.showEditModal
    }]
}
```

### 2. Disable Buttons/Actions (PREFERRED over hiding)
```typescript
// For dropdown actions - filter out disabled actions
get dropdownActions() {
    const allActions = [{
        id: 'create',
        name: 'Create Item',
        icon: 'fa-regular fa-circle-plus'
    }, {
        id: 'import',
        name: 'Import Items',
        icon: 'fa-regular fa-file-import'
    }];

    return allActions.filter(action => !this.disabledActions.includes(action.id));
}

// In beforeMount, populate disabledActions array
beforeMount() {
    if(!ComponentUtils.hasPermissions(ChildComponentHasRequiredPermissions)) {
        this.disabledActions.push('create');
    }
}

// In action handlers, check if action is disabled
dropdownClicked(action: DropdownButtonAction) {
    if(action.id === 'create' && !this.disabledActions.includes('create')) {
        this.showCreateForm = true;
    }
}
```

### 3. Conditional Feature Display (Use sparingly - prefer disabling)
```typescript
get canCreateItems() {
    return ComponentUtils.hasPermissions(ChildComponentHasRequiredPermissions);
}
```

**Note:** Only use conditional rendering when disabling is not possible or when the UI element is not an action button.

### 4. Template Conditional Rendering (Use sparingly - prefer disabling)
```vue
<template>
    <!-- PREFERRED: Disable button instead of hiding -->
    <button :disabled="!canCreateItems" @click="openModal">
        Create New Item
    </button>

    <!-- ONLY when disabling is not possible -->
    <div v-if="canCreateItems" class="special-feature">
        Advanced Options
    </div>
</template>
```

**Note:** Always prefer disabling buttons over hiding them. Only hide UI elements when disabling is not appropriate (e.g., entire feature sections).

### 4. Double-Click Edit Protection (When Needed)
```typescript
toggleSelectedItem(item: ItemApiOut, event?: Event) {
    if (event && event.type === 'dblclick' && ComponentUtils.hasPermissions(FormComponentHasRequiredPermissions)) {
        this.selectedItem = item;
        this.editingItem = item;
        this.showModal = true;
        return;
    }
    // Regular selection logic
}
```

**Note:** Only add permission checks to methods if they are called from places other than disabled header actions.

### 5. Multiple Permission Checks
```typescript
async mounted() {
    if(!ComponentUtils.hasPermissions(ImportComponentHasRequiredPermissions)) {
        ComponentUtils.disableActions(this.headerParameters, ['import']);
    }
    
    if(!ComponentUtils.hasPermissions(FormComponentHasRequiredPermissions)) {
        ComponentUtils.disableActions(this.headerParameters, ['new']);
    }
    
    if(!ComponentUtils.hasPermissions(ExportComponentHasRequiredPermissions)) {
        ComponentUtils.disableActions(this.headerParameters, ['export']);
    }
}
```

## Implementation Checklist

### For New Components:
- [ ] Export permission function with correct naming convention **at the top of the file**
- [ ] Import required types: `ApplicationPermission`, `EstablishmentAccountPermissionModel`
- [ ] Analyze all `callContract()` calls in component
- [ ] List only the contracts actually called by the component
- [ ] Test permission function returns boolean

### For Parent Components:
- [ ] **Import child component's permission function from `.ts` file, NOT from `.vue` file**
- [ ] Use `ComponentUtils.hasPermissions()` to check permissions
- [ ] **Ensure actions have `id` properties** in headerParameters for disableActions() to work
- [ ] **PREFER disabling over hiding** - disable actions in `beforeMount()` using action IDs
- [ ] For dropdown actions, filter disabled actions from computed properties
- [ ] Add disabled action checks in action handlers (e.g., `dropdownClicked`)
- [ ] **Do NOT add permission checks to methods called only from disabled header actions**
- [ ] Only use conditional rendering when disabling is not possible
- [ ] Use `:disabled` attribute on buttons instead of `v-if` when possible

## Common Contracts Examples
```typescript
// Product operations
ProductHttpProductContract.list
ProductHttpProductContract.create
ProductHttpProductContract.update
ProductHttpProductContract.search

// Event operations  
ProductHttpEventContract.create
ProductHttpEventContract.update

// Billing operations
ProductBillingAccountHttpContract.set

// IoT operations
IotHttpEstablishmentDeviceContract.list

// Metadata operations
MetadataHttpCustomerDescriptorContract.listDescriptors
MetadataHttpCustomerDescriptorContract.updateDescriptor
```

## Import Guidelines

### Importing Permission Functions
**CRITICAL:** Permission functions must be imported from the TypeScript (`.ts`) file, NOT from the Vue (`.vue`) file.

```typescript
// ✅ CORRECT - Import from .ts file
import ComponentName from "./ComponentName/ComponentName.vue";
import { ComponentNameHasRequiredPermissions } from "./ComponentName/ComponentName";

// ❌ INCORRECT - Do NOT import from .vue file
import ComponentName, { ComponentNameHasRequiredPermissions } from "./ComponentName/ComponentName.vue";
```

**Rationale:** Vue files cannot export TypeScript functions properly. The permission function is defined in the `.ts` file and should be imported from there.

### Key Imports
```typescript
import { ApplicationPermission, EstablishmentAccountPermissionModel } from "@groupk/mastodon-core";
import { ComponentUtils } from "../../../../../shared/utils/ComponentUtils";
```

## Notes for AI Implementation
- Always follow the exact naming convention
- Function must be exported from the component file **at the top, after imports**
- **CRITICAL:** Import permission functions from `.ts` files, NOT from `.vue` files
- Only include contracts the component actually uses
- **PREFER disabling over hiding** - disable actions/buttons instead of hiding them with v-if
- Parent components should check permissions in beforeMount() and disable actions
- For dropdown actions, filter disabled actions and check in handlers
- **Do NOT add permission checks to methods that are only called from disabled header actions**
- Use ComponentUtils.hasPermissions() wrapper, never call permission functions directly
- Permission functions should be pure functions with no side effects
- Use `:disabled` attribute on buttons instead of `v-if` when possible
