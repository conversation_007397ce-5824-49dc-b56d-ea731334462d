import {Component, Prop, Vue} from "vue-facing-decorator";
import {
	ColumnBindingError,
	CsvImportError, CsvImportSaverParameter, EntityBuilderReturn,
	ImportColumn,
	ModalOrDrawerComponent,
	ParsedCsv,
	TableImportComponent
} from "@groupk/vue3-interface-sdk";
import {
	ApplicationPermission,
	CustomerApiIn,
	CustomerHttpCustomerContract,
	EstablishmentAccountPermissionModel,
	uuidScopeCustomer_customer,
	UuidScopeCustomer_customer
} from "@groupk/mastodon-core";
import {AutoWired, Uuid, UuidUtils, VisualScopedUuid} from "@groupk/horizon2-core";
import {CustomerRepository} from "../../../../../shared/repositories/CustomerRepository";
import {AppState} from "../../../../../shared/AppState";

export function CustomerImportModalComponentHasRequiredPermissions(ownedPermissions: ApplicationPermission[]): boolean {
    return EstablishmentAccountPermissionModel.hasPermissionsForContracts(ownedPermissions, [
        CustomerHttpCustomerContract.create,
        CustomerHttpCustomerContract.update,
    ]);
}

@Component({
	components: {
		'modal-or-drawer': ModalOrDrawerComponent,
		'table-import': TableImportComponent,
	},
	emits: ['close']
})
export default class CustomerImportModalComponent extends Vue {
	@Prop()	state!: boolean;

	public csvImportColumns: ImportColumn[] = [
		{
			name: 'uid',
			translation: 'Identifiant unique',
			primaryKey: true,
			matching: ['uid'],
			required: false
		}, {
			name: 'lastname',
			translation: 'Nom',
			matching: ['lastname', 'nom', 'nom de famille'],
			required: true
		}, {
			name: 'firstname',
			translation: 'Prénom',
			matching: ['prenom', 'firstname'],
			required: true
		}, {
			name: 'email',
			translation: 'Email',
			matching: ['mail', 'email', 'e-mail', 'adresse email'],
			required: true
		}, {
			name: 'description',
			translation: 'Description',
			matching: ['description'],
			required: false
		}
	];

	customerApiInEntity = CustomerApiIn;

	@AutoWired(CustomerRepository) accessor customerRepository!: CustomerRepository;
	@AutoWired(AppState) accessor appState!: AppState;

	entityBuilder(bindings: Record<string, string>, importedLines: ParsedCsv): EntityBuilderReturn {
		const result: CsvImportSaverParameter<CustomerApiIn>[] = [];

		// Used to display csv line in errors, for user debugging
		let lineNumber = 2;

		for (let line of importedLines.lines) {
			if(line[bindings['uid']] && !UuidUtils.isVisual<UuidScopeCustomer_customer>(line[bindings['uid']], uuidScopeCustomer_customer)) {
				throw new ColumnBindingError(`L\'identifiant "${line[bindings['uid']]}" n\'est pas un identifiant de client valide`, bindings['uid']);
			}

			const apiIn = new CustomerApiIn({
				firstname: line[bindings['firstname']],
				lastname: line[bindings['lastname']],
				email: line[bindings['email']],
				description: line[bindings['description']] || '',
			});

			result.push({
				uid: line[bindings['uid']] as Uuid || null,
				entity: apiIn,
				lineNumber: lineNumber,
				rawLine: line
			});

			lineNumber++;
		}

		return {entities: result, updatedLines: importedLines};
	}

	async entitySaver(entities: CsvImportSaverParameter<CustomerApiIn>[]) {
		let index: number = 1;
		const errors: CsvImportError[] = [];

		for (let data of entities) {
			const event = new CustomEvent('processing-entity', {detail: {total: entities.length, current: index, entity: `${data.entity.lastname} ${data.entity.firstname}`}});
			window.dispatchEvent(event)

			if (!data.uid) {
				const response = await this.customerRepository.callContract('create', {establishmentUid: this.appState.requireUrlEstablishmentUid()}, data.entity);
				if (!response.isSuccess()) {
					try {
						const error = response.error();
						errors.push({
							entity: data.entity,
							details: error && 'error' in error ? error.error : 'Erreur inconnue',
							rawLine: data.rawLine,
							lineNumber: data.lineNumber
						})
					} catch(err) {
						errors.push({
							entity: data.entity,
							details: 'Erreur inconnue',
							rawLine: data.rawLine,
							lineNumber: data.lineNumber
						})
					}
				}
			} else {
				const response = await this.customerRepository.callContract('update', {establishmentUid: this.appState.requireUrlEstablishmentUid(), customerUid: data.uid as VisualScopedUuid<UuidScopeCustomer_customer>}, data.entity);
				if (!response.isSuccess()) {
					try {
						const error = response.error();
						errors.push({
							entity: data.entity,
							details: error && 'error' in error ? error.error : 'Erreur inconnue',
							rawLine: data.rawLine,
							lineNumber: data.lineNumber
						})
					} catch(err) {
						errors.push({
							entity: data.entity,
							details: 'Erreur inconnue',
							rawLine: data.rawLine,
							lineNumber: data.lineNumber
						})
					}
				}
			}
			index++;
		}
		return errors;
	}

	close() {
		this.$emit('close');
	}
}