import {Component, Vue} from "vue-facing-decorator";
import {BrowserQRCodeReader} from "@zxing/library";
import {randomUUID, Router} from "@groupk/horizon2-front";
import {FormModalOrDrawerComponent} from "@groupk/vue3-interface-sdk";
import {AutoWired} from "@groupk/horizon2-core";
import {EstablishmentUrlBuilder} from "../../../../../shared/utils/EstablishmentUrlBuilder";

@Component({
	components: {
		'form-modal-or-drawer': FormModalOrDrawerComponent
	},
	emits: ['scanned']
})
export default class WebQrCodeReaderComponent extends Vue {
	uuid: string = randomUUID();
	codeReader!: BrowserQRCodeReader;

	availableVideoDevices: MediaDeviceInfo[] = [];
	selectedVideoDeviceId: string|null = null;

	loading: boolean = false;
	opened: boolean = false;

	@AutoWired(Router) accessor router!: Router;

	async mounted() {
		this.loading = true;

		try {
			this.codeReader = new BrowserQRCodeReader();
			const defaultStream = await navigator.mediaDevices.getUserMedia({video: true});
			defaultStream.getTracks().forEach(function(track) {
				track.stop();
			});

			this.availableVideoDevices = (await navigator.mediaDevices.enumerateDevices()).filter((device) => {
				return device.label !== '' && device.kind === 'videoinput'
			});

			if(this.availableVideoDevices.length === 1) this.selectedVideoDeviceId = this.availableVideoDevices[0].deviceId;
		} catch(err) {}

		this.loading = false;
	}

	unmounted() {
		this.codeReader.reset();
	}

	start() {
		if(this.selectedVideoDeviceId === null) return;
		this.opened = true;

		this.codeReader.decodeOnceFromVideoDevice(this.selectedVideoDeviceId, 'video-' + this.uuid).then((result) => {
			this.$emit('scanned', result);
			this.done();
		}).catch((err) => {
			console.log(err);
			this.done();
		});
	}

	done() {
		this.codeReader.reset();
		this.opened = false;
	}

	getDeviceName(device: MediaDeviceInfo) {
		let name = device.label;
		name = name.replace('facing back', 'arrière');
		name = name.replace('facing front', 'avant');
		return name;
	}

	back() {
		this.router.changePage(EstablishmentUrlBuilder.buildUrl('/'));
	}
}