.calendar-export-component {
    .month-selection {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;

        .current-month {
            font-weight: 600;
        }
    }

    .key-numbers {
        display: grid;
        grid-template-columns: 1fr 1fr 1fr;
        gap: 20px;

        @media (max-width: 900px) {
            grid-template-columns: 1fr;
        }

        .key-number {
            display: flex;
            flex-direction: column;
            gap: 10px;
            padding: 25px;
            border: 1px solid #E2E2E2;
            border-radius: 8px;

            .type {
                font-size: 14px;
            }

            .value {
                font-size: 24px;
                font-weight: 700;
            }

            .indicator {
                display: flex;
                align-items: center;
                gap: 4px;
                color: #079455;
                font-weight: 600;
                font-size: 14px;

                span {
                    color: #545454;
                    font-size: 12px;
                    font-weight: 500;
                }
            }
        }
    }

    .bold {
        font-weight: 600;
    }
}