import CanvasRendererSDK from "./CanvasRendererSDK";
import {SimplifiedPrintingPart_Text} from "./SimplifiedPrintingPartData";
import {SimplifiedRenderPart_Common} from "./SimplifiedRenderPart_Common";

export class SimplifiedPart_TextRenderer extends SimplifiedRenderPart_Common{

	private getFinalBaseLineHeight(baseLineHeight: number, textPart : SimplifiedPrintingPart_Text){
		return baseLineHeight * (textPart.baseLineMultiplier ?? 1);
	}

	render(renderer: CanvasRendererSDK, y : number, baseLineHeight: number, textPart : SimplifiedPrintingPart_Text) : number{
		const finalBaseLineHeight = this.getFinalBaseLineHeight(baseLineHeight, textPart);

		if (textPart.spaceBefore) {
			renderer.renderText("", this.getRenderLinePosition(baseLineHeight, y), this.getRenderLineSize(renderer, baseLineHeight), {
				horizontalAlign: textPart.textAlign!,
				fontWeight: textPart.fontWeight,
			});
			y += baseLineHeight;
		}

		for (let line of Array.isArray(textPart.text) ? textPart.text : [textPart.text]) {
			renderer.renderText(line, this.getRenderLinePosition(baseLineHeight, y), this.getRenderLineSize(renderer, finalBaseLineHeight), {
				horizontalAlign: textPart.textAlign!,
				fontWeight: textPart.fontWeight,
			});
			y += finalBaseLineHeight;
		}

		if (textPart.spaceAfter) {
			renderer.renderText("", this.getRenderLinePosition(baseLineHeight, y), this.getRenderLineSize(renderer, baseLineHeight), {
				horizontalAlign: textPart.textAlign!,
				fontWeight: textPart.fontWeight,
			});
			y += baseLineHeight;
		}

		return y;
	}

	estimateHeight(baseLineHeight: number, textPart : SimplifiedPrintingPart_Text): number{
		let height = 0;
		height += (Array.isArray(textPart.text) ? textPart.text : [textPart.text]).length * this.getFinalBaseLineHeight(baseLineHeight, textPart);
		if (textPart.spaceBefore) height += baseLineHeight;
		if (textPart.spaceAfter) height += baseLineHeight;
		return height;
	}

}
