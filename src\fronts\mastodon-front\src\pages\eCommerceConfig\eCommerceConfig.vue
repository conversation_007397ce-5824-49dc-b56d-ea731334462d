<script lang="ts" src="./eCommerceConfig.ts" />

<style lang="sass" scoped>
@import './eCommerceConfig.scss'
</style>

<template>
    <div id="e-commerce-config-page">
        <layout
            :drawer-opened="false"
        >
            <template v-slot:content>
                <content-header
                    :parameters="headerParameters"
                ></content-header>

                <div class="content">
                    <div v-if="loading" class="loading-container">
                        <div class="loader"></div>
                    </div>

                    <div class="grid" v-else>
                        <div class="column">
                            <div class="input-group">
                                <label> CSS brut </label>
                                <textarea v-model="ecommerceConfig.customCss"></textarea>
                            </div>
                        </div>
                        <div class="column">
                            <div class="external-css">

                                <div class="input-group" v-for="(cssLink, index) of ecommerceConfig.externalCssLinkList" v-if="ecommerceConfig.externalCssLinkList">
                                    <label> CSS brut </label>

                                    <div class="css-group">
                                        <input placeholder="url" v-model="ecommerceConfig.externalCssLinkList[index]">
                                        <button class="grey button" @click="ecommerceConfig.externalCssLinkList.splice(index, 1)">
                                            <i class="fa-regular fa-trash-alt"></i>
                                        </button>
                                    </div>
                                </div>

                                <button class="button" @click="addExternalCss()"> Ajouter un url </button>
                            </div>
                        </div>
                    </div>

                    <button class="button" @click="save()"> Sauvegarder </button>
                </div>

            </template>
        </layout>
    </div>
</template>