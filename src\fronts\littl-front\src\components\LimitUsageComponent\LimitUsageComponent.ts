import {Component, Vue} from "vue-facing-decorator";
import {OptionBuilder} from "vue-facing-decorator/dist/optionBuilder";
import {AutoWired} from "@groupk/horizon2-core";
import {LittlEstablishmentRepository} from "../../repositories/LittlEstablishmentRepository";
import {LittlEstablishmentApiOut} from "@groupk/mastodon-core";
import {Router} from "@groupk/horizon2-front";
import {LinkRepository} from "../../repositories/LinkRepository";
import {AppBus} from "../../config/AppBus";
import {EstablishmentUrlBuilder} from "../../../../../shared/utils/EstablishmentUrlBuilder";
import {AppState} from "../../../../../shared/AppState";

@Component({})
export default class LimitUsageComponent extends Vue {

	littlEstablishment!: LittlEstablishmentApiOut;
	linksCount: number = 0;

	loading: boolean = false;

	@AutoWired(LittlEstablishmentRepository) accessor littlEstablishmentRepository!: LittlEstablishmentRepository;
	@AutoWired(LinkRepository) accessor linkRepository!: LinkRepository;
	@AutoWired(Router) accessor router!: Router;
	@AutoWired(AppBus) accessor appBus!: AppBus;
	@AutoWired(AppState) accessor appState!: AppState;

	constructor(optionBuilder: OptionBuilder, vueInstance: any) {
		super(optionBuilder, vueInstance);
		this.loading = true;

		this.appBus.on('linkCreated', this.increaseCount);
		this.appBus.on('linkDeleted', this.decreaseCount);
	}

	increaseCount() {
		this.linksCount++;
	}

	decreaseCount() {
		this.linksCount--;
	}

	async beforeMount() {
		try {
			this.littlEstablishment = (await this.littlEstablishmentRepository.callContract('get', {establishmentUid: this.appState.requireUrlEstablishmentUid()}, undefined)).success();
			this.linksCount = (await this.linkRepository.callContract('listCount', {establishmentUid: this.appState.requireUrlEstablishmentUid()}, {})).success().rows ?? 0;
		} catch(err) {
			console.log(err);
		}
		this.loading = false;
	}

	get upgradeUrl() {
		return EstablishmentUrlBuilder.buildUrl('/upgrade');
	}
}