import {BluetoothDeviceConnection, BluetoothDeviceDescriptor, BluetoothNative} from "@groupk/native-bridge";
import {AutoWired, BufferUtils, EventEmitter} from "@groupk/horizon2-core";
import {Watch} from "vue-facing-decorator";
import {AppBus} from "../config/AppBus";

export class BluetoothManager extends EventEmitter<{ }>{
	private lsKey = 'connected-bluetooth-devices';

	bluetoothDevices: BluetoothDeviceDescriptor[] = [];
	connectedDevices: BluetoothDeviceDescriptor[] = [];
	connectingDevices: BluetoothDeviceDescriptor[] = [];

	wrapper: BluetoothDeviceConnection|null = null;

	refreshing: boolean = false;

	@AutoWired(AppBus) accessor appBus!: AppBus;

	constructor() {
		super();
		this.detectAvailableDevices();

		const rawConnectedDevices = localStorage.getItem(this.lsKey);
		if(rawConnectedDevices) {
			const connectedDevices: BluetoothDeviceDescriptor[] = JSON.parse(rawConnectedDevices);
			for(let device of connectedDevices) {
				this.connectDevice(device);
			}
		}
	}

	@AutoWired(BluetoothNative) accessor bluetoothNative!: BluetoothNative;

	@Watch('connectedDevices')
	connectedDevicesWatch() {
		localStorage.setItem(this.lsKey, JSON.stringify(this.connectedDevices));
	}

	connectDevice(device: BluetoothDeviceDescriptor) {
		this.connectingDevices.push(device);
		setTimeout(() => {
			this.connectingDevices = this.connectingDevices.filter((connectingDevice) => connectingDevice.name !== device.name);
		}, 5000);

		this.bluetoothNative.connectService(device.address, BluetoothNative.SPP_SERVICE_UUID).then((wrapper) => {
			wrapper.on('connected', () => {
				this.connectedDevices.push(device);
				this.connectingDevices = this.connectingDevices.filter((connectingDevice) => connectingDevice.name !== device.name);
			});
			wrapper.on('data', (bytes) => {
				// Send data
				this.appBus.emit('bluetoothData', String.fromCharCode(...(typeof bytes === 'string' ? BufferUtils.hex2Buffer(bytes) : bytes)));
			})
			wrapper.on('closed', () => {
				this.connectedDevices = this.connectedDevices.filter((connectedDevice) => connectedDevice.name !== device.name);
				this.connectingDevices = this.connectingDevices.filter((connectingDevice) => connectingDevice.name !== device.name);
			});
		});
	}

	async refreshDevicesList() {
		this.refreshing = true;
		try {
			await Promise.all([
				this.detectAvailableDevices(),
				new Promise((resolve) => {
					setTimeout(() => resolve(null), 1000);
				})
			]);
		} catch(err) {
			console.log(err);
		}

		this.refreshing = false;
	}

	detectAvailableDevices() {
		return new Promise((resolve) => {
			try {
				this.bluetoothNative.getState().then((devices) => {
					this.bluetoothDevices = [];
					for(let device of devices.bonded) {
						if(device.services.includes(BluetoothNative.SPP_SERVICE_UUID)) this.bluetoothDevices.push(device);
					}
					resolve(null);
				}).catch((err) => {
					console.log((err.message ?? err));
					resolve(null);
				})
			} catch(err) {
				const error = err as Error;
				console.log((error.message ?? err));
				resolve(null);
			}
		})
	}

	isConnected(device: BluetoothDeviceDescriptor) {
		return this.connectedDevices.findIndex((connectedDevice) => connectedDevice.name === device.name) !== -1;
	}

	isConnecting(device: BluetoothDeviceDescriptor) {
		return this.connectingDevices.findIndex((connectingDevice) => connectingDevice.name === device.name) !== -1;
	}
}