
<script lang="ts" src="./IntegrationsPageComponent.ts" />

<style lang="sass" scoped>
@import './IntegrationsPageComponent.scss'
</style>

<template>
    <div class="integrations-page-component">
        <layout>
            <template v-slot:content>
                <content-header
                    :parameters="headerParameters"
                ></content-header>

                <div class="integrations">
                    <div class="integration">
                        <div class="top">
                            <div class="head">
                                <div class="logo" :style="`background-image: url(${$assets.stripe});`"></div>

                                <a class="to-website" href="https://stripe.com/" target="_blank">
                                    stripe.com
                                    <i class="fa-regular fa-external-link"></i>
                                </a>
                            </div>
                            <span class="name"> Stripe </span>
                            <span class="description"> Stripe est une société américaine, destinée au paiement internet pour professionnels. </span>
                        </div>

                        <div class="bottom">
                            <template v-if="getAppsForType(AppType.STRIPE).length > 0">
                                <div class="green label"> Activé </div>
                                <button class="small white button" @click="showStripeModal = true">
                                    <i class="fa-regular fa-cog"></i>
                                    Configurer
                                </button>
                            </template>

                            <template v-else>
                                <button class="small white button" @click="showStripeModal = true">
                                    Activer
                                </button>
                            </template>
                        </div>
                    </div>

                    <div class="integration">
                        <div class="top">
                            <div class="head">
                                <div class="logo" :style="`background-image: url(${$assets.reservit});`"></div>

                                <a class="to-website" href="https://www.reservit.com/" target="_blank">
                                    reservit.com
                                    <i class="fa-regular fa-external-link"></i>
                                </a>
                            </div>
                            <span class="name"> Reservit </span>
                            <span class="description"> Reservit PMS · Outil simple et intuitif qui évolue pour s'adapter à vos besoins </span>
                        </div>

                        <div class="bottom">
                            <template v-if="getAppsForType(AppType.RESERVIT).length > 0">
                                <div class="green label"> Activé </div>
                                <button class="small white button" @click="showReservitConfigurationModal = true">
                                    <i class="fa-regular fa-cog"></i>
                                    Configurer
                                </button>
                            </template>

                            <template v-else>
                                <button class="small white button" @click="showReservitModal = true">
                                    Activer
                                </button>
                            </template>
                        </div>
                    </div>

                    <div class="integration">
                        <div class="top">
                            <div class="head">
                                <div class="logo">
                                    <i class="fa-regular fa-plug"></i>
                                </div>
                            </div>
                            <span class="name"> Webhooks </span>
                            <span class="description"> Reliez une application non gérée manuellement </span>
                        </div>

                        <div class="bottom">
                            <template v-if="getAppsForType(AppType.CUSTOM).length > 0">
                                <div class="green label"> Activé </div>
                                <button class="small white button" @click="showIntegrationListModal = AppType.CUSTOM">
                                    <i class="fa-regular fa-cog"></i>
                                    Configurer
                                </button>
                            </template>

                            <template v-else>
                                <button class="small white button" @click="showCustomIntegrationFormModal = true">
                                    Activer
                                </button>
                            </template>
                        </div>
                    </div>
                </div>
            </template>
        </layout>

        <app-integration-list
            v-if="showIntegrationListModal"
            :apps="getAppsForType(showIntegrationListModal)"
            @create="showIntegrationListModal = null; showCustomIntegrationFormModal = true"
            @edit="editingCustomIntegration = $event; showIntegrationListModal = null; showCustomIntegrationFormModal = true"
            @close="showIntegrationListModal = null"
        ></app-integration-list>

        <stripe-app-selector v-if="showStripeModal" @selected="selectedSpecificStripeApp($event)" @close="showStripeModal = false"></stripe-app-selector>

        <custom-integration-form
            v-if="showCustomIntegrationFormModal"
            :editing-app="editingCustomIntegration"
            @close="showCustomIntegrationFormModal = false"
        ></custom-integration-form>

        <reservit-integration
            v-if="showReservitModal"
            @created="apps.push($event)"
            @close="showReservitModal = false"
        ></reservit-integration>

        <product-reservit-binding
            v-if="showReservitConfigurationModal"
            :reservit-app="getAppsForType(AppType.RESERVIT)[0]"
            @close="showReservitConfigurationModal = false"
        ></product-reservit-binding>

        <form-modal-or-drawer
            :state="showStripeProcessorModal"
            title="Définir stripe comme méthode de paiement par défaut ?"
            subtitle="Voulez vous choisir votre compte stripe pour les paiements en ligne ?"
            @close="showStripeProcessorModal = false"
        >
            <template v-slot:buttons>
                <button type="button" class="red button" @click="showStripeProcessorModal = false"> Non, garder la même configuration </button>
                <button
                    type="button"
                    class="button"
                    :class="{loading: creatingStripeProcessorModal, disabled: creatingStripeProcessorModal}"
                    @click="selectedStripeApp()"
                >
                    Oui, choisir Stripe
                </button>
            </template>
        </form-modal-or-drawer>
    </div>
</template>