#customers-page {
    height: 100%;
    display: flex;

    @media (max-width: 900px) {
        .mobile-content-dimmer {
            position: fixed;
            inset: 0;
            opacity: 0;
            pointer-events: none;
            transition: opacity 0.5s cubic-bezier(0.22, 1, 0.36, 1);
            z-index: 100;

            &.displayed {
                opacity: 1;
                background: rgba(0, 0, 0, 0.5);
            }
        }
    }

    .layout, .customer-details {
        flex-grow: 2;
    }

    .customers-list {
        width: 395px;
        border-right: 1px solid #E2E2E2;
        padding: 20px 0;
        box-sizing: border-box;
        overflow: auto;
        flex-shrink: 0;
        transition: transform .5s cubic-bezier(0.22, 1, 0.36, 1);

        .empty-customers {
            text-align: center;
            margin-top: 20px;
            font-style: italic;
        }

        .new-customer {
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 10px 0 20px 0;
        }

        @media (max-width: 900px) {
            &.opened {
                transform: translateX(-25%);
            }
        }

        .search {
            padding: 10px 20px;

            .head {
                position: relative;
                display: flex;
                align-items: center;
                justify-content: space-between;

                .action-head {
                    display: flex;
                    align-items: center;
                    gap: 10px;
                }

                .modal-custom-filter-dimmer {
                    position: fixed;
                    inset: 0;
                    z-index: 998;
                }

                .active-filter {
                    display: flex;
                    align-items: center;
                    gap: 6px;
                    font-size: 12px;
                    background: #b5ffb5;
                    padding: 1px 8px;
                    border-radius: 4px;
                    margin: -3px 0;

                    i {
                        margin-top: 2px;
                        font-size: 13px;
                    }
                }

                .custom-filter-modal {
                    position: fixed;
                    top: 80px;
                    z-index: 999;
                    background: white;
                    padding: 20px;
                    border: 1px #E2E2E2 solid;
                    border-radius: 8px;
                    box-shadow: 0 4px 86px 0 rgba(0, 0, 0, 0.13);
                }
            }
        }

        h1 {
            font-size: 22px;
        }

        @media (max-width: 900px) {
            width: 100%;
        }

        .letter-separator {
            padding: 10px 20px;
            font-size: 16px;
            font-weight: 600;
            background: #F2F2F2;

        }

        .letter-group {
            display: flex;
            flex-direction: column;
            gap: 5px;
            border-radius: 12px;
            background: white;
            padding: 5px 5px;

            .customer {
                display: flex;
                gap: 10px;
                align-items: center;
                margin: 0 5px;
                padding: 15px;
                border-radius: 8px;
                cursor: pointer;
                user-select: none;

                &:hover {
                    background: var(--secondary-hover-color);
                }

                &.active {
                    background: var(--primary-hover-color);
                }

                .profile-picture {
                    border-radius: 50%;
                    width: 42px;
                    height: 42px;
                    flex-shrink: 0;
                    background-size: cover;
                    background-position: center;
                }

                .infos {
                    display: flex;
                    flex-direction: column;

                    .name {
                        color: #000;
                        font-size: 16px;
                        font-weight: 600;
                        text-transform: capitalize;
                    }

                    .email {
                        font-size: 14px;
                        font-weight: 400;
                        word-break: break-all;
                    }
                }
            }
        }

        .load-more {
            display: flex;
            justify-content: center;
            margin-top: 10px;
        }
    }

    .customer-details {
        @media (max-width: 900px) {
            position: fixed;
            inset: 0;
            transform: translateX(100%);
            transition: transform .5s cubic-bezier(0.22, 1, 0.36, 1);

            z-index: 102;

            &.opened {
                transform: none;
            }
        }

        .empty-state {
            display: flex;
            flex-direction: column;
            gap: 20px;
            align-items: center;
            justify-content: flex-start;
            margin-top: 200px;
            height: 100%;
            width: 100%;
            text-align: center;

            img {
                height: 36px;
            }
        }

        .selected-customer {
            display: flex;
            flex-direction: column;
            position: relative;

            .close-area {
                position: absolute;
                right: 20px;
                display: flex;
                align-items: flex-start;
                margin-bottom: 20px;
            }

            .profile-data-container {
                display: flex;
                align-items: flex-end;
                flex-wrap: wrap;
                gap: 20px;
                padding: 40px 40px 20px 40px;

                @media (max-width: 900px) {
                    padding: 20px;

                    .qr-code {
                        display: none;
                    }
                }

                border-bottom: 1px solid var(--border-color);

                .qr-code {
                    height: 96px;
                    width: 96px;
                    background-image: url('https://upload.wikimedia.org/wikipedia/commons/7/78/Qrcode_wikipedia_fr_v2clean.png');
                    background-position: center;
                    background-size: cover;
                    flex-shrink: 0;
                    margin-bottom: 10px;
                }
            }

            .profile-data {
                display: flex;
                flex-direction: column;
                gap: 10px;
                flex-grow: 2;

                .header {
                    display: flex;
                    align-items: center;
                    gap: 20px;

                    @media (max-width: 900px) {
                        padding-top: 40px;
                    }

                    .profile-picture {
                        border-radius: 50%;
                        width: 96px;
                        height: 96px;
                        flex-shrink: 0;
                        border: 4px solid #F2F2F2;
                        background-size: cover;
                        background-position: center;

                        @media (max-width: 900px) {
                            width: 64px;
                            height: 64px;
                        }
                    }

                    .infos {
                        display: flex;
                        flex-direction: column;
                        gap: 5px;

                        .name {
                            font-weight: bold;
                            font-size: 22px;
                            text-transform: capitalize;
                        }

                        .email {
                            font-size: 16px;
                        }
                    }
                }

                .various-data {
                    display: flex;
                    gap: 10px;
                    flex-wrap: wrap;
                    position: relative;

                    .data {
                        display: flex;
                        flex-direction: column;
                        gap: 4px;
                        padding: 14px;

                        .title {
                            font-size: 14px;
                            font-weight: 600;
                        }

                        .content {
                            font-size: 16px;
                            font-weight: 400;
                        }
                    }

                    //.qr-code {
                    //    height: 96px;
                    //    width: 96px;
                    //    background: red;
                    //}
                }

                .actions {
                    display: flex;
                    gap: 15px 20px;

                    @media screen and (max-width: 900px) {
                        &.mobile-hidden {
                            display: none;
                        }
                    }

                    .action {
                        display: flex;
                        align-items: center;
                        gap: 8px;
                        font-size: 14px;
                        font-style: normal;
                        font-weight: 700;
                        cursor: pointer;
                        user-select: none;
                        padding: 6px 10px;
                        border-radius: 8px;

                        &:hover {
                            background: #F2F2F2;
                        }

                        i {
                            font-size: 20px;
                        }
                    }
                }
            }

            .button-group {
                border-radius: 8px;
                display: flex;
                border: 1px solid #E2E2E2;

                &.small {
                    div {
                        font-size: 12px;
                        font-weight: 500;
                        padding: 8px 15px;
                    }
                }

                button {
                    all: unset;
                    padding: 10px 15px;
                    border-right: 1px solid #E2E2E2;
                    cursor: pointer;
                    font-size: 14px;
                    user-select: none;

                    &:first-child {
                        border-radius: 8px 0 0 8px;
                    }

                    &:last-child {
                        border: none;
                        border-radius: 0 8px 8px 0;
                    }

                    &:hover {
                        background: var(--secondary-hover-color);
                    }

                    &.active {
                        background: #F7F7F8;
                        font-weight: 600;
                    }
                }
            }

            .customer-content {
                display: flex;
                flex-direction: column;
                gap: 20px;
                padding: 40px;

                @media (max-width: 900px) {
                    padding: 20px;
                }

                .tabs {
                    display: flex;
                    justify-content: flex-start;
                }

                .empty {
                    text-align: center;
                }
            }
        }
    }

    .right-view {
        display: flex;
        flex-direction: column;
        gap: 20px;
        padding: 40px;

        @media screen and (max-width: 900px) {
            padding: 16px 20px;
        }
    }
}