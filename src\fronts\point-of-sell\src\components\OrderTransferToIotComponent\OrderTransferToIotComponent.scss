.right-modal-dimmer {
    position: fixed;
    inset: 0;
    background: rgba(0, 0, 0, 0.4);
}

.order-transfer-to-iot-component {
    .container {
        position: relative;
        display: flex;
        flex-direction: column;
        gap: 20px;
        padding: 20px 20px 80px 20px;
        height: 100%;
        overflow: auto;

        .head {
            display: flex;
            flex-direction: column;
            gap: 4px;

            .title {
                font-size: 20px;
                font-weight: 600;
            }

            .subtitle {
                font-size: 15px;
            }
        }

        .iot-establishment-devices {
            display: flex;
            flex-direction: column;
            gap: 10px;

            .device {
                display: flex;
                align-items: center;
                gap: 15px;
                padding: 10px 15px;
                border-radius: 8px;
                background: #F2F4F7;

                .left {
                    flex-grow: 2;
                    display: flex;
                    flex-direction: column;
                    gap: 4px;

                    .name {
                        font-size: 15px;
                        font-weight: 600;
                    }

                    .id {
                        font-size: 12px;
                        font-weight: 400;
                    }
                }

                .button {
                    background: #E8E8E8;
                }
            }
        }
    }

    .fa-solid.fa-star {
        color: #F49025;
    }

    .empty {
        font-style: italic;
        text-align: center;
    }

}
