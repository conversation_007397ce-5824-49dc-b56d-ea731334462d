import {Component, Prop, Vue} from "vue-facing-decorator";
import {AutoWired} from "@groupk/horizon2-core";
import {Router} from "@groupk/horizon2-front";
import {IotUtils} from "../../class/IotUtils";
import {EstablishmentDeviceRepository} from "../../../../../shared/repositories/EstablishmentDeviceRepository";
import {PosState} from "../../model/PosState";
import {MainConfig} from "../../../../../shared/MainConfig";

export type PosError = 'ESTABLISHMENT_ACCOUNT'|'AUTHENTICATION'|'CANCEL_STATUS'|'POINT_OF_SALE'|'UNKNOWN_ERROR'|null;

@Component({
	emits: []
})
export default class PosErrorComponent extends Vue {
	@Prop() error!: PosError;

	serial: string = '';

	@AutoWired(EstablishmentDeviceRepository) accessor establishmentDeviceRepository!: EstablishmentDeviceRepository;
	@AutoWired(PosState) accessor posState!: PosState
	@AutoWired(Router) accessor router!: Router;
	@AutoWired(MainConfig) accessor config!: MainConfig;

	async mounted() {
		const iotDevice = (await IotUtils.getCurrentDeviceSerialAndBrand());
		this.serial = iotDevice.hardwareId ?? iotDevice.softwareId ?? '';
	}

	back() {
		this.router.previous();
	}

	goToAuthentication() {
		let regexMatch = this.router.lastRouteRegexMatches;
		if (regexMatch && regexMatch[1]) {
			window.location.href = this.config.configuration.casFrontUrl + 'establishment/' + regexMatch[1] + '/login?redirect=' + window.location.href;
		} else {
			window.location.href = this.config.configuration.casFrontUrl + 'login?redirect=' + window.location.href;
		}
	}

	refresh() {
		window.location.reload();
	}
}