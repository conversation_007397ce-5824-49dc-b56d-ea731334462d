<script lang="ts" src="./sidebar.ts" />
<style scoped lang="sass">
@use './sidebar.scss' as *
</style>

<template>
    <sidebar
        v-if="!hiddenSidebar"
        :opened="opened"
        :minimized="minimized"
        :hidden="hidden"
        :header="header"
        :menus="menus"
        :bottom="bottom"
        :selected-navigation="selectedNavigation"
        v-on:navigation-clicked="navigationClicked($event)"
        @close="opened = false"
    >
        <template v-slot:default>
            <div class="default-slot">
                <div class="logo-container">
                    <img v-if="!minimized" src="/img/logo-long.svg" class="platform-logo" />
                    <img v-else src="/img/logo.svg" class="platform-logo" />
                </div>

                <establishment-switch></establishment-switch>
            </div>
        </template>
        <template v-slot:bottom>
            <limit-usage></limit-usage>

            <div class="negative-space">
                <application-switcher
                    @open="loadPlatform()"
                    :platform-descriptor="platformDescriptor"
                    :config="{
                      title:'Littl',
                      icon:'/img/logo.svg'
                    }"
                ></application-switcher>
            </div>
        </template>
    </sidebar>
</template>
