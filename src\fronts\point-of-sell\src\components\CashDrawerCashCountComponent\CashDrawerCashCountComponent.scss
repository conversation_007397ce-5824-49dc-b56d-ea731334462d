.cash-drawer-cash-count-component {
    position: fixed;
    inset: 0;
    display: grid;
    grid-template-columns: 1fr 1fr;
    height: 100%;

    .left, .right {
        padding: 80px;
        height: 100%;
        box-sizing: border-box;
    }

    .left {
        background: #F2F4F7;
        overflow: auto;

        .tables {
            display: flex;
            flex-direction: column;
            justify-content: center;
            gap: 20px;

            .money-table {
                background: white;
                border-radius: 8px;
                border-spacing: 0;
                font-size: 18px;

                td {
                    padding: 10px 20px;
                }

                thead {
                    font-weight: 600;
                }

                thead {
                    tr {
                        &:first-child {
                            td {
                                padding-top: 20px;
                            }
                        }
                    }
                }

                tbody {
                    tr {
                        &:last-child {
                            td {
                                padding-bottom: 20px;
                            }
                        }

                        &.selected {
                            background: #F0F1F2;
                            font-weight: 600;
                        }
                    }
                }
            }

            .total {
                text-align: right;
            }
        }
    }

    .right {
        display: flex;
        flex-direction: column;
        justify-content: center;
        gap: 20px;
        background: white;

        .hint {
            font-size: 20px;
            font-weight: 500;
            text-align: center;
            margin-bottom: 20px;
        }

        .keypad-component {
            border: none;
            grid-gap: 5px;
            width: 100%;

            .line {
                border: none;
                grid-gap: 5px;

                .key {
                    border: none;
                    background: #F2F4F7;
                    border-radius: 6px;
                    font-size: 20px;
                }
            }
        }

        .big.button {
            all: unset;
            flex-shrink: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            height: 56px;
            width: 100%;
            border-radius: 8px;
            background: var(--primary-color);
            color: var(--primary-text-color);
            font-family: Montserrat, sans-serif;
            font-size: 15px;
            font-weight: 700;
            cursor: pointer;

            @media (max-width: 900px) {
                &.mobile-only {
                    display: flex !important;
                }
            }

            &:hover {
                background: var(--primary-button-hover-color);
                color: var(--primary-text-color);
            }

            &.grey {
                background: #F2F2F2;
                color: black;
            }

            &.disabled {
                opacity: 0.5;
                pointer-events: none;
            }
        }
    }
}