<script lang="ts" src="./OrderPaymentsQuickEditComponent.ts">
</script>

<style lang="sass" scoped>
@import './OrderPaymentsQuickEditComponent.scss'
</style>

<template>
    <div class="order-payments-quick-edit-component">
        <div class="header">
            <span class="title"> Modifier les paiements de la commande </span>
            <span class="subtitle"> Passer d’un moyen de paiement à un autre sur la commande </span>
        </div>

        <span class="text-separator">
            Moyens de paiement actuels
        </span>

        <div class="current-payments">
            <div class="current">
                <span class="method"> CB </span>
                <span class="amount"> 16,00 € </span>
            </div>

            <div class="current">
                <span class="method"> Liquide </span>
                <span class="amount"> 8,00 € </span>
            </div>
        </div>

        <span class="text-separator">
                Sélectionnez un moyen de paiement
            </span>

        <div class="payment-methods">
            <div class="payment-method">
                CB
            </div>
            <div class="payment-method">
                Liquide
            </div>
        </div>

        <div class="buttons">
            <button class="disabled button">
                Passer toute la commande en CB
            </button>
        </div>
    </div>
</template>