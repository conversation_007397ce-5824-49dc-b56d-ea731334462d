<script lang="ts" src="./addChip.ts">
</script>

<style lang="sass">
@import './addChip.scss'
</style>

<template>
    <div id="add-chip-page">
        <div class="back-button grey button" @click="router.previous()">
            <i class="fa-regular fa-arrow-left"></i>
            Retour
        </div>

        <div class="form">
            <div class="input">
                <label> Code affiché sur le support Cashless </label>

                <input type="text" placeholder="XXXX-XXXX" />

                <div class="info" @click="showCodeHint = true"> Où trouver le code ? </div>
            </div>

            <div class="input">
                <div>
                    <label> Nom du support </label>
                    <div class="description"> Identifiez cette puce plus facilement en lui attribuant un nom </div>
                </div>

                <input type="text" placeholder="Nom du support" />
            </div>

            <button class="add-button disabled button">
                Lier la puce à mon compte
            </button>
        </div>

        <template v-if="showCodeHint">
            <div @click="showCodeHint = false;" class="code-hint-dimmer"></div>
            <div @click="showCodeHint = false;" class="close-code-hint">
                <i class="fa-regular fa-xmark"></i>
                Fermer
            </div>
            <img alt="Le code est écrit sur votre support Cashless" class="code-hint" src="https://help.gopay.com/img.php?hash=8f7504336c52aa0b9986d13f37e43d1142201e4be608a643b3b802d3d6009167.png">
        </template>
    </div>
</template>