#!/bin/sh

set -e

FRONT_NAME="$1"

IMAGE_NAME_FINAL="${CI_REGISTRY_IMAGE}/$FRONT_NAME/nginx"
IMAGE_VERSION="$(cat src/fronts/$FRONT_NAME/project.json | jq -r '.version')"

docker login -u "$CI_REGISTRY_USER" -p "$CI_JOB_TOKEN" "$CI_REGISTRY"

docker build -t "${IMAGE_NAME_FINAL}:${CI_COMMIT_SHORT_SHA}" --build-arg FRONT_NAME=$FRONT_NAME  -f docker/nginx.dockerfile .

IMAGE_VERSION_DATE=$(date +%Y-%m-%d)
echo "Creating docker images with version $IMAGE_VERSION_DATE"
docker tag "${IMAGE_NAME_FINAL}:${CI_COMMIT_SHORT_SHA}" "${IMAGE_NAME_FINAL}:${IMAGE_VERSION_DATE}"
docker push "${IMAGE_NAME_FINAL}:${IMAGE_VERSION_DATE}"
echo "Creating docker images with version $IMAGE_VERSION"
docker tag "${IMAGE_NAME_FINAL}:${CI_COMMIT_SHORT_SHA}" "${IMAGE_NAME_FINAL}:${IMAGE_VERSION}"
docker push "${IMAGE_NAME_FINAL}:${IMAGE_VERSION}"

