.settings-component {
    display: flex;
    height: 100%;

    .settings-navigation {
        display: flex;
        flex-direction: column;
        gap: 10px;
        width: 340px;
        padding: 20px;
        box-sizing: border-box;
        flex-shrink: 0;

        @media (max-width: 900px) {
            width: 100%;

            .element {
                padding: 20px !important;
                background: white;

                &.active {
                    background: white !important;
                    color: black !important;
                }
            }
        }

        .element {
            display: flex;
            align-items: center;
            gap: 15px;
            padding: 15px 20px;
            border-radius: 8px;

            &.active {
                background: var(--primary-color);
                color: var(--primary-text-color);
            }

            i {
                font-size: 22px;
            }

            .content {
                display: flex;
                flex-direction: column;
                gap: 4px;

                .title {
                    font-size: 15px;
                    font-weight: 600;
                }

                .subtitle {
                    font-size: 13px;
                }
            }

        }
    }

    .back-button {
        display: flex;
        align-items: center;
        gap: 10px;
        margin-bottom: 10px;
        padding: 10px 0;

        @media (min-width: 900px) {
            display: none;
        }
    }

    .settings-content {
        display: flex;
        flex-direction: column;
        gap: 20px;
        padding: 25px 20px;
        background: white;
        flex-grow: 2;
        height: 100%;
        overflow: auto;
        box-sizing: border-box;

        @media (max-width: 900px) {
            position: fixed;
            inset: 0;
            z-index: 950;
            transform: translateX(100%);
            transition: transform .3s cubic-bezier(0.22, 1, 0.36, 1);
            padding-top: var(--safe-area-top);

            .two-inputs{
                grid-template-columns: 1fr;
            }


            &.opened {
                transform: none;
            }
        }

        .header {
            display: flex;
            flex-direction: column;
            gap: 4px;

            .title {
                font-size: 18px;
                font-weight: 600;
            }

            .subtitle {
                font-size: 16px;
            }
        }
    }

    h2 {
        margin: 10px 0 0 0;
        font-size: 20px;
        font-weight: 600;
    }


    .two-areas {
        display: grid;
        grid-template-columns: 1fr 1fr;
        grid-gap: 15px;
    }

    .area {
        display: flex;
        padding: 20px;
        flex-direction: column;
        gap: 20px;
        border-radius: 8px;
        background: #FFF;

        >.title {
            color: #636363;
            font-family: Montserrat, sans-serif;
            font-size: 16px;
        }
    }

    .boolean-input {
        box-sizing: border-box;
        border: none;
        background: #F2F4F7;
        color: black;

        .right {
            flex-grow: 2;

            .title {
                font-size: 15px;
                font-weight: 600;
            }

            .description {
                font-size: 14px;
            }
        }

        &.selected {
            background: var(--primary-color);
            color: var(--primary-text-color);

            .title {
                font-weight: 700;
            }
        }

        input {
            width: 200px;
        }

        .button-group {
            display: flex;
            align-items: center;

            .button {
                border-radius: 0;

                &:first-child {
                    border-top-left-radius: 8px;
                    border-bottom-left-radius: 8px;
                }

                &:last-child {
                    border-top-right-radius: 8px;
                    border-bottom-right-radius: 8px;
                }

                &:not(.active) {
                    background: white !important;
                    color: black !important;
                }

                &.active {
                    background: var(--primary-color);
                    color: var(--primary-text-color);
                }
            }
        }
    }

    .debug-content {
        display: flex;
        flex-direction: column;
        gap: 20px;

        .buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }

        table {
            .empty-row {
                text-align: center;
            }

            tr {
                td {
                    padding: 15px;
                }
            }

            * {
                font-size: 16px;
            }
        }
    }

    .table-scroll-wrapper {
        overflow: auto;

        .no-break {
            white-space: nowrap;
        }

        &.no-margin {
            margin-right: -40px;
            padding-right: 40px;
        }
    }
}