.page-top {
    display: flex;
    flex-direction: column;
    gap: 25px;
    padding: 40px 40px 20px 40px;
    border-bottom: 1px solid #E2E2E2;

    @media screen and (max-width: 900px) {
        padding: 16px 20px;
    }

    h1 {
        margin: 0;
        font-size: 32px;
        font-style: normal;
        font-weight: 700;
    }

    .actions {
        display: flex;
        gap: 15px 40px;

        .action {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 16px;
            font-style: normal;
            font-weight: 700;
            cursor: pointer;

            i {
                font-size: 20px;
            }
        }
    }
}

.page-search {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 20px 40px;
    border-bottom: 1px solid #E2E2E2;

    @media screen and (max-width: 900px) {
        padding: 16px 20px;
    }

    input {
        all: unset;
        color: black;
        font-size: 14px;
        font-family: Montserrat, sans-serif;
        font-style: normal;
        font-weight: 500;
        width: 100%;
    }
}
