<script lang="ts" src="./SettingsComponent.ts">
</script>

<style lang="sass" scoped>
@import './SettingsComponent.scss'
</style>

<template>
    <div class="settings-component">

        <div class="settings-navigation">
            <div class="back-button" @click="posState.showQuickActions = false">
                <i class="fa-regular fa-chevron-left"></i>
                Retour
            </div>

            <div class="element" :class="{active: page === 'PERSO' || page === null}" @click="page = 'PERSO'">
                <i class="fa-regular fa-universal-access fa-fw"></i>
                <div class="content">
                    <span class="title"> Personnalisation </span>
                    <span class="subtitle"> Affichage - Performances </span>
                </div>
            </div>

            <div class="element" :class="{active: page === 'PAYMENT'}" @click="page = 'PAYMENT'">
                <i class="fa-regular fa-network-wired fa-fw"></i>
                <div class="content">
                    <span class="title"> Paiements </span>
                    <span class="subtitle"> Configurer les appareils de paiement </span>
                </div>
            </div>

            <div class="element" :class="{active: page === 'PRINTERS'}" @click="page = 'PRINTERS'">
                <i class="fa-regular fa-print fa-fw"></i>
                <div class="content">
                    <span class="title"> Imprimantes </span>
                    <span class="subtitle"> Impression reçu / ticket de caisse </span>
                </div>
            </div>

            <div class="element" :class="{active: page === 'BLUETOOTH'}" @click="page = 'BLUETOOTH'">
                <i class="fa-regular fa-bluetooth fa-fw"></i>
                <div class="content">
                    <span class="title"> Douchette laser </span>
                    <span class="subtitle"> Configurer une douchette laser </span>
                </div>
            </div>

            <div class="element" :class="{active: page === 'TRANSFERS'}" @click="page = 'TRANSFERS'">
                <i class="fa-regular fa-arrow-right-arrow-left fa-fw"></i>
                <div class="content">
                    <span class="title"> Transferts </span>
                    <span class="subtitle"> Liste des demandes de transfert </span>
                </div>
            </div>

            <div class="element" :class="{active: page === 'DEBUG'}" @click="page = 'DEBUG'">
                <i class="fa-regular fa-code fa-fw"></i>
                <div class="content">
                    <span class="title"> Debug </span>
                    <span class="subtitle"> Debug </span>
                </div>
            </div>
        </div>

        <div class="settings-content" :class="{opened: page !== null}">
            <template v-if="page === 'PERSO' || page === null">
                <div class="header">
                    <div class="back-button" @click="page = null">
                        <i class="fa-regular fa-chevron-left"></i>
                        Retour
                    </div>
                    <div class="title"> Personnalisation </div>
                    <div class="subtitle"> Adapter les fonctionnalités et l'interface selon vos besoins spécifiques </div>
                </div>

                <div class="two-inputs">
                    <div class="boolean-input" :class="{selected: mainPosProfile.displayProductsSearch}" @click="mainPosProfile.displayProductsSearch = !mainPosProfile.displayProductsSearch; savePosProfile()">
                        <i class="fa-regular" :class="mainPosProfile.displayProductsSearch ? 'fa-square-check' : 'fa-square'"></i>
                        <div class="right">
                            <span class="title"> Recherche </span>
                            <span class="description"> Afficher une barre de recherche au dessus des produits </span>
                        </div>
                    </div>

                    <div class="boolean-input" :class="{selected: mainPosProfile.displayQuickPay}" @click="mainPosProfile.displayQuickPay = !mainPosProfile.displayQuickPay; savePosProfile()">
                        <i class="fa-regular" :class="mainPosProfile.displayQuickPay ? 'fa-square-check' : 'fa-square'"></i>
                        <div class="right">
                            <span class="title"> Paiement rapide </span>
                            <span class="description"> Afficher deux méthodes de paiement rapides dans la commande </span>
                        </div>
                    </div>
                </div>

                <div class="two-inputs">
                    <div class="boolean-input" :class="{selected: mainPosProfile.displayProductImage}" @click="mainPosProfile.displayProductImage = !mainPosProfile.displayProductImage; savePosProfile()">
                        <i class="fa-regular" :class="mainPosProfile.displayProductImage ? 'fa-square-check' : 'fa-square'"></i>
                        <div class="right">
                            <span class="title"> Image produit </span>
                            <span class="description"> Afficher l’image du produit dans la caisse </span>
                        </div>
                    </div>

                    <div class="boolean-input" :class="{selected: mainPosProfile.allowPurchaseDiscounts}" @click="mainPosProfile.allowPurchaseDiscounts = !mainPosProfile.allowPurchaseDiscounts; savePosProfile()">
                        <i class="fa-regular" :class="mainPosProfile.allowPurchaseDiscounts ? 'fa-square-check' : 'fa-square'"></i>
                        <div class="right">
                            <span class="title"> Remises </span>
                            <span class="description"> Autoriser à appliquer des remises à une commande </span>
                        </div>
                    </div>
                </div>

                <div class="two-inputs">
                    <div class="boolean-input" :class="{selected: localSettings.autoCloseOrderAfterPayment}" @click="localSettings.autoCloseOrderAfterPayment = !localSettings.autoCloseOrderAfterPayment; saveLocalSettings()">
                        <i class="fa-regular" :class="localSettings.autoCloseOrderAfterPayment ? 'fa-square-check' : 'fa-square'"></i>
                        <div class="right">
                            <span class="title"> Fermer la commande automatiquement </span>
                            <span class="description"> lorsque la commande est 100% payée </span>
                        </div>
                    </div>

                    <div class="boolean-input" :class="{selected: localSettings.autoSendToKitchenAfterPayment}" @click="localSettings.autoSendToKitchenAfterPayment = !localSettings.autoSendToKitchenAfterPayment; saveLocalSettings()">
                        <i class="fa-regular" :class="localSettings.autoSendToKitchenAfterPayment ? 'fa-square-check' : 'fa-square'"></i>
                        <div class="right">
                            <span class="title"> Envoyer le ticket cuisine automatiquement </span>
                            <span class="description"> lorsque la commande est 100% payée </span>
                        </div>
                    </div>

                    <div class="boolean-input" :class="{selected: mainPosProfile.performanceMode}" @click="mainPosProfile.performanceMode = !mainPosProfile.performanceMode; savePosProfile()">
                        <i class="fa-regular" :class="mainPosProfile.performanceMode ? 'fa-square-check' : 'fa-square'"></i>
                        <div class="right">
                            <span class="title"> Mode performances </span>
                            <span class="description"> A activer sur les appareils peu puissants </span>
                        </div>
                    </div>
                </div>


                <div class="boolean-input">
                    <div class="right">
                        <span class="title"> Nombre de colonnes </span>
                        <span class="description"> Nombre de produits affichés par ligne dans la caisse </span>
                    </div>
                    <div class="button-group">
                        <button class="button" :class="{active: posState.columns === 3}" @click="posState.columns = 3"> 3 </button>
                        <button class="button" :class="{active: posState.columns === 4}" @click="posState.columns = 4"> 4 </button>
                        <button class="button" :class="{active: posState.columns === 6}" @click="posState.columns = 6"> 6 </button>
                    </div>
                </div>

            </template>

            <template v-if="page === 'PAYMENT'">
                <div class="header">
                    <div class="back-button" @click="page = null">
                        <i class="fa-regular fa-chevron-left"></i>
                        Retour
                    </div>
                    <div class="title"> Paiements </div>
                    <div class="subtitle"> Reliez vos appareils de paiements à votre caisse afin d’automatiser la saisie des montants </div>
                </div>

                <payment-method-settings :main-profile ="mainPosProfile" :pos-state="posState"></payment-method-settings>
            </template>

            <template v-if="page === 'PRINTERS'">
                <div class="header">
                    <div class="back-button" @click="page = null">
                        <i class="fa-regular fa-chevron-left"></i>
                        Retour
                    </div>
                    <div class="title"> Imprimantes </div>
                    <div class="subtitle"> Configurez l'imprimante pour l'impression des reçus / tickets de caisse </div>
                </div>

                <print-settings></print-settings>
            </template>

            <template v-if="page === 'BLUETOOTH'">
                <div class="header">
                    <div class="back-button" @click="page = null">
                        <i class="fa-regular fa-chevron-left"></i>
                        Retour
                    </div>
                    <div class="title"> Douchette laser </div>
                    <div class="subtitle"> Connectez une douchette en bluetooth à votre caisse puis sélectionnez la ici </div>
                </div>

                <bluetooth-settings></bluetooth-settings>
            </template>

            <template v-if="page === 'TRANSFERS'">
                <div class="header">
                    <div class="back-button" @click="page = null">
                        <i class="fa-regular fa-chevron-left"></i>
                        Retour
                    </div>
                    <div class="title"> Transferts de commandes </div>
                    <div class="subtitle"> Liste des demandes de transfert de commande reçus et envoyées </div>
                </div>

                <transfers-settings></transfers-settings>
            </template>

            <template v-if="page === 'DEBUG'">
                <div class="header">
                    <div class="back-button" @click="page = null">
                        <i class="fa-regular fa-chevron-left"></i>
                        Retour
                    </div>
                    <div class="title"> Debug </div>
                    <div class="subtitle"> {{ device.brand }} - {{ device.hardwareId ?? device.softwareId }} </div>
                </div>

                <div class="debug-content">
                    <div class="buttons">
                        <button class="button" @click="setFullScreen()">
                            Fullscreen
                        </button>
                        <button class="button" @click="reload()">
                            reload
                        </button>
						<button class="button" @click="deletePrinters()">
							Delete printers
						</button>
                        <button class="button" aria-label="error-sentry-button" @click="logError()">
                            Debug error
                        </button>
                        <button class="button" aria-label="error-sentry-button" @click="startRemoteAssist()">
                            Lancer remote assist
                        </button>
                        <button class="white button" :class="{disabled: syncing, loading: syncing}" @click="forceAllSync()">
                            <i class="fa-regular fa-sync"></i>
                            Tout synchroniser
                        </button>
                    </div>

                    <div class="table-scroll-wrapper">
                        <table class="data-table">
                            <thead>
                            <tr>
                                <td> Créé par </td>
                                <td> Total TTC </td>
                                <td> Reste à payer </td>
                                <td> Payé en </td>
                                <td> Date de création </td>
                                <td> Actions </td>
                            </tr>
                            </thead>
                            <tbody>
                            <tr v-if="notSyncedOrders.length === 0">
                                <td class="empty-row" colspan="100%"> Aucune commande non synchronisée </td>
                            </tr>
                            <tr v-for="localOrder of notSyncedOrders">
                                <order-line :local-order="localOrder" :pos-state="posState"></order-line>
                            </tr>
                            </tbody>
                        </table>
                    </div>

                </div>
            </template>
        </div>
    </div>
</template>