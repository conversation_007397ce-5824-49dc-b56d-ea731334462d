
<script lang="ts" src="./WalletNetworkTransactionFormComponent.ts">
</script>

<style lang="sass">
@use './WalletNetworkTransactionFormComponent.scss' as *
</style>

<template>
    <div class="wallet-empty-balance-form-component">
        <form-modal-or-drawer
            :state="opened"
            title="Êtes-vous sûr de vouloir vider la puce ?"
            subtitle="Le solde de la puce est sur le point d'être vidé à distance"
            @close="close()"
        >
            <template v-slot:content>
                <div class="loading-container" v-if="loading">
                    <div class="loader"></div>
                </div>

                <template v-else>
                    <div class="form-warning">
                        <i class="fa-solid fa-exclamation-circle"></i>
                        <div class="details">
                            <span class="title"> Information </span>
                            <span class="description">
                            Attention, malgré la mise à jour du solde à distance, il est possible que la puce soit
                            acceptée sur des terminaux hors ligne tant qu'ils n'ont pas retrouvé de connexion internet.
                        </span>
                        </div>
                    </div>

                    <div class="input-group">
                        <label> Choisir le point de vente dans lequel la transaction de réinitialisation apparaîtra </label>
                        <dropdown
                            placeholder="Point de vente"
                            :searchable="true"
                            :values="profilesDropdownValues"
                            @update="selectedProfile = $event"
                        ></dropdown>
                    </div>

                    <div class="form-error" v-if="error">
                        <i class="fa-solid fa-exclamation-circle"></i>
                        <div class="details">
                            <span class="title"> {{ error.title }} </span>
                            <span class="description">
                            {{ error.description }}
                        </span>
                        </div>
                    </div>
                </template>
            </template>

            <template v-slot:buttons>
                <button class="white button" :class="{disabled: loading}" @click="close()"> Annuler </button>
                <button class="button" :class="{red: transactionType === 'CLEAR', disabled: loading, loading: loading}" @click="validate">
                    <i v-if="transactionType === 'CLEAR'" class="fa-regular fa-trash-alt"></i>
                    {{ transactionType === 'CLEAR' ? 'Vider' : 'Activer' }}
                </button>
            </template>
        </form-modal-or-drawer>
    </div>
</template>