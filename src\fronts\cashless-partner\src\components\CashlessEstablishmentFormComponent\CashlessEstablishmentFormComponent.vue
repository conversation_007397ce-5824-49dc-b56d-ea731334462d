<script lang="ts" src="./CashlessEstablishmentFormComponent.ts">
</script>

<style lang="sass">
@use './CashlessEstablishmentFormComponent.scss' as *
</style>

<template>
    <div class="cashless-establishment-form-component">
        <form-modal-or-drawer
            :title="'Paramètres globaux'"
            :subtitle="'Modifiez les paramètres de tous les points de vente'"
            :state="opened"
            @close="close()"
        >
            <template v-slot:content>
                <div class="input-group right-icon">
                    <label for="title"> Mot de passe admin </label>
                    <template v-if="cashlessEstablishment.adminPassword !== undefined">
                        <div class="icon-group">
                            <i class="fa-regular fa-xmark" @click="cashlessEstablishment.adminPassword = undefined"></i>
                            <div class="ui input" v-if="cashlessEstablishment.adminPassword !== undefined">
                                <input v-model="cashlessEstablishment.adminPassword" type="text" />
                            </div>
                        </div>
                    </template>

                    <button class="button" @click="cashlessEstablishment.adminPassword = ''" v-else>
                        <i class="fa-regular fa-plus"></i>
                        Ajouter un mot de passe admin global
                    </button>
                </div>

                <div class="sub-form">
                    <div class="header">
                        <span class="title"> Puces admin </span>
                    </div>

                    <div class="two-inputs">
                        <span v-if="!cashlessEstablishment.adminPublicChipIds || cashlessEstablishment.adminPublicChipIds.length === 0" class="empty-sub-form"> Aucune puce admin </span>
                        <template v-else>
                            <div class="input-group" v-for="(chipId, index) in cashlessEstablishment.adminPublicChipIds">
                                <label for="title"> Numéro de puce </label>
                                <div class="action-input">
                                    <input maxlength="8" class="white" :value="cashlessEstablishment.adminPublicChipIds[index]" @input="setAdminPublicChip($event, index)"  type="text" />
                                    <i @click="cashlessEstablishment.adminPublicChipIds.splice(index, 1)" class="fa-regular fa-trash-alt"></i>
                                </div>
                            </div>
                        </template>
                    </div>

                    <div>
                        <button class="small transparent button" @click="addAdminChip()">
                            <i class="fa-regular fa-plus"></i>
                            Ajouter une puce
                        </button>
                    </div>
                </div>

                <div class="form-error" v-if="error">
                    <i class="fa-solid fa-exclamation-circle"></i>
                    <div class="details">
                        <span class="title"> Erreur </span>
                        <span class="description"> {{ error }} </span>
                    </div>
                </div>
            </template>

            <template v-slot:buttons>
                <button class="button" :class="{disabled: saving, loading: saving}" @click="saveCashlessEstablishment()"> Sauvegarder </button>
            </template>
        </form-modal-or-drawer>
    </div>
</template>