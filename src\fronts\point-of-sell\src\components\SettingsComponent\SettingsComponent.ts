import {Component, Prop, Vue} from "vue-facing-decorator";
import BluetoothDeviceComponent from "../BluetoothDeviceComponent/BluetoothDeviceComponent.vue";
import PrintSettingsComponent from "../PrintSettingsComponent/PrintSettingsComponent.vue";
import PaymentMethodSettingsComponent from "../PaymentMethodSettingsComponent/PaymentMethodSettingsComponent.vue";
import {PosState} from "../../model/PosState";
import {AutoWired} from "@groupk/horizon2-core";
import {PosProfileRepository} from "../../../../../shared/repositories/PosProfileRepository";
import {ErrorHandler} from "../../class/ErrorHandler";
import {LocalOrderRepository} from "../../repositories/LocalOrderRepository";
import {LocalOrder} from "../../model/LocalOrder";
import {AppBus} from "../../config/AppBus";
import OrderLineComponent from "../OrderLineComponent/OrderLineComponent.vue";
import {
	EstablishmentDeviceCreationNewApiIn,
	PosProfileApiOut
} from "@groupk/mastodon-core";
import {PosProfile} from "../../model/PosProfile";
import {IotUtils} from "../../class/IotUtils";
import {PrinterRepository} from "../../repositories/PrinterRepository";
import {LocalSettings, LocalSettingsRepository} from "../../repositories/LocalSettingsRepository";
import {RemoteAssistNative} from "@groupk/native-bridge";
import TransfersSettingsComponent from "../TransfersSettingsComponent/TransfersSettingsComponent.vue";

@Component({
	components: {
		'order-line': OrderLineComponent,
		'bluetooth-settings': BluetoothDeviceComponent,
		'print-settings': PrintSettingsComponent,
		'payment-method-settings': PaymentMethodSettingsComponent,
		'transfers-settings': TransfersSettingsComponent,
	}
})
export default class SettingsComponent extends Vue {
	@Prop() posState!: PosState;

	mainPosProfile!: PosProfileApiOut;

	device!: EstablishmentDeviceCreationNewApiIn;

	orders: LocalOrder[] = [];

	localSettings!: LocalSettings;

	page: 'PERSO'|'PAYMENT'|'PRINTERS'|'BLUETOOTH'|'TRANSFERS'|'DEBUG'|null = null;
	syncing: boolean = false;

	@AutoWired(PosProfileRepository) accessor posProfileRepository!: PosProfileRepository;
	@AutoWired(LocalOrderRepository) accessor localOrderRepository!: LocalOrderRepository;
	@AutoWired(LocalSettingsRepository) accessor localSettingsRepository!: LocalSettingsRepository;
	@AutoWired(PrinterRepository) accessor printerRepository!: PrinterRepository;
	@AutoWired(RemoteAssistNative) accessor remoteAssistNative!: RemoteAssistNative;
	@AutoWired(ErrorHandler) accessor errorHandler!: ErrorHandler;
	@AutoWired(AppBus) accessor appBus!: AppBus;

	async beforeMount() {
		const firstLevelPosProfile = this.posState.getFirstLevelProfile();
		if(!firstLevelPosProfile) throw new Error('no_main_pos_profile');
		this.mainPosProfile = firstLevelPosProfile
		this.localSettings = this.localSettingsRepository.get();

		this.appBus.on('orderSaved', this.updateOrder);

		this.orders = await this.localOrderRepository.findAll();
		this.device = await IotUtils.getCurrentDeviceSerialAndBrand();
	}

	unmounted() {
		this.appBus.off('orderSaved', this.updateOrder);
	}

	async updateOrder(localOrder: LocalOrder) {
		const index = this.orders.findIndex((ramLocalOrder) => ramLocalOrder.uid === localOrder.uid);
		if(index !== -1) {
			this.orders.splice(index, 1, localOrder);
		} else {
			this.orders.push(localOrder);
		}
		this.$forceUpdate();
	}

	setFullScreen() {
		if (!document.fullscreenElement) {
			document.documentElement.requestFullscreen();
		} else if (document.exitFullscreen) {
			document.exitFullscreen();
		}
	}

	reload() {
		window.location.reload();
	}

	deletePrinters() {
		this.printerRepository.deleteReceiptPrinter();
	}

	async savePosProfile() {
		await this.posProfileRepository.callContract('update', {establishmentUid: this.posState.establishmentUid, posProfileUid: this.mainPosProfile.uid}, PosProfile.apiOutToApiIn(this.mainPosProfile))
	}

	async saveLocalSettings() {
		this.localSettingsRepository.save(this.localSettings);
	}

	async startRemoteAssist() {
		try {
			await this.remoteAssistNative.enable();
		} catch(err) {
			this.appBus.emit('displayToast', {
				title: `Echec`,
				description: JSON.stringify(err),
				color: 'red',
				closable: true,
				duration: 3000,
			});
		}
	}

	logError() {
		this.errorHandler.logToSentry({
			title: 'Debug error',
			description: 'Sentry is initialized'
		});
	}

	get notSyncedOrders() {
		return this.orders.filter((localOrder) => !localOrder.synced);
	}

	async forceAllSync(){
		this.syncing = true;
		for await(let order of (await this.localOrderRepository.findAllNotSyncedCursor()).generator()) {
			try {
				await this.localOrderRepository.saveAndResync(order);
			} catch(err) {}
		}
		this.syncing = false;
	}

}