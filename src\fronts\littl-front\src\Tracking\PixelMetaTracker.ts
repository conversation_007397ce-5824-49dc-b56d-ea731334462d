import {AutoWired} from "@groupk/horizon2-core";
import {AppBus} from "../AppBus";

declare global {
	interface Window {
		fbq: (arg1: string, arg2: string, arg3?: unknown) => void;
	}
}

export class PixelMetaTracker {
	@AutoWired(AppBus) accessor appBus!: AppBus;

	constructor(pixelId: string) {
		let scriptEle = document.createElement('script');
		scriptEle.innerHTML = `
		 !function(f,b,e,v,n,t,s)
		  {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
		  n.callMethod.apply(n,arguments):n.queue.push(arguments)};
		  if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
		  n.queue=[];t=b.createElement(e);t.async=!0;
		  t.src=v;s=b.getElementsByTagName(e)[0];
		  s.parentNode.insertBefore(t,s)}(window, document,'script',
		  'https://connect.facebook.net/en_US/fbevents.js');
		  fbq('init', '${pixelId}');
		`;
		document.head.appendChild(scriptEle);

		this.appBus.on('page-view', this.propagatePageViewEvent);
		this.appBus.on('login', this.propagateLoginEvent);
		this.appBus.on('sign-up', this.propagateSignupEvent);
	}

	unbind() {
		this.appBus.off('page-view', this.propagatePageViewEvent);
		this.appBus.off('login', this.propagateLoginEvent);
		this.appBus.off('sign-up', this.propagateSignupEvent);
	}

	propagatePageViewEvent = () => {
		window.fbq('track', 'PageView');
	}


	propagateLoginEvent = () => {
		window.fbq('track', 'login');
	}

	propagateSignupEvent = () => {
		window.fbq('track', 'CompleteRegistration');
	}
}