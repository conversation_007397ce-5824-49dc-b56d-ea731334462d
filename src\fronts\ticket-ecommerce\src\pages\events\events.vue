<script lang="ts" src="./events.ts"/>

<style lang="sass" scoped>
@import './events.scss'
</style>

<template>
    <div id="events-page" class="scrolling-page">
        <div class="loading-container" v-if="loading">
            <div class="loader"></div>
        </div>

        <div v-else class="centered-content">
            <h1>
                {{ establishment.name }}
            </h1>

            <h3> Événements à venir </h3>
            <div class="events">
                <div class="event" v-for="event of filteredEvents">
                    <a :href="getEventUrl(event)" class="banner" :style="`background-image: url(${getEventBannerUrl(event)})`">
                        <i v-if="getEventBannerUrl(event) === null" class="fa-regular fa-camera-slash"></i>
                    </a>

                    <div class="content">
                        <a class="data" :href="getEventUrl(event)">
                            <span class="name"> {{ event.name }}</span>
                            <span class="description" v-if="event.startingDate"> Le {{ $filters.Day(event.startingDate) }} {{ $filters.Year(event.startingDate) }}</span>
                            <span class="description" v-else> Pas de date </span>
                        </a>
                    </div>
                </div>
            </div>
        </div>

    </div>
</template>