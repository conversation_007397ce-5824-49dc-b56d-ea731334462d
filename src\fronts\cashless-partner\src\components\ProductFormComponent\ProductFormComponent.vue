<script lang="ts" src="./ProductFormComponent.ts">
</script>

<style lang="sass" scoped>
@use './ProductFormComponent.scss' as *
</style>

<template>
    <form-modal-or-drawer
        :state="true"
        :title="editingProduct ? 'Modifier le produit' : 'Créer un produit'"
        :subtitle="'Le produit pourra être associé a un terminal de vente'"
    >
        <template v-slot:content>
            <div class="input-group">
                <label for="title"> Nom </label>
                <div class="ui input">
                    <input v-model="product.name" type="text" id="name" placeholder="Nom" />
                </div>
            </div>

            <div class="input-group">
                <label for="title"> Prix (€) </label>
                <div class="ui input">
                    <price-input
                        :p="product.price"
                        @change="product.price = $event"
                        placeholder="Ajouter un '-' devant le prix pour débiter"
                        :options="{numeralPositiveOnly: false}">
                    </price-input>
                </div>
            </div>

            <div class="price-warning" v-if="product.price > 0">
                <i class="fa-solid fa-circle-exclamation"></i>
                Un prix positif va créditer les puces
            </div>

            <div class="input-group">
                <label for="title"> Image </label>

                <div class="logo-input">
                    <file-input-component
                        :default-image="product.img"
                        @selected="product.img = $event"
                    ></file-input-component>

<!--                    <div class="icon-bank-button" @click="showIconModal = true">-->
<!--                        <div class="icons">-->
<!--                            <i class="fa-regular fa-star"></i>-->
<!--                        </div>-->

<!--                        <span> Banque d'icônes </span>-->
<!--                    </div>-->

                    <div v-if="showIconModal" class="icon-bank-dimmer" @click="showIconModal = false"></div>
                    <icon-bank
                        v-if="showIconModal"
                        class="icon-bank"
                        @selected-icon="selectIcon($event)"
                    ></icon-bank>
                </div>
             </div>

            <div class="form-error" v-if="error">
                <i class="fa-solid fa-exclamation-circle"></i>
                <div class="details">
                    <span class="title"> Erreur </span>
                    <span class="description">{{ error }}</span>
                </div>
            </div>
        </template>

        <template v-slot:buttons>
            <button class="button" :class="{loading: saving, disabled: saving}" @click="save()">
                <i class="fa-regular fa-check"></i>
                Sauvegarder
            </button>
        </template>
    </form-modal-or-drawer>
</template>