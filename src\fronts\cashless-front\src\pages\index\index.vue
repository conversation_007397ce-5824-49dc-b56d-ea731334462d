<script lang="ts" src="./index.ts">
</script>

<style lang="sass">
@import './index.scss'
</style>

<template>
    <div id="index-page">
        <customer-info-form v-if="!specificState.customer" @updated="update()"></customer-info-form>
        <template v-else>
            <div class="top">
                <div class="top-actions">
                    <div class="logo">
                        <span class="bold"> Cashless </span> <br />
                        By Weecop
                    </div>
                    <a :href="urlBuilder.buildUrl('/account')" class="button">
                        <i class="fa-regular fa-user"></i>
                        Mon compte
                    </a>
                </div>

                <div class="main-container">

                    <div class="balance-container" v-if="specificState.currentWallet">
                        <span class="label"> Solde puce {{ $filters.Chip(specificState.currentWallet.chipVisualId) }} </span>
                        <span class="balance"> {{ $filters.Money(specificState.currentWallet.balance) }} </span>
                    </div>

                    <div class="actions">
                        <a :href="urlBuilder.buildUrl('/refill')" class="button">
                            <i class="fa-regular fa-arrow-up"></i>
                            Recharger
                        </a>
                        <a :href="urlBuilder.buildUrl('/add-chip')" class="button" v-if="'uid' in specificState.customer">
                            <i class="fa-regular fa-plus"></i>
                            Ajouter un support
                        </a>
                        <button class="icon button" @click="logout()">
                            <i class="fa-regular fa-arrow-right-from-bracket"></i>
                        </button>
                    </div>

<!--                    <div class="warning" v-if="wallets.length === 0">-->
<!--                        <i class="fa-regular fa-circle-exclamation"></i>-->
<!--                        <div class="right">-->
<!--                            <span class="title"> Aucun support lié </span>-->
<!--                            <span class="description">-->
<!--                                Vous n'avez pas encore lié de support à votre compte-->
<!--                            </span>-->
<!--                        </div>-->
<!--                    </div>-->

                </div>

<!--                <div class="chips-container" v-if="wallets.length > 1">-->
<!--                    <span> Mes supports Cashless </span>-->

<!--                    <div class="chips">-->
<!--                        <div class="chip active"> n°4FB6-618A </div>-->
<!--                        <div class="chip"> Lucas </div>-->
<!--                        <div class="chip"> David </div>-->
<!--                    </div>-->
<!--                </div>-->
            </div>

            <div class="transactions-container" v-if="specificState.currentWallet">
                <h3>
                    Transactions
                    <span> {{ $filters.Chip(specificState.currentWallet.chipVisualId) }} </span>
                </h3>

                <div class="transactions">
                    <div class="transaction" :class="{green: transaction.status === 'WRITTEN' && transaction.amount > 0, red: transaction.status === 'CANCELED'}" v-for="transaction in transactions">
                        <div class="left">
                            <div class="icon">
                                <i v-if="transaction.status === 'PENDING'" class="fa-regular fa-clock-rotate-left"></i>
                                <i v-else-if="transaction.status === 'CANCELED'" class="fa-regular fa-xmark"></i>
                                <i v-else-if="transaction.amount > 0" class="fa-regular fa-arrow-down-left"></i>
                                <i v-else class="fa-regular fa-arrow-up-right"></i>
                            </div>
                            <div class="data">
                                <span class="label" v-if="transaction.status === 'PENDING'"> En cours... </span>
                                <span class="label" v-else-if="transaction.amount > 0"> Rechargement </span>
                                <span class="label" v-else> Paiement </span>
                                <span class="date">
                                    {{ $filters.Date(transaction.creationDatetime) }} -
                                    {{ $filters.Hour(transaction.creationDatetime) }}
                                </span>
                            </div>
                        </div>
                        <span class="amount">
                            <span v-if="transaction.amount > 0">+</span>{{ $filters.Money(transaction.amount) }}
                        </span>
                    </div>
                </div>
            </div>
        </template>
    </div>
</template>