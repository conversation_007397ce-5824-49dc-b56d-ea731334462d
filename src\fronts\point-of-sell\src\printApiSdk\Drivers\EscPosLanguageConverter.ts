import type PrinterLanguageConverter from "./PrinterLanguageConverter";
import {ImageConversionStrategy, ImageUtils} from "../ImageUtils";
import type {FormArea} from "../CanvasRendererSDK";
import {PrinterConfig} from "../PrinterHelper";

const ESC = "\x1B";
const GS = "\x1D";

const HEX_d = "\x64";
const HEX_E = "\x45";
const HEX_i = "\x69";
const HEX_L = "\x4c";
const HEX_V = "\x56";
const HEX_AT = "\x40";
const HEX_DOLLAR = "\x24";

const ESCPOS_START = ESC + HEX_AT; // + ESC + HEX_L; // page mode
const ESCPOS_END = ESC + HEX_d;
const ESCPOS_CUT = GS + HEX_V + (0).toString(16); // 0 = full cut
// const ESCPOS_CUT = ESC + HEX_i; // Deprecated cut command
const ESCPOS_DRAWER = "\x10" + "\x14" + "\x01" + "\x00" + "\x05";
const ESCPOS_ABSOLUTE_POS = ESC + HEX_DOLLAR;
const ESCPOS_FONTS = {normal: ESC + HEX_E + "\x0A", bold: ESC + HEX_E + "\x0D"};

const IMG_DENSITY_DEFAULT = 0;
const IMG_DENSITY_DOUBLE_WIDTH = 1;
const IMG_DENSITY_DOUBLE_HEIGHT = 2;

const ESCPOS_PPMM = 8;

export default class EscPosLanguageConverter implements PrinterLanguageConverter {
	convertFromBase64(base64Str: string, formAreas?: FormArea[], printerRelayConfig?: PrinterConfig): Promise<number[]> {
		let printerDensity = printerRelayConfig?.density ?? ESCPOS_PPMM;

		return ImageUtils.base64ToRawImage(base64Str).then((image) => {
			if (image.width % printerDensity !== 0 || image.height % printerDensity !== 0) throw new Error("escpos:image_doesnt_match_required_density(" + printerDensity + ")");

			let grayscalePixels = ImageUtils.convertColor1DMatrixToGrayscale(image.pixels, image.colors, ImageConversionStrategy.average);
			let imageAsBlack = ImageUtils.convert1DMatrixTo2DMatrix(ImageUtils.convertGrayscale1DToMono(grayscalePixels), image.width, image.height);

			//https://github.com/escpos/escpos-image/blob/master/lib/escpos/image.rb
			//https://github.com/mike42/escpos-php/blob/203c53e97e0365b91491bea6fc1996e6629343a7/src/Mike42/Escpos/Printer.php
			//https://github.com/mike42/escpos-php/blob/5543a4c8bd0383d3240a24d22c1ebc23643dbac8/src/Mike42/Escpos/EscposImage.php
			//https://github.com/mike42/escpos-php/blob/e548e4ce676f9283a835b7c14cd3285d9f9e7f81/example/bit-image.php

			console.log(imageAsBlack);
			let convertedImage = ImageUtils.toRasterFormat(imageAsBlack, "binary");
			console.log(convertedImage);
			let bytesPerRow = Math.ceil(image.width / 8);

			let header = this.dataHeader([bytesPerRow, image.height], true);

			const bytes : number[] = [];
			bytes.push(...[...this.getStart()].map(c=>c.charCodeAt(0)))
			bytes.push(...[...this.getMediaType()].map(c=>c.charCodeAt(0)))
			bytes.push(...[...(GS + "v0" + String.fromCharCode(IMG_DENSITY_DEFAULT) + header)].map(c=>c.charCodeAt(0)))

			bytes.push(...convertedImage)

			bytes.push(...[...this.getEnd(printerRelayConfig?.openDrawer ?? false)].map(c=>c.charCodeAt(0)))

			return Promise.resolve(bytes);
		});
	}

	protected dataHeader(inputs: number[], long: boolean = true): string {
		let $outp: string[] = [];
		for (let input of inputs) {
			if (long) {
				$outp.push(this.intLowHigh(input, 2));
			} else {
				$outp.push(String.fromCharCode(input));
			}
		}
		return $outp.join("");
	}

	/**
	 * Generate two characters for a number: In lower and higher parts, or more parts as needed.
	 *
	 * @param $input Input number
	 * @param $length The number of bytes to output (1 - 4).
	 */
	protected intLowHigh($input: number, $length: number): string {
		//https://github.com/mike42/escpos-php/blob/203c53e97e0365b91491bea6fc1996e6629343a7/src/Mike42/Escpos/Printer.php#L1088
		let $outp = "";
		for (let $i = 0; $i < $length; $i++) {
			$outp += String.fromCharCode($input % 256);
			$input = Math.floor($input / 256);
		}
		return $outp;
	}

	getStart(): string {
		return ESCPOS_START;
	}

	getMediaType(): string {
		return "";
	}

	getEnd(openDrawer: boolean = true): string {
		return "\x0A" + "\x0A" + "\x0A" + "\x0A" + "\x0A" + ESCPOS_CUT + (openDrawer ? ESCPOS_DRAWER : "");
	}
}
