import {Component, Vue} from "vue-facing-decorator";
import {OptionBuilder} from "vue-facing-decorator/dist/optionBuilder";
import {
	AutoWired,
	EntityDefinitionFieldType,
	ScopedUuid,
	UuidUtils,
	VisualScopedUuid
} from "@groupk/horizon2-core";
import {AccountRepository} from "../../../../../shared/repositories/AccountRepository";
import {EstablishmentRepository} from "../../../../../shared/repositories/EstablishmentRepository";
import {
	AccountApiOut,
	EstablishmentCreationApiIn,
	MastodonEstablishmentAccountContractAggregate,
	PlatformDescriptorApiOut,
	PublicEstablishmentJoiningApiOut,
	PublicEstablishmentJoiningItemApiOut,
	uuidScopeEstablishment,
	UuidScopeEstablishment, uuidScopeEstablishmentJoining, UuidScopeEstablishmentJoining,
	UuidScopeReseller,
	uuidScopeReseller,
} from "@groupk/mastodon-core";
import {AuthRepository} from "../../../../../shared/repositories/AuthRepository";
import {AuthCredentialsApiIn} from "@groupk/mastodon-core";
import {PlatformDescriptorFrontApiOut, ResellerPublicApiOut} from "@groupk/mastodon-core";
import {PlatformRepository} from "../../../../../shared/repositories/PlatformRepository";
import {ResellerRepository} from "../../../../../shared/repositories/ResellerRepository";
import {CrossDomainTracker, GoogleTracker} from "@groupk/horizon2-front";
import {SignupData} from "../../../../../shared/mastodonCoreFront/SignupData";
import {AuthStateModel} from "../../../../../shared/AuthStateModel";
import {translateResponseError} from "../../../../../shared/RepositoryExtensions";
import {EstablishmentAccountRepository} from "../../../../../shared/repositories/EstablishmentAccountRepository";
import {FormModalOrDrawerComponent} from "@groupk/vue3-interface-sdk";

@Component({
	components: {
		'form-modal-or-drawer': FormModalOrDrawerComponent
	}
})
export default class SignupView extends Vue {
	signupData: SignupData = new SignupData();

	platforms!: PlatformDescriptorApiOut;
	currentPlatform!: PlatformDescriptorFrontApiOut;
	redirectAfter: string|null = null;
	establishmentJoining: PublicEstablishmentJoiningItemApiOut|null = null;
	reseller!: ResellerPublicApiOut|null;

	createdAccount: AccountApiOut|null = null;

	loading: boolean = false;
	creatingAccount: boolean = false;
	error: string|null = null;
	joiningError: string|null = null;

	EstablishmentCreationDefinition = EstablishmentCreationApiIn.__entityDefinition;

	@AutoWired(AccountRepository) accessor accountRepository!: AccountRepository;
	@AutoWired(EstablishmentRepository) accessor establishmentRepository!: EstablishmentRepository;
	@AutoWired(EstablishmentAccountRepository) accessor establishmentAccountRepository!: EstablishmentAccountRepository;
	@AutoWired(ResellerRepository) accessor resellerRepository!: ResellerRepository;
	@AutoWired(PlatformRepository) accessor platformRepository!: PlatformRepository;
	@AutoWired(AuthRepository) accessor authRepository!: AuthRepository;
	@AutoWired(AuthStateModel) accessor authStateModel!: AuthStateModel;

	constructor(optionBuilder: OptionBuilder, vueInstance: any) {
		super(optionBuilder, vueInstance);

		this.loading = true;
	}

	async beforeMount() {
		try {
			this.platforms = (await this.platformRepository.callContract('get', undefined, undefined)).success();

			const url = new URL(window.location.href);
			if(url.searchParams.has('redirectAfter')) this.redirectAfter = url.searchParams.get('redirectAfter');

			if(url.searchParams.has('establishmentInvitation')) {
				const establishmentJoiningUid: VisualScopedUuid<UuidScopeEstablishmentJoining> = UuidUtils.scopedToVisual<UuidScopeEstablishmentJoining>(url.searchParams.get('establishmentInvitation') as ScopedUuid<UuidScopeEstablishmentJoining>, uuidScopeEstablishmentJoining);
				const joiningResponse = await this.establishmentAccountRepository.callContract('getJoinRequestPublic', {joiningRequestUid: establishmentJoiningUid}, undefined);

				if(joiningResponse.isSuccess()) {
					const joining = joiningResponse.success();
					if(new Date(joining.item.expiresDatetime) < new Date()) {
						this.joiningError = 'Ce lien a expiré';
					} else if(joining.item.usedDatetime) {
						this.joiningError = 'Ce lien a déjà été utilisé';
					} else {
						this.establishmentJoining = joiningResponse.success();
					}
				} else {
					this.joiningError = translateResponseError<typeof MastodonEstablishmentAccountContractAggregate, 'getJoinRequestPublic'>(joiningResponse, {});
				}
			}

			const platformName = url.searchParams.get('platform') ?? '';
			const platform = this.platforms.fronts.find((platform) => platform.id.toLowerCase() === platformName.toLowerCase());
			if(!platform) {
				if(this.platforms.fronts.length === 1) this.currentPlatform = this.platforms.fronts[0];
				else {
					window.location.href = '/not-found';
					return;
				}
			} else {
				this.currentPlatform = platform;
			}

			const resellerUid = (url.searchParams.get('reseller') ?? null) as ScopedUuid<UuidScopeReseller>|null;
			if(resellerUid) {
				this.reseller = (await this.resellerRepository.callContract('getOnePublic', {resellerUid: UuidUtils.scopedToVisual<UuidScopeReseller>(resellerUid, uuidScopeReseller)}, undefined)).success()
			}
		} catch(err) {
			// ??
		}

		this.loading = false;
	}

	verifyForm() {
		if(!this.establishmentJoining && this.signupData.establishmentName === '') {
			throw new Error('Un nom d\'établissement est requis');
		}
		const field = EstablishmentCreationApiIn.__entityDefinition.getField('name');
		if(field.type === EntityDefinitionFieldType.STRING && field.maxLength !== null && this.signupData.establishmentName.length > field.maxLength) {
			throw new Error(`Le nom d\'établissement est trop long (${this.signupData.establishmentName.length}/${field.maxLength} caractères)`);
		}
		if(this.signupData.lastname === '') {
			throw new Error('Un nom de famille est requis');
		}
		if(this.signupData.firstname === '') {
			throw new Error('Un prénom est requis');
		}
		if(this.signupData.email === '') {
			throw new Error('Un email est requis');
		}
	}

	async signup() {
		this.error = null;
		this.creatingAccount = true;

		GoogleTracker.instance.event('sign_up');

		try {
			this.verifyForm();

			let account!: AccountApiOut;
			if(this.createdAccount) account = this.createdAccount;
			else {
				const accountApiIn = this.signupData.toAccountApiIn();
				const response = await this.accountRepository.callContract('create', undefined, accountApiIn);
				if(response.isSuccess()) {
					account = response.success();
				} else {
					this.error = translateResponseError(response, {
						account_emailAlreadyUsed: 'Un compte est déjà relié à l\'email renseigné',
						emailBlacklisted: 'L\'email renseigné semble invalide',
						account_password_not_set: 'Le mot de passe est requis'
					})
					this.creatingAccount = false;
					return;
				}
			}

			const authCredentialsApiIn = new AuthCredentialsApiIn(account.email, this.signupData.password);
			await this.authStateModel.authAccount(authCredentialsApiIn);
			localStorage.setItem('latest-used-email', this.signupData.email);

			if(!this.establishmentJoining) {
				const establishmentCreationApiIn = this.signupData.toEstablishmentCreationApiIn();
				if(this.reseller) establishmentCreationApiIn.resellerUid = this.reseller.uid;
				const establishmentResponse = await this.establishmentRepository.callContract('create', undefined, establishmentCreationApiIn);
				if(establishmentResponse.isSuccess()) {
					this.redirectToEstablishment(establishmentResponse.success().uid);
				} else {
					this.createdAccount = account;
					this.error = translateResponseError(establishmentResponse, {
						establishment_ownTooMany: 'Le compte à atteint la limite d\'établissements',
						unknown_reseller: 'Le compte à atteint la limite d\'établissements',
					})
					this.creatingAccount = false;
				}
			} else {
				const response = await this.establishmentAccountRepository.callContract('useJoinRequest', {joiningRequestUid: this.establishmentJoining.item.uid}, undefined);
				if(!response.isSuccess()) {
					this.error = translateResponseError<typeof MastodonEstablishmentAccountContractAggregate, 'useJoinRequest'>(response, {
						establishmentAccount_limitReached: 'Le nombre de comptes liés à cet établissement est atteint',
						establishmentAccount_already_exists: 'Ce compte est déjà lié à cet établissement'
					});
					return;
				}

				this.redirectToEstablishment(UuidUtils.scopedToVisual<UuidScopeEstablishment>('0000652c-741f-8927-add0-c9dcd10dfb14' as  any, uuidScopeEstablishment) as any);
			}
		} catch(err) {
			this.error = (err as any).message;
			this.creatingAccount = false;
		}
	}

	redirectToEstablishment(establishmentUid: VisualScopedUuid<UuidScopeEstablishment>) {
		const token = this.authStateModel.getStateSync();
		if(!token) {
			this.error = 'Impossible de trouver un identifiant de connexion';
			return;
		}

		// Optimize, may be too long for url, but we'll have to fetch full token in targeted platform
		// if(token instanceof AccountAuthTokenApiOut) token.establishmentAccounts = [];

		window.location.href = CrossDomainTracker.instance.addTrackingToLink((this.currentPlatform.url + 'establishment/' + UuidUtils.visualToScoped(establishmentUid) + '/auth?token=' + btoa(JSON.stringify(token)) + (this.redirectAfter ? '&redirectAfter=' + this.redirectAfter : '')));
	}

	goToLogin() {
		window.location.href = '/login' + window.location.search;
	}
}