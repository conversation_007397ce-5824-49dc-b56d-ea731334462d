<script lang="ts" src="./CalendarExportComponent.ts"/>

<style lang="sass" scoped>
@import './CalendarExportComponent.scss'
</style>

<template>
    <div class="calendar-export-component">
        <form-modal-or-drawer
            :state="opened"
            title="Exporter les données"
            subtitle="Choisissez une période de un mois à exporter"
            @close="close()"
        >
            <template v-slot:content>
                <div class="month-selection">
                    <div class="grey button" @click="previousMonth()">
                        <i class="fa-regular fa-chevron-left"></i>
                    </div>

                    <span class="current-month"> {{ monthNames[selectedMonth.getMonth()].toUpperCase() }} {{ selectedMonth.getFullYear() }} </span>

                    <div class="grey button" @click="nextMonth()">
                        <i class="fa-regular fa-chevron-right"></i>
                    </div>
                </div>

                <div v-if="loading" class="loading-container">
                    <div class="loader"></div>
                </div>

                <div v-else class="key-numbers">
                    <div class="key-number">
                        <span class="type"> Nombre de billets (commandes) </span>
                        <span class="value"> {{ monthStats.orderTicketQuantity }} </span>
                    </div>
                    <div class="key-number">
                        <span class="type"> Nombre de billets (manuel) </span>
                        <span class="value"> {{ monthStats.batchTicketQuantity }} </span>
                    </div>
                </div>

                <div class="table-scroll-wrapper">
                    <table class="data-table">
                        <thead>
                        <tr>
                            <td> Date </td>
                            <td> Billets commandes </td>
                            <td> Billets manuel </td>
                        </tr>
                        </thead>
                        <tbody>
                        <tr v-for="stats of detailedStats">
                            <td> {{ $filters.Date(stats.date) }} </td>
                            <td :class="{'bold': stats.orderQuantity > 0}"> {{ stats.orderTicketQuantity }} </td>
                            <td :class="{'bold': stats.batchTicketQuantity > 0}"> {{ stats.batchTicketQuantity }} </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </template>
            <template v-slot:buttons>
                <button type="button" class="white button" @click="close()"> Annuler </button>
                <button type="button" class="button" :class="{disabled: exporting, loading: exporting}" @click="exportUsages()">
                    <i class="fa-regular fa-arrow-down-to-line"></i>
                    Exporter le détail
                </button>
            </template>
        </form-modal-or-drawer>
    </div>
</template>