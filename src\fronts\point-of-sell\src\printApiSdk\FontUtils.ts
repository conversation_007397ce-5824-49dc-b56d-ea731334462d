function setZeroEvents(element: HTMLElement) {
	element.style.display = "block";
	element.style.margin = "none";
	element.style.padding = "none";
	element.style.position = "absolute";
	element.style.zIndex = "-5000";
	element.style.pointerEvents = "none";
	element.style.visibility = "hidden";
	element.style.whiteSpace = "nowrap";
	element.style.lineHeight = "normal";
}

// init an hidden container to make computations in it
let fakeElementGlobalContainer = document.createElement("div");
setZeroEvents(fakeElementGlobalContainer);
fakeElementGlobalContainer.classList.add("sizeEvaluator_no_style_container");
fakeElementGlobalContainer.style.width = "0px";
fakeElementGlobalContainer.style.height = "0px";
fakeElementGlobalContainer.style.top = "0";
fakeElementGlobalContainer.style.left = "0";
fakeElementGlobalContainer.style.overflow = "hidden";
document.body.appendChild(fakeElementGlobalContainer);

// init element to compute size
let fakeElementComputeTextSize = document.createElement("div");
setZeroEvents(fakeElementComputeTextSize);
fakeElementComputeTextSize.style.left = "0";
fakeElementComputeTextSize.style.top = "0";
fakeElementGlobalContainer.appendChild(fakeElementComputeTextSize);

//
let fakeElementParentDefinedSize = document.createElement("div");
setZeroEvents(fakeElementParentDefinedSize);
let fakeElementPercentageSize = document.createElement("div");
fakeElementPercentageSize.style.display = "block";
fakeElementPercentageSize.style.margin = "none";
fakeElementPercentageSize.style.padding = "none";
fakeElementPercentageSize.style.position = "absolute";
fakeElementPercentageSize.style.pointerEvents = "none";
fakeElementParentDefinedSize.appendChild(fakeElementPercentageSize);

fakeElementGlobalContainer.appendChild(fakeElementParentDefinedSize);

export default class FontUtils {
	static checkLoadedFallbackDelay = 1500;

	protected static loadedFonts = new Map<string, Promise<void>>();

	static async preloadWithFiles(
		files: {
			eot?: string;
			woff?: string;
			woff2?: string;
			ttf?: string;
			svg?: string;
		},
		fontFamily: string
	): Promise<void> {
		const existingLoaderPromise = this.loadedFonts.get(fontFamily);
		if (existingLoaderPromise) return await existingLoaderPromise;

		let srcs = "";
		if (files.eot) srcs += (srcs.length ? ", " : "") + 'url("' + files.eot + '") format("embedded-opentype")';
		if (files.woff) srcs += (srcs.length ? ", " : "") + 'url("' + files.woff + '") format("woff")';
		if (files.woff2) srcs += (srcs.length ? ", " : "") + 'url("' + files.woff2 + '") format("woff2")';
		if (files.ttf) srcs += (srcs.length ? ", " : "") + 'url("' + files.ttf + '") format("truetype")';
		if (files.svg) srcs += (srcs.length ? ", " : "") + 'url("' + files.svg + '") format("svg")';

		const cssToInject =
			`
@font-face {
	font-family: '` +
			fontFamily +
			`';
	src: ` +
			srcs +
			`;
	font-weight: normal;
	font-style: normal;
}
`;
		const style = document.createElement("style");
		if ((<any>style).styleSheet) {
			// This is required for IE8 and below.
			(<any>style).styleSheet.cssText = cssToInject;
		} else {
			style.appendChild(document.createTextNode(cssToInject));
		}
		document.head.appendChild(style);
		this.injectPreloaderSpan(fontFamily);

		const fontLoadingPromise = this.waitFontLoaded(fontFamily);
		this.loadedFonts.set(fontFamily, fontLoadingPromise);

		return fontLoadingPromise;
	}

	static waitFontLoaded(fontName: string): Promise<void> {
		if ((document as any).fonts) {
			return (document as any).fonts.ready.then(function () {
				if ((document as any).fonts.check("1em " + fontName)) {
					return undefined;
				} else {
					return new Promise<void>(function (resolve, reject) {
						setTimeout(() => {
							FontUtils.waitFontLoaded(fontName).then(resolve).catch(reject);
						}, 100);
					});
				}
			});
		} else {
			return new Promise<void>(function (resolve) {
				setTimeout(() => {
					resolve();
				}, FontUtils.checkLoadedFallbackDelay);
			});
		}
	}

	static async preloadWithUrl(url: string, fontFamily: string): Promise<void> {
		const existingLoaderPromise = this.loadedFonts.get(fontFamily);
		if (existingLoaderPromise) return await existingLoaderPromise;

		let link = document.createElement("link");
		link.rel = "stylesheet";
		link.href = url;
		document.head.appendChild(link);
		this.injectPreloaderSpan(fontFamily);

		const fontLoadingPromise = this.waitFontLoaded(fontFamily);
		this.loadedFonts.set(fontFamily, fontLoadingPromise);

		return fontLoadingPromise;
	}

	protected static injectPreloaderSpan(fontFamily: string) {
		let container = document.getElementById("fontsPreloaderContainer");
		if (container === null) {
			container = document.createElement("div");
			container.style.width = "0";
			container.style.height = "0";
			container.style.overflow = "hidden";
			container.style.position = "absolute";
			container.style.top = "0";
			container.style.left = "0";
			container.id = "fontsPreloaderContainer";
			document.body.appendChild(container);
		}
		let span = document.createElement("span");
		span.style.fontFamily = '"' + fontFamily + '"';
		span.innerText = "Preloader";
		container.appendChild(span);
	}


	static computeTextSizeOffscreenInPixels(text: string, font: {family: string; weight: string}, fontSize: string) {
		fakeElementComputeTextSize.innerHTML = text;
		fakeElementComputeTextSize.style.fontFamily = font.family;
		fakeElementComputeTextSize.style.fontWeight = font.weight;
		fakeElementComputeTextSize.style.fontSize = fontSize;
		fakeElementComputeTextSize.style.width = "default";
		fakeElementComputeTextSize.style.height = "default";

		let width = fakeElementComputeTextSize.clientWidth;
		let height = fakeElementComputeTextSize.clientHeight;

		return {
			width: width,
			height: height,
		};
	}

	static computeElementSizeInPixels(templateSize: {width: number; height: number}, elementSize: {width: number; height: number}) {
		fakeElementParentDefinedSize.style.width = templateSize.width + "mm";
		fakeElementParentDefinedSize.style.height = templateSize.height + "mm";

		fakeElementPercentageSize.style.width = elementSize.width + "%";
		fakeElementPercentageSize.style.height = elementSize.height + "%";

		let width = fakeElementPercentageSize.clientWidth;
		let height = fakeElementPercentageSize.clientHeight;
		return {
			width: width,
			height: height,
		};
	}

	static computeBestFontSizeForElement(
		elementSizeInPixels: {width: number; height: number},
		text: string,
		font: {family: string; weight: string},
		checkHeightContained: boolean = true
	) {
		let textSizeForWidth = FontUtils.computeTextSizeOffscreenInPixels(text, font, "100px");
		// prevents some browser rounding (width=170.41 returns width=170) by increasing by one pixel the size
		textSizeForWidth.width += 1;
		textSizeForWidth.height += 1;

		let maxSizeForWidth = textSizeForWidth.width <= 0 ? 0 : Math.floor((elementSizeInPixels.width * 100) / textSizeForWidth.width);
		let maxSizeForHeight = textSizeForWidth.height <= 0 ? 0 : Math.floor((elementSizeInPixels.height * 100) / textSizeForWidth.height);

		if (checkHeightContained) {
			return Math.min(maxSizeForWidth, maxSizeForHeight);
		} else {
			return maxSizeForWidth;
		}
	}

	static getOffset(element: HTMLElement) {
		if (!element.getClientRects().length) {
			return {top: 0, left: 0};
		}

		let rect = element.getBoundingClientRect();
		let win = element.ownerDocument.defaultView;
		return {
			top: rect.top + (win ? win.pageYOffset : 0),
			left: rect.left + (win ? win.pageXOffset : 0),
		};
	}

	static getTextIntrinsicSizes(text: string, fontFamily: string, fontSize: string) {
		let textElement = document.createElement("span");
		textElement.style.fontFamily = fontFamily;
		textElement.style.fontSize = fontSize;
		textElement.innerHTML = text;

		let blockElement = document.createElement("div");
		blockElement.style.display = "inline-block";
		blockElement.style.width = "1px";
		blockElement.style.height = "0";

		let containerElement = document.createElement("div");
		containerElement.append(textElement, blockElement);
		setZeroEvents(containerElement);

		document.body.append(containerElement);

		let result = {
			ascent: 0,
			height: 0,
			descent: 0,
		};
		try {
			blockElement.style.verticalAlign = "baseline";
			let blockBaselineOffset = FontUtils.getOffset(blockElement);
			let textBaselineOffset = FontUtils.getOffset(textElement);
			result.ascent = blockBaselineOffset.top - textBaselineOffset.top;

			blockElement.style.verticalAlign = "bottom";
			let blockBottomOffset = FontUtils.getOffset(blockElement);
			let textBottomOffset = FontUtils.getOffset(textElement);
			result.height = blockBottomOffset.top - textBottomOffset.top;

			result.descent = result.height - result.ascent;
		} finally {
			containerElement.remove();
		}

		return result;
	}
}
