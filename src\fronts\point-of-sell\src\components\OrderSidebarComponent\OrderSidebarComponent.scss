.empty-order-sidebar-component {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    gap: 10px;
    user-select: none;
    align-items: center;
    justify-content: center;
    padding: 20px;
    text-align: center;
    box-sizing: border-box;

    i {
        font-size: 25px;
    }
}

.order-sidebar-component {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    user-select: none;
    position: relative;

    padding: var(--safe-area-top) 0 var(--safe-area-bottom) 0;
    box-sizing: border-box;

    .transferred {
        display: flex;
        flex-direction: column;
        gap: 4px;
        padding: 10px;

        .title {
            font-size: 20px;
            font-weight: 600;
        }

        .subtitle {
            font-size: 15px;
        }
    }

    .quick-actions {
        display: flex;
        padding: 10px;
        align-items: stretch;
        gap: 10px;

        > .action, .dropdown-button-component > .action {
            display: flex;
            padding: 10px;
            justify-content: center;
            align-items: center;
            gap: 10px;
            border-radius: 6px;
            background: #E8E8E8;
            flex-grow: 2;
            cursor: pointer;

            font-family: <PERSON><PERSON><PERSON>, sans-serif;
            font-size: 14px;
            font-weight: 500;

            i {
                font-size: 17px;
            }

            &:hover {
                background: #cbcbcb;
            }

            &.disabled {
                opacity: 0.5;
                pointer-events: none;
            }

            &.small {
                flex-grow: 0;
                flex-shrink: 0;
                i {
                    margin-top: 2px;
                }
            }

            .number {
                display: flex;
                width: 20px;
                height: 20px;
                justify-content: center;
                align-items: center;
                gap: 10px;
                flex-shrink: 0;
                border-radius: 50%;
                background: #FFF;
                box-sizing: border-box;
                font-size: 12px;
                font-weight: 500;
                line-height: 20px;
            }
        }

        .button.square {
            height: 100%;
            box-sizing: border-box;
        }
    }

    .dropdowns {
        display: grid;
        grid-template-columns: 1fr 1fr;
        grid-gap: 10px;
        padding: 10px 10px 0 10px;

        .action {
            display: flex;
            padding: 10px;
            justify-content: center;
            align-items: center;
            gap: 10px;
            border-radius: 6px;
            background: #E8E8E8;
            flex-grow: 2;
            cursor: pointer;

            font-family: Montserrat, sans-serif;
            font-size: 14px;
            font-weight: 500;

            i {
                font-size: 17px;
            }

            &:hover {
                background: #cbcbcb;
            }

            &.disabled {
                opacity: 0.5;
                pointer-events: none;
            }

            &.small {
                flex-grow: 0;
                flex-shrink: 0;
                i {
                    margin-top: 2px;
                }
            }

            .number {
                display: flex;
                width: 20px;
                height: 20px;
                justify-content: center;
                align-items: center;
                gap: 10px;
                flex-shrink: 0;
                border-radius: 50%;
                background: #FFF;
                box-sizing: border-box;
                font-size: 12px;
                font-weight: 500;
                line-height: 20px;
            }
        }
    }

    .group-by {
        padding: 10px 10px 0 10px;

        .button-group {
            display: flex;
            width: 100%;

            button {
                all: unset;
                display: flex;
                align-items: center;
                justify-content: center;
                gap: 10px;
                padding: 8px 12px;
                flex-grow: 2;
                background: #F2F4F7;

                &.selected {
                    background: var(--primary-color);
                    color: var(--primary-text-color);
                }

                &:first-child {
                    border-radius: 8px 0 0 8px;
                }

                &:last-child {
                    border-radius: 0 8px 8px 0;
                }
            }
        }
    }


    .header {
        display: flex;
        padding: 20px 10px 10px 10px;
        justify-content: space-between;
        align-self: stretch;

        .title {
            font-family: Montserrat, sans-serif;
            font-size: 14px;
            font-weight: 700;
        }

        .action {
            font-family: Montserrat, sans-serif;
            font-size: 14px;
            text-decoration-line: underline;
            text-underline-offset: 3px;
            cursor: pointer;
        }
    }

    .purchases {
        flex-grow: 2;
        display: flex;
        flex-direction: column;
        gap: 10px;
        padding: 10px;
        overflow: auto;

        &::-webkit-scrollbar {
            display: none;
        }

        .purchase {
            display: flex;
            flex-direction: column;
            padding: 10px 15px;
            border-radius: 8px;
            background: #F2F4F7;
            gap: 10px;

            .top {
                display: flex;
                align-items: center;
                gap: 10px;

                .quantity {
                    flex-shrink: 0;
                    display: flex;
                    width: 25px;
                    height: 25px;
                    justify-content: center;
                    align-items: center;
                    background: var(--primary-color);
                    color: var(--primary-text-color);
                    border-radius: 50%;

                    font-family: Montserrat, sans-serif;
                    font-size: 12px;
                    font-weight: 700;
                    line-height: 20px;
                }

                .data {
                    flex-grow: 2;
                    display: flex;
                    flex-direction: column;
                    gap: 4px;
                    padding: 5px 10px;

                    .parent {
                        font-size: 14px;
                    }

                    .product-composition {
                        font-size: 14px;
                        
                        span:not(:first-child):not(:last-child)::after {
                            content: ',';
                        }
                    }

                    .name {
                        font-family: Montserrat, sans-serif;
                        font-size: 14px;
                        font-weight: 700;
                    }

                    .total {
                        font-family: Montserrat, sans-serif;
                        font-size: 13px;
                    }
                }
            }

            &.discounted {
                .quantity {
                    background: #FFF066;
                    color: black;
                }

                .price {
                    text-decoration: line-through;
                }
            }

            .bottom {
                display: flex;
                flex-direction: column;
                gap: 10px;

                .metadata {
                    word-break: break-all;
                    opacity: 0.5;

                    .name {
                        font-weight: 500;
                    }
                }
            }
        }

        .status-image-container {
            position: relative;

            .status-image {
                height: 38px;
            }

            span {
                display: inline-block;
                position: absolute;
                bottom: -2px;
                right: 0;
                padding: 3px;
                font-weight: 600;
                background: rgba(217, 219, 220, 0.5);
                border-radius: 6px;
                font-size: 12px;
            }
        }

        .remove, .discount {
            flex-shrink: 0;
            padding: 10px;
            cursor: pointer;

            &:hover {
                background: #e6e9ec;
                border-radius: 4px;
            }

            i {
                font-size: 18px;
            }
        }
    }

    .summary {
        display: flex;
        flex-direction: column;
        padding: 10px;
        gap: 5px;

        .separator {
            margin-bottom: 15px;
            border-top: 1px dashed black;
        }

        .line {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-family: Montserrat, sans-serif;
            font-size: 15px;
            padding: 0 10px;

            &.bold {
                font-weight: bold;
            }

            &.last {
                margin-top: 10px;
            }
        }
    }

    .payment {
        display: flex;
        flex-direction: column;
        gap: 10px;
        padding: 10px;

        .discount {
            display: flex;
            padding: 10px;
            justify-content: center;
            align-items: center;
            gap: 10px;
            height: 40px;
            box-sizing: border-box;
            border-radius: 8px;
            border: 1px solid #000;
            background: #FFF;
            font-family: Montserrat, sans-serif;
            font-size: 13px;
            font-weight: 700;

            i {
                font-size: 18px;
            }

            &.disabled {
                opacity: 0.5;
                pointer-events: none;
            }
        }

        .quick-pays {
            display: grid;
            grid-template-columns: 1fr 1fr;
            grid-gap: 10px;

            .quick-pay {
                all: unset;
                display: flex;
                align-items: center;
                justify-content: center;
                font-family: Montserrat, sans-serif;
                font-size: 15px;
                font-weight: 700;
                height: 56px;
                border-radius: 8px;
                cursor: pointer;
                box-sizing: border-box;

                &.black {
                    background: black;
                    color: white;

                    &:hover {
                        background: #2a2a2a;
                    }
                }

                &.white {
                    background: white;
                    border: 2px solid black;

                    &:hover {
                        background: #f3f3f3;
                    }
                }

                &.disabled {
                    opacity: 0.5;
                    pointer-events: none;
                }
            }
        }

        .custom-pay {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 56px;
            width: 100%;
            border-radius: 8px;
            background: var(--primary-color);
            color: var(--primary-text-color);
            font-family: Montserrat, sans-serif;
            font-size: 15px;
            font-weight: 700;
            cursor: pointer;

            .small {
                font-weight: 400;
                font-size: 14px;
            }

            &:hover {
                background: var(--primary-button-hover-color);
                color: var(--primary-text-color);
            }

            &.disabled {
                opacity: 0.5;
                pointer-events: none;
            }
        }
    }

    .discount-manager {
        position: absolute;
        inset: 0;
        transform: translateX(100%);
        transition: transform .3s cubic-bezier(0.22, 1, 0.36, 1);
        z-index: 82;
        &.displayed {
            transform: none;
        }
    }

    .keypad-dimmer {
        position: absolute;
        z-index: 80;
        inset: 0;
        background: rgba(0, 0, 0, 0.16);
        backdrop-filter: blur(4px); // Performance problems ???
        opacity: 0;
        pointer-events: none;
        transition: opacity .3s cubic-bezier(0.22, 1, 0.36, 1);

        &.displayed {
            opacity: 1;
            pointer-events: all;
        }
    }

    .hiding-keypad {
        display: flex;
        flex-direction: column;
        gap: 10px;
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        background: white;
        padding: var(--safe-area-top) 10px var(--safe-area-bottom) 10px;
        transform: translateY(100%);
        transition: transform .3s cubic-bezier(0.22, 1, 0.36, 1);
        z-index: 90;
        max-height: 100%;
        box-sizing: border-box;

        &.opened {
            transform: translateY(0);
        }

        .payment-methods {
            display: grid;
            grid-template-columns: 1fr 1fr;
            grid-gap: 10px;
            overflow: auto;

            .payment-method {
                display: flex;
                align-items: center;
                gap: 10px;
                padding: 15px;
                border: 1px solid black;
                border-radius: 6px;
                cursor: pointer;

                .name {
                    white-space: nowrap;
                    font-size: 15px;
                }

                &.selected {
                    background: rgba(0, 102, 255, 0.15);
                    background: var(--primary-color);
                    border-color: var(--primary-color);
                    color: var(--primary-text-color);
                    font-weight: 600;
                }
            }
        }

        .display {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
            padding: 20px;

            .amount {
                font-size: 26px;
                font-weight: bold;

                &.red {
                    color: #e83636;
                }
            }

            .overpaid {
                color: #e83636;
                font-weight: bold;
            }

            .left-to-pay {
                font-size: 14px;

                .striped {
                    text-decoration: line-through;
                }
            }
        }

        .actions {
            display: grid;
            grid-template-columns: 1fr 1fr;
            grid-gap: 10px;

            .cancel, .pay {
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                height: 56px;
                width: 100%;
                border-radius: 8px;
                background: #e83636;
                color: #FFF;
                font-family: Montserrat, sans-serif;
                font-size: 15px;
                font-weight: 700;
                cursor: pointer;
                text-overflow: ellipsis;
                overflow: hidden;
                white-space: nowrap;

                &.disabled {
                    pointer-events: none;
                    opacity: 0.7;
                }

                &:hover {
                    background: #cb1818;
                }
            }

            .pay {
                background: var(--primary-color);
                color: var(--primary-text-color);

                &:hover {
                    background: #004bc5;
                }

                .small {
                    font-size: 12px;
                    font-weight: 400;
                }
            }
        }
    }

    &.performance-mode {
        .discount-manager {
            transition: none;
        }

        .keypad-dimmer {
            transition: none;
        }

        .hiding-keypad {
            transition: none;
        }
    }

    .right-modal-dimmer {
        position: fixed;
        inset: 0;
        background: rgba(0, 0, 0, 0.4);
    }

    .generated-tickets {
        display: flex;
        flex-direction: column;
        gap: 20px;
        padding: 20px 20px 80px 20px;
        height: 100%;
        overflow: auto;

        .buttons {
            margin: 0;
        }

        .head {
            display: flex;
            flex-direction: column;
            gap: 4px;

            .title {
                font-size: 20px;
                font-weight: 600;
            }

            .subtitle {
                font-size: 15px;
            }
        }

        .tickets {
            display: flex;
            flex-direction: column;
            gap: 10px;


            .ticket {
                display: flex;
                align-items: center;
                padding: 15px;
                border-radius: 8px;
                background: #F2F4F7;
                gap: 20px;

                .fa-square {
                    font-size: 22px;
                }

                .left {
                    flex-grow: 2;
                    display: flex;
                    flex-direction: column;
                    gap: 4px;

                    .name {
                        font-family: Montserrat, sans-serif;
                        font-size: 15px;
                        font-weight: 700;
                    }

                    .subtitle {
                        font-family: Montserrat, sans-serif;
                        font-size: 14px;
                    }
                }

                .actions {
                    display: flex;
                    align-items: center;
                    gap: 5px;

                    .action {
                        flex-shrink: 0;
                        padding: 10px;
                        cursor: pointer;

                        &:hover {
                            background: #e6e9ec;
                            border-radius: 4px;
                        }

                        i {
                            font-size: 22px;
                        }
                    }
                }
            }
        }
    }

    .order-steps {
        display: flex;
        flex-direction: column;
        gap: 20px;
        flex-grow: 2;
        overflow: auto;

        .order-step {
            display: flex;
            flex-direction: column;
            gap: 10px;

            .head {
                display: flex;
                align-items: center;
                padding: 6px 15px;
                background: #f6f5f5;

                .title {
                    flex-grow: 2;
                    font-weight: 500;
                }
            }

            &.selected {
                //border: 1px solid var(--primary-color);
                //border-radius: 12px;

                .head {
                    background: var(--primary-hover-color);
                }
            }

            .purchases {
                padding: 0 10px;
            }
        }

        .order-actions {
            all: unset;
            flex-shrink: 0;
            padding: 5px 10px;
            cursor: pointer;

            &:hover {
                background: #e6e9ec;
                border-radius: 4px;
            }

            i {
                font-size: 18px;
            }
        }

        .buttons {
            all: unset;
            padding: 0 10px;

            button {
                box-sizing: border-box;
                width: 100%;
            }
        }
    }

}