import type {Component} from "@vue/runtime-core";
import {GetInstance} from "@groupk/horizon2-core";
import type {RouterRouteLoadedDescriptor, VueRouteOptions} from "@groupk/horizon2-front";
import {VueRoute} from "@groupk/horizon2-front";
import type {Router, RouterRouteModuleLoader} from "@groupk/horizon2-front";
import {AuthStateModel} from "../../../shared/AuthStateModel";
import {MainConfig} from "../../../shared/MainConfig";

class AuthedVueJsRoute<TState> extends VueRoute<TState> {
	async load(router: Router, state: TState | undefined): Promise<RouterRouteLoadedDescriptor|void|undefined> {
		let authCenter: AuthStateModel = GetInstance(AuthStateModel);
		let localToken = authCenter.getStateSync();

		if (localToken) {
			return super.load(router, state);
		} else {
			const config = GetInstance(MainConfig);
			let regexMatch = router.lastRouteRegexMatches;
			if (regexMatch && regexMatch[1]) {
				window.location.href = config.configuration.casFrontUrl + 'establishment/' + regexMatch[1] + '/login?platform=PointOfSell2';
			} else {
				window.location.href = config.configuration.casFrontUrl + '/login?platform=PointOfSell2';
			}
		}
	}
}

export function VueAuthedRouteFactory<TState>(rootComponent: Component, userOptions: Partial<VueRouteOptions> = {}): RouterRouteModuleLoader<TState> {
	return new AuthedVueJsRoute(rootComponent, userOptions);
}
