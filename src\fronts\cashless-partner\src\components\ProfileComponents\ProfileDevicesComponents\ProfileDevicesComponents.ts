import {Component, Prop, Vue} from "vue-facing-decorator";
import {
	EstablishmentDeviceApiOut, UuidScopeIot_deviceApp,
} from "@groupk/mastodon-core";
import {
	CashlessDeviceStateApiOut,
	EstablishmentAccountProfileApi,
} from "@groupk/mastodon-core";
import {
	DropdownButtonAction,
	DropdownButtonComponent,
	DropdownComponent as dropdown,
	ModalOrDrawerComponent,
} from "@groupk/vue3-interface-sdk";
import {AutoWired, VisualScopedUuid} from "@groupk/horizon2-core";
import {EstablishmentDevicesRepository} from "../../../../../../shared/repositories/EstablishmentDevicesRepository";
import {EstablishmentAccountRepository} from "../../../../../../shared/repositories/EstablishmentAccountRepository";
import {AppState} from "../../../../../../shared/AppState";
import DeviceSelectionComponent from "../../DeviceSelectionComponent/DeviceSelectionComponent.vue";
import {EstablishmentAccountProfileRepository} from "../../../../../../shared/repositories/EstablishmentAccountProfileRepository";
import {AppBus} from "../../../config/AppBus";
import DeviceStateComponent from "../../DeviceStateComponent/DeviceStateComponent.vue";
import {ProfileData} from "../../../../../../shared/mastodonCoreFront/cashless/ProfileData";

@Component({
	components: {
		dropdown,
		'dropdown-button': DropdownButtonComponent,
		'modal-or-drawer': ModalOrDrawerComponent,
		'device-selection': DeviceSelectionComponent,
		'device-state': DeviceStateComponent,
	}
})
export default class ProfileDevicesComponents extends Vue {
	@Prop({required: true}) profile!: ProfileData;
	@Prop({required: true}) devices!: EstablishmentDeviceApiOut[];
	@Prop({required: true}) accountProfiles!: EstablishmentAccountProfileApi[];
	@Prop({default: []}) cashlessDevices!: CashlessDeviceStateApiOut[];

	showDeviceSelectionModal: boolean = false;
	showDeviceStateModal: CashlessDeviceStateApiOut|null = null;
	linkingDevices: boolean = false;

	@AutoWired(EstablishmentDevicesRepository) accessor establishmentDevicesRepository!: EstablishmentDevicesRepository;
	@AutoWired(EstablishmentAccountRepository) accessor establishmentAccountRepository!: EstablishmentAccountRepository;
	@AutoWired(EstablishmentAccountProfileRepository) accessor establishmentAccountProfileRepository!: EstablishmentAccountProfileRepository;
	@AutoWired(AppState) accessor appState!: AppState;
	@AutoWired(AppBus) accessor appBus!: AppBus;

	mounted() {

	}

	get filteredDevices() {
		return this.devices.filter((device) => {
			const deviceAccountProfile = this.accountProfiles.find((accountProfile) => accountProfile.establishmentAccountUid === device.establishmentAccountUid);
			return deviceAccountProfile && deviceAccountProfile.profileUid === this.profile.uid;
		});
	}

	getCashlessDeviceWithUid(uid: VisualScopedUuid<UuidScopeIot_deviceApp>) {
		return this.cashlessDevices.find((cashlessDevice) => cashlessDevice.uid === uid) || null;
	}

	async bindDevices(deviceUids: VisualScopedUuid<UuidScopeIot_deviceApp>[]) {
		this.linkingDevices = true;

		try {
			for(const deviceUid of deviceUids) {
				const device = this.requireDeviceWithUid(deviceUid);

				if(!device.establishmentAccountUid || !this.profile.uid) continue;

				const establishmentAccountProfileApi = new EstablishmentAccountProfileApi({
					establishmentAccountUid: device.establishmentAccountUid,
					profileUid: this.profile.uid
				});

				const updatedAccountProfiles = (await this.establishmentAccountProfileRepository.callContract('update', {
					establishmentUid: this.appState.requireUrlEstablishmentUid(),
					establishmentAccountUid: device.establishmentAccountUid
				}, [establishmentAccountProfileApi])).success();

				const index = this.accountProfiles.findIndex((accountProfile) => accountProfile.establishmentAccountUid === device.establishmentAccountUid);
				if(index !== -1) this.accountProfiles[index].profileUid = this.profile.uid;
				else this.accountProfiles.push(updatedAccountProfiles[0]);
			}
		} catch(err) {
			console.log(err);
		}

		this.linkingDevices = false;
	}

	async unbindDevice(device: EstablishmentDeviceApiOut) {
		try {
			if(!device.establishmentAccountUid) return;
			const accountProfile = this.accountProfiles.find((accountProfile) => accountProfile.establishmentAccountUid === device.establishmentAccountUid);
			if(!accountProfile || accountProfile.profileUid !== this.profile.uid) return;

			await this.establishmentAccountProfileRepository.callContract(
				'delete',
				{establishmentUid: this.appState.requireUrlEstablishmentUid(), establishmentAccountUid: device.establishmentAccountUid, profileUid: this.profile.uid},
				undefined
			);

			const index = this.accountProfiles.findIndex((accountProfile) => accountProfile.establishmentAccountUid === device.establishmentAccountUid);
			if(index !== -1) this.accountProfiles.splice(index, 1);
		} catch(err) {
			this.appBus.emit('emit-toast', {
				title: 'La sauvegarde à échoué',
				description: 'L\'appareil n\'a pas pû être supprimé',
				duration: 3000,
				type: 'ERROR',
				closable: true
			});
		}
	}

	getDropdownButtonActions(device: EstablishmentDeviceApiOut) {
		const actions: DropdownButtonAction[] = [];
		if(this.getCashlessDeviceWithUid(device.uid)) {
			actions.push({
				id: 'open-ticket',
				name: 'Afficher l\'état',
				icon: 'fa-regular fa-circle-question fa-fw'
			})
		}

		actions.push({
			id: 'delete',
			name: 'Supprimer',
			color: 'red',
			icon: 'fa-regular fa-trash-alt fa-fw'
		})

		return actions;
	}

	dropdownClicked(action: DropdownButtonAction, device: EstablishmentDeviceApiOut) {
		if(action.id === 'delete') this.unbindDevice(device);
		else this.showDeviceStateModal = this.requireCashlessDeviceWithUid(device.uid)
	}

	requireDeviceWithUid(deviceUid: VisualScopedUuid<UuidScopeIot_deviceApp>) {
		const device = this.devices.find((device) => device.uid === deviceUid);
		if(!device) throw new Error('missing_device');
		return device;
	}

	requireCashlessDeviceWithUid(uid: VisualScopedUuid<UuidScopeIot_deviceApp>) {
		const cashlessDevice = this.cashlessDevices.find((cashlessDevice) => cashlessDevice.uid === uid) || null;
		if(!cashlessDevice) throw new Error('missing_device');
		return cashlessDevice;
	}
}