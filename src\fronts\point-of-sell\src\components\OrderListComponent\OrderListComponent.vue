<script lang="ts" src="./OrderListComponent.ts">
</script>

<style lang="sass" scoped>
@import './OrderListComponent.scss'
</style>

<template>
    <div class="order-list-component">
        <div class="left">
            <div class="head">
                <h2> Commandes </h2>

                <div class="buttons">
                    <div class="white button">
                        {{ notSyncOrders.length }} commande{{ notSyncOrders.length > 1 ? 's' : '' }} non sync.
                    </div>
                    <div class="button-group">
                        <button :class="{selected: sort === 'DATE'}" @click="sort = 'DATE'; sortAndChunkOrders()">
                            <i class="fa-regular fa-arrow-down-9-1"></i>
                            Date
                        </button>
                        <button :class="{selected: sort === 'NOT_PAYED'}" @click="sort = 'NOT_PAYED'; sortAndChunkOrders()">
                            <i class="fa-regular fa-arrow-down-wide-short"></i>
                            Impayé
                        </button>
                        <button :class="{selected: sort === 'ALL'}" @click="sort = 'ALL'; sortAndChunkOrders()">
                            <i class="fa-regular fa-eye"></i>
                            Tout voir
                        </button>
                    </div>
                </div>
            </div>

            <div class="form-error" v-if="!hasValidToken">
                <i class="fa-solid fa-exclamation-circle"></i>
                <div class="details">
                    <span class="title"> Compte déconnecté </span>
                    <span class="description"> Votre compte a été déconnecté et les commandes ne peuvent plus être synchronisées. </span>
                </div>
                <button class="red button" @click="goToCas()"> Se reconnecter </button>
            </div>


<!--            <div class="filter input-group">-->
<!--                <dropdown-->
<!--                    placeholder="Aucun filtre"-->
<!--                    :values="[-->
<!--                        {name: '', value: ''}-->
<!--                    ]"-->
<!--                ></dropdown>-->
<!--            </div>-->


            <div class="pages" v-if="pagesNumber() > 1">
                <span>
                    {{ page * quantityPerPage - quantityPerPage + 1 }}-{{ Math.min(page * quantityPerPage, totalOrders()) }}
                    sur {{ totalOrders() }}
                </span>
                <div class="small white button" :class="{disabled: page === 1}" @click="page--">
                    <i class="fa-regular fa-arrow-left" ></i>
                </div>
                <div class="small white button" :class="{disabled: page === Math.ceil(totalOrders() / quantityPerPage)}" @click="page++">
                    <i class="fa-regular fa-arrow-right"></i>
                </div>
            </div>

            <div class="table-scroll-wrapper">
                <table class="data-table">
                    <thead>
                    <tr>
                        <td> Créé par </td>
                        <td> Total TTC </td>
                        <td> Reste à payer </td>
                        <td> Payé en </td>
                        <td> Date de création </td>
                        <td> </td>
                    </tr>
                    </thead>
                    <tbody>
                    <tr v-if="!chunkedOrders[0] || chunkedOrders[0].length === 0">
                        <td class="empty-row" colspan="100%"> Aucune commande </td>
                    </tr>
                    <tr v-for="localOrder of chunkedOrders[page - 1]" @click="selectOrder(localOrder.uid)">
                        <order-line
                            :local-order="localOrder"
                            :local-order-transfer="orderTransfers.find((transfer) => transfer.order.uid === localOrder.uid && !transfer.canceled)"
                            :pos-state="posState"
                            @unlock-transfer="unlockTransfer = $event"
                        ></order-line>
                    </tr>
                    </tbody>
                </table>
            </div>

            <div class="pages" v-if="pagesNumber() > 1">
                <span>
                    {{ page * quantityPerPage - quantityPerPage + 1 }}-{{ Math.min(page * quantityPerPage, totalOrders()) }}
                    sur {{ totalOrders() }}
                </span>
                <div class="small white button" :class="{disabled: page === 1}" @click="page--">
                    <i class="fa-regular fa-arrow-left" ></i>
                </div>
                <div class="small white button" :class="{disabled: page === Math.ceil(totalOrders() / quantityPerPage)}" @click="page++">
                    <i class="fa-regular fa-arrow-right"></i>
                </div>
            </div>
        </div>

        <div class="right">
            <order-sidebar :pos-state="posState" :pos-profile="posProfile"></order-sidebar>
        </div>

        <unlock-transfer-modal
            v-if="unlockTransfer"
            :local-order-transfer="unlockTransfer"
            @unlocked="unlockedTransfer($event)"
            @close="unlockTransfer = null"
        ></unlock-transfer-modal>

    </div>
</template>