import {Component, Prop, Vue} from "vue-facing-decorator";
import {FormModalOrDrawerComponent} from "@groupk/vue3-interface-sdk";
import {AutoWired, UuidUtils, VisualScopedUuid} from "@groupk/horizon2-core";
import {UuidScopeEstablishment} from "@groupk/mastodon-core";
import {CustomerRepository} from "../../../../../shared/repositories/CustomerRepository";
import {CustomerApiOut, UuidScopeCustomer_customer} from "@groupk/mastodon-core";
import {CustomerChipRepository} from "../../../../../shared/repositories/CustomerChipRepository";
import {CustomerChipApiIn} from "@groupk/mastodon-core";
import {EstablishmentUrlBuilder} from "../../../../../shared/utils/EstablishmentUrlBuilder";
import {CleaveDirective} from "../../../../../shared/directives/CleaveDirective";

@Component({
	directives: {
		cleave: CleaveDirective,
	},
	components: {
		'form-modal-or-drawer': FormModalOrDrawerComponent
	},
	emits: ['close']
})
export default class CustomerChipCreationFormComponent extends Vue {
	@Prop() establishmentUid!: VisualScopedUuid<UuidScopeEstablishment>;
	@Prop() customer!: CustomerApiOut;

	data: {chipId: string} = {chipId: ''};

	cleaveChip = {
		blocks: [4, 4],
		delimiter: '-',
		uppercase: true
	}

	opened: boolean = false;
	error: { message: string|null, customer: VisualScopedUuid<UuidScopeCustomer_customer>|null }|null = null;

	@AutoWired(CustomerRepository) accessor customerRepository!: CustomerRepository;
	@AutoWired(CustomerChipRepository) accessor customerChipRepository!: CustomerChipRepository;

	mounted() {
		setTimeout(() => this.opened = true, 0);
	}

	close() {
		this.opened = false;
		setTimeout(() => this.$emit('close'), 300);
	}

	async create() {
		this.error = {
			message: null,
			customer: null
		};
		if(this.data.chipId.length < 9) {
			this.error.message = 'L\'id de la puce est trop court';
			return;
		}

		const apiIn = new CustomerChipApiIn({
			chipVisualId: this.data.chipId.split('-').join(''),
			chipId: null,
			chipUid: null,
			customerUid: this.customer.uid,
			startingDatetime: new Date().toISOString(),
			endingDatetime: null,
			comment: null
		})
		const response = await this.customerChipRepository.callContract('create', {establishmentUid: this.establishmentUid}, apiIn);
		if(response.isSuccess()) {
			this.$emit('created', response.success());
			this.$emit('close');
		} else if(response.isError()) {
			const error = response.error();
			if(!error || !('error' in error)) {
				this.error.message = 'Une erreur inconnue est survenue';
			} else if(error.error === 'invalid_data') {
				this.error.message = error.error_details;
			} else if(error.error === 'invalid_chip_id') {
				this.error.message = 'Cet identifiant de puce est invalide';
			} else if(error.error === 'customer_chip_time_overlaps') {
				this.error.message = 'Cette puce est déjà liée à un utilisateur';
				this.error.customer = error.customerUid;
			}
		} else {
			this.error.message = 'Une erreur inconnue est survenue';
		}
	}

	getCustomerUrl(customerUid: VisualScopedUuid<UuidScopeCustomer_customer>) {
		return EstablishmentUrlBuilder.buildUrl('/customers?uid=' + UuidUtils.visualToUuid(customerUid));
	}
}