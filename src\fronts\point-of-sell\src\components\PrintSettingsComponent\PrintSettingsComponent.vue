<script lang="ts" src="./PrintSettingsComponent.ts">
</script>

<style lang="sass" scoped>
@import './PrintSettingsComponent.scss'
</style>

<template>
    <div class="print-settings-component">

        <div class="separator">
            Imprimantes réseau
        </div>

        <div class="no-device" v-if="!isNative || error ">
            Impression réseau non disponible sur cette plateforme
        </div>

        <template v-else>
            <div class="no-device" v-if="tcpPrinters.length === 0">
                Aucune imprimante réseau paramétrée
            </div>

            <div
                class="printer-device"
                :class="{selected: isPrinterSelected(tcpPrinter)}"
                v-for="tcpPrinter of tcpPrinters" @click="isPrinterSelected(tcpPrinter) ? editPrinterCustomConfig(tcpPrinter) : selectPrinter(tcpPrinter)"
            >
                <div class="left">
                    <i class="fa-regular fa-check" v-if="isPrinterSelected(tcpPrinter)"></i>
                    {{ tcpPrinter.name ? tcpPrinter.name : `${tcpPrinter.ip}:${tcpPrinter.port}` }}
                </div>

                <i class="fa-solid fa-cog" v-if="isPrinterSelected(tcpPrinter)"></i>
            </div>

            <div class="add-network-printer">
                <div class="white button" @click="showTcpPrinterCreationModal()">
                    <i class="fa-regular fa-plus"></i>
                    Ajouter une imprimante réseau
                </div>
            </div>

        </template>

        <div class="separator">
            Imprimantes USB

            <button v-if="isNative" class="tertiary button" @click="refreshDevicesList()">
                <i class="fa-regular fa-arrows-rotate" :class="{'fa-spin': refreshing}"></i>
                Rafraîchir la liste
            </button>
        </div>

        <div class="no-device" v-if="!isNative || error ">
            Impression USB non disponible sur cette plateforme
        </div>

        <template v-else>
            <div class="no-device" v-if="usbPrinters.length === 0">
                Aucune imprimante détecté
            </div>
            <div
                class="printer-device"
                :class="{selected: isPrinterSelected(usbPrinter)}"
                v-for="usbPrinter of usbPrinters" @click="isPrinterSelected(usbPrinter) ? editPrinterCustomConfig(usbPrinter) : selectPrinter(usbPrinter)"
            >
                <div class="left">
                    <i class="fa-regular fa-check" v-if="isPrinterSelected(usbPrinter)"></i>
                    {{ usbPrinter.usbDevice.manufacturerName }} {{ usbPrinter.usbDevice.productName }}
                    <i class="fa-solid fa-circle-small green"></i>
                </div>

                <i class="fa-solid fa-cog" v-if="isPrinterSelected(usbPrinter)"></i>
            </div>
        </template>

        <form-modal-or-drawer
            title="Paramétrer l'imprimante"
            :state="editingConfig !== null"
            @close="editingConfig = null"
        >
            <template v-slot:content>
                <div v-if="editingConfig" class="printer-settings-modal">
                    <div class="form">
                        <div class="two-inputs">
                            <div class="input-group">
                                <label for="title"> Largeur du papier </label>
                                <div class="ui input">
                                    <input v-model="editingConfig.loadedMediaWidth" type="number" placeholder="Largeur du papier (mm)" />
                                </div>
                            </div>

                            <div class="input-group">
                                <label for="title"> Densité </label>
                                <div class="ui input">
                                    <input v-model="editingConfig.density" type="number" placeholder="Densité" />
                                </div>
                            </div>

                            <div class="input-group">
                                <div class="boolean-input" :class="{selected: editingConfig.autoPrint}" @click="editingConfig.autoPrint = !editingConfig.autoPrint">
                                    <i class="fa-regular" :class="editingConfig.autoPrint ? 'fa-square-check' : 'fa-square'"></i>
                                    <div class="right">
                                        <span class="title"> Impression automatique </span>
                                        <span class="description"> Imprimer le reçu automatiquement </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </template>

            <template v-slot:buttons>
                <div class="vertical-buttons" v-if="editingConfig">
                    <button class="ui white button" @click="editingConfig = null">
                        Annuler
                    </button>

                    <button class="ui white button" @click="editingTcpPrinter = requireTcpPrinterWithUid(editingConfig.printerId)" v-if="editingConfig.printerType === 'TCP'">
                        <i class="fa-regular fa-pen-line"></i>
                        Modifier l'imprimante
                    </button>

                    <button class="ui black button" @click="savePrinterCustomConfig()">
                        <i class="fa-regular fa-check" style="margin-top: 3px"></i>
                        Appliquer les paramètres
                    </button>
                </div>
            </template>
        </form-modal-or-drawer>

        <form-modal-or-drawer
            title="Ajouter une imprimante réseau"
            :state="editingTcpPrinter !== null"
            @close="editingTcpPrinter = null"
        >
            <template v-slot:content>
                <div v-if="editingTcpPrinter !== null" class="printer-settings-modal">
                    <div class="form">
                        <div class="two-inputs">
                            <div class="input-group">
                                <label for="title"> Adresse IP </label>
                                <div class="ui input">
                                    <input v-model="editingTcpPrinter.ip" type="text" placeholder="Adresse IP" />
                                </div>
                            </div>

                            <div class="input-group">
                                <label for="title"> Port </label>
                                <div class="ui input">
                                    <input v-model="editingTcpPrinter.port" type="number" placeholder="Port" />
                                </div>
                            </div>
                        </div>

                        <div class="input-group">
                            <label for="title"> Nom (informatif) </label>
                            <div class="ui input">
                                <input v-model="editingTcpPrinter.name" type="text" placeholder="Nom" />
                            </div>
                        </div>

                        <div class="tcp-test-connection">
                            <button class="tertiary button" :class="{loading: connectionTest === 'LOADING', disabled: connectionTest === 'LOADING'}" @click="testTcpPrinterConnection()">
                                <i class="fa-regular fa-signal-stream"></i>
                                Tester la connection
                            </button>
                        </div>

                        <div class="form-error" v-if="connectionTest === 'ERROR'">
                            <i class="fa-solid fa-exclamation-circle"></i>
                            <div class="details">
                                <span class="title"> Connexion impossible </span>
                                <span class="description"> Il semble que les informations fournies ne permettent pas de se connecter a l'imprimante </span>
                            </div>
                        </div>

                        <div class="form-success" v-if="connectionTest === 'SUCCESS'">
                            <i class="fa-solid fa-circle-check"></i>
                            <div class="details">
                                <span class="title"> Connexion réussie </span>
                                <span class="description"> Les informations ont bien permis de se connecter a l'imprimante </span>
                            </div>
                        </div>

                        <div class="form-error" v-if="editingTcpPrinterError">
                            <i class="fa-solid fa-exclamation-circle"></i>
                            <div class="details">
                                <span class="title"> Erreur </span>
                                <span class="description">{{ editingTcpPrinterError }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </template>

            <template v-slot:buttons>
                <div class="vertical-buttons">
                    <button class="ui white button" @click="editingTcpPrinter = null">
                        Annuler
                    </button>

                    <button class="ui black button" @click="saveTcpPrinter()">
                        <i class="fa-regular fa-check" style="margin-top: 3px"></i>
                        Appliquer les paramètres
                    </button>
                </div>
            </template>
        </form-modal-or-drawer>
    </div>
</template>