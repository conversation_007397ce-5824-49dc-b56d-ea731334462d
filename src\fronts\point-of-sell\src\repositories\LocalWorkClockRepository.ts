import {AutoWired, Cursor} from "@groupk/horizon2-core";
import {AppBus} from "../config/AppBus";
import {EntityIndexeddbRepository} from "@groupk/horizon2-front";
import {<PERSON>rror<PERSON>and<PERSON>} from "../class/ErrorHandler";
import {LocalWorkClock} from "../model/LocalWorkClock";

export class LocalWorkClockRepository extends EntityIndexeddbRepository<LocalWorkClock> {
	protected static timestampIndexName = 'lastLocalUpdateDate';
	protected static syncedIndexName = 'synced';

	@AutoWired(AppBus) private accessor appBus !: AppBus;
	@AutoWired(ErrorHandler) private accessor errorHandler !: ErrorHandler;

	constructor() {
		super(LocalWorkClock, 'uid', 'pos_work_clocks', 'pos_work_clocks', 1);
		this.addEntityBooleanIndex('synced');
	}

	protected onUpgrade(db: IDBDatabase, transaction: IDBTransaction | null, oldVersion: number, newVersion: number | null) {
		let objectStore = super.onUpgrade(db, transaction, oldVersion, newVersion);
		this.putEntityIndex(objectStore, LocalWorkClockRepository.timestampIndexName, 'lastLocalUpdateDate');
		this.putEntityIndex(objectStore, LocalWorkClockRepository.syncedIndexName, 'synced');
		return objectStore;
	}

	async findAllNotSyncedCursor(): Promise<Cursor<LocalWorkClock>> {
		return await this._findAllBooleanIndexCursor('synced', false);
	}

	async saveAndResync(object: LocalWorkClock) {
		object.synced = false;
		object.lastLocalUpdateDate = new Date().getTime();

		try {
			await this.save(object);
		} catch(err) {
			this.errorHandler.logEverywhere({
				error: err as Error,
				title: 'Une erreur inconnue est survenue',
				description: 'La commande n\'a pa pu être sauvegardée',
			});
		}
	}

	async save(object: LocalWorkClock): Promise<void> {
		await super.save(object);
		this.appBus.emit('workClockSaved', object);
	}

	async clear(){
		await this._clear();
	}

}