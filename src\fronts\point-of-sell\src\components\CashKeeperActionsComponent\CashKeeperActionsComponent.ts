import {Component, Vue} from "vue-facing-decorator";
import {ModalComponent} from "@groupk/vue3-interface-sdk";
import {AutoWired} from "@groupk/horizon2-core";
import {PaymentMethodSettingsCashKeeper} from "@groupk/mastodon-core";
import {PaymentProtocol} from "@groupk/mastodon-core";
import {PaymentMethodSettings_type} from "@groupk/mastodon-core";
import {CashKeeperTcpConnection, CashKeeperTcpWrapper} from "../../class/CashKeeperTcpWrapper";
import {CashkeeperDenomination} from "@groupk/cashkeeper-protocol";
import {CashkeeperState} from "@groupk/cashkeeper-protocol";
import {PosProfile} from "../../model/PosProfile";
import {SocketNative} from "@groupk/native-bridge";

function isPaymentMethodSettingsCashKeeper(object : PaymentMethodSettings_type) : object is PaymentMethodSettingsCashKeeper {
	return object.protocol === PaymentProtocol.PHYSICAL_CASHKEEPER;
}

@Component({
	components: {
		'modal': ModalComponent
	}
})
export default class CashKeeperActionsComponent extends Vue {
	cashKeeperSettings: PaymentMethodSettingsCashKeeper[] = [];
	selectedCashKeeper: { settings: PaymentMethodSettingsCashKeeper, connection: CashKeeperTcpConnection, denominations: ReadonlyArray<CashkeeperDenomination>|null }|null = null;

	connecting: boolean = false;
	emptying: boolean = false;
	filling: boolean = false;
	fillingAmountIn: number = 0;

	error: string|null = null;


	@AutoWired(SocketNative) accessor nativeSocket!: SocketNative;
	@AutoWired(PosProfile) accessor posProfile!: PosProfile;

	mounted() {
		this.cashKeeperSettings = this.posProfile.paymentMethodConfigs
			.map((config) => config.settings)
			.filter(isPaymentMethodSettingsCashKeeper);
	}

	async selectCashKeeper(settings: PaymentMethodSettingsCashKeeper) {
		if(this.selectedCashKeeper !== null) {
			try {
				await CashKeeperTcpWrapper.closeConnection(this.selectedCashKeeper.connection);
			} catch(err) {}
			this.selectedCashKeeper = null;
		}

		try {
			const cashKeeperTcpConnection = await CashKeeperTcpWrapper.getCashKeeperConnection(settings, () => {
				console.log('error');
				this.connecting = false;
			});

			cashKeeperTcpConnection.cashKeeperConnection.on('ready', async ()=> {
				if(this.selectedCashKeeper) {
					this.selectedCashKeeper.denominations = cashKeeperTcpConnection.cashKeeperConnection.getDenominations();
				}
			});

			cashKeeperTcpConnection.cashKeeperConnection.connect();

			this.selectedCashKeeper = {
				settings: settings,
				connection: cashKeeperTcpConnection,
				denominations: null
			};
		} catch(err) {
			this.connecting = false;
			this.error = 'Connection au CashKeeper impossible';
		}
	}

	async emptyCashKeeper() {
		if(!this.selectedCashKeeper) return;

		this.error = null;
		this.emptying = true;

		try {
			await this.selectedCashKeeper.connection.cashKeeperConnection.emptyToVault();

			this.selectedCashKeeper.connection.cashKeeperConnection.on('stateChange', async (state) => {
				if(!this.selectedCashKeeper) return;
				if(state === CashkeeperState.IDLE) {
					await this.selectedCashKeeper.connection.cashKeeperConnection.emptyVault();

					await CashKeeperTcpWrapper.closeConnection(this.selectedCashKeeper.connection);
					this.emptying = false;
					this.$emit('close');
				}
			});
		} catch(err) {
			this.error = 'La demande de purge a échouée';
		}
	}

	async fillCashKeeper() {
		if(!this.selectedCashKeeper) return;
		this.error = null;

		this.filling = true;
		this.fillingAmountIn = 0;

		try {
			this.selectedCashKeeper.connection.cashKeeperConnection.on('amountIn', (r)=>{
				this.fillingAmountIn = r.totalPending;
			});

			await this.selectedCashKeeper.connection.cashKeeperConnection.enable();
		} catch(err) {
			this.filling = false;
			this.error = 'Impossible d\'activer le mode remplissage';
		}
	}

	async validateFill() {
		if(!this.selectedCashKeeper) return;
		await this.selectedCashKeeper.connection.cashKeeperConnection.totalize(this.fillingAmountIn);
		await CashKeeperTcpWrapper.closeConnection(this.selectedCashKeeper.connection);
		this.$emit('close');
	}

	async cancelFill () {
		if(!this.selectedCashKeeper) return;
		await this.selectedCashKeeper.connection.cashKeeperConnection.disable(true);
		await CashKeeperTcpWrapper.closeConnection(this.selectedCashKeeper.connection);
		this.$emit('close');
	}


	async close() {
		if(this.selectedCashKeeper !== null) {
			try {
				await CashKeeperTcpWrapper.closeConnection(this.selectedCashKeeper.connection);
			} catch(err) {}
			this.selectedCashKeeper = null;
		}

		this.$emit('close');
	}

	getDenominationsTotal() {
		if(!this.selectedCashKeeper || !this.selectedCashKeeper.denominations) return 0;
		return this.selectedCashKeeper.denominations.reduce((total, denomination) => total + denomination.value * denomination.level, 0)
	}

	getDenominationLevel(value: number) {
		if(!this.selectedCashKeeper || !this.selectedCashKeeper.denominations) return 0
		return this.selectedCashKeeper.denominations.find((denomination) => denomination.value === value)?.level ?? 0;
	}
}