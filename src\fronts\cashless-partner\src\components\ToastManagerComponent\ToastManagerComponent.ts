import {Component, Vue} from "vue-facing-decorator";
import {AutoWired} from "@groupk/horizon2-core";
import {AppBus} from "../../config/AppBus";

export type Toast = {
	title: string,
	description: string,
	type: 'SUCCESS'|'ERROR',
	closable: boolean,
	duration: number, // Duration in ms
}

type ToastState = {
	toast: Toast,
	lifetime: number,
}


@Component({})
export default class ToastManagerComponent extends Vue {
	state: ToastState[] = [];
	max: number = 8;

	@AutoWired(AppBus) accessor appBus!: AppBus;

	beforeMount() {
		this.tick();

		this.appBus.on('emit-toast', this.displayToast)
	}

	unmounted() {
		this.appBus.off('emit-toast', this.displayToast)
	}

	tick(){
		for (let i = this.state.length - 1; i >= 0; i--) {
			this.state[i].lifetime -= 100;
			if (this.state[i].lifetime < 0) {
				this.removeToast(i);
			}
		}
		setTimeout(() => this.tick(), 100);
	}


	removeToast(index: number) {
		this.state.splice(index, 1);
	}

	displayToast(toast: Toast) {
		if(this.state.length === this.max) {
			this.state.splice(0, 1);
		}
		this.state.push({
			toast: toast,
			lifetime: toast.duration ?? 3000
		});
	}
}