export class SearchUtils{

	static readonly replacementMap : {search:RegExp, replace:string}[] = [
		{search:/[éêèë]/g, replace:'e'},
		{search:/[à@áâãäå]/g, replace:'a'},
		{search:/[æ]/g, replace:'ae'},
		{search:/[ç]/g, replace:'c'},
		{search:/[ìíîï]/g, replace:'i'},
		{search:/[ñ]/g, replace:'n'},
		{search:/[òóôõö]/g, replace:'o'},
		{search:/[œ]/g, replace:'oe'},
		{search:/[ùúûü]/g, replace:'u'},
		{search:/[ýÿ]/g, replace:'y'},
	]

	static cleanForHumanSearch(str : string) : string[]{
		str = str.toLowerCase();
		for(const search of this.replacementMap){
			str = str.replace(search.search, search.replace);
		}

		// all not in range of [a-z0-9] ?
		str = str.replace(/[^a-z0-9]/g, ' ');

		return str.split(' ').filter(s=>s.length!==0);
	}

	static searchInTab<ElementType>(array : ElementType[], accessor:(element : ElementType)=>string|string[], searchStr : string): ElementType[]{
		const searchWords = this.cleanForHumanSearch(searchStr.toLowerCase()) ;

		return array.filter((element)=>{
			const elementSearchStrRaw = accessor(element);
			const elementSearchStrs = Array.isArray(elementSearchStrRaw) ? elementSearchStrRaw : [elementSearchStrRaw];

			for(const elementSearchStr of elementSearchStrs){
				const elementWords = this.cleanForHumanSearch(elementSearchStr);

				let countContains = 0;
				for(const searchWord of searchWords){
					let contains = false;
					for(const elementWord of elementWords){
						if(elementWord.includes(searchWord)){
							contains = true;
							break;
						}
					}

					if(contains) countContains++;
				}

				if(countContains === searchWords.length){
					return true;
				}
			}

			return false;
		});
	}
}