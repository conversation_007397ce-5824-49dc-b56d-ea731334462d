.order-refund-component {
    h3 {
        margin: 10px 0 0 0;
        font-size: 16px;
        font-weight: 600;
    }

    .section {
        display: flex;
        flex-direction: column;
        gap: 10px;
    }

    .ticket-data {
        display: flex;
        align-items: center;
        gap: 15px;

        .ticket-icon {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 32px;
            width: 32px;
            background: var(--secondary-hover-color);
            border-radius: 4px;
        }

        .ticket-name {
            display: flex;
            flex-direction: column;
            gap: 4px;
            font-weight: 500;

            .ticket-description {
                font-size: 13px;
                color: #494949;
                font-weight: 400;
            }
        }
    }

    .refund-methods {
        display: flex;
        flex-direction: column;
        gap: 10px;

        .method {
            display: flex;
            align-items: center;
            gap: 10px;
            cursor: pointer;

            .radio {
                display: flex;
                align-items: center;
                justify-content: center;
                height: 20px;
                width: 20px;
                border-radius: 50%;
                border: 1px solid #D8D8D8;
                cursor: pointer;

                &.selected {
                    border: none;
                    background: #1099FD;

                    .center {
                        height: 6px;
                        width: 6px;
                        background: white;
                        border-radius: 50%;
                    }
                }
            }
        }
    }

    .custom-amount-section {
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 10px;
        background: var(--secondary-hover-color);
        border-radius: 8px;
        padding: 15px;
        cursor: pointer;
        user-select: none;
        font-size: 15px;

        .custom-amount-header {
            display: flex;
            flex-direction: column;
            gap: 4px;

            i {
                transition: transform .3s ease;
            }

            &.open {
                i {
                    transform: rotate(90deg);
                }
            }
        }

        .custom-amount-subtitle {
            font-size: 14px;
            color: #494949;
            font-weight: 400;
        }
    }

    .toggle-input {
        display: flex;
        align-items: center;
        gap: 15px;

        .infos {
            display: flex;
            flex-direction: column;
            gap: 4px;
            flex-grow: 2;

            .title {
                font-size: 15px;
            }

            .subtitle {
                font-size: 14px;
            }
        }
    }

    .input.flex {
        display: flex;
        align-items: center;
        gap: 10px;

        .button {
            height: 100%;
        }
    }
}