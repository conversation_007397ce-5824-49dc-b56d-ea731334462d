<script lang="ts" src="./selfOrder.ts" />

<style lang="sass" scoped>
@import './selfOrder.scss'
</style>

<template>
    <div id="self-order-page">
        <div v-if="error" class="error">
            <div class="top">
                <div class="title"> Erreur </div>
                <div class="subtitle"> {{ error }} </div>
            </div>

            <div class="buttons">
                <button class="double white button" @click="reset()">
                  <span class="yellow-button-icon">
                    <i class="fa-regular fa-chevron-left"></i>
                </span>

                    Changer de {{ config.objectName }}
                </button>
            </div>

        </div>
        <div v-else-if="selectedProduct" class="selected-product">
            <div class="top">
                <div class="small-title"> Vous avez sélectionné </div>
                <div class="title"> {{ selectedProduct.name }} </div>
                <div class="price"> {{ $filters.Money(selectedProduct.prices[0])  }} </div>
            </div>

            <div class="buttons">
                <button class="white button" @click="reset()">
                    <span class="yellow-button-icon">
                        <i class="fa-regular fa-chevron-left"></i>
                    </span>
                    Retour
                </button>
                <button class="white button" @click="reset()">
                    <span class="green-button-icon">
                        <i class="fa-regular fa-circle"></i>
                    </span>
                    Payer {{ $filters.Money(selectedProduct.prices[0]) }}
                </button>
            </div>

        </div>
        <div v-else class="selection">
            <div class="number">
                {{ String(currentId).padStart(2, '0') }}
            </div>
            <div class="description">
                Sélectionnez {{ config.objectGender === 'M' ? 'un' : 'une' }} {{ config.objectName }} en tapant son numéro
            </div>
        </div>
    </div>
</template>