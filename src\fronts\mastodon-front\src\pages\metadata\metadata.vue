<script lang="ts" src="./metadata.ts">
</script>

<style lang="sass">
@import './metadata.scss'
</style>

<template>
    <div id="metadata-page" class="page">
        <filter-table-layout
            :header-parameters="headerParameters"
            :allowed-filters="{filters: [], sorts: availableSorts}"
            :table-columns="tableColumns"
            :filters="{}"
            :drawer-opened="selectedMetadata !== null"
            @search="search($event)"
            @changed-column-preferences="saveColumnPreferences($event)"
            @sorted="sorted($event)"
        >
            <template v-slot:table-data>
                <div class="table-dimmer" v-if="loading">
                    <div class="loader-container">
                        <div class="loader"></div>
                    </div>
                </div>
                <tr class="table-no-data" v-else-if="metadataDescriptors.length === 0">
                    <td colspan="100%"> Aucune donnée </td>
                </tr>
                <tr class="table-no-data" v-else-if="filteredMetadataDescriptors.length === 0">
                    <td colspan="100%"> Aucun descripteur ne correspond à la recherche </td>
                </tr>
                <tr v-else v-for="metadata in filteredMetadataDescriptors" 
                    :key="metadata.uid"
                    :class="{selected: selectedMetadata && metadata.uid === selectedMetadata.uid}"
                    @click="toggleSelectedMetadata(metadata)">
                    <td :class="{'mobile-hidden': column.mobileHidden}" v-for="column in tableColumns.filter((column) => column.displayed)">
                        <template v-if="column.name === 'uid'">{{ metadata.uid }}</template>
                        <template v-else-if="column.name === 'name'">{{ metadata.descriptorTemplate.name }}</template>
                        <template v-else-if="column.name === 'type'">{{ metadata.descriptorTemplate.type }}</template>
                        <template v-else-if="column.name === 'required'">
                            <div class="status">
                                <div class="square" :class="{green: metadata.descriptorTemplate.required}">
                                    <i class="fa-regular fa-check" v-if="metadata.descriptorTemplate.required"></i>
                                    <i class="fa-regular fa-times" v-else></i>
                                </div>
                            </div>
                        </template>
                        <template v-else-if="column.name === 'description'">{{ metadata.description }}</template>
                    </td>
                </tr>
            </template>
            <template v-slot:right>
                <!-- Empty state -->
                <div v-if="!selectedMetadata" class="empty-right-panel">
                    <img src="../../assets/img/select-hint.svg" />
                    Cliquez sur un descripteur pour <br/> le sélectionner
                </div>
                
                <!-- Selected metadata details -->
                <div v-else class="selected-metadata">
                    <!-- Close button -->
                    <div class="close" @click="selectedMetadata = null">
                        <i class="fa-regular fa-xmark"></i>
                        <span> Fermer </span>
                    </div>
                    
                    <!-- Header with metadata name -->
                    <div class="header">
                        <div class="left">
                            <h2>{{ selectedMetadata.descriptorTemplate.name }}</h2>
                            <span>{{ selectedMetadata.descriptorTemplate.type }}</span>
                        </div>
                    </div>
                    
                    <!-- Metadata details -->
                    <div class="metadata-details">
                        <h3>Informations générales</h3>
                        <div class="properties-table">
                            <div class="row">
                                <span class="title">Identifiant</span>
                                <span class="value">{{ selectedMetadata.uid }}</span>
                            </div>
                            <div class="row">
                                <span class="title">Nom</span>
                                <span class="value">{{ selectedMetadata.descriptorTemplate.name }}</span>
                            </div>
                            <div class="row">
                                <span class="title">Type</span>
                                <span class="value">{{ selectedMetadata.descriptorTemplate.type }}</span>
                            </div>
                            <div class="row">
                                <span class="title">Requis</span>
                                <span class="value">
                                    <span v-if="selectedMetadata.descriptorTemplate.required" class="label green">Oui</span>
                                    <span v-else class="label grey">Non</span>
                                </span>
                            </div>
                            <div class="row" v-if="selectedMetadata.description">
                                <span class="title">Description</span>
                                <span class="value">{{ selectedMetadata.description }}</span>
                            </div>
                            <div class="row" v-if="selectedMetadata.descriptorTemplate.description">
                                <span class="title">Description du modèle</span>
                                <span class="value">{{ selectedMetadata.descriptorTemplate.description }}</span>
                            </div>
                            <div class="row">
                                <span class="title">Date de création</span>
                                <span class="value">{{ $filters.Date(selectedMetadata.creationDatetime) }}</span>
                            </div>
                        </div>
                        
                        <!-- Target information -->
                        <h3 v-if="selectedMetadata.targetList && selectedMetadata.targetList.length > 0">Cibles</h3>
                        <div v-if="selectedMetadata.targetList && selectedMetadata.targetList.length > 0" class="properties-table">
                            <div class="row" v-for="target in selectedMetadata.targetList" :key="target.uid">
                                <span class="title">{{ target.uid }}</span>
                                <span class="value">{{ target.children ? 'Avec enfants' : 'Sans enfants' }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </template>
        </filter-table-layout>
    </div>
</template>