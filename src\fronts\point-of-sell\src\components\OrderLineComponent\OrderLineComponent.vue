<script lang="ts" src="./OrderLineComponent.ts">
</script>

<style lang="sass" scoped>
@import './OrderLineComponent.scss'
</style>

<template>
    <td class="name" @click.stop="localOrder.error || havePendingPayment() ? showError = true : ()=>{}">
        <div>
            <div class="error" v-if="localOrder.error">
                <i class="fa-solid fa-triangle-exclamation"></i>
            </div>
            <div class="warning" v-if="havePendingPayment()">
                <i class="fa-solid fa-triangle-exclamation"></i>
            </div>
            <img class="reservit-logo" :src="$assets.reservit"  v-if="localOrder.order.transferredTo">
            {{ localOrder.order.sellerEstablishmentAccountUid ? posState.getEstablishmentAccountWithUid(localOrder.order.sellerEstablishmentAccountUid).firstname : 'En ligne' }}
        </div>
    </td>
    <td>
        {{ $filters.Money(getOrderTotals.purchases.withTaxesBeforeDiscount) }}
    </td>
    <td>
        <div class="label orange" v-if="havePendingPayment()">
            En attente
        </div>
        <div class="label orange" v-else-if="getOrderTotals.leftToPay > 0">
            {{ $filters.Money(getOrderTotals.leftToPay) }}
        </div>
        <div class="label green" v-else>
            Payé
        </div>
    </td>
    <td>
        {{ getOrderPaymentMethods().join(', ') || 'Aucun'}}
    </td>
    <td>
        {{ $filters.Day(localOrder.order.creationDatetime) }}
        {{ $filters.Hour(localOrder.order.creationDatetime) }}<br/>
        ({{ $filters.Since(localOrder.order.creationDatetime) }})
    </td>
    <td width="1px" class="action">
        <dropdown-button
            class="custom-dropdown-button"
            icon="fa-regular fa-ellipsis-h"
            :touch-compatible="true"
            :icon-only="true"
            alignment="RIGHT"
            :actions="[{
                title: '',
                actions: OrderUtils.getOrderDropdownActions(localOrder)
            }]"
            @clicked="dropdownClicked($event)"
        ></dropdown-button>
    </td>

    <div class="transferring-hover" v-if="localOrderTransfer && !localOrderTransfer.canceled" @click.stop="">
        En cours de transfert...

        <dropdown-button
            class="custom-dropdown-button"
            icon="fa-regular fa-ellipsis-h"
            :touch-compatible="true"
            :icon-only="true"
            alignment="RIGHT"
            :actions="[{
                title: '',
                actions: getTransferDropdownValues(localOrderTransfer)
            }]"
            @clicked="transfertDropdownClicked($event, localOrderTransfer)"
        ></dropdown-button>
    </div>

    <div class="modal-dimmer" v-if="showQuickEdit" @click.stop="showQuickEdit = false">
        <div class="modal" @click.stop>
            <order-payments-quick-edit :local-order="localOrder"></order-payments-quick-edit>
        </div>
    </div>

    <div class="modal-dimmer" v-if="showError" @click.stop="showError = false">
        <div class="modal" @click.stop>
            <h3> Description de l'erreur </h3>
            <template v-if="localOrder.error">
                Une erreur empêche cette commande d'être synchronisée avec le serveur.
                Veuillez contacter l'assistance nous nous occuperons de régler ce problème au plus vite. <br/><br/>
                Erreur : {{ localOrder.error }}
            </template>
            <template v-if="havePendingPayment()">
                Un paiement n'a pas pu être traité et doit être renseigné manuellement
                dans l'onglet 'Paiements' de la commande
            </template>

            <div class="buttons">
                <button class="black button" @click="showError = false">
                    Fermer
                </button>
            </div>
        </div>
    </div>
</template>