.toast-manager-component {
    position: fixed;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 5001;

    &.display-all {
        display: flex;
        flex-direction: column;
        gap: 10px;
        overflow: auto;
        max-height: 100%;
        padding-top: 40px;
        box-sizing: border-box;

        .toast {
            position: relative;
            transform: none !important;
            background: #E12727 !important;

            &.green {
                background: #43DB76 !important;
            }

            &.grey {
                background: #D9D9D9 !important;
            }

            &.orange {
                background: #FFCE46 !important;
            }
        }
    }

    .toast {
        position: absolute;
        display: flex;
        justify-content: space-between;
        align-items: center;
        gap: 20px;
        padding: 15px;
        border-radius: 8px;
        background: #E12727;
        width: 100%;
        box-sizing: border-box;

        @media (max-width: 900px) {
            flex-direction: column;
        }

        &.green {
            background: #43DB76;

            * {
                color: black !important;
            }
        }

        &.grey {
            background: #D9D9D9;

            * {
                color: black !important;
            }
        }

        &.orange {
            background: #FFCE46;

            * {
                color: black !important;
            }
        }


        animation: .2s ease-out 0s 1 slideIn;

        .left {
            display: flex;
            gap: 20px;
            align-items: center;

            .close {
                margin-left: 10px;
                i {
                    font-size: 20px;
                    color: white;
                }
            }

            .content {
                display: flex;
                flex-direction: column;
                gap: 4px;

                .title {
                    color: #FFF;
                    font-size: 15px;
                    font-weight: 600;
                }

                .description {
                    color: #FFF;
                    font-size: 15px;
                    white-space: nowrap;
                }
            }

        }

        &.green {
            .button {
                border: 2px solid black;
            }
        }

        .button {
            background: transparent;
            color: white;
            border: 2px solid white;
        }

        transition: transform ease-out .2s, background ease-out .2s;

        &.position-1 {
            position: relative;
            z-index: 50;
        }

        &.position-2 {
            background: #E64C4C;
            z-index: 49;
            transform: scale(0.95) translateY(-20%);

            &.green {
                background: #5cde87;
            }

            &.orange {
                background: #FFCE46;
            }

            &.grey {
                background: #D9D9D9 !important;
            }

        }

        &.position-3 {
            background: #ED7878;
            z-index: 49;
            transform: scale(0.90) translateY(-40%);

            &.green {
                background: #92eeb1;
            }

            &.orange {
                background: #FFCE46;
            }

            &.grey {
                background: #D9D9D9 !important;
            }

        }

        &.position-big {
            background: #ED7878;
            z-index: 49;
            transform: scale(0.90) translateY(-40%);

            &.green {
                background: #92eeb1;
            }

            &.orange {
                background: #FFCE46;
            }

            &.grey {
                background: #D9D9D9 !important;
            }

        }
    }

    @media (max-width: 900px) {
        left: 20px;
        right: 20px;
        transform: none;
        z-index: 1005;

        .content {
            .description {
                white-space: normal !important;
            }
        }
    }
}

@-webkit-keyframes slideIn {
    from {
        transform: translateY(40%);
    }
    to {
        transform: translateY(0)
    }
}