import {Component, Prop, Vue} from "vue-facing-decorator";
import {FormModalOrDrawerComponent} from "@groupk/vue3-interface-sdk";
import {AutoWired, VisualScopedUuid} from "@groupk/horizon2-core";
import {UuidScopeEstablishment, ApplicationPermission, EstablishmentAccountPermissionModel, CustomerHttpCustomerContract} from "@groupk/mastodon-core";
import {CustomerApiOut} from "@groupk/mastodon-core";
import {CustomerRepository} from "../../../../../shared/repositories/CustomerRepository";
import {CustomerData} from "../../../../../shared/mastodonCoreFront/cashless/CustomerData";

export function CustomerFormComponentHasRequiredPermissions(ownedPermissions: ApplicationPermission[]): boolean {
	return EstablishmentAccountPermissionModel.hasPermissionsForContracts(ownedPermissions, [
		CustomerHttpCustomerContract.create,
		CustomerHttpCustomerContract.update,
	]);
}

@Component({
	components: {
		'form-modal-or-drawer': FormModalOrDrawerComponent
	},
	emits: ['close']
})
export default class CustomerFormComponent extends Vue {
	@Prop() establishmentUid!: VisualScopedUuid<UuidScopeEstablishment>;
	@Prop({default: null}) editingCustomer!: CustomerApiOut|null;

	customer: CustomerData = new CustomerData();

	opened: boolean = false;
	error: string|null = null;

	@AutoWired(CustomerRepository) accessor customerRepository!: CustomerRepository;

	mounted() {
		if(this.editingCustomer) {
			this.customer = new CustomerData(this.editingCustomer);
		}
		setTimeout(() => this.opened = true, 0);
	}

	close() {
		this.opened = false;
		setTimeout(() => this.$emit('close'), 300);
	}

	async create() {
		if(this.editingCustomer !== null) {
			const result = await this.customerRepository.callContract('update', {establishmentUid: this.establishmentUid, customerUid: this.editingCustomer.uid}, this.customer.toApi());

			if(result.isSuccess()) {
				this.$emit('updated', result.success());
			} else {
				const error = result.error();
				if(error && 'error' in error && error.error === 'invalid_data') {
					this.error = 'Données invalides ' + error.error_details;
				} else if(error && 'error' in error && error.error === 'email_already_used') {
					this.error = 'Cet email est déjà utilisé';
				} else {
					this.error = 'Erreur inconnue';
				}
			}
		} else {
			const result = await this.customerRepository.callContract('create', {establishmentUid: this.establishmentUid}, this.customer.toApi());
			if(result.isSuccess()) {
				this.$emit('created', result.success());
			} else {
				const error = result.error();
				if(error && 'error' in error && error.error === 'invalid_data') {
					this.error = 'Données invalides ' + error.error_details;
				} else if(error && 'error' in error && error.error === 'email_already_used') {
					this.error = 'Cet email est déjà utilisé';
				} else {
					this.error = 'Erreur inconnue';
				}
			}
		}
	}
}