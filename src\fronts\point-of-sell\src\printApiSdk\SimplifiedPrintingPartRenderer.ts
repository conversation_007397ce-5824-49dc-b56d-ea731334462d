import {SimplifiedPart_TextRenderer} from "./SimplifiedPart_TextRenderer";
import {SimplifiedPart_SeparatorRenderer} from "./SimplifiedPart_SeparatorRenderer";
import {SimplifiedPart_TableRenderer} from "./SimplifiedPart_TableRenderer";
import {SimplifiedPrintingPart, SimplifiedPrintingPartRendererConfig} from "./SimplifiedPrintingPartData";
import CanvasRendererSDK, {PrintRendererSDKConfiguration} from "./CanvasRendererSDK";


export class SimplifiedPrintingPartRenderer {
	private parts : SimplifiedPrintingPart[] = [];
	private config : SimplifiedPrintingPartRendererConfig;

	private textRenderer : SimplifiedPart_TextRenderer;
	private tableRenderer : SimplifiedPart_TableRenderer;
	private separatorRenderer : SimplifiedPart_SeparatorRenderer;

	constructor(config : SimplifiedPrintingPartRendererConfig) {
		this.config = config;
		this.textRenderer = new SimplifiedPart_TextRenderer(this.config);
		this.tableRenderer = new SimplifiedPart_TableRenderer(this.config);
		this.separatorRenderer = new SimplifiedPart_SeparatorRenderer(this.config);

	}

	addParts(parts : SimplifiedPrintingPart|SimplifiedPrintingPart[]) : this{
		if(Array.isArray(parts)) this.parts.push(...parts);
		else this.parts.push(parts);

		return this;
	}

	getCanvasRenderer(
		userConfig : Partial<PrintRendererSDKConfiguration>&{targetSize:PrintRendererSDKConfiguration['targetSize']},
		baseLineHeight: number = -1
	){
		if (baseLineHeight <= 0) baseLineHeight = SimplifiedPrintingPartRenderer.estimateBaseLineHeightWithWidth(userConfig.targetSize.width);

		let totalHeight = this.getTotalHeightInPixels(baseLineHeight);

		let defaultConfig: PrintRendererSDKConfiguration = {
			formsMetricSystem: "pixels",
			clearColor: 'white',
			allowTaintedImages: false,
			targetSize: userConfig.targetSize,
		};
		if (userConfig.targetSize.height <= 0) {
			userConfig.targetSize.height = Math.ceil(totalHeight / userConfig.targetSize.height) * userConfig.targetSize.height;
		}

		let config : PrintRendererSDKConfiguration = {...defaultConfig, ...userConfig};
		let renderer = new CanvasRendererSDK(config);

		let y = this.config.margins.top * baseLineHeight;
		for(const part of this.parts){
			y = this.renderPart(renderer, y, baseLineHeight, part);
		}

		return renderer;
	}

	private renderPart(renderer: CanvasRendererSDK, y : number, baseLineHeight: number, part : SimplifiedPrintingPart) : number{
		if(part.type === 'TEXT') return this.textRenderer.render(renderer, y, baseLineHeight,part);
		else if(part.type === 'TABLE') return this.tableRenderer.render(renderer, y, baseLineHeight,part);
		else if(part.type === 'SEPARATOR') return this.separatorRenderer.render(renderer, y, baseLineHeight,part);
		else throw new Error('part_type_not_supported');
	}


	static estimateBaseLineHeightWithWidth(width: number, numberOfCharactersPerLine = 50, ratio: number = 2.5) {
		return Math.floor((width / numberOfCharactersPerLine) * ratio);
	}

	public getTotalHeightInPixels(baseLineHeight: number): number {
		let height = (this.config.margins.top + this.config.margins.bottom) * baseLineHeight;

		for(const part of this.parts){
			if(part.type === 'TEXT') height += this.textRenderer.estimateHeight(baseLineHeight, part);
			else if(part.type === 'TABLE') height += this.tableRenderer.estimateHeight(baseLineHeight, part);
			else if(part.type === 'SEPARATOR') height += this.separatorRenderer.estimateHeight(baseLineHeight, part);
		}

		return height;
	}

}
