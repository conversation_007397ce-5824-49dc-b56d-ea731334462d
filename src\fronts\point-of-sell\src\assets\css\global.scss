
:root {
    --primary-hover-color: rgba(0, 102, 255, 0.1);
    --primary-hover-text-color: #06F;
    --primary-color: #06F;
    --primary-text-color: white;
    --sidebar-width: 350px;
    --pos-color: white;
    --pos-current-text-color: black;
    --pos-current-selected-color: rgba(0, 102, 255, 0.2);
    --pos-current-selected-text-color: #0066FF;

    --pos-default-text-color: white;
    --pos-default-selected-color: rgba(0, 102, 255, 0.2);
    --pos-default-selected-text-color: #0066FF;

    --pos-light-text-color: black;
    --pos-light-selected-color: rgba(0, 0, 0, 0.2);
    --pos-light-selected-text-color: black;

    --pos-dark-text-color: white;
    --pos-dark-selected-color: rgba(255, 255, 255, 0.2);
    --pos-dark-selected-text-color: white;

    --safe-area-top: 10px;
    --safe-area-bottom: 10px;
}

html, body {
    height: 100%;
    margin: 0;
    padding: 0;
    overflow: hidden;
    background: #F2F4F7;
}

*:not(i, text) {
    font-family: 'Mont<PERSON>rat', sans-serif !important;
}

.page {
    overflow: hidden;
    height: 100%;
    width: 100%;
}

#mainRouterContainer {
    width: 100%;
    height: 100%;
}

// Computer
@media (min-width: 900px) {
    body, #mainRouterContainer {
        display: flex;
    }
}

// Mobile & Tablet
@media (max-width: 900px) {
    #mainRouterContainer .page {
        padding-top: 60px;
    }
}

.layout-main-content-right-panel {
    .close {
        display: none;
        margin-bottom: 20px;
        cursor: pointer;
        user-select: none;
    }

    @media screen and (max-width: 1400px) {
        .close {
            display: flex;
            gap: 10px;
            align-items: center;
        }
    }
}

.loading-container {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 20px;
}

.label {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 5px;
    padding: 5px 8px;
    font-size: 12px;
    font-weight: 600;
    color: #2788E1;
    border-radius: 4px;
    background: rgba(39, 136, 225, 0.2);
    min-width: 30px;
    box-sizing: border-box;

    i {
        font-size: 12px;
    }

    &.white {
        background: white;
        border-color: black;
        color: black;

    }

    &.red {
        background: rgba(225, 39, 39, 0.2);
        border-color: #E12727;
        color: #E12727;
    }

    &.green {
        background: rgba(13, 184, 27, 0.2);
        color: #0db71b;
    }

    &.grey {
        background: rgba(148, 148, 148, 0.2);
        color: #726f6f;
    }


    &.orange {
        background: rgba(225, 138, 39, 0.2);
        color: #f37901;
    }

    &.clickable {
        cursor: pointer;
    }
}

.modal-dimmer {
    position: fixed;
    inset: 0;
    z-index: 5000;
    background: rgba(0, 0, 0, 0.16);
    backdrop-filter: blur(10px);
    display: flex;
    align-items: center;
    justify-content: center;

    .modal {
        background: white;
        padding: 40px;
        border-radius: 8px;
        max-width: min(100%, 600px);
        min-width: 400px;
        margin: 20px;
        max-height: 80vh;
        box-sizing: border-box;
        overflow: auto;
    }
}

.loading {
    display: flex;
    flex-direction: column;
    gap: 10px;
    align-items: center;
    justify-content: center;
    width: 100%;
    font-weight: 600;
    font-size: 13px;
}

.right-modal-dimmer {
    position: fixed;
    inset: 0;
    z-index: 998;
    background: rgba(0, 0, 0, 0.4);
}