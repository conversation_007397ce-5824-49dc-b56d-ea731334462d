import {Component, Vue} from "vue-facing-decorator";
import {AutoWired, ScopedUuid, Uuid, UuidUtils, VisualScopedUuid} from "@groupk/horizon2-core";
import {
	uuidScopeEstablishment,
	UuidScopeEstablishment,
	EstablishmentApiOut,
	PlatformDescriptorFrontApiOut
} from "@groupk/mastodon-core";
import {CashlessEstablishmentActivationApiIn} from "@groupk/mastodon-core";
import {OptionBuilder} from "vue-facing-decorator/dist/optionBuilder";
import {Router} from "@groupk/horizon2-front";
import {EstablishmentRepository} from "../../../../../shared/repositories/EstablishmentRepository";
import {CashlessEstablishmentRepository} from "../../../../../shared/repositories/CashlessEstablishmentRepository";
import {EstablishmentUrlBuilder} from "../../../../../shared/utils/EstablishmentUrlBuilder";
import SidebarStateListener from "../../../../../shared/utils/SidebarStateListener";


export interface PricingPlan {
	uid: Uuid,
	name: string,
	description: string,
	order: number,
	prices: {
		price: number[],
	},
	requires: Uuid[],
	unlockedApps: PlatformDescriptorFrontApiOut[]
}


@Component({})
export default class PricingView extends Vue {
	establishmentUid!: VisualScopedUuid<UuidScopeEstablishment>;
	establishment!: EstablishmentApiOut;

	enabling: boolean = false;

	@AutoWired(EstablishmentRepository) accessor establishmentRepository!: EstablishmentRepository;
	@AutoWired(CashlessEstablishmentRepository) accessor cashlessEstablishmentRepository!: CashlessEstablishmentRepository;
	@AutoWired(SidebarStateListener) accessor sidebarStateListener!: SidebarStateListener;
	@AutoWired(Router) accessor router!: Router;

	constructor(optionBuilder: OptionBuilder, vueInstance: any) {
		super(optionBuilder, vueInstance);

		this.sidebarStateListener.setHiddenSidebar(true);
	}

	async mounted() {
		let regexMatch = this.router.lastRouteRegexMatches;

		if (regexMatch && regexMatch[1]) {
			this.establishmentUid = UuidUtils.uuidToVisual<UuidScopeEstablishment>(uuidScopeEstablishment,regexMatch[1] as ScopedUuid<UuidScopeEstablishment>);

			const data = await this.establishmentRepository.callContract('get', {establishmentUid: this.establishmentUid}, undefined);
			if(data.isSuccess()) {
				this.establishment = data.success();
			} else {
				throw new Error('404');
			}
		}
	}

	async enableCashless() {
		this.enabling = true;

		try {
			await this.cashlessEstablishmentRepository.callContract('enable', {establishmentUid: this.establishmentUid}, new CashlessEstablishmentActivationApiIn());
			window.location.href = EstablishmentUrlBuilder.buildUrl('/products');
		} catch(err) {
			console.log(err);
		}

		this.enabling = false;
	}
}