
:root {
  --primary-hover-color: rgba(42, 185, 217, 0.1) !important;
  --primary-hover-text-color: black !important;
  --primary-color: #2AB9D9 !important;
  --primary-text-color: white !important;
  --primary-button-hover-color: #1ea8c7 !important;
  --desktop-padding: 40px !important;
  --mobile-padding: 20px !important;
}

html, body {
  height: 100%;
  margin: 0;
  padding: 0;
  overflow: hidden;
}

body.native {
  .page .layout-center-content, .page .layout-main-content-right-panel {
    padding-top: 40px !important;
  }
  .top-bar-component {
    padding-top: 70px;
  }
}

*:not(i, text) {
  font-family: 'Montserrat', sans-serif !important;
}

.page {
  overflow: hidden;
  height: 100%;
  width: 100%;
  box-sizing: border-box;
}

#mainRouterContainer, #oldRouterContainer {
  width: 100%;
  height: 100%;
}

// Computer
@media (min-width: 900px) {
  body, #mainRouterContainer, #oldRouterContainer {
    display: flex;
  }
}

// Mobile & Tablet
@media (max-width: 900px) {
  #mainRouterContainer .page, #oldRouterContainer .page {
    padding-top: 60px;
  }
}

.layout-main-content-right-panel {
  .close {
    display: none;
    margin-bottom: 20px;
    cursor: pointer;
    user-select: none;
  }

  @media screen and (max-width: 1400px) {
    .close {
      display: flex;
      gap: 10px;
      align-items: center;
    }
  }
}

.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 20px;
}

.empty-right-panel {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
  justify-content: center;
  height: 100%;
  color: #636363;
  font-size: 14px;
  text-align: center;

  img {
    width: 150px;
  }
}

.table-scroll-wrapper {
  overflow: auto;

  .no-break {
    white-space: nowrap;
  }

  &.no-margin {
    margin-right: -40px;
    padding-right: 40px;
  }
}

.contact {
  display: flex;
  flex-direction: column;
  gap: 40px;

  .head {
    display: flex;
    flex-direction: column;
    gap: 6px;
  }

  .contact-name {
    font-weight: bold;
    font-size: 20px;
  }

  .grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
  }

  .item {
    display: flex;
    flex-direction: column;
    gap: 10px;

    .method {
      font-size: 14px;
    }

    .how {
      display: flex;
      gap: 10px;
      align-items: center;
      font-weight: 600;
    }
  }
}