import {Component, Ref, Vue} from "vue-facing-decorator";
import {
    AccountAuthTokenApiOut,
    EstablishmentAuthTokenApiOut,
    PlatformDescriptorApiOut,
    PlatformDescriptorFrontApiOut, uuidScopeEstablishment, UuidScopeEstablishment, AccountPasswordResetCreateApiIn
} from "@groupk/mastodon-core";
import {AutoWired, EmailUtils, ScopedUuid, UuidUtils, VisualScopedUuid} from "@groupk/horizon2-core";
import {AuthRepository} from "../../../../../shared/repositories/AuthRepository";
import {Router} from "@groupk/horizon2-front";
import {AuthStateModel} from "../../../../../shared/AuthStateModel";
import {PlatformRepository} from "../../../../../shared/repositories/PlatformRepository";
import {AccountRepository} from "../../../../../shared/repositories/AccountRepository";
import {translateResponseError} from "../../../../../shared/RepositoryExtensions";
import {MastodonAccountContractAggregate} from "@groupk/mastodon-core";
import {AccountPasswordResetCreateApiOut, AccountPasswordResetConfirmApiIn} from "@groupk/mastodon-core";
import {EstablishmentUrlBuilder} from "../../../../../shared/utils/EstablishmentUrlBuilder";

@Component({})
export default class passwordReset extends Vue {
    establishmentUid: VisualScopedUuid<UuidScopeEstablishment>|null = null;

    platforms!: PlatformDescriptorApiOut;
    currentPlatform!: PlatformDescriptorFrontApiOut;
    currentToken: EstablishmentAuthTokenApiOut|AccountAuthTokenApiOut|null = null;

    email: string = '';
    emailCode: { code: string[], validated: boolean } = {
        code: [],
        validated: false
    };

    newPassword: {new: string, validation: string} = {
        new: '', validation: ''
    }

    resetApiOut: AccountPasswordResetCreateApiOut|null = null;

    sending: boolean = false;
    error: string|null = null;
    validatingCode: boolean = false;
    savingPassword: boolean = false;
    changedPassword: boolean = false;

    loading: boolean = true;

    @AutoWired(AuthRepository) private accessor authRepository!: AuthRepository;
    @AutoWired(PlatformRepository) accessor platformRepository!: PlatformRepository;
    @AutoWired(AccountRepository) accessor accountRepository!: AccountRepository;
    @AutoWired(AuthStateModel) accessor authStateModel!: AuthStateModel;
    @AutoWired(Router) private accessor router!: Router;

    async mounted() {
        this.currentToken = this.authStateModel.getStateSync();

        let regexMatch = this.router.lastRouteRegexMatches;
        if (regexMatch && regexMatch[1]) {
            this.establishmentUid = UuidUtils.scopedToVisual<UuidScopeEstablishment>(regexMatch[1] as ScopedUuid<UuidScopeEstablishment>, uuidScopeEstablishment);
        }

        try {
            this.platforms = (await this.platformRepository.callContract('get', undefined, undefined)).success();

            const url = new URL(window.location.href);
            const platformName = url.searchParams.get('platform') ?? '';
            const platform = this.platforms.fronts.find((platform) => platform.id.toLowerCase() === platformName.toLowerCase());
            if(!platform) {
                if(this.platforms.fronts.length === 1) this.currentPlatform = this.platforms.fronts[0];
                else {
                    window.location.href = '/not-found';
                    return;
                }
            } else {
                this.currentPlatform = platform;
            }
        } catch(err) {
            // ??
            console.log(err);
        }

        addEventListener('paste', this.handlePaste);

        this.loading = false;
    }

    unmounted() {
        removeEventListener('paste', this.handlePaste);
    }

    async sendResetEmail() {
        // TODO: Verifications (empty, ...)
        if(!EmailUtils.isEmailValid(this.email)) {
            this.error = 'Veuillez renseigner un email valide';
            return;
        }

        this.error = null;
        this.sending = true;

        const apiIn = new AccountPasswordResetCreateApiIn({
            email: this.email
        });
        const response = await this.accountRepository.callContract('resetPassword', {}, apiIn);
        if(response.isSuccess()) {
            this.resetApiOut = response.success();
        } else {
            this.error = translateResponseError<typeof MastodonAccountContractAggregate, 'resetPassword'>(response, {
                invalid_data: undefined,
                account_password_reset_invalid_code: 'Code invalide'
            })
        }

        this.sending = false;
    }

    codeInputChanged(inputIndex: number) {
        const elements = document.getElementsByClassName('code-input') as HTMLCollectionOf<HTMLInputElement>;
        if(this.emailCode.code[inputIndex]) {
            if(elements[inputIndex + 1]) elements[inputIndex + 1].focus();
        }
    }

    codeInputKeydown(inputIndex: number, event: KeyboardEvent) {
        if(event.key !== 'Backspace') return;
        const elements = document.getElementsByClassName('code-input') as HTMLCollectionOf<HTMLInputElement>;
        if((this.emailCode.code[inputIndex] === '' || this.emailCode.code[inputIndex] === undefined) && inputIndex > 0) {
            elements[inputIndex - 1].focus();
        }
    }

    handlePaste(event: ClipboardEvent) {
        if(!this.resetApiOut || this.emailCode.validated) return;
        let paste = (event.clipboardData || (window as any).clipboardData).getData("text");
        paste.replaceAll('-', '');
        this.emailCode.code = paste.split('').slice(0, 6);
    }

    async verifyResetCode() {
        if(!this.resetApiOut) return;

        this.error = null;
        this.validatingCode = true;

        const apiIn = new AccountPasswordResetConfirmApiIn({
            uid: this.resetApiOut.uid,
            code: this.emailCode.code.join(''),
            password: null
        });
        const response = await this.accountRepository.callContract('confirmResetPassword', {}, apiIn);
        if(response.isSuccess()) {
            this.emailCode.validated = true;
        } else {
            this.error = translateResponseError<typeof MastodonAccountContractAggregate, 'confirmResetPassword'>(response, {
                invalid_data: undefined,
                account_password_reset_expired: 'Le code est expiré',
                account_password_reset_invalid_code: 'Le code est invalide'
            })
        }

        this.validatingCode = false;
    }

    async modifyPassword() {
        if(!this.resetApiOut) return;

        if(this.newPassword.new.length === 0) {
            this.error = 'Veuillez renseigner un mot de passe';
            return;
        }

        if(this.newPassword.new !== this.newPassword.validation) {
            this.error = 'Les mots de passe ne correspondent pas';
            return;
        }

        this.error = null;
        this.savingPassword = true;

        const apiIn = new AccountPasswordResetConfirmApiIn({
            uid: this.resetApiOut.uid,
            code: this.emailCode.code.join(''),
            password: this.newPassword.new
        });
        const response = await this.accountRepository.callContract('confirmResetPassword', {}, apiIn);
        if(response.isSuccess()) {
            this.changedPassword = true;
        } else {
            this.error = translateResponseError<typeof MastodonAccountContractAggregate, 'confirmResetPassword'>(response, {
                invalid_data: undefined,
                account_password_reset_expired: 'Le code est expiré',
                account_password_reset_invalid_code: 'Le code est invalide'
            })
        }

        this.savingPassword = false;
    }

    getLoginUrl() {
        return EstablishmentUrlBuilder.buildUrl('/' + window.location.search);
    }
}