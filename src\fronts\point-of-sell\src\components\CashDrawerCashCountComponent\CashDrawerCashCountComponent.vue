<script lang="ts" src="./CashDrawerCashCountComponent.ts" />

<style lang="sass">
@import './CashDrawerCashCountComponent.scss'
</style>

<template>
    <div class="cash-drawer-cash-count-component">
        <div class="left">
            <div class="tables">
                <table class="money-table">
                    <thead>
                    <tr>
                        <td>  </td>
                        <td> Pièces </td>
                        <td> Quantité </td>
                        <td> Total </td>
                    </tr>
                    </thead>
                    <tbody>
                    <tr v-for="state of coinsState" :class="{selected: selectedAmount === state.amount}" @click="selectedAmount = state.amount">
                        <td>
                            <i v-if="selectedAmount === state.amount" class="fa-regular fa-circle-dot"></i>
                            <i v-else class="fa-regular fa-circle"></i>
                        </td>
                        <td> {{ state.amount.toFixed(2) }} </td>
                        <td> x{{ state.quantity }} </td>
                        <td class="total"> {{ $filters.Money(state.amount * state.quantity * 100) }} </td>
                    </tr>
                    </tbody>
                </table>

                <table class="money-table">
                    <thead>
                    <tr>
                        <td>  </td>
                        <td> Billets </td>
                        <td> Quantité </td>
                        <td> Total </td>
                    </tr>
                    </thead>
                    <tbody>
                    <tr v-for="state of notesState" :class="{selected: selectedAmount === state.amount}" @click="selectedAmount = state.amount">
                        <td>
                            <i class="fa-regular fa-circle"></i>
                        </td>
                        <td> {{ state.amount.toFixed(2) }} </td>
                        <td> x{{ state.quantity }} </td>
                        <td class="total"> {{ $filters.Money(state.amount * state.quantity * 100) }} </td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
        <div class="right">
            <span class="hint">
                Rentrez la quantité de <b> {{ selectedAmount > 2 ? 'billets' : 'pièces' }} de {{ $filters.Money(selectedAmount * 100) }} </b>
            </span>

            <keypad :simple-mode="true" @clicked-key="updateAmount($event)" :next-key="true"></keypad>

            <button class="big primary button">
                Valider le fond de caisse ({{ $filters.Money(totalCashAmount) }})
            </button>
        </div>
    </div>
</template>