import {Component, Prop, Vue} from "vue-facing-decorator";

export enum KeypadKey {
	MAX = 10,
	QUARTER = 11,
	HALF = 12,
	ZERO = 0,
	ONE = 1,
	TWO = 2,
	THREE = 3,
	FOUR = 4,
	FIVE = 5,
	SIX = 6,
	SEVEN = 7,
	EIGHT = 8,
	NINE = 9,
	ZERO_ZERO = 13,
	BACKSPACE = 14,
	TRASH = 15,
	NEXT = 16,
}

@Component({
	emits: ['clicked-key']
})
export default class KeypadComponent extends Vue {
	@Prop({default: false}) simpleMode!: boolean;
	@Prop({default: false}) nextKey!: boolean;
	@Prop({default: false}) hideOrderRelatedKeys!: boolean;

	KeypadKey = KeypadKey;

	clickedKey(key: number) {
		this.$emit('clicked-key', key)
	}
}