.profile-settings-component {
    position: relative;
    display: flex;
    flex-direction: column;
    gap: 20px;
    padding: 20px 40px;

    @media screen and (max-width: 900px) {
        padding: 20px;
    }

    .settings-line {
        display: grid;
        grid-template-columns: 400px 1fr;
        grid-gap: 40px;
        padding: 30px 0;
        border-bottom: 1px solid var(--border-color);

        @media screen and (max-width: 900px) {
            grid-template-columns: 1fr;
        }

        .left {
            display: flex;

            .infos {
                display: flex;
                flex-direction: column;
                gap: 4px;

                .title {
                    font-size: 15px;
                    font-weight: 600;
                }

                .subtitle {
                    font-size: 15px;
                }
            }
        }

        .right {
            flex-grow: 2;
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            gap: 10px;
            overflow: hidden;

            .global-settings {
                width: 100%;
            }

            .terminal-keypad-credit-settings {
                width: 100%;
            }

            .terminal-pos-settings {
                width: 100%;
            }
        }
    }

    .kiosk-product {
        display: flex;
        flex-direction: column;
        gap: 20px;
        width: 100%;

        .products-cards {
            display: flex;
            flex-direction: column;
            gap: 15px;

            .product-card {
                border: 1px solid #E2E2E2;
                border-radius: 8px;
                background: white;
                overflow: hidden;

                .card-header {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    padding: 10px 15px;
                    background: #F8F9FA;
                    border-bottom: 1px solid #E2E2E2;

                    .product-name {
                        display: flex;
                        align-items: center;
                        gap: 10px;
                        flex-grow: 1;

                        i {
                            color: #6C757D;
                            font-size: 16px;
                        }

                        .name {
                            font-weight: 500;
                            font-size: 15px;
                            color: #212529;
                        }
                    }

                    .card-actions {
                        display: flex;
                        gap: 8px;
                    }
                }

                .card-content {
                    padding: 20px;
                }
            }
        }

        .no-products {
            .empty-state {
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                padding: 40px 20px;
                text-align: center;
                border: 2px dashed #E2E2E2;
                border-radius: 8px;
                background: #F8F9FA;

                i {
                    font-size: 48px;
                    color: #ADB5BD;
                    margin-bottom: 15px;
                }

                .message {
                    font-size: 16px;
                    font-weight: 600;
                    color: #6C757D;
                    margin-bottom: 5px;
                }

                .submessage {
                    font-size: 14px;
                    color: #ADB5BD;
                }
            }
        }

        @media screen and (max-width: 900px) {
            .products-cards {
                .product-card {
                    .card-header {
                        padding: 12px 15px;

                        .product-name {
                            .name {
                                font-size: 14px;
                            }
                        }
                    }

                    .card-content {
                        padding: 15px;

                        .properties-table {
                            .row {
                                flex-direction: column;
                                align-items: flex-start;
                                gap: 5px;
                                padding: 10px 0;

                                .title {
                                    font-size: 13px;
                                }

                                .value {
                                    font-size: 13px;
                                }
                            }
                        }
                    }
                }
            }

            .no-products {
                .empty-state {
                    padding: 30px 15px;

                    i {
                        font-size: 36px;
                    }

                    .message {
                        font-size: 15px;
                    }

                    .submessage {
                        font-size: 13px;
                    }
                }
            }
        }
    }

    .toggle-input {
        display: flex;
        align-items: center;
        gap: 15px;

        .toggle-button-component {
            flex-shrink: 0;
        }

        .infos {
            display: flex;
            flex-direction: column;
            gap: 4px;
            flex-grow: 2;

            .title {
                font-size: 15px;
            }

            .subtitle {
                font-size: 14px;
            }
        }
    }

    .toggles {
        display: flex;
        flex-direction: column;
        gap: 15px;
        margin-top: 20px;

        .toggle-button-component {
            flex-shrink: 0;
        }
    }
}