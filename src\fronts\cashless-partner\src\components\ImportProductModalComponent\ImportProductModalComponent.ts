import {Component, Prop, Vue} from "vue-facing-decorator";
import {ImportColumn, ModalOrDrawerComponent, TableImportComponent} from "@groupk/vue3-interface-sdk";
import {AutoWired} from "@groupk/horizon2-core";
import {ProductsRepository} from "../../../../../shared/repositories/ProductsRepository";
import {
	SimpleProductApiIn,
	SimpleProductApiOut,
	ApplicationPermission,
	EstablishmentAccountPermissionModel,
	CashlessHttpSimpleProductContract,
} from "@groupk/mastodon-core";
import {CashlessProductImportExportHelper} from "../../../../../shared/utils/CashlessProductImportExportHelper";
import {AppState} from "../../../../../shared/AppState";

export function ImportProductModalComponentHasRequiredPermissions(ownedPermissions: ApplicationPermission[]): boolean {
	return EstablishmentAccountPermissionModel.hasPermissionsForContracts(ownedPermissions, [
		CashlessHttpSimpleProductContract.list,
		CashlessHttpSimpleProductContract.create,
		CashlessHttpSimpleProductContract.update,
	]);
}

@Component({
	components: {
		'modal-or-drawer': ModalOrDrawerComponent,
		'table-import': TableImportComponent,
	},
	emits: ['close']
})
export default class ImportProductModalComponent extends Vue {
	csvImportProductsColumns: ImportColumn[] = CashlessProductImportExportHelper.csvImportColumns;

    csvImportEntityType = SimpleProductApiIn;

    products:SimpleProductApiOut[]|null=null;

    productImportExportHelper!: CashlessProductImportExportHelper;

    loading: boolean = false;
	opened: boolean = false;

    @AutoWired(ProductsRepository) accessor productRepository!: ProductsRepository;
    @AutoWired(AppState) accessor appState!: AppState;

	async mounted(){
		this.loading = true;

        this.productImportExportHelper = new CashlessProductImportExportHelper(this.appState.requireUrlEstablishmentUid());
        this.products = (await this.productRepository.callContract('list', {establishmentUid: this.appState.requireUrlEstablishmentUid()},{})).success();

		setTimeout(() => {
			this.opened = true;
		}, 0);

		this.loading = false;
	}

	close(){
		this.opened = false;
		setTimeout(() => {
			this.$emit('close');
		}, 500);
	}
}