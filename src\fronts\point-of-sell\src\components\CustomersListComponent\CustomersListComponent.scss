.customers-list {
    height: 100%;
    overflow: auto;

    .empty-customers {
        text-align: center;
        margin-top: 20px;
        font-style: italic;
    }

    .top {
        display: flex;
        align-items: stretch;
        gap: 10px;
        padding: 10px;
        position: sticky;
        top: 0;
        background: white;

        .input-group {
            position: relative;
            flex-grow: 2;

            input {
                font-size: 16px;
                padding: 15px;
            }

            .loader {
                position: absolute;
                right: 15px;
                top: 50%;
                transform: translateY(-50%);
                height: 15px;
                width: 15px;
                border-width: 2px;

                &:after {
                    height: 15px;
                    width: 15px;
                    border-width: 2px;
                }
            }
        }
    }

    .letter-separator {
        padding: 10px 20px;
        font-size: 16px;
        font-weight: 600;
        background: #F2F2F2;

    }

    .letter-group {
        display: flex;
        flex-direction: column;
        gap: 5px;
        border-radius: 12px;
        background: white;
        padding: 5px 5px;

        .customer {
            display: flex;
            gap: 10px;
            align-items: center;
            margin: 0 5px;
            padding: 15px;
            border-radius: 8px;
            cursor: pointer;
            user-select: none;

            //&:hover {
            //    background: var(--secondary-hover-color);
            //}
            //
            //&.active {
            //    background: var(--primary-hover-color);
            //}

            .profile-picture {
                border-radius: 50%;
                width: 42px;
                height: 42px;
                flex-shrink: 0;
                background-size: cover;
                background-position: center;
            }

            .infos {
                flex-grow: 2;
                display: flex;
                flex-direction: column;

                .name {
                    color: #000;
                    font-size: 16px;
                    font-weight: 600;
                    text-transform: capitalize;
                }

                .email {
                    font-size: 14px;
                    font-weight: 400;
                    word-break: break-all;
                }
            }

            i {
                font-size: 20px;

                &.fa-circle-dot {
                    color: var(--primary-color)
                }
            }
        }
    }
}


.create-customer {
    position: relative;
    display: flex;
    flex-direction: column;
    gap: 10px;
    padding: 20px 20px 80px 20px;
    height: 100%;
    overflow: auto;

    .close {
        position: absolute;
        top: 0;
        right: 0;
        padding: 20px;

        i {
            font-size: 20px;
        }
    }

    .head {
        display: flex;
        flex-direction: column;
        gap: 4px;

        .title {
            font-size: 20px;
            font-weight: 600;
        }

        .subtitle {
            font-size: 15px;
        }
    }

    .profile-picture-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 5px;
        padding: 10px 0;

        .profile-picture {
            height: 86px;
            width: 86px;
            border-radius: 50%;
            background-size: cover;
            background-position: center;
        }

        span {
            font-size: 13px;
            text-decoration-line: underline;
        }

    }
}