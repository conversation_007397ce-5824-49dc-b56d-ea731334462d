import {Component, Ref, Vue} from "vue-facing-decorator";
import {
	ContentHeaderComponent,
	LayoutContentWithRightPanelComponent,
	ModalCustomFilterComponent,
	ModalOrDrawerComponent,
	TableColumnsOrganizationComponent,
	TableColumn,
	ContentHeaderParameters,
	DropdownComponent,
	DropdownValue, DropdownButtonComponent, FormModalOrDrawerComponent,
	ForbiddenMessageComponent
} from "@groupk/vue3-interface-sdk";
import {
	AutoWired, EnumUtils,
	ScopedUuid, UuidUtils,
	VisualScopedUuid
} from "@groupk/horizon2-core";
import {
	EstablishmentAccountProfileApi,
	ProfileApiOut,
	CashlessDeviceStateApiOut,
	UuidScopeCashless_profile,
	UuidScopeIot_deviceApp,
	EstablishmentDeviceAppV2ApiOut,
	uuidScopeIot_deviceApp,
	uuidScopeEstablishmentAccount,
	IotEstablishmentDeviceV2HttpContract,
	uuidScopeIot_quickLink,
	UuidScopeIot_quickLink,
	ApplicationPermission,
	EstablishmentAccountPermissionModel,
	CashlessHttpEstablishmentAccountProfileContract,
	CashlessHttpDeviceContract,
	CashlessHttpProfileContract,
} from "@groupk/mastodon-core";
import {
	DeviceKnownBrand,
	IotPermissions
} from "@groupk/mastodon-core";
import {OptionBuilder} from "vue-facing-decorator/dist/optionBuilder";
import {Router} from "@groupk/horizon2-front";
import {TableColumnsRepository} from "../../../../../shared/repositories/TableColumnsRepository";
import {
	PermissionBuilder,
	uuidScopeEstablishment,
	UuidScopeEstablishment, UuidScopeEstablishmentAccount,
} from "@groupk/mastodon-core";
import SidebarStateListener from "../../../../../shared/utils/SidebarStateListener";
import {EstablishmentAccountRepository} from "../../../../../shared/repositories/EstablishmentAccountRepository";
import {ProfilesRepository} from "../../../../../shared/repositories/ProfilesRepository";
import {EstablishmentAccountProfileRepository} from "../../../../../shared/repositories/EstablishmentAccountProfileRepository";
import {CashlessDeviceRepository} from "../../../../../shared/repositories/CashlessDeviceRepository";
import DeviceStateComponent from "../../components/DeviceStateComponent/DeviceStateComponent.vue";
import {DeviceData} from "../../../../../shared/mastodonCoreFront/DeviceData";
import {AppState} from "../../../../../shared/AppState";
import DeviceOnboardingScanComponent from "../../components/DeviceOnboardingScanComponent/DeviceOnboardingScanComponent.vue";
import {
	DeviceOnboardingScanComponentHasRequiredPermissions
} from "../../components/DeviceOnboardingScanComponent/DeviceOnboardingScanComponent";
import {EstablishmentDeviceRepository} from "../../../../../shared/repositories/EstablishmentDeviceRepository";
import {DeviceImportExportHelper} from "../../../../../shared/utils/DeviceImportExportHelper";
import {translateResponseError} from "../../../../../shared/RepositoryExtensions";
import {AppBus} from "../../config/AppBus";
import ToastManagerComponent from "../../components/ToastManagerComponent/ToastManagerComponent.vue";
import {BarcodeCameraReaderNative, NativeInterface} from "@groupk/native-bridge";
import WebQrCodeReaderComponent
	from "../../../../ticketing-partner/src/components/WebQrCodeReaderComponent/WebQrCodeReaderComponent.vue";
import {EstablishmentDeviceV2ApiOut} from "@groupk/mastodon-core";
import {ComponentUtils} from "../../../../../shared/utils/ComponentUtils";

@Component({
	components: {
		'layout': LayoutContentWithRightPanelComponent,
		'content-header': ContentHeaderComponent,
		'modal-custom-filter': ModalCustomFilterComponent,
		'table-columns-organization': TableColumnsOrganizationComponent,
		'modal-or-drawer': ModalOrDrawerComponent,
		'dropdown': DropdownComponent,
		'dropdown-button': DropdownButtonComponent,
		'device-state': DeviceStateComponent,
		'device-onboarding-scan': DeviceOnboardingScanComponent,
		'form-modal-or-drawer': FormModalOrDrawerComponent,
		'toast-manager': ToastManagerComponent,
		'web-qr-code-reader': WebQrCodeReaderComponent,
		'forbidden-message': ForbiddenMessageComponent,
	}
})
export default class DevicesView extends Vue {
	establishmentUid!: VisualScopedUuid<UuidScopeEstablishment>;

	devices: EstablishmentDeviceV2ApiOut[] = [];
	deviceApps: EstablishmentDeviceAppV2ApiOut[] = [];
	cashlessDevices: CashlessDeviceStateApiOut[] = [];
	profiles: ProfileApiOut[] = [];
	accountProfiles: EstablishmentAccountProfileApi[] = [];
	forbidden: boolean = false;

	headerParameters: ContentHeaderParameters = {
		header: 'Appareils',
		subtitle: 'Liez un appareil à un point de vente',
		actions: [{
			id: 'scan',
			type: 'SIMPLE_ACTION',
			name: 'Scan alentours',
			icon: 'fa-regular fa-satellite-dish',
			callback: this.toggleScanModal
		}, {
			id: 'manual',
			type: 'SIMPLE_ACTION',
			name: 'Ajout manuel',
			icon: 'fa-regular fa-circle-plus',
			callback: this.toggleManualModal
		}] as any,
		hideSearch: true,
		searchPlaceholder: 'Rechercher un appareil'
	}

	selectedProfile: VisualScopedUuid<UuidScopeCashless_profile>|null = null;

	devicesTableKey = 'cashless-devices';
	devicesTableColumns: TableColumn[] = [{
		title: 'Uid', name: 'uid', displayed: true, mobileHidden: false
	}, {
		title: 'Id physique', name: 'hardwareId', displayed: true, mobileHidden: false
	}, {
		title: 'Point de vente', name: 'profile', displayed: true, mobileHidden: false
	}, {
		title: 'Dernière synchronisation', name: 'lastHeartbeat', displayed: false, mobileHidden: true
	}, {
		title: 'Niveau de batterie', name: 'batteryLevel', displayed: false, mobileHidden: true
	}, {
		title: 'Date de création', name: 'creationDatetime', displayed: false, mobileHidden: true
	},{
		title: `Version de l'application`, name: 'applicationVersion', displayed: false, mobileHidden: true
	}];

	sortedRow: {
		name: string,
		direction: 'asc'|'desc'
	}|null = null;

	selectedDevices: EstablishmentDeviceAppV2ApiOut[] = []

	showDeviceStateModal: CashlessDeviceStateApiOut|null = null;
	showColumnsOrganization: boolean = false;
	showImportModal: boolean = false;
	creatingDeviceData: DeviceData|null = null;
	showScanModal: boolean = false;
	showManualLinkModal: {quickLinkUid: string, error: string|null}|null = null;

	isNative: boolean = true;
	desktopScanning: boolean = false;
	linkingDevices: boolean = false;
	loading: boolean = false;
	drawerOpened: boolean = false;

	@Ref() webQrReader!: WebQrCodeReaderComponent;

	@AutoWired(EstablishmentDeviceRepository) accessor establishmentDeviceRepository!: EstablishmentDeviceRepository;
	@AutoWired(TableColumnsRepository) accessor tableColumnsRepository!: TableColumnsRepository;
	@AutoWired(EstablishmentAccountRepository) accessor establishmentAccountRepository!: EstablishmentAccountRepository;
	@AutoWired(EstablishmentAccountProfileRepository) accessor establishmentAccountProfileRepository!: EstablishmentAccountProfileRepository;
	@AutoWired(ProfilesRepository) accessor profilesRepository!: ProfilesRepository;
	@AutoWired(CashlessDeviceRepository) accessor cashlessDeviceRepository!: CashlessDeviceRepository;
	@AutoWired(Router) accessor router!: Router;
	@AutoWired(BarcodeCameraReaderNative) accessor barcodeCameraReader!: BarcodeCameraReaderNative;
	@AutoWired(SidebarStateListener) accessor sidebarStateListener!: SidebarStateListener;
	@AutoWired(AppState) accessor appState!: AppState;
	@AutoWired(AppBus) accessor appBus!: AppBus;

	constructor(optionBuilder: OptionBuilder, vueInstance: any) {
		super(optionBuilder, vueInstance);
		let regexMatch = this.router.lastRouteRegexMatches;

		this.sidebarStateListener.setHiddenSidebar(false);

		addEventListener("resize", (event) => {
			if(window.innerWidth < 1400) this.sidebarStateListener.setMinimizedSidebar(true);
			else this.sidebarStateListener.setMinimizedSidebar(false);
		});


		if (regexMatch && regexMatch[1]) {
			this.establishmentUid = UuidUtils.scopedToVisual<UuidScopeEstablishment>(regexMatch[1] as ScopedUuid<UuidScopeEstablishment>, uuidScopeEstablishment);
			this.loading = true;
		}

		if (!this.appState.hasPermission(PermissionBuilder(IotPermissions, 'ESTABLISHMENT-DEVICE_WRITE'))) {
			for (let id of ['new']) {
				const index = this.headerParameters.actions.findIndex(e => (e as any).id === id);
				if (index !== -1) this.headerParameters.actions.splice(index, 1);
			}
		}

		if(!this.appState.advancedInterfaces) {
			for (let id of ['manual']) {
				const index = this.headerParameters.actions.findIndex(e => (e as any).id === id);
				if (index !== -1) this.headerParameters.actions.splice(index, 1);
			}
		}
	}

	async mounted() {
		// Check for essential page functionality
		if(!ComponentUtils.hasPermissions((ownedPermissions: ApplicationPermission[]) => {
			return EstablishmentAccountPermissionModel.hasPermissionsForContracts(ownedPermissions, [
				CashlessHttpDeviceContract.list,
				IotEstablishmentDeviceV2HttpContract.list,
				CashlessHttpProfileContract.list,
				CashlessHttpEstablishmentAccountProfileContract.list,
			])
		})) {
			this.forbidden = true;
			this.loading = false;
			return;
		}

		if(!ComponentUtils.hasPermissions(DeviceOnboardingScanComponentHasRequiredPermissions)) {
			ComponentUtils.disableActions(this.headerParameters, ['scan']);
		}

		this.isNative = NativeInterface.instance.available();

		await this.loadDevices();
		this.cashlessDevices = (await this.cashlessDeviceRepository.callContract('list', {establishmentUid: this.establishmentUid}, undefined)).success();
		this.profiles = (await this.profilesRepository.callContract('list', {establishmentUid: this.establishmentUid}, undefined)).success();
		this.accountProfiles = (await this.establishmentAccountProfileRepository.callContract('list', {establishmentUid: this.establishmentUid}, undefined)).success()

		if(window.innerWidth < 1400) this.sidebarStateListener.setMinimizedSidebar(true);
		else this.sidebarStateListener.setMinimizedSidebar(false);

		this.loading = false;
	}

	async loadDevices() {
        const data = (await this.establishmentDeviceRepository.callContract('list', {establishmentUid: this.establishmentUid}, undefined)).success();
        this.deviceApps = data.appList.filter((device) => device.endingDatetime === null);
		this.devices = data.list;
    }

	saveColumnPreferences(columns: TableColumn[]) {
		this.tableColumnsRepository.saveColumnsPreferences(this.devicesTableKey, columns);
	}

	startCreateDevice() {
		this.creatingDeviceData = new DeviceData();
	}

	getDeviceBrandsValues(): DropdownValue[] {
		return EnumUtils.values(DeviceKnownBrand).map((value) => {
			return {
				name: value,
				value: value
			}
		});
	}

	unselectAll() {
		const selection = document.getSelection();
		if(selection) selection.removeAllRanges();
	}

	toggleDevice(event: MouseEvent, deviceToToggle: EstablishmentDeviceAppV2ApiOut) {
		const selection = document.getSelection();
		if (selection && !selection.isCollapsed) {
			return;
		}
		if(event.shiftKey) {
			event.preventDefault();
			event.stopPropagation();
			if(selection) selection.removeAllRanges();

			// Shift clic = select multiple
			const firstSelectedIndex = this.deviceApps
				.findIndex((device) => this.selectedDevices.map((selected) => selected.uid).includes(device.uid));

			const lastSelectedIndex = this.deviceApps
				.findLastIndex((device) => this.selectedDevices.map((selected) => selected.uid).includes(device.uid));

			const toggleIndex =  this.deviceApps.findIndex((device) => device.uid === deviceToToggle.uid);

			if(toggleIndex < firstSelectedIndex) {
				this.selectedDevices = this.deviceApps.slice(toggleIndex, firstSelectedIndex + 1);
			} else if (toggleIndex > lastSelectedIndex) {
				this.selectedDevices = this.deviceApps.slice(lastSelectedIndex, toggleIndex + 1);
			} else {
				this.selectedDevices = this.deviceApps.slice(firstSelectedIndex, toggleIndex + 1);
			}
		} else {
			const index = this.selectedDevices.findIndex((device) => device.uid === deviceToToggle.uid);
			if(index !== -1) {
				this.selectedDevices.splice(index, 1);
			} else this.selectedDevices.push(deviceToToggle);
		}
	}

	isDeviceSelected(searchingDevice: EstablishmentDeviceAppV2ApiOut) {
		return this.selectedDevices.findIndex((device) => device.uid === searchingDevice.uid) !== -1;
	}

	isOneDeviceSelected() {
		return this.selectedDevices.length > 0;
	}

	areAllDevicesSelected() {
		return this.selectedDevices.length === this.deviceApps.length;
	}

	toggleAll() {
		if(this.isOneDeviceSelected()) {
			this.selectedDevices = [];
		} else {
			this.selectedDevices = [...this.deviceApps];
		}
	}

	// async addDevice() {
	// 	if(!this.creatingDeviceData) return;
	//
	// 	this.creatingDevice = true;
	// 	this.linkError = null;
	//
	// 	try {
	// 		const apiIn = new EstablishmentDeviceCreationNewApiIn({
	// 			hardwareId: this.creatingDeviceData.serial,
	// 			softwareId: this.creatingDeviceData.serial,
	// 			brand: this.creatingDeviceData.brand,
	// 			model: null,
	// 			dumb: true,
	// 			localIpList: undefined,
	// 			serviceList: undefined,
	// 			batteryLevel: undefined,
	// 			ssidName: undefined,
	// 			comment: null
	// 		});
	//
	// 		const result = await this.establishmentDevicesRepository.callContract('create', {establishmentUid: this.establishmentUid}, apiIn);
	// 		if(result.isSuccess()) {
	// 			const device = result.success();
	// 			this.devices.push(device.device);
	//
	// 			await DeviceImportExportHelper.createEstablishmentAccountForDevice(device)
	//
	// 			this.creatingDeviceData = null;
	// 		} else {
	// 			const error = result.error();
	// 			if(error && 'error' in error) {
	// 				if(error.error === 'device_already_in_use') {
	// 					this.linkError = 'Appareil déjà relié';
	// 				} else if(error.error === 'invalid_data') {
	// 					this.linkError = 'Données invalides';
	// 				}
	// 			} else {
	// 				this.linkError = 'Erreur inconnue';
	// 			}
	// 		}
	// 	} catch(err) {
	// 		console.log(err);
	// 	}
	//
	// 	this.creatingDevice = false;
	// }

	getProfilesDropdownValues(): DropdownValue[] {
		return [{
			name: 'Aucun',
			value: null
		} as DropdownValue].concat(this.profiles.map((profile) => {
			return {
				name: profile.name,
				value: profile.uid as string
			} as DropdownValue
		}));
	}

	sortRow(name: string) {
		let order = 1;
		if(this.sortedRow && this.sortedRow.name === name) {
			if(this.sortedRow.direction === 'asc') {
				order = -1;
			}
		}
		this.sortedRow = {
			name: name,
			direction: order === -1 ? 'desc' : 'asc'
		}
		this.deviceApps.sort((a, b) => {
			if(name === 'profile') {
				const profileA = (a.establishmentAccountUid ? this.getLinkedProfile(a.establishmentAccountUid)?.name : 'Aucun') ?? 'Aucun';
				const profileB = (b.establishmentAccountUid ? this.getLinkedProfile(b.establishmentAccountUid)?.name : 'Aucun') ?? 'Aucun';
				return profileA.toLowerCase() > profileB.toLowerCase() ? order : order * -1;
			} else return 0;
		});
	}

	getLinkedProfile(establishmentAccountUid: ScopedUuid<UuidScopeEstablishmentAccount>) {
		const accountProfile = this.accountProfiles.find((accountProfile) => accountProfile.establishmentAccountUid === UuidUtils.scopedToVisual(establishmentAccountUid, uuidScopeEstablishmentAccount));
		if(!accountProfile) return null;
		const profile = this.profiles.find((profile) => profile.uid === accountProfile.profileUid);
		if(!profile) throw new Error('missing_profile');
		return profile;
	}

	async linkDevicesWithAccount() {
		this.linkingDevices = true;

		try {
			for(let device of this.selectedDevices) {
				if(!device.establishmentAccountUid) continue;

				if(this.selectedProfile === null) {
					const profile = this.getLinkedProfile(device.establishmentAccountUid);

					if(profile) {
						await this.establishmentAccountProfileRepository.callContract('delete', {establishmentUid: this.establishmentUid, establishmentAccountUid: device.establishmentAccountUid, profileUid: profile.uid}, undefined);
						const index = this.accountProfiles.findIndex((accountProfile) => accountProfile.establishmentAccountUid === UuidUtils.scopedToVisual(device.establishmentAccountUid, uuidScopeEstablishmentAccount));
						if(index !== -1) this.accountProfiles.splice(index, 1);
					}
				} else {
					const establishmentAccountProfileApi = new EstablishmentAccountProfileApi({
						establishmentAccountUid: UuidUtils.scopedToVisual(device.establishmentAccountUid, uuidScopeEstablishmentAccount),
						profileUid: this.selectedProfile
					});

					const updatedAccountProfiles = (await this.establishmentAccountProfileRepository.callContract('update', {
						establishmentUid: this.establishmentUid,
						establishmentAccountUid: device.establishmentAccountUid
					}, [establishmentAccountProfileApi])).success();

					const index = this.accountProfiles.findIndex((accountProfile) => accountProfile.establishmentAccountUid === UuidUtils.scopedToVisual(device.establishmentAccountUid, uuidScopeEstablishmentAccount));
					if(index !== -1) this.accountProfiles[index].profileUid = this.selectedProfile;
					else this.accountProfiles.push(updatedAccountProfiles[0]);
				}


			}
		} catch(err) {
			console.log(err);
		}

		this.linkingDevices = false;
		this.drawerOpened = false;
	}


	readCodeWithCamera(event: Event) {
		this.barcodeCameraReader.read({side: 'back'}).then((content: any) => {
			if (content.content) {
				this.importWithQrCode(content.content);
			}
		});

		((event.target!) as HTMLButtonElement ).blur();
	}

	readCodeWithWebCamera() {
		this.webQrReader.start();
	}

	importWithQrCode(quickLinkUid: string) {
		this.showManualLinkModal = {quickLinkUid: quickLinkUid, error: null};
		this.manualImportQuickLink();
	}

	async manualImportQuickLink() {
		if(!this.showManualLinkModal) return;

		if(!UuidUtils.isScoped(this.showManualLinkModal.quickLinkUid, uuidScopeIot_quickLink)) {
			this.showManualLinkModal.error = 'L\'identifiant de l\'appareil est invalide';
			return;
		}

		const quickLinkUid = this.showManualLinkModal.quickLinkUid as ScopedUuid<UuidScopeIot_quickLink>;

		const response = await this.establishmentDeviceRepository.callContract(
			'importQuickLink',
			{establishmentUid: this.appState.requireUrlEstablishmentUid(), quickLinkUid: quickLinkUid},
			undefined
		);

		if(response.isSuccess()) {
			const quickLink = response.success().item;

			await DeviceImportExportHelper.createEstablishmentAccountForDevice(quickLink);

			await this.loadDevices();

			this.showManualLinkModal = null;

			this.appBus.emit('emit-toast', {
				title: 'Appareil ajouté',
				description: 'L\'appareil a bien été ajouté à votre établissement',
				duration: 3000,
				type: 'SUCCESS',
				closable: true
			});
		} else {
			this.appBus.emit('emit-toast', {
				title: 'L\'appareil n\'a pas pu être importé',
				description: translateResponseError<typeof IotEstablishmentDeviceV2HttpContract, 'importQuickLink'>(response, {}) ?? '',
				duration: 3000,
				type: 'ERROR',
				closable: true
			});
		}
	}

	requireDeviceForApp(app: EstablishmentDeviceAppV2ApiOut) {
		const device = this.devices.find((device) => device.uid === app.deviceUid);
		if(!device) throw new Error('missing_device');
		return device;
	}

	showDevicesImportModal() {
		this.showImportModal = true;
	}

	toggleScanModal() {
		this.showScanModal = true;
	}

	toggleManualModal() {
		this.showManualLinkModal = {quickLinkUid: '', error: null};
	}

	getCashlessDeviceWithUid(uid: ScopedUuid<UuidScopeIot_deviceApp>) {
		return this.cashlessDevices.find((cashlessDevice) => cashlessDevice.uid === UuidUtils.scopedToVisual(uid, uuidScopeIot_deviceApp)) || null;
	}

	// importedDevices(importedDevices: EstablishmentDeviceApiOut[]) {
	// 	for(let importedDevice of importedDevices) {
	// 		const index = this.devices.findIndex(device => device.uid === importedDevice.uid);
	// 		if (index !== -1) this.devices.splice(index, 1, importedDevice);
	// 		else this.devices.push(importedDevice);
	// 	}
	// }

	requireCashlessDeviceWithUid(uid: ScopedUuid<UuidScopeIot_deviceApp>) {
		const cashlessDevice = this.cashlessDevices.find((cashlessDevice) => cashlessDevice.uid === UuidUtils.scopedToVisual(uid, uuidScopeIot_deviceApp)) || null;
		if(!cashlessDevice) throw new Error('missing_device');
		return cashlessDevice;
	}

	getCashlessDeviceStateWithUid(uid: ScopedUuid<UuidScopeIot_deviceApp>){
		try{
			return this.requireCashlessDeviceWithUid(uid);
		}catch (e){
			return null;
		}
	}
}