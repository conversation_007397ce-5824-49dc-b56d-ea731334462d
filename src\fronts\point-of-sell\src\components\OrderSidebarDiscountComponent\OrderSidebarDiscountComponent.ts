import {Component, Prop, Vue} from "vue-facing-decorator";
import KeypadComponent from "../KeypadComponent/KeypadComponent.vue";
import {KeypadKey} from "../KeypadComponent/KeypadComponent";
import {LocalOrder} from "../../model/LocalOrder";
import {PosState} from "../../model/PosState";
import {PurchaseApiOut,PurchaseDiscountApiOut} from "@groupk/mastodon-core";
import {AutoWired} from "@groupk/horizon2-core";
import {LocalOrderRepository} from "../../repositories/LocalOrderRepository";
import {AppBus} from "../../config/AppBus";

@Component({
	components: {
		keypad: KeypadComponent
	},
	emits: ['close']
})
export default class OrderSidebarDiscountComponent extends Vue {
	@Prop() posState!: PosState;
	@Prop() localOrder!: LocalOrder;

	mode: 'AMOUNT'|'PERCENT' = 'AMOUNT';
	currentDiscountAmount: number = 0;
	selectedPurchases: PurchaseApiOut[] = [];
	showKeypad: boolean = false;

	@AutoWired(LocalOrderRepository) accessor localOrderRepository!: LocalOrderRepository;
	@AutoWired(AppBus) accessor appBus!: AppBus;

	togglePurchase(purchase: PurchaseApiOut) {
		const discount = this.havePurchaseAlreadyBeenRefund(purchase);
		if(discount) {
			this.removeDiscount(discount);
		} else {
			const index = this.selectedPurchases.findIndex((selectedPurchase) => selectedPurchase.uid === purchase.uid);
			if(index === -1) {
				this.selectedPurchases.push(purchase);
			} else {
				this.selectedPurchases.splice(index, 1);
			}
		}
	}

	isPurchaseSelected(purchase: PurchaseApiOut) {
		return this.selectedPurchases.findIndex((selectedPurchase) => selectedPurchase.uid === purchase.uid) !== -1;
	}

	havePurchaseAlreadyBeenRefund(purchase: PurchaseApiOut) {
		for(let discount of this.localOrder.order.discounts) {
			if(discount.rootPurchaseUids.includes(purchase.uid) && discount.cancelDatetime === null) return discount;
		}
		return null;
	}

	get selectedPurchasesTotalPrice() {
		if(!this.posState.currentOrder) return 0;
		return Math.min(
			this.posState.orderExecutorModel.getOrderTotals(this.posState.currentOrder.order).leftToPay,
			this.selectedPurchases.reduce((totalPriceWithTaxes, purchase) => {
				const computedPrice = this.posState.orderExecutorModel.getPurchasePrice(this.localOrder.order, purchase);
				if(!('withTaxes' in computedPrice)) throw new Error('cannot_handle_price_without_tax');
				return totalPriceWithTaxes + computedPrice.withTaxes;
			}, 0)
		);
	}

	updatePaymentAmount(key: KeypadKey) {
		if(!this.posState.currentOrder) return;

		if (key < 10) {
			if (this.currentDiscountAmount === 0) {
				this.currentDiscountAmount = key;
			} else {
				this.currentDiscountAmount = parseInt(this.currentDiscountAmount + '' + key);
			}
		} else {
			const leftToPay = this.selectedPurchasesTotalPrice;

			if (key === KeypadKey.MAX) {
				if(this.mode === 'AMOUNT') this.currentDiscountAmount = leftToPay;
				else this.currentDiscountAmount = 100;
			} else if (key === KeypadKey.QUARTER) {
				if(this.mode === 'AMOUNT') this.currentDiscountAmount = Math.round(leftToPay * 0.25);
				else this.currentDiscountAmount = 25;
			} else if (key === KeypadKey.HALF) {
				if(this.mode === 'AMOUNT') this.currentDiscountAmount = Math.round(leftToPay * 0.5);
				else this.currentDiscountAmount = 50;
			} else if (key === KeypadKey.BACKSPACE) {
				if ((this.currentDiscountAmount + "").length === 1) {
					this.currentDiscountAmount = 0;
				} else {
					this.currentDiscountAmount = parseInt((this.currentDiscountAmount + "").slice(0, -1));
				}
			} else if (key === KeypadKey.TRASH) {
				this.currentDiscountAmount = 0;
			} else if (key === KeypadKey.ZERO_ZERO) {
				this.updatePaymentAmount(KeypadKey.ZERO);
				this.updatePaymentAmount(KeypadKey.ZERO);
			}
		}
	}

	getPurchaseQuantity(purchase: PurchaseApiOut) {
		return purchase.items.reduce((quantity, item) => {
			const isItemCanceled = this.posState.orderExecutorModel.isPurchaseItemCanceled(item);
			return quantity + (isItemCanceled ? 0 : item.quantity);
		}, 0);
	}


	addDiscount() {
		try {
			let remise = this.currentDiscountAmount;
			if(this.mode === 'PERCENT') remise = Math.floor(this.selectedPurchasesTotalPrice * this.currentDiscountAmount / 100);
			this.posState.orderExecutorModel.addDiscount(this.localOrder.order, this.selectedPurchases, 'Remise', remise, false);
		} catch(err) {
			console.log(err);
		}
		this.localOrderRepository.saveAndResync(this.localOrder);
		this.showKeypad = false;
		this.currentDiscountAmount = 0;
		this.selectedPurchases = [];
		this.$emit('close');
	}

	removeDiscount(discount: PurchaseDiscountApiOut) {
		this.posState.orderExecutorModel.cancelDiscount(this.localOrder.order, discount);
		this.localOrderRepository.saveAndResync(this.localOrder);
	}

	getPurchaseTotals(purchase: PurchaseApiOut) {
		const totals = this.posState.orderExecutorModel.getPurchasePrice(this.localOrder.order, purchase);
		if(!('withTaxes' in totals)) throw new Error('invalid_pos_rounding');
		return totals;
	}

	changeMode() {
		if(this.mode === 'AMOUNT') this.mode = 'PERCENT';
		else this.mode = 'AMOUNT';
	}

	close() {
		this.$emit('close');
	}
}