# Forbidden-Message Permissions Implementation Guide

## Overview
The forbidden-message mechanic is a page-level permission control system that conditionally displays content based on user permissions. When a user lacks essential permissions for a page's core functionality, the entire page content is replaced with a standardized "forbidden access" message.

## Core Concept
**Principle**: "A page should be forbidden if the user cannot perform ALL of its core, essential functionality"

Unlike individual component permissions, forbidden-message checks for **multiple essential contracts** that are required for the page to function meaningfully.

## Implementation Steps

### 1. Add Required Imports

```typescript
import {
    ForbiddenMessageComponent,
    // ... other imports
} from "@groupk/vue3-interface-sdk";
import {
    ApplicationPermission,
    EstablishmentAccountPermissionModel,
    // ... specific contracts you need
} from "@groupk/mastodon-core";
import { ComponentUtils } from "path/to/ComponentUtils";
```

### 2. Add Forbidden State Property

```typescript
export default class YourPageView extends Vue {
    forbidden: boolean = false;
    // ... other properties
}
```

### 3. Register ForbiddenMessageComponent

```typescript
@Component({
    components: {
        'forbidden-message': ForbiddenMessageComponent,
        // ... other components
    }
})
```

### 4. Implement Permission Check in mounted()

```typescript
async mounted() {
    // Check for essential page functionality
    if(!ComponentUtils.hasPermissions((ownedPermissions: ApplicationPermission[]) => {
        return EstablishmentAccountPermissionModel.hasPermissionsForContracts(ownedPermissions, [
            // List ALL essential contracts for core page functionality
            ContractName.essentialOperation1,
            ContractName.essentialOperation2,
            AnotherContract.criticalOperation,
        ])
    })) {
        this.forbidden = true;
        // Optionally disable specific actions
        ComponentUtils.disableActions(this.headerParameters, ['export', 'other-action']);
    }
    
    // Other individual component permission checks...
    if(!ComponentUtils.hasPermissions(SomeComponentHasRequiredPermissions)) {
        ComponentUtils.disableActions(this.headerParameters, ['create']);
    }
}
```

### 5. Update Template with Conditional Rendering

```vue
<template>
    <div id="your-page" class="page">
        <forbidden-message v-if="forbidden"></forbidden-message>
        <main-page-content
            v-else
            <!-- All your page content goes here -->
        >
            <!-- Page content only renders when NOT forbidden -->
        </main-page-content>
    </div>
</template>
```

## Determining Essential Contracts

### Analysis Framework

1. **Identify Core Page Purpose**
   - What is the primary function of this page?
   - What data must be loaded for the page to be meaningful?

2. **Map Essential Operations**
   - List all contract calls that are absolutely necessary
   - Focus on data retrieval operations that populate the main content
   - Include operations needed for basic page navigation/functionality

3. **Exclude Non-Essential Operations**
   - Don't include contracts for optional features
   - Don't include contracts for actions that can be individually disabled
   - Don't include contracts for secondary/supplementary data

### Examples of Essential vs Non-Essential

#### Products Listing Page
```typescript
// ESSENTIAL - Page cannot function without these
ProductHttpProductContract.search,     // Core: load products list
ProductHttpProductContract.searchCount, // Core: pagination

// NON-ESSENTIAL - Can be individually disabled
ProductHttpProductContract.create,     // Optional: create new products
ProductHttpProductContract.update,     // Optional: edit products
ProductHttpProductContract.export,     // Optional: export functionality
```

#### Physical Sales Point Page
```typescript
// ESSENTIAL - All required for basic page functionality
ProductHttpProductContract.list,
ProductHttpPrintingLocationContract.list,
MastodonEstablishmentAccountContractAggregate.list,
ProductHttpCategoriesContract.list,
IotHttpEstablishmentDeviceContract.list,
PaymentHttpMethodContract.list,
ProductHttpPosProfileContract.list,
```

## Common Patterns

### Pattern 1: Simple Data Listing Page
```typescript
// For pages that primarily display a list of items
if(!ComponentUtils.hasPermissions((ownedPermissions: ApplicationPermission[]) => {
    return EstablishmentAccountPermissionModel.hasPermissionsForContracts(ownedPermissions, [
        MainDataContract.search,        // or .list
        MainDataContract.searchCount,   // if pagination is used
    ])
})) {
    this.forbidden = true;
}
```

### Pattern 2: Complex Management Page
```typescript
// For pages that manage multiple related entities
if(!ComponentUtils.hasPermissions((ownedPermissions: ApplicationPermission[]) => {
    return EstablishmentAccountPermissionModel.hasPermissionsForContracts(ownedPermissions, [
        PrimaryDataContract.list,
        RelatedDataContract.list,
        ConfigurationContract.list,
        // All contracts needed for basic page operation
    ])
})) {
    this.forbidden = true;
}
```

### Pattern 3: Dashboard/Overview Page
```typescript
// For pages that display overview information from multiple sources
if(!ComponentUtils.hasPermissions((ownedPermissions: ApplicationPermission[]) => {
    return EstablishmentAccountPermissionModel.hasPermissionsForContracts(ownedPermissions, [
        DataSource1Contract.list,
        DataSource2Contract.list,
        DataSource3Contract.list,
        // All data sources needed for meaningful dashboard
    ])
})) {
    this.forbidden = true;
}
```

## Best Practices

### 1. Comprehensive Contract Analysis
- Review ALL contract calls made during page initialization
- Include contracts called in `mounted()`, `created()`, and initial data loading
- Don't forget contracts called by child components that are essential to page function

### 2. User Experience Considerations
- Only set `forbidden = true` when the page would be genuinely unusable
- Prefer individual action disabling for optional features
- Ensure the forbidden message provides clear feedback

### 3. Testing Approach
- Test with users who have minimal permissions
- Verify that forbidden pages show the message instead of errors
- Confirm that users with proper permissions see full functionality

### 4. Documentation
- Document why specific contracts were chosen as essential
- Note any business logic that influenced the decision
- Keep the contract list updated when page functionality changes

## Common Mistakes to Avoid

1. **Including Too Many Contracts**: Don't include every possible contract - only truly essential ones
2. **Including Action Contracts**: Don't include create/update/delete contracts unless the page is primarily for those actions
3. **Forgetting Pagination**: Include searchCount contracts if the page uses pagination
4. **Missing Related Data**: Include contracts for data that child components absolutely need
5. **Wrong Template Structure**: Ensure `v-else` is used so content doesn't show when forbidden

## Integration with Existing Permission System

The forbidden-message mechanic works alongside the existing ComponentHasRequiredPermissions system:

- **forbidden-message**: Page-level access control for essential functionality
- **ComponentHasRequiredPermissions**: Component-level access control for specific features
- **Action disabling**: Individual action control within accessible pages

All three systems work together to provide granular permission control while maintaining good user experience.
