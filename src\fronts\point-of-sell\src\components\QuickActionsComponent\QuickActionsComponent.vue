<script lang="ts" src="./QuickActionsComponent.ts">
</script>

<style lang="sass" scoped>
  @import './QuickActionsComponent.scss'
</style>

<template>
	<div class="quick-actions-component" @click=close()>

        <div class="actions">
            <div class="action" :class="{disabled: posState.isClockedIn()}" @click.stop="clockIn()">
                <i class="fa-regular fa-arrow-right-to-arc"></i>
                Début travail
            </div>
            <div class="action" :class="{disabled: !posState.isClockedIn()}" @click.stop="clockOut()">
                <i class="fa-regular fa-arrow-right-from-arc"></i>
                Fin travail
            </div>
            <div class="action" @click="cashKeeper()">
                <img src="../../assets/img/cashkeeper.svg" />
                Cash Keepers
            </div>
            <div class="red action" @click="closeCashStatement()">
                <i class="fa-regular fa-triangle-exclamation"></i>
                Fermer le ticket Z
            </div>

            <div class="action mobile-only" @click="posState.forceProfileSwitch = true;">
                <i class="fa-regular fa-circle-user"></i>
                Changer de vendeur
            </div>

            <div class="action mobile-only" @click="posState.showQuickActions = true">
                <i class="fa-regular fa-cog"></i>
                Paramètres
            </div>
        </div>

	</div>
</template>