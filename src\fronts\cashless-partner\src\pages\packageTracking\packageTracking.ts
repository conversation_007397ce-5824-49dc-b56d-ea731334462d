import {Component, Vue} from "vue-facing-decorator";
import {AutoWired, TypedQuerySearch} from "@groupk/horizon2-core";
import {PostalPackageRepository} from "../../../../../shared/repositories/PostalPackageRepository";
import {AppState} from "../../../../../shared/AppState";
import SidebarStateListener from "../../../../../shared/utils/SidebarStateListener";
import {ContentHeaderParameters, FilterTableLayoutComponent, TableColumn} from "@groupk/vue3-interface-sdk";
import {PostalPackageApiOut} from "@groupk/mastodon-core";
import {TableColumnsRepository} from "../../../../../shared/repositories/TableColumnsRepository";

@Component({
    components: {
        'filter-table-layout': FilterTableLayoutComponent,
    }
})
export default class packageTracking extends Vue {

    packages: PostalPackageApiOut[] = [];
    selectedPackage: PostalPackageApiOut|null = null;
    loading: boolean = true;

    headerParameters: ContentHeaderParameters = {
        header: 'Colis',
        subtitle: 'Suivi de vos colis',
        actions: [],
        hideSearch: true,
        searchPlaceholder: 'Rechercher une campagne'
    }

    allowedFilters = { filters: [], sorts: [] };
    filters: TypedQuerySearch<{ filters: [], sorts: [] }> = {};

    tableKey = 'cashless-package-tracking';
    tableColumns: TableColumn[] = [{
        title: 'ID de tracking', name: 'trackingId', displayed: true, mobileHidden: true
    }, {
        title: 'Transporteur', name: 'provider', displayed: true, mobileHidden: false
    }, {
        title: 'Commentaire', name: 'comment', displayed: true, mobileHidden: true
    }];

    @AutoWired(PostalPackageRepository) private accessor postalPackageRepository!: PostalPackageRepository;
    @AutoWired(AppState) private accessor appState!: AppState;
    @AutoWired(SidebarStateListener) private accessor sidebarStateListener!: SidebarStateListener;
    @AutoWired(TableColumnsRepository) private accessor tableColumnsRepository!: TableColumnsRepository;

    async mounted() {
        this.sidebarStateListener.setHiddenSidebar(false);
        this.sidebarStateListener.setMinimizedSidebar(false);

        let savedPreferences = this.tableColumnsRepository.getColumnPreferences(this.tableKey, this.tableColumns);
        if(savedPreferences) this.tableColumns = savedPreferences;

        this.packages = (await this.postalPackageRepository.callContract('list', {establishmentUid: this.appState.requireUrlEstablishmentUid()}, undefined)).success().list;

        this.loading = false;
    }

    saveColumnPreferences(columns: TableColumn[]) {
        this.tableColumnsRepository.saveColumnsPreferences(this.tableKey, columns);
    }
}