import {Component, Prop, Ref, Vue} from "vue-facing-decorator";
import OrderSidebarComponent from "../OrderSidebarComponent/OrderSidebarComponent.vue";
import {PosProfile} from "../../model/PosProfile";
import {SearchUtils} from "../../class/SearchUtils";
import {PosState} from "../../model/PosState";
import {
	CategoryApiOut,
	DiningPurchaseItemStepApiOut,
	MastodonHttpImagesContract,
	MetadataApiOut_type,
	OrderApiOut,
	OrderExecutorModelPurchaseAddDescriptor,
	OrderExecutorModelPurchaseGroupItemAddDescriptor,
	ProductApiOut, uuidScopeProduct_diningTable, UuidScopeProduct_diningTable,
	UuidScopeProductProduct
} from "@groupk/mastodon-core";
import {AutoWired, buildHttpRoutePathWithArgs, ScopedUuid, UuidUtils, VisualScopedUuid} from "@groupk/horizon2-core";
import {LocalOrderRepository} from "../../repositories/LocalOrderRepository";
import {LocalOrder} from "../../model/LocalOrder";
import {AppBus} from "../../config/AppBus";
import {ModalComponent} from "@groupk/vue3-interface-sdk";
import GroupOptionWithCartComponent from "../GroupOptionsComponent/GroupOptionWithCartComponent.vue";
import {ColorUtils} from "../../class/ColorUtils";
import {
	UuidScopeProductProductGroup
} from "@groupk/mastodon-core";
import {LocalFavoriteStockRepository} from "../../repositories/LocalFavoriteStockRepository";
import MobileBottomCartComponent from "../MobileBottomCartComponent/MobileBottomCartComponent.vue";
import {MetadataUtils} from "../../../../../shared/utils/MetadataUtils";
import ProductRequiredMetadataListComponent
	from "../metadataComponents/ProductRequiredMetadataListComponent/ProductRequiredMetadataListComponent.vue";
import {AuthStateModel} from "../../../../../shared/AuthStateModel";
import {MainConfig} from "../../../../../shared/MainConfig";
import {ProductUtils} from "../../class/ProductUtils";
import {ImageManager} from "../../class/ImageManager";
import ReservitRoomTransferComponent from "../ReservitRoomTransferComponent/ReservitRoomTransferComponent.vue";

@Component({
	components: {
		'order-sidebar': OrderSidebarComponent,
		'group-option': GroupOptionWithCartComponent,
		'mobile-bottom-cart': MobileBottomCartComponent,
		'product-required-metadata-list': ProductRequiredMetadataListComponent,
		'modal': ModalComponent
	}
})
export default class SellComponent extends Vue {
	@Prop() posState!: PosState;
	@Prop() posProfile!: PosProfile;

	favoriteStockProductUids: VisualScopedUuid<UuidScopeProductProduct>[] = [];
	addingProductWithGroups: ProductApiOut|null = null;
	addingProductWithRequiredMetadata: { product: ProductApiOut, descriptor: OrderExecutorModelPurchaseAddDescriptor|null }|null = null;

	selectedCategory: CategoryApiOut|null = null;

	animateMobileCart: boolean = false;
	showMobileCart: boolean = false;

	searchValue: string = '';
	products: ProductApiOut[] = [];

	@Ref() searchInput!: HTMLInputElement;

	@AutoWired(LocalOrderRepository) accessor localOrderRepository!: LocalOrderRepository;
	@AutoWired(AuthStateModel) accessor authStateModel!: AuthStateModel;
	@AutoWired(AppBus) accessor appBus!: AppBus;
	@AutoWired(MainConfig) accessor mainConfig!: MainConfig;
	@AutoWired(LocalFavoriteStockRepository) accessor localFavoriteStockRepository!: LocalFavoriteStockRepository;
	@AutoWired(ImageManager) accessor imageManager!: ImageManager;

	private bluetoothDataListener : ((data : string)=>void)|undefined = undefined;

	mounted() {
		window.addEventListener('keypress', this.keyPressed);

		this.products = this.posState.products;

		if(this.posState.pointOfSale.categoriesUid.length > 0) {
			this.selectedCategory = this.posState.requireCategoryWithUid(this.posState.pointOfSale.categoriesUid[0]);
		}

		this.search();

		this.favoriteStockProductUids = this.localFavoriteStockRepository.get();

		this.bluetoothDataListener = this.appBus.on('bluetoothData', (data: string) => {
			// TODO: sometimes doesn't match ean13 when it should
			const matchingProduct = this.findProductWithEan13(data);
			if(matchingProduct) {
				this.addProductToOrder(matchingProduct);
			} else {
				this.searchValue = data;
				this.search();
			}
		}).listener;

		this.imageManager.initialize();
	}

	unmounted(){
		if(this.bluetoothDataListener) this.appBus.off('bluetoothData', this.bluetoothDataListener);
		window.removeEventListener('keypress', this.keyPressed);
		// Note: We don't clear ImageManager state here since it's global and should persist
	}

	private scannerData: string = '';
	private scannerInputActive: boolean = false;

	keyPressed(e: KeyboardEvent) {
		// if (e.key === 'Enter') {
		// 	if (this.scannerInputActive && this.scannerData.length > 0) {
		// 		this.searchValue = this.scannerData;
		// 		this.search();
		// 		this.scannerData = '';
		// 		this.scannerInputActive = false;
		// 	}
		// } else {
		// 	this.scannerInputActive = true;
		// 	this.scannerData += e.key;
		// }

		if(this.searchInput && !this.addingProductWithRequiredMetadata) this.searchInput.focus();
	}

	searchingTimeout: ReturnType<typeof setTimeout>|null = null;
	search(timeout: number = 0) {
		if(this.searchingTimeout) {
			clearTimeout(this.searchingTimeout);
			this.searchingTimeout = null;
		}

		this.searchingTimeout = setTimeout(() => {

			if(this.doesMatchTableUrl(this.searchValue)) {
				const tableUid: VisualScopedUuid<UuidScopeProduct_diningTable> = UuidUtils.scopedToVisual(this.doesMatchTableUrl(this.searchValue) as ScopedUuid<UuidScopeProduct_diningTable>, uuidScopeProduct_diningTable) ;

				const tableExists = this.posState.diningAreas.some(area =>
					area.tables.some(table => table.uid === tableUid)
				);

				if (!tableExists) {
					this.appBus.emit('displayToast', {
						title: 'Table introuvable',
						description: 'Cette table n\'existe pas dans l\'établissement',
						closable: true,
						duration: 3000,
						color: 'red'
					});
				} else {
					const currentOrder = this.getOrCreateCurrentOrder();
					currentOrder.diningExtra.tableUid = tableUid;
				}

				this.searchValue = '';
				this.search(0);
				this.submitSearch();
				return;
			}


			let searchingProducts = this.posState.products;

			const matchingProduct = this.findProductWithEan13(this.searchValue);
			if(matchingProduct) {
				this.addProductToOrder(matchingProduct);
				this.searchValue = '';
				this.search(0);
				this.submitSearch();
			} else {
				const matchingProducts = SearchUtils.searchInTab(searchingProducts, (product) => {
					return [product.lastRevision.name, product.lastRevision.ean13 ?? ''];
				}, this.searchValue);

				if(this.selectedCategory !== null) {
					this.products = matchingProducts.filter((matchingProduct) => {
						return this.selectedCategory!.productsConfig.find((productConfig) => productConfig.productUid === matchingProduct.uid) !== undefined;
					});
					this.products.sort((p1, p2) => {
						const p1Index = this.selectedCategory!.productsConfig.findIndex((productConfig) => productConfig.productUid === p1.uid);
						const p2Index = this.selectedCategory!.productsConfig.findIndex((productConfig) => productConfig.productUid === p2.uid);
						return p1Index > p2Index ? 1 : -1;
					});
				} else {
					this.products = matchingProducts;
				}
			}
		}, timeout);
	}

	getProductColorInSelectedCategory(productUid: VisualScopedUuid<UuidScopeProductProduct>) {
		if(!this.selectedCategory) return null;
		const productConfig = this.selectedCategory.productsConfig.find((config) => config.productUid === productUid);
		if(productConfig) return productConfig.color;
		return null;
	}

	isDark(productUid: VisualScopedUuid<UuidScopeProductProduct>) {
		const color = this.getProductColorInSelectedCategory(productUid);
		if(!color) return false;
		return ColorUtils.isDark(color);
	}

	selectCategory(category: CategoryApiOut) {
		this.selectedCategory = category;
		this.search(0);

		// Preload images for the newly selected category immediately
		this.imageManager.preloadCategoryImages(category, true);
	}

	submitSearch() {
		this.searchInput.blur();
	}

	getOrCreateCurrentOrder(): LocalOrder {
		if(!this.posState.currentOrder) {
			const order  = this.posState.orderExecutorModel.createOrder({
				toCountry: this.posState.toCountry,
				sellerEstablishmentAccountUid: this.posState.currentEstablishmentAccountUid,
				cashStatementSessionUid: this.posState.currentCashStatementSession.uid,
				roundPricesAtEnd: false
			});
			this.posState.currentOrder = new LocalOrder({
				uid: order.uid,
				order: order
			});
		}
		return this.posState.currentOrder;
	}

	async addProductToOrder(product: ProductApiOut, requiredMetadataList: MetadataApiOut_type[]|null = null) {
		this.addingProductWithRequiredMetadata = null;

		if(this.posState.currentOrder && this.posState.currentOrder.order.locked) {
			this.appBus.emit('displayToast', {
				title: 'La commande a été finalisée',
				description: 'Un reçu a été imprimé et la commande n\'est plus modifiable',
				closable: true,
				duration: 3000,
				color: 'red'
			})
			return;
		}

		if(this.posState.currentOrder && this.posState.currentOrder.order.transferredTo) {
			this.appBus.emit('displayToast', {
				title: 'La commande a été envoyée sur Reservit',
				description: 'La commande n\'est plus modifiable',
				closable: true,
				duration: 3000,
				color: 'red'
			})
			return;
		}

		if(this.posProfile.enableWorkClock && !this.posState.isClockedIn()) {
			this.appBus.emit('displayToast', {
				title: 'Vous n\'avez pas pointé',
				description: 'Pointez sur la caisse avant de commencer à vendre',
				closable: true,
				duration: 3000,
				color: 'red',
				action: {
					name: 'Actions',
					icon: 'fa-regular fa-ellipsis',
					callback: () => {
						this.posState.showQuickActions = true;
					}
				}
			})
			return;
		}

		const productRequiredMetadata = MetadataUtils.getRequiredMetadata(product, this.posState.metadataDescriptors);

		if(product.lastRevision.groups.length > 0) {
			if(ProductUtils.doesProductNeedManualConfiguration(product)) {
				this.addingProductWithGroups = product;
			} else {
				if(productRequiredMetadata.length > 0 && !requiredMetadataList) {
					this.addingProductWithRequiredMetadata = {product: product, descriptor: null}
				} else {
					const descriptor: OrderExecutorModelPurchaseAddDescriptor = {
						productUid: product.uid,
						quantity: 1,
						groups: this.getProductAutomaticDescriptor(product),
						dining: {
							step: this.getOrCreateCurrentOrder().currentStep
						},
						metadataList: requiredMetadataList ?? []
					}
					await this.addDescriptorToOrder(descriptor);
				}
			}
		} else if(productRequiredMetadata.length > 0 && !requiredMetadataList) {
			this.addingProductWithRequiredMetadata = {product: product, descriptor: null};
		} else {
			const localOrder = this.getOrCreateCurrentOrder();
			this.posState.orderExecutorModel.cancelAllContextDiscounts(localOrder.order);
			const purchase = this.posState.orderExecutorModel.createOrAddToRootPurchaseWithProduct(localOrder.order, product.uid, 1);
			this.addApplicableDiscountsToOrder(localOrder.order);

			const item = purchase.items[purchase.items.length - 1];
			if(
				localOrder.diningExtra.purchaseItemSteps.findIndex(
					(purchaseItemStep) => purchaseItemStep.step === localOrder.currentStep && purchaseItemStep.purchaseItemUid === item.uid
				) === -1
			) {
				localOrder.diningExtra.purchaseItemSteps.push(new DiningPurchaseItemStepApiOut({
					step: localOrder.currentStep,
					purchaseItemUid: item.uid
				}));
			}

			item.metadataList = requiredMetadataList ?? [];

			await this.localOrderRepository.saveAndResync(localOrder);
		}
	}

	async addDescriptorToOrder(descriptor: OrderExecutorModelPurchaseAddDescriptor, requiredMetadataList: MetadataApiOut_type[]|null = null) {
		this.addingProductWithRequiredMetadata = null;
		console.log(descriptor);

		const product = this.posState.orderExecutorModel.requireProduct(descriptor.productUid);
		const productRequiredMetadata = MetadataUtils.getRequiredMetadata(product, this.posState.metadataDescriptors);

		if(productRequiredMetadata.length > 0 && !requiredMetadataList) {
			this.addingProductWithGroups = null;
			this.addingProductWithRequiredMetadata = {product: product, descriptor: descriptor};
		} else {
			descriptor.metadataList = requiredMetadataList ?? [];
			const localOrder = this.getOrCreateCurrentOrder();
			this.posState.orderExecutorModel.cancelAllContextDiscounts(localOrder.order);
			this.posState.orderExecutorModel.createRootPurchaseOnOrderWithProduct(localOrder.order, descriptor);
			this.addApplicableDiscountsToOrder(localOrder.order);
			await this.localOrderRepository.saveAndResync(localOrder);
			this.addingProductWithGroups = null;
		}
	}

	addApplicableDiscountsToOrder(order: OrderApiOut) {
		const applicableDiscounts = this.posState.orderDiscountTemplateModel.computeApplicableDiscounts(order, this.posState.orderExecutorModel);
		for(const applicableDiscount of applicableDiscounts) {
			this.posState.orderExecutorModel.addDiscountFromObject(order, applicableDiscount);
		}
	}

	getProductQuantityInOrder(product: ProductApiOut) {
		if(!this.posState.currentOrder) return 0;
		return this.posState.currentOrder.order.purchases.reduce((quantity, purchase) => {
			if(purchase.productRevisionUid === product.lastRevision.uid) {
				return quantity + purchase.items.reduce((quantity, item) => {
					const isItemCanceled = this.posState.orderExecutorModel.isPurchaseItemCanceled(item);
					return isItemCanceled ? quantity : quantity + item.quantity;
				}, 0);
			} else {
				return quantity;
			}
		}, 0);
	}

	findProductWithEan13(ean13: string) {
		return this.posState.products.find((product) => product.lastRevision.ean13 == ean13) ?? null;
	}

	getProductImageUrl(product: ProductApiOut) {
		if(!product.mainImageUpload) return null;
		return this.mainConfig.configuration.mastodonApiEndpoint + buildHttpRoutePathWithArgs(MastodonHttpImagesContract.getPublicImage, {
			establishmentUid: this.posState.establishmentUid,
			imageId: product.mainImageUpload.uid,
			options: {
				quality: 100
			},
			dimensions: {width: 100},
			extension: 'png',
			resizeType: 'contain',
			revision: product.mainImageUpload.lastUpdateDatetime,
		});
	}

	getProductAutomaticDescriptor(product: ProductApiOut): {
		groupUid: VisualScopedUuid<UuidScopeProductProductGroup>,
		items: OrderExecutorModelPurchaseGroupItemAddDescriptor[],
	}[] {
		const groups: {
			groupUid: VisualScopedUuid<UuidScopeProductProductGroup>,
			items: OrderExecutorModelPurchaseGroupItemAddDescriptor[],
		}[] = [];

		for(let group of product.lastRevision.groups) {
			const items: OrderExecutorModelPurchaseGroupItemAddDescriptor[] = [];
			for(let item of group.items) {
				items.push({
					itemUid: item.uid,
					productUid: item.product.uid,
					quantity: item.minQuantity,
					groups: this.getProductAutomaticDescriptor(item.product)
				});
			}

			groups.push({
				groupUid: group.uid,
				items: items
			});
		}

		return groups;
	}

	get getPurchasesQuantity(){
		if(!this.posState.currentOrder) return 0;
		let quantity = 0;
		for(let purchase of this.posState.currentOrder.order.purchases) {
			for(let item of purchase.items) {
				quantity += item.quantity;
			}
		}
		return quantity;
	}

	toggleMobileCart() {
		this.animateMobileCart = true;
		setTimeout(() => {
			this.showMobileCart = true;
		}, 150)
	}

	closeMobileCart() {
		this.showMobileCart = false;
		this.animateMobileCart = false;
	}

	doesMatchTableUrl(url: string): string | null {
		const pattern = /^https:\/\/littl\.fr\/pos-table\?table=([0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12})$/;
		const match = url.match(pattern);
		return match ? match[1] : null;
	}
}