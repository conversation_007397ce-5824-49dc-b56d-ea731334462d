import {ContentHeaderParameters} from "@groupk/vue3-interface-sdk";
import {GetInstance} from "@groupk/horizon2-core";
import {AppState} from "../AppState";
import {ApplicationPermission} from "@groupk/mastodon-core";

export class ComponentUtils {
    public static disableActions(headerParameters: ContentHeaderParameters, actionIds: string[]): void {
        actionIds.forEach(id => {
            const index = headerParameters.actions.findIndex(action => action.id === id);
            if (index !== -1) {
                headerParameters.actions[index].disabled = true;
            }
        });
    }

    public static hasPermissions(permissionsChecker: (ownedPermissions: ApplicationPermission[]) => boolean) {
        const appState = GetInstance(AppState);

        if (!appState.connectedEstablishmentAccount) return false;

        const permissions = appState.connectedEstablishmentAccount.permissions.map(permission => ({
            applicationId: permission.application,
            id: permission.name
        } satisfies ApplicationPermission));

        return permissionsChecker(permissions);
    }
}