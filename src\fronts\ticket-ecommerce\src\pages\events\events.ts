import {Component, Vue} from "vue-facing-decorator";
import {AutoWired, buildHttpRoutePathWithArgs, ScopedUuid, UuidUtils} from "@groupk/horizon2-core";
import {EventRepository} from "../../../../../shared/repositories/EventRepository";
import {
    MastodonHttpImagesContract,
    uuidScopeEstablishment,
    UuidScopeEstablishment
} from "@groupk/mastodon-core";
import {PublicEstablishmentApiOut} from "@groupk/mastodon-core";
import {Router} from "@groupk/horizon2-front";
import {EstablishmentRepository} from "../../../../../shared/repositories/EstablishmentRepository";
import {MainConfig} from "../../../../../shared/MainConfig";
import {PublicEventResumeApiOut} from "@groupk/mastodon-core";

@Component({
    components: {}
})
export default class eventPage extends Vue {
    establishment!: PublicEstablishmentApiOut;
    events: PublicEventResumeApiOut[] = [];

    loading: boolean = true;

    @AutoWired(EventRepository) accessor eventRepository!: EventRepository;
    @AutoWired(EstablishmentRepository) accessor establishmentRepository!: EstablishmentRepository;
    @AutoWired(Router) accessor router!: Router;
    @AutoWired(MainConfig) accessor mainConfig!: MainConfig;

    async mounted() {
        if(this.router.lastRouteRegexMatches && this.router.lastRouteRegexMatches[1]) {
            const establishmentUid = UuidUtils.scopedToVisual(this.router.lastRouteRegexMatches[1] as ScopedUuid<UuidScopeEstablishment>, uuidScopeEstablishment);

            this.establishment = (await this.establishmentRepository.callContract('getPublic', {establishmentUid: establishmentUid}, undefined)).success()
            this.events = (await this.eventRepository.callContract('listPublic', {establishmentUid: establishmentUid}, undefined)).success().list;

            this.loading = false;
        }
    }
    
    private getEventFullStartDate(event: PublicEventResumeApiOut): Date {
        if (!event.startingDate) {
            return new Date(0); // Default date for events without a starting date
        }
        
        const eventDate = new Date(event.startingDate);
        
        // If there's a time component, add it to the date
        if (event.startingTime) {
            const timeParts = event.startingTime.split(':');
            if (timeParts.length >= 2) {
                eventDate.setHours(parseInt(timeParts[0], 10));
                eventDate.setMinutes(parseInt(timeParts[1], 10));
                // If seconds are included
                if (timeParts.length >= 3) {
                    eventDate.setSeconds(parseInt(timeParts[2], 10));
                }
            }
        }
        
        return eventDate;
    }

    private getEventFullEndDate(event: PublicEventResumeApiOut): Date | null {
        if (!event.endingDate) {
            return null;
        }

        const eventDate = new Date(event.endingDate);

        // If there's a time component, add it to the date
        if (event.endingTime) {
            const timeParts = event.endingTime.split(':');
            if (timeParts.length >= 2) {
                eventDate.setHours(parseInt(timeParts[0], 10));
                eventDate.setMinutes(parseInt(timeParts[1], 10));
                // If seconds are included
                if (timeParts.length >= 3) {
                    eventDate.setSeconds(parseInt(timeParts[2], 10));
                }
            }
        }

        return eventDate;
    }

    get filteredEvents() {
        const now = new Date();
        return [...this.events]
            .filter(event => {
                // Si l'événement a une date de fin, vérifier si celle-ci est passée
                const endDate = this.getEventFullEndDate(event);
                if (endDate) {
                    return endDate.getTime() >= now.getTime();
                }
                
                // Si l'événement n'a pas de date de fin mais a une date de début,
                // considérer qu'il est passé si sa date de début est antérieure à maintenant - 24h
                // (on garde l'événement si date de début > now - 24h)
                if (event.startingDate) {
                    const startDate = this.getEventFullStartDate(event);
                    return startDate.getTime() > (now.getTime() - 24 * 60 * 60 * 1000);
                }
                
                // Si l'événement n'a ni date de début ni date de fin, le considérer comme à venir
                return true;
            })
            .sort((a, b) => {
                if (!a.startingDate) return -1; // Les événements sans date apparaissent en premier
                if (!b.startingDate) return 1;
                return this.getEventFullStartDate(b).getTime() - this.getEventFullStartDate(a).getTime();
            });
    }

    getEventUrl(event: PublicEventResumeApiOut) {
        return window.location.origin + '/establishment/' + UuidUtils.visualToScoped(this.establishment.uid) + '/event/' + UuidUtils.visualToScoped(event.uid);
    }

    getEventBannerUrl(event: PublicEventResumeApiOut) {
        if(!event.mainImageUpload) return '';
        return this.mainConfig.configuration.mastodonApiEndpoint + buildHttpRoutePathWithArgs(MastodonHttpImagesContract.getPublicImage, {
            establishmentUid: this.establishment.uid,
            imageId: event.mainImageUpload.uid,
            options: {},
            dimensions: {width: 800},
            extension: 'png',
            resizeType: 'contain',
            revision: event.mainImageUpload.lastUpdateDatetime,
        });
    }
}