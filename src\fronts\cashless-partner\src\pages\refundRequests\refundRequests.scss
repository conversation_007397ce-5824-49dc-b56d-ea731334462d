#refund-requests-page {
    box-sizing: border-box;
    height: 100%;

    .selected-refund {
        padding: 40px;

        @media (max-width: 900px) {
            padding: 20px;
        }

        .content {
            display: flex;
            flex-direction: column;
            gap: 20px;

            .title{
                display: flex;
                flex-direction: column;
                .subtitle{
                    font-size: 10px;
                    font-weight: normal;
                }
            }
        }
    }

    .top-buttons {
        display: flex;
        gap: 10px;
        margin-bottom: 10px;
    }

    .data-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;

        .column {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;

            .value {
                font-size: 15px;
                font-weight: 500;
            }

            .name {
                font-size: 13px;
            }
        }
    }

    .page-content {
        display: flex;
        gap: 20px;
        box-sizing: border-box;

        .fa-phone {
            font-size: 10px;
            color: #555555;
        }
    }

    .customer {
        background: var(--secondary-hover-color);
        padding: 15px;
        border-radius: 8px;

        .ticket-data {
            .ticket-icon {
                background: #D9D9D9;
            }
        }
    }

    .ticket-data {
        display: flex;
        align-items: center;
        gap: 15px;

        &.no-padding {
            padding-left: 0;
        }

        .ticket-icon {
            flex-shrink: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            background: var(--secondary-hover-color);
            font-size: 12px;
            font-weight: 600;
            border-radius: 50%;
            height: 30px;
            width: 30px;
            text-transform: uppercase;
        }

        .ticket-name {
            display: flex;
            flex-direction: column;
            gap: 4px;
            font-weight: 500;
            text-transform: capitalize;

            .ticket-description {
                font-size: 13px;
                color: #494949;
                font-weight: 400;
                text-transform: lowercase;
            }
        }
    }

    .table-scroll-wrapper {
        overflow: auto;

        .no-break {
            white-space: nowrap;
        }

        &.no-margin {
            margin-right: -40px;
            padding-right: 40px;
        }
    }
}