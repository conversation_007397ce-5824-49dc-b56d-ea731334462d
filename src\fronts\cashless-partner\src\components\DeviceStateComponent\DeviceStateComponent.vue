<script lang="ts" src="./DeviceStateComponent.ts">
</script>

<style lang="sass">
@use './DeviceStateComponent.scss' as *
</style>

<template>
    <div class="device-state-component">
        <form-modal-or-drawer
            :state="opened"
            title="État de l'appareil"
            subtitle="Informations supplémentaires relatives à l'appareil Cashless"
            @close="close()"
        >
            <template v-slot:content>
                <div class="states">
                    <div class="state">
                        Niveau de batterie : {{ deviceState.batteryLevel }}%
                    </div>
                    <div class="state">
                        Dernière transaction réseau : {{ deviceState.lastNetworkDatetime ? $filters.Date(deviceState.lastNetworkDatetime) : 'Aucune' }}
                    </div>
                    <div class="state">
                        Point de vente manuel : {{ deviceState.manualProfile ? 'Oui' : 'Non' }}
                    </div>
                    <div class="state">
                       Dernière mise à jour : {{ $filters.Date(deviceState.lastUpdateDatetime) }} à {{ $filters.Hour(deviceState.lastUpdateDatetime) }}
                    </div>
                    <div class="state">
                        SSID: {{ deviceState.ssidName ? deviceState.ssidName : 'Aucun' }}
                    </div>
                    <div class="state">
                        Point de vente : {{ deviceState.profileUid ? deviceState.profileUid : 'Aucun' }}
                    </div>
                    <div class="state">
                        Version de l'app : {{ deviceState.applicationVersionCode }}
                    </div>
                </div>

            </template>

            <template v-slot:buttons>
                <button class="white button" @click="close()"> Fermer </button>
            </template>
        </form-modal-or-drawer>
    </div>
</template>