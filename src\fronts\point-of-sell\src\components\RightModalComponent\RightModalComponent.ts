import {Component, Prop, Vue} from "vue-facing-decorator";
import {PosProfile} from "../../model/PosProfile";
import {AutoWired} from "@groupk/horizon2-core";

@Component({
	emits: ['bottom-button-clicked']
})
export default class RightModalComponent extends Vue {
	@Prop() bottomButtonText!: string;
	@Prop({default: false}) stacked!: boolean;
	@Prop({default: false}) bottomButtonLoading!: boolean;
	@Prop({default: null}) error!: string|null;

	opened: boolean = false;

	@AutoWired(PosProfile) accessor posProfile!: PosProfile

	mounted() {
		setTimeout(() => this.opened = true, 0);
	}

	close() {
		this.opened = false;
		setTimeout(() => this.$emit('close'), 300);
	}

	bottomButtonClicked() {
		this.$emit('bottom-button-clicked')
	}
}