<script lang="ts" src="./pricing.ts">
</script>

<style scoped lang="sass">
@use './pricing.scss' as *
</style>

<template>
    <div id="pricing-page">
        <button class="button" :class="{loading: enabling, disabled: enabling}" @click="enableCashless()">
            <i class="fa-solid fa-sparkles"></i>
            Activer Cashless
            <i class="fa-solid fa-sparkles fa-rotate-180"></i>
        </button>
    </div>
</template>