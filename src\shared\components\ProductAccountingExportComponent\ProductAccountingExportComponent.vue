<script lang="ts" src="./ProductAccountingExportComponent.ts" />

<style lang="sass" scoped>
@import './ProductAccountingExportComponent.scss'
</style>

<template>
    <div class="product-accounting-export-component">
        <form-modal-or-drawer
            :state="true"
            title="Export par compte de classe"
            subtitle="Exportez les totaux des commandes par compte de classe"
            @close="close()"
        >
            <template v-slot:content>
                <div class="shortcuts">
                    <button class="small white button" @click="setExportDates('today')"> Aujourd'hui </button>
                    <button class="small white button" @click="setExportDates('this-month')"> Ce mois </button>
                    <button class="small white button" @click="setExportDates('last-month')"> Le mois dernier </button>
                </div>

                <div class="two-inputs">
                    <div class="input-group">
                        <label> Date de début </label>

                        <div class="fluid input">
                            <input class="fluid" placeholder="jj/mm/aaaa" :value="exportDates.start" v-cleave="{obj: exportDates, key: 'start', options: cleaveDate}" />
                        </div>
                    </div>

                    <div class="input-group">
                        <label> Date de fin </label>

                        <div class="fluid input">
                            <input class="fluid" placeholder="jj/mm/aaaa" :value="exportDates.end" v-cleave="{obj: exportDates, key: 'end', options: cleaveDate}" />
                        </div>
                    </div>
                </div>

                <div class="toggle-input">
                    <toggle :default-toggled-value="bySession" @toggled="bySession = $event"></toggle>
                    <div class="infos">
                        <span class="subtitle"> Par session </span>
                    </div>
                </div>


                <div class="input-group">
                    <label> Format </label>

                    <div class="input">
                        <dropdown
                            placeholder="Sélectionner un format de fichier"
                            :values="getFormatDropdownValues()"
                            @update="selectedFormat = $event"
                            :default-selected="selectedFormat"
                        ></dropdown>
                    </div>
                </div>

                <div class="form-error" v-if="error">
                    <i class="fa-solid fa-exclamation-circle"></i>
                    <div class="details">
                        <span class="title"> Erreur </span>
                        <span class="description">{{ error }}</span>
                    </div>
                </div>
            </template>

            <template v-slot:buttons>
                <button type="button" class="white button" @click="close()">
                    Annuler
                </button>
                <button
                    type="button"
                    class="button"
                    @click="exportStats()"
                    :class="{loading: downloading, disabled: downloading}"
                >
                    <i class="fa-regular fa-arrow-down-to-line"></i>
                    Exporter
                </button>
            </template>
        </form-modal-or-drawer>
    </div>
</template>