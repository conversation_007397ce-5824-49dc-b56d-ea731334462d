import {Component, Prop, Vue} from "vue-facing-decorator";
import KeypadComponent from "../KeypadComponent/KeypadComponent.vue";
import {KeypadKey} from "../KeypadComponent/KeypadComponent";
import {PosProfile} from "../../model/PosProfile";
import {PosState} from "../../model/PosState";
import {OptionBuilder} from "vue-facing-decorator/dist/optionBuilder";
import {
	AutoWired,
	buildHttpRoutePathWithArgs,
	UuidUtils,
	VisualScopedUuid
} from "@groupk/horizon2-core";
import {LocalOrderRepository} from "../../repositories/LocalOrderRepository";
import {
	MastodonHttpImagesContract,
	MetadataBoolApiOut,
	MetadataDescriptorApiOut, MetadataStringListApiOut,
	OrderPaymentStatus,
	OrderPurchaseStatusApiOut,
	PurchaseApiOut,
	PurchaseItemApiOut,
	UuidScopeMastodon_upload,
	uuidScopeMetadata_descriptor,
	UuidScopeMetadata_descriptor,
	uuidScopeMetadata_metadata, UuidScopeProductProduct, uuidScopeProductPurchaseStatus
} from "@groupk/mastodon-core";
import {PrinterRepository} from "../../repositories/PrinterRepository";
import {AppBus} from "../../config/AppBus";
import OrderSidebarDiscountComponent from "../OrderSidebarDiscountComponent/OrderSidebarDiscountComponent.vue";
import OrderSidebarPaymentsComponent from "../OrderSidebarPaymentsComponent/OrderSidebarPaymentsComponent.vue";
import {ErrorHandler} from "../../class/ErrorHandler";
import {PaymentMethodApiOut} from "@groupk/mastodon-core";
import PaymentComponent from "../PaymentComponent/PaymentComponent.vue";
import {PaymentManager, PendingPayment} from "../../class/payment/PaymentManager";
import {LocalOrder} from "../../model/LocalOrder";
import {PrinterConfig} from "../../printApiSdk/PrinterHelper";
import RightModalComponent from "../RightModalComponent/RightModalComponent.vue";
import CustomersListComponent from "../CustomersListComponent/CustomersListComponent.vue";
import {
	DropdownButtonAction,
	DropdownButtonComponent,
	DropdownComponent,
	DropdownValue
} from "@groupk/vue3-interface-sdk";
import OrderAdvancedPaymentInterfaceComponent
	from "../OrderAdvancedPaymentInterfaceComponent/OrderAdvancedPaymentInterfaceComponent.vue";
import TextModalComponent from "../TextModalComponent/TextModalComponent.vue";
import MetadataDescriptorFormComponent
	from "../metadataComponents/MetadataDescriptorFormComponent/MetadataDescriptorFormComponent.vue";
import {
	MetadataApiOut_type,
	MetadataDescriptorType, MetadataStringApiOut
} from "@groupk/mastodon-core";
import {UuidScopeProductPurchaseStatus} from "@groupk/mastodon-core";
import {PurchaseStatusModel} from "@groupk/mastodon-core";
import {MainConfig} from "../../../../../shared/MainConfig";
import OrderUtils from "../../class/OrderUtils";
import {LocalSettingsRepository} from "../../repositories/LocalSettingsRepository";
import {DiningPurchaseItemStepApiOut} from "@groupk/mastodon-core";
import {MetadataTargetSpecific} from "@groupk/mastodon-core";
import {MetadataModel} from "@groupk/mastodon-core";
import {ProductUtils} from "../../class/ProductUtils";

@Component({
	components: {
		keypad: KeypadComponent,
		'order-sidebar-discount': OrderSidebarDiscountComponent,
		'order-sidebar-payments': OrderSidebarPaymentsComponent,
		'payment': PaymentComponent,
		'right-modal': RightModalComponent,
		'customers-list': CustomersListComponent,
		'dropdown': DropdownComponent,
		'dropdown-button': DropdownButtonComponent,
		'text-modal': TextModalComponent,
		'order-advanced-payment-interface': OrderAdvancedPaymentInterfaceComponent,
		'metadata-descriptor-form': MetadataDescriptorFormComponent
	},
	emits: ['close']
})
export default class OrderSidebarComponent extends Vue {
	@Prop() posState!: PosState;
	@Prop() posProfile!: PosProfile;

	selectedPaymentMethod: PaymentMethodApiOut|null = null;
	currentPaymentAmount: number = 0;
	showDiscountManager: boolean = false;
	showPayments: boolean = false;
	showKeypad: boolean = false;
	showCustomerBind: boolean = false;
	selectMultipleTickets: boolean = false;
	showMetadataDescriptorModal: { purchaseItem: PurchaseItemApiOut, descriptor: MetadataDescriptorApiOut, currentValue: any|null }|null = null;
	showAdvancedPaymentInterface: boolean = false;
	generatedTickets: boolean = false;

	pendingPaymentData: PendingPayment|null = null;

	OrderUtils = OrderUtils;
	ProductUtils = ProductUtils;

	@AutoWired(PrinterRepository) accessor printerRepository!: PrinterRepository
	@AutoWired(LocalOrderRepository) accessor localOrderRepository!: LocalOrderRepository
	@AutoWired(LocalSettingsRepository) accessor localSettingsRepository!: LocalSettingsRepository
	@AutoWired(ErrorHandler) accessor errorHandler!: ErrorHandler
	@AutoWired(AppBus) accessor appBus!: AppBus
	@AutoWired(MainConfig) accessor mainConfig!: MainConfig

	constructor(optionBuilder: OptionBuilder, vueInstance: any) {
		super(optionBuilder, vueInstance);
	}

	get getOrderTotals(){
		if(!this.posState.currentOrder) throw new Error('no_current_order');
		return this.posState.orderExecutorModel.getOrderTotals(this.posState.currentOrder.order);
	}

	initiatePayment() {
		this.selectedPaymentMethod = this.posState.requirePaymentMethodWithUid(this.posState.pointOfSale.paymentMethodsUid[0]);
		this.showKeypad = true;
	}

	updatePaymentAmount(key: KeypadKey) {
		if(!this.posState.currentOrder) return;

		if (key < 10) {
			if (this.currentPaymentAmount === 0) {
				this.currentPaymentAmount = key;
			} else {
				this.currentPaymentAmount = parseInt(this.currentPaymentAmount + '' + key);
			}
		} else {
			const leftToPay = this.posState.orderExecutorModel.getOrderTotals(this.posState.currentOrder.order).leftToPay;

			if (key === KeypadKey.MAX) {
					this.currentPaymentAmount = leftToPay;
			} else if (key === KeypadKey.QUARTER) {
				this.currentPaymentAmount = Math.round(leftToPay * 0.25);
			} else if (key === KeypadKey.HALF) {
				this.currentPaymentAmount = Math.round(leftToPay * 0.5);
			} else if (key === KeypadKey.BACKSPACE) {
				if ((this.currentPaymentAmount + "").length === 1) {
					this.currentPaymentAmount = 0;
				} else {
					this.currentPaymentAmount = parseInt((this.currentPaymentAmount + "").slice(0, -1));
				}
			} else if (key === KeypadKey.TRASH) {
				this.currentPaymentAmount = 0;
			} else if (key === KeypadKey.ZERO_ZERO) {
				this.updatePaymentAmount(KeypadKey.ZERO);
				this.updatePaymentAmount(KeypadKey.ZERO);
			}
		}
	}

	getCancelStatus(): OrderPurchaseStatusApiOut {
		const cancelStatus = this.posState.purchaseStatuses.find((status) => status.cancel === true);
		if(!cancelStatus) {
			this.errorHandler.logToSentry({
				title: 'Missing cancel status',
				description: 'POS should have been blocked since no cancel status have been found but user tried to cancel a purchase'
			});
			throw new Error('missing_cancel_status');
		}
		return cancelStatus;
	}

	async removeOneFromPurchase(purchase: PurchaseApiOut) {
		if(!this.posState.currentOrder) throw new Error('no_current_order');

		const cancelStatus = this.getCancelStatus();
		if(!this.posState.orderExecutorModel.isPurchaseItemCancelable(this.posState.currentOrder.order, purchase, cancelStatus.uid)) {
			this.errorHandler.logToToastManager({
				title: 'Produit non supprimable',
				description: 'Ce produit a déjà en parti été payé ou remisé, annulez le paiement ou la remise avant de le supprimer'
			});
			return;
		}

		this.posState.orderExecutorModel.cancelOnePurchaseItem(this.posState.currentOrder.order, purchase, cancelStatus.uid);
		await this.localOrderRepository.saveAndResync(this.posState.currentOrder);
	}

	async removePurchaseItem(purchase: PurchaseApiOut, purchaseItem: PurchaseItemApiOut) {
		if(!this.posState.currentOrder) throw new Error('no_current_order');

		const cancelStatus = this.getCancelStatus();
		if(!this.posState.orderExecutorModel.isSpecificPurchaseItemCancelable(this.posState.currentOrder.order, purchase, purchaseItem, cancelStatus.uid)) {
			this.errorHandler.logToToastManager({
				title: 'Produit non supprimable',
				description: 'Ce produit a déjà en parti été payé ou remisé, annulez le paiement ou la remise avant de le supprimer'
			});
			return;
		}

		this.posState.orderExecutorModel.cancelOneSpecificPurchaseItem(this.posState.currentOrder.order, purchase, purchaseItem, cancelStatus.uid);
		await this.localOrderRepository.saveAndResync(this.posState.currentOrder);
	}

	async cancelAllOrder(){
		if(!this.posState.currentOrder) throw new Error('no_current_order');

		if(this.posState.currentOrder.order.payments.filter((payment) => payment.status !== OrderPaymentStatus.ERROR).length > 0) {
			this.errorHandler.logToToastManager({
				title: 'Impossible de vider la commande',
				description: 'Des paiements on déjà été effectués, veuillez les annuler avant de vider la commande'
			});
			return;
		}

		const cancelStatus = this.getCancelStatus();
		this.posState.orderExecutorModel.cancelAllOrder(this.posState.currentOrder.order, cancelStatus.uid);
		await this.localOrderRepository.saveAndResync(this.posState.currentOrder);
	}

	async quickPay(paymentMethod: PaymentMethodApiOut) {
		this.selectedPaymentMethod = paymentMethod;
		this.currentPaymentAmount = this.getOrderTotals.leftToPay;
		await this.pay();
	}

	get willPaymentBeAutomatic() {
		if(!this.selectedPaymentMethod) return false;
		const paymentManager = new PaymentManager(this.posState);
		return paymentManager.willPaymentBeAutomatic(this.selectedPaymentMethod);
	}

	async pay() {
		if(!this.posState.currentOrder) return;
		if(!this.selectedPaymentMethod) return;

		const paymentManager = new PaymentManager(this.posState);
		const data = await paymentManager.addPaymentToCurrentOrder(this.currentPaymentAmount, this.selectedPaymentMethod);
		if(data.payment.status === OrderPaymentStatus.SUCCESS) {
			this.closePaymentModal();
		} else {
			this.pendingPaymentData = data;
		}

		this.showKeypad = false;
	}

	isCancelable(purchase: PurchaseApiOut) {
		if(!this.posState.currentOrder) throw new Error('no_current_order');
		const discountsWithPurchase = this.posState.currentOrder.order.discounts.filter((discount) => discount.rootPurchaseUids.includes(purchase.uid))
		if(discountsWithPurchase.length > 0) return false;
		const orderLeftToPay = this.posState.orderExecutorModel.getOrderTotals(this.posState.currentOrder.order).leftToPay;
		const purchasePriceWithTax = this.posState.getPurchaseTotals(purchase).withTaxes;
		return purchasePriceWithTax <= orderLeftToPay;
	}

	get orderTotalDiscounts() {
		return this.getOrderTotals.purchases.discountDetails.reduce((sum, detail) => {
			if(!('amountWithTaxes' in detail)) throw new Error('invalid_pos_rounding');
			return sum + detail.amountWithTaxes
		}, 0);
	}

	closePaymentModal() {
		if(!this.posState.currentOrder) throw new Error('no_current_order');
		this.currentPaymentAmount = 0;
		const totals = this.posState.orderExecutorModel.getOrderTotals(this.posState.currentOrder.order);
		if(totals.leftToPay === 0) {
			for(let purchase of this.posState.currentOrder.order.purchases) {
				if(this.posState.orderExecutorModel.requireProductRevision(purchase.productRevisionUid).templateTickets.length > 0) {
					this.generatedTickets = true;
					return;
				}
			}

			let autoPrint: boolean = false;
			const printer = this.printerRepository.getReceiptPrinter();
			if(printer) {
				const customPrinterConfig: PrinterConfig|null = this.printerRepository.getCustomConfigForPrinter(printer.id);
				if(customPrinterConfig && customPrinterConfig.autoPrint) {
					autoPrint = true;
					OrderUtils.printReceipt(this.posState.currentOrder);
				}
			}
			if(!autoPrint) {
				const paidOrder = this.posState.currentOrder;
				this.appBus.emit('displayToast', {
					title: 'Commande payée',
					description: 'Vous pouvez imprimer le reçu',
					color: 'green',
					duration: 3000,
					closable: true,
					action: [{
						name: 'Imprimer',
						icon: 'fa-regular fa-print',
						callback: () => OrderUtils.printReceipt(this.posState.currentOrder!)
					}, {
						name: 'Envoyer',
						icon: 'fa-regular fa-envelope',
						callback: () => OrderUtils.printReceipt(this.posState.currentOrder!, 'SEND')
					}]
				});
			}

			const localSettings = this.localSettingsRepository.get();
			if(localSettings.autoSendToKitchenAfterPayment) {
				const sendToKitchenStatus = this.posState.purchaseStatuses.find((status) => status.system === false);
				if(sendToKitchenStatus) {
					OrderUtils.addPurchaseStatus(sendToKitchenStatus.uid, null)
				}
			}

			if(localSettings.autoCloseOrderAfterPayment) {
				this.close();
				this.posState.currentOrder = null;
			}
		}
		this.pendingPaymentData = null;
	}

	getTableDropdownValues() {
		let values: DropdownValue[] = [];
		for(const area of this.posState.diningAreas) {
			values = values.concat(area.tables.map((table) => {
				return {
					name: table.name,
					value: table.uid
				}
			}))
		}
		return values;
	}

	getGuestCountDropdownValues() {
		let values: DropdownValue[] = [];
		for(let i = 1; i <= 20; i++) {
			values.push({
				name: i+'',
				value: i
			})
		}
		return values;
	}

	getPurchaseItemDescriptorDropdownValues(purchase: PurchaseApiOut, item: PurchaseItemApiOut) {
		const dropdownValues: DropdownButtonAction[] = [];

		for(let purchaseStatus of this.posState.purchaseStatuses) {
			if(
				!purchaseStatus.cancel &&
				!purchaseStatus.system &&
				PurchaseStatusModel.canExecuteAfter(
					this.posState.pointOfSaleConfiguration.executionFlow,
					this.posState.orderExecutorModel.requireStatus(purchaseStatus.uid),
					this.posState.orderExecutorModel.requireStatus(item.statusUpdates[item.statusUpdates.length - 1].statusUid),
					item.statusUpdates[item.statusUpdates.length - 1].status,
				)
			) {
				dropdownValues.push({
					icon: 'fa-regular fa-send',
					name: purchaseStatus.name,
					id: purchaseStatus.uid
				});
			}
		}

		const purchaseProduct = this.posState.orderExecutorModel.requireProductWithRevision(purchase.productRevisionUid);

		const productMetadataList = MetadataModel.applicableDescriptorOnTarget(this.posState.metadataDescriptors, [
			{uid: MetadataTargetSpecific.ORDER_PURCHASE_ITEM, isChildren: true},
			{uid: UuidUtils.visualToScoped<UuidScopeProductProduct>(purchaseProduct.uid), isChildren: true}
		]);
		for(const productMetadata of productMetadataList) {
			dropdownValues.push({
				icon: 'fa-regular fa-plus',
				name: productMetadata.descriptorTemplate.name,
				id: productMetadata.uid
			});
		}

		dropdownValues.push({
			icon: 'fa-regular fa-arrow-up',
			name: 'Etape précédente',
			id: 'previous-step'
		});

		dropdownValues.push({
			icon: 'fa-regular fa-arrow-down',
			name: 'Etape suivante',
			id: 'next-step'
		});

		return dropdownValues;
	}

	getOrderStepDescriptorDropdownValues() {
		const dropdownValues: DropdownButtonAction[] = [];
		for(let purchaseStatus of this.posState.purchaseStatuses) {
			if(!purchaseStatus.cancel && !purchaseStatus.system) {
				dropdownValues.push({
					icon: 'fa-regular fa-send',
					name: purchaseStatus.name,
					id: purchaseStatus.uid
				});
			}
		}
		dropdownValues.push({
			icon: 'fa-regular fa-check',
			name: 'Sélectionner le groupe',
			id: 'select-step'
		});
		return dropdownValues;
	}

	async purchaseItemActionClicked(action: DropdownButtonAction, step: number, purchase: PurchaseApiOut, purchaseItem: PurchaseItemApiOut) {
		if(UuidUtils.isVisual<UuidScopeMetadata_descriptor>(action.id, uuidScopeMetadata_descriptor)) {
			const existingMetadata = purchaseItem.metadataList.find((metadata) => metadata.descriptorUid === action.id);

			this.showMetadataDescriptorModal = {
				purchaseItem: purchaseItem,
				descriptor: this.posState.requireMetadataDescriptorWithUid(action.id),
				currentValue: existingMetadata?.value ?? null,
			};
		} else if(UuidUtils.isVisual<UuidScopeProductPurchaseStatus>(action.id, uuidScopeProductPurchaseStatus)) {
			if(!this.posState.currentOrder) return;

			if(PurchaseStatusModel.canExecuteAfter(
				this.posState.pointOfSaleConfiguration.executionFlow,
				this.posState.orderExecutorModel.requireStatus(action.id),
				this.posState.orderExecutorModel.requireStatus(purchaseItem.statusUpdates[purchaseItem.statusUpdates.length - 1].statusUid),
				purchaseItem.statusUpdates[purchaseItem.statusUpdates.length - 1].status,
			)) {
				try {
					await this.printerRepository.printToKitchen(step, this.posState.currentOrder, [purchaseItem.uid], {
						successCallback: async () => {
							if(!this.posState.currentOrder) return;

							this.posState.orderExecutorModel.addStatusToPurchaseItem(
								this.posState.currentOrder.order,
								purchase,
								purchaseItem,
								action.id as VisualScopedUuid<UuidScopeProductPurchaseStatus>
							);

							await this.posState.orderExecutorModel.processPendingPurchaseItemStatusUpdateActions(this.posState.currentOrder.order);

							this.saveAndResync(this.posState.currentOrder);
						}
					});
				} catch(err) {
					this.appBus.emit('displayToast', {
						title: 'L\'impression du ticket a échoué',
						description: 'Vérifiez que l\'imprimante est connectée et a encore du papier.',
						color: 'red',
						closable: true,
						duration: 3000,
					});
				}
			}
		} else if(action.id === 'previous-step') {
			if(!this.posState.currentOrder || step === 0) return;
			const index = this.posState.currentOrder.diningExtra.purchaseItemSteps.findIndex((data) => data.step === step && data.purchaseItemUid === purchaseItem.uid);
			if(index !== -1) {
				this.posState.currentOrder.diningExtra.purchaseItemSteps.splice(index, 1, new DiningPurchaseItemStepApiOut({
					purchaseItemUid: purchaseItem.uid,
					step: step - 1
				}));
			}

			this.saveAndResync(this.posState.currentOrder);
		} else if(action.id === 'next-step') {
			if(!this.posState.currentOrder) return;

			const index = this.posState.currentOrder.diningExtra.purchaseItemSteps.findIndex((data) => data.step === step && data.purchaseItemUid === purchaseItem.uid);
			if(index !== -1) {
				this.posState.currentOrder.diningExtra.purchaseItemSteps.splice(index, 1, new DiningPurchaseItemStepApiOut({
					purchaseItemUid: purchaseItem.uid,
					step: step + 1
				}));
			}

			this.saveAndResync(this.posState.currentOrder);
		}
	}

	countLastStatusUpdates(purchaseItem: PurchaseItemApiOut) {
		return purchaseItem.statusUpdates.filter((statusUpdate) => statusUpdate.statusUid === purchaseItem.statusUpdates[purchaseItem.statusUpdates.length - 1].statusUid).length
	}

	validatedMetadataDescriptor(value: any) {
		if(!this.showMetadataDescriptorModal) return;
		if(!this.posState.currentOrder) return;

		let newMetadata!: MetadataApiOut_type;
		if(this.showMetadataDescriptorModal.descriptor.descriptorTemplate.type === MetadataDescriptorType.STRING) {
			newMetadata = new MetadataStringApiOut({
				uid: UuidUtils.randomVisualScopedUUID(uuidScopeMetadata_metadata),
				descriptorUid: this.showMetadataDescriptorModal.descriptor.uid,
				value: value,
				type: MetadataDescriptorType.STRING
			});
		} else if(this.showMetadataDescriptorModal.descriptor.descriptorTemplate.type === MetadataDescriptorType.BOOL) {
			newMetadata = new MetadataBoolApiOut({
				uid: UuidUtils.randomVisualScopedUUID(uuidScopeMetadata_metadata),
				descriptorUid: this.showMetadataDescriptorModal.descriptor.uid,
				value: value,
				type: MetadataDescriptorType.BOOL
			});
		} else if(this.showMetadataDescriptorModal.descriptor.descriptorTemplate.type === MetadataDescriptorType.STRING_LIST) {
			newMetadata = new MetadataStringListApiOut({
				uid: UuidUtils.randomVisualScopedUUID(uuidScopeMetadata_metadata),
				descriptorUid: this.showMetadataDescriptorModal.descriptor.uid,
				value: value,
				type: MetadataDescriptorType.STRING_LIST
			});
		}

		const existingMetadataIndex = this.showMetadataDescriptorModal.purchaseItem.metadataList.findIndex((metadata) => metadata.descriptorUid === this.showMetadataDescriptorModal!.descriptor.uid);
		if(existingMetadataIndex !== -1) {
			this.showMetadataDescriptorModal.purchaseItem.metadataList.splice(existingMetadataIndex, 1, newMetadata);
		} else {
			this.showMetadataDescriptorModal.purchaseItem.metadataList.push(newMetadata);
		}

		this.saveAndResync(this.posState.currentOrder);
		this.showMetadataDescriptorModal = null;
	}

	getPurchaseCompositionAsString(purchase: PurchaseApiOut) {
		let value: string[] = [];
		for(const item of purchase.items) {
			for(const group of item.groups) {
				for(const subPurchase of group.purchases) {
					const productRevision = this.posState.orderExecutorModel.requireProductRevision(subPurchase.productRevisionUid);
					value.push(productRevision.name);
				}
			}
		}
		return value;
	}

	dropdownClicked(action: DropdownButtonAction) {
		if(!this.posState.currentOrder) return;
		if(action.id === 'force-sync') this.saveAndResync(this.posState.currentOrder);
		else if(action.id === 'receipt') OrderUtils.printReceipt(this.posState.currentOrder, 'DISPLAY');
		else if(action.id === 'note') OrderUtils.printNote(this.posState.currentOrder);
		else if(action.id === 'send-receipt') OrderUtils.printReceipt(this.posState.currentOrder, 'SEND');
		else if(action.id === 'transfer-order') this.posState.showTransferOrderMenu = this.posState.currentOrder;
		else if(action.id === 'customer-link') this.posState.linkCustomerToOrder = this.posState.currentOrder;
		else if(action.id === 'transfer-reservit') this.posState.showReservitTransferOrderMenu = this.posState.currentOrder;
		else if(action.id === 'show-debug') this.posState.showDebugInformations = this.posState.currentOrder;
		else if(UuidUtils.isVisual(action.id, uuidScopeProductPurchaseStatus)) OrderUtils.addPurchaseStatus(action.id, null);
	}


	getUploadImageUrl(uploadUid: VisualScopedUuid<UuidScopeMastodon_upload>) {
		return this.mainConfig.configuration.mastodonApiEndpoint + buildHttpRoutePathWithArgs(MastodonHttpImagesContract.getPublicImage, {
			establishmentUid: this.posState.establishmentUid,
			imageId: uploadUid,
			options: {
				quality: 100
			},
			dimensions: {width: 100},
			extension: 'png',
			resizeType: 'contain',
			revision: new Date().toISOString(),
		});
	}

	requireStatusIconUploadUid(status: OrderPurchaseStatusApiOut): VisualScopedUuid<UuidScopeMastodon_upload> {
		if(!status.iconUploadUid) throw new Error('no_upload');
		return status.iconUploadUid;
	}

	saveAndResync(localOrder: LocalOrder) {
		this.localOrderRepository.saveAndResync(localOrder)
	}

	close() {
		this.$emit('close');
	}
}