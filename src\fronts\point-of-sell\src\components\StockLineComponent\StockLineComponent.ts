import {Component, Prop, Vue} from "vue-facing-decorator";
import {ProductApiOut, UuidScopeProductProduct} from "@groupk/mastodon-core";
import {PosState} from "../../model/PosState";
import {AutoWired, VisualScopedUuid} from "@groupk/horizon2-core";
import {LocalFavoriteStockRepository} from "../../repositories/LocalFavoriteStockRepository";

@Component({})
export default class StockLineComponent extends Vue {
	@Prop() posState!: PosState;
	@Prop() product!: ProductApiOut;

	favoriteProductUids: VisualScopedUuid<UuidScopeProductProduct>[] = [];

	@AutoWired(LocalFavoriteStockRepository) accessor localFavoriteStockRepository!: LocalFavoriteStockRepository;

	async mounted() {
		this.favoriteProductUids = await this.localFavoriteStockRepository.get();
	}

	get remainingStock() {
		return this.posState.virtualStockHandler.estimateRemainingStock(this.product.lastRevision)
	}

	async addToFavorites(productUid: VisualScopedUuid<UuidScopeProductProduct>) {
		this.favoriteProductUids = await this.localFavoriteStockRepository.get()
		this.favoriteProductUids.push(productUid);
		this.localFavoriteStockRepository.save(this.favoriteProductUids);
	}

	async removeFromFavorites(productUid: VisualScopedUuid<UuidScopeProductProduct>) {
		this.favoriteProductUids = await this.localFavoriteStockRepository.get()
		const index = this.favoriteProductUids.indexOf(productUid);
		if(index !== -1) this.favoriteProductUids.splice(index, 1);
		this.localFavoriteStockRepository.save(this.favoriteProductUids);
	}
}