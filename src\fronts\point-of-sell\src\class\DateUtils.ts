export type Range = {
	start: Date|null, end: Date|null, color: string, disabled: boolean, chained:boolean
}

export default class DateUtils {
	public static convertFrToIso(dateString: string) {
		const dateParts = dateString.split('/');
		return dateParts[2] + '-' + dateParts[1] + '-' + dateParts[0];
	}

	public static formatDateToIsoString(date: Date) {
		return date.toISOString().split('T')[0];
	}

	public static formatDateFrFormat(dateString: string) {
		let date = new Date(dateString);
		return Intl.DateTimeFormat('fr' , {dateStyle: 'short'}).format(date);
	}

	public static formatDateReadableFrFormat(dateString: string) {
		let date = new Date(dateString);
		let parts = Intl.DateTimeFormat('fr' , {day: '2-digit', month: 'short', year: 'numeric'}).formatToParts(date);
		return this.getPartWithType('day', parts) + ' ' + this.getPartWithType('month', parts) + ' ' + this.getPartWithType('year', parts)
	}

	public static formatDateDayReadableFrFormat(dateString: string) {
		let date = new Date(dateString);
		let parts = Intl.DateTimeFormat('fr' , {weekday: 'short', day: '2-digit', month: 'short'}).formatToParts(date);
		return this.getPartWithType('weekday', parts) + ' ' + this.getPartWithType('day', parts) + ' ' + this.getPartWithType('month', parts)
	}

	public static formatDateDayShortFrFormat(dateString: string) {
		let date = new Date(dateString);
		let parts = Intl.DateTimeFormat('fr' , {weekday: 'short', day: '2-digit', month: 'short'}).formatToParts(date);
		return this.getPartWithType('day', parts) + ' ' + this.getPartWithType('month', parts)
	}

	public static formatDateDayNumberFormat(dateString: string) {
		let date = new Date(dateString);
		let parts = Intl.DateTimeFormat('fr' , {weekday: 'short', day: '2-digit', month: 'short'}).formatToParts(date);
		return this.getPartWithType('day', parts);
	}

	public static formatDateDayNameFormat(dateString: string) {
		let date = new Date(dateString);
		let parts = Intl.DateTimeFormat('fr' , {weekday: 'short', day: '2-digit', month: 'short'}).formatToParts(date);
		return this.getPartWithType('weekday', parts);
	}

	public static formatDateFullDayNameFormat(dateString: string) {
		let date = new Date(dateString);
		let parts = Intl.DateTimeFormat('fr' , {weekday: 'long', day: '2-digit', month: 'short'}).formatToParts(date);
		return this.getPartWithType('weekday', parts);
	}


	public static formatDateYearFormat(dateString: string) {
		let date = new Date(dateString);
		let parts = Intl.DateTimeFormat('fr' , {weekday: 'short', day: '2-digit', year: 'numeric'}).formatToParts(date);
		return this.getPartWithType('year', parts);
	}


	public static formatDateMonth(dateString: string) {
		let date = new Date(dateString);
		return Intl.DateTimeFormat('fr' , {month: 'long'}).format(date);
	}

	public static formatDateHour(dateString: string) {
		let date = new Date(dateString);
		let parts = Intl.DateTimeFormat('fr' , {hour: '2-digit', minute: '2-digit'}).formatToParts(date);
		return this.getPartWithType('hour', parts) + 'h' + this.getPartWithType('minute', parts)
	}

	private static getPartWithType(type: string, parts: { type: string, value: string }[]): string {
		for(let part of parts) {
			if(part.type === type) {
				return part.value;
			}
		}
		return '';
	}
}