<script lang="ts" src="./iotOnboarding.ts">
</script>

<style lang="sass">
@import './iotOnboarding.scss'
</style>

<template>
    <div id="iot-onboarding-page">

        <div class="loading" v-if="loading">
            <div class="loader"></div>
            Chargement...
        </div>
        <template v-else>
            <div class="left">
                <h2> Liaison de l’appareil avec votre établissement </h2>

                <span> Pour utiliser cet appareil sur l’établissement sélectionné vous devez le lier. </span>

                <button class="margin-top big button" :class="{loading: creating, disabled: creating || posServiceEnabledDevices.length >= 10, red: posServiceEnabledDevices.length, green: posServiceEnabledDevices.length < 10}" @click="createEstablishmentDevice()">
                    <i class="fa-regular fa-link"></i>
                    Lier cet appareil
                </button>

                <div class="form-error" v-if="error">
                    <i class="fa-solid fa-exclamation-circle"></i>
                    <div class="details">
                        <span class="title"> Erreur </span>
                        <span class="description"> {{ error }}</span>
                    </div>
                </div>

                <button class="big white button">
                    Annuler
                </button>
            </div>
            <div class="right">
                <h2> Statut </h2>

<!--                <div class="status">-->
<!--                    <div class="left">-->
<!--                        <span class="title"> Appareil disponible </span>-->
<!--                        <span class="description">-->
<!--                        Cet appareil n’est utilisé par aucun autre établissement-->
<!--                    </span>-->
<!--                    </div>-->

<!--                    <i class="fa-solid fa-check-circle"></i>-->
<!--                </div>-->

                <div class="status" :class="{'color-red': posServiceEnabledDevices.length >= 10}">
                    <div class="left">
                        <span class="title"> Quota d’appareils </span>
                        <span class="description">
                        Votre plan vous donne accès à 10 appareils en simultané
                    </span>

                        <div class="progress-group">
                            <span class="quota"> {{ posServiceEnabledDevices.length }}/10 caisses utilisées </span>

                            <div class="progress-bar">
                                <div class="progress" :style="`width: ${(posServiceEnabledDevices.length / 10) * 100}%;`"></div>
                            </div>
                        </div>
                    </div>

                    <i class="fa-solid fa-circle-check" v-if="posServiceEnabledDevices.length < 10"></i>
                    <i class="fa-solid fa-circle-xmark" v-else></i>
                </div>

                <div class="green status" v-if="posServiceEnabledDevices.length < 10">
                    <i class="fa-solid fa-circle-check"></i>

                    <div class="left">
                        <span class="title"> Tout est prêt ! </span>
                        <span class="description">
                            Vous pouvez lier cet appareil à votre établissement afin de commencer à vendre !
                        </span>
                    </div>
                </div>

                <div class="red status" v-else>
                    <i class="fa-solid fa-circle-xmark"></i>

                    <div class="left">
                        <span class="title"> Liaison impossible </span>
                        <span class="description">
                           Votre quota d'appareils a été atteint. Changez de plan de facturation pour débloquer plus d'appareils.
                        </span>
                    </div>
                </div>
            </div>
        </template>
    </div>
</template>