<script lang="ts" src="./QuickProductIntoPosComponent.ts" />

<style lang="sass" scoped>
@import './QuickProductIntoPosComponent.scss'
</style>

<template>
    <div class="quick-product-into-pos-component">
        <form-modal-or-drawer
            :state="opened"
            title="Ajouter le produit à un point de vente ?"
            subtitle="Voulez vous ajouter le produit à un ou plusieurs points de vente ?"
            @close="close()"
        >
            <template v-slot:content>

                <div v-if="loading" class="loading-container">
                    <div class="loader"></div>
                </div>

                <table v-else class="data-table">
                    <thead>
                    <tr>
                        <td> Point de vente </td>
                    </tr>
                    </thead>
                    <tbody>
                    <template v-for="pointOfSale of pointOfSales">
                        <tr>
                            <td>
                                <div class="point-of-sale-data" @click="togglePointOfSale(pointOfSale)">
                                    <div class="checkbox" :class="{selected: selectedPointOfSales.includes(pointOfSale.uid)}">
                                        <i class="fa-regular fa-check"></i>
                                    </div>

                                    {{ pointOfSale.name }}
                                </div>
                            </td>
                        </tr>

                        <tr v-if="selectedPointOfSales.includes(pointOfSale.uid)" v-for="category of pointOfSale.categoriesUid" @click="toggleCategory(category)">
                            <td>
                                <div class="category">
                                    <div class="checkbox" :class="{selected: isCategoryToggled(category)}">
                                        <i class="fa-regular fa-check"></i>
                                    </div>

                                    {{ requireCategoryWithUid(category).name }}
                                </div>
                            </td>
                        </tr>
                    </template>
                    </tbody>
                </table>
            </template>
            <template v-slot:buttons>
                <button class="white button" @click="close()"> Fermer </button>
                <button class="button" :class="{loading: saving, disabled: saving}" @click="save()"> Valider </button>
            </template>
        </form-modal-or-drawer>
    </div>
</template>