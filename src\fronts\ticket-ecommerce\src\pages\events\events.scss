#events-page {
    height: 100%;
    overflow: auto;
    background: var(--primary-background-color);
    color: var(--background-text-color);

    .centered-content {
        display: flex;
        flex-direction: column;
        gap: 20px;
        max-width: 1200px;
        margin: auto;
        padding: 16px;

        h1 {
            text-align: center;
            margin: 0;
        }

        h3 {
            font-size: 22px;
            font-weight: bold;
            margin: 0;
        }

        @media (min-width: 900px) {
            margin-top: 40px;
            padding: 40px;
        }
    }

    .events {
        display: grid;
        grid-template-columns: 1fr 1fr 1fr;
        grid-gap: 20px;
        width: 100%;

        @media (max-width: 900px) {
            grid-template-columns: 1fr;
        }

        &.old {
            grid-template-columns: 1fr 1fr 1fr 1fr;

            @media (max-width: 900px) {
                grid-template-columns: 1fr 1fr;
            }
        }

        .event {
            position: relative;
            display: flex;
            flex-direction: column;
            border-radius: 8px;
            border: 1px solid var(--border-color);

            .banner {
                all: unset;
                position: relative;
                display: flex;
                align-items: center;
                justify-content: center;
                height: 125px;
                background: #D9D9D9;
                background-size: cover;
                background-position: center;
                cursor: pointer;
                border-radius: 8px 8px 0 0;

                .label {
                    position: absolute;
                    bottom: 15px;
                    left: 15px;
                    background: rgba(13, 184, 27, 0.6);
                    color: white;
                }

                > i {
                    font-size: 38px;
                    color: #636363;
                }

                .popup {
                    color: black;
                }
            }

            .content {
                display: flex;
                align-items: center;
                justify-content: space-between;
                gap: 10px;
                padding: 15px 10px 15px 20px;
                flex-grow: 2;
                cursor: pointer;

                .data {
                    flex-grow: 2 !important;
                    all: unset;
                    display: flex;
                    flex-direction: column;
                    justify-content: center;
                    gap: 4px;

                    &:hover {
                        text-decoration: underline;
                    }

                    .name {
                        font-size: 16px;
                        font-weight: 600;
                    }

                    .description {
                        font-size: 14px;
                    }
                }
            }
        }
    }
}