<script lang="ts" src="./pos.ts">
</script>

<style scoped lang="sass">
@import './pos.scss'
</style>

<template>
    <!-- FontAwesome icons preload -->
    <div class="pos-fa-preload"></div>

    <div id="pos" v-if="!isMobileScreenSize">
        <pos-error v-if="error" :error="error"></pos-error>

        <div class="loading" v-else-if="loading">
            <div class="loader"></div>
            Chargement...
        </div>
        <template v-else>
            <div class="sidebar">
                <sidebar :pos-state="posState" :pos-profile="posProfile"></sidebar>
            </div>
            <div class="content">
                <sell :pos-profile="posProfile" :pos-state="posState" v-if="posState.currentPage === 'sell'"></sell>
                <order-list :pos-profile="posProfile" :pos-state="posState" v-if="posState.currentPage === 'orders'"></order-list>
                <tables :pos-profile="posProfile" :pos-state="posState" v-if="posState.currentPage === 'tables'"></tables>
                <statistics :pos-profile="posProfile" :pos-state="posState" v-if="posState.currentPage === 'statistics'"></statistics>
                <settings :pos-profile="posProfile" :pos-state="posState" v-if="posState.currentPage === 'settings'"></settings>
            </div>
        </template>

        <div class="modal-dimmer" v-if="posState.showCashStatementModal" @click="posState.showCashStatementModal = false">
            <div class="modal" @click.stop>
                <closing-cash-statement :pos-state="posState" @close=""></closing-cash-statement>
            </div>
        </div>

        <quick-actions :pos-state="posState" v-if="posState.showQuickActions" @close="posState.showQuickActions = false"></quick-actions>
        <cash-keeper v-if="posState.showCashKeeperActions" @close="posState.showCashKeeperActions = false"></cash-keeper>
        <account-switch :pos-profile="posProfile" :pos-state="posState" v-if="posState.forceProfileSwitch && !lockedProfileSwitchState"></account-switch>
        <toast-manager :pos-state="posState"></toast-manager>

        <purchase-print-progress-modal @close="posState.showTicketPrintingModal = false" v-if="posState.showTicketPrintingModal"></purchase-print-progress-modal>

        <div class="displayed-receipt" v-if="posState.displayedReceipt" @click="posState.displayedReceipt = null">
            <img :src="posState.displayedReceipt">
        </div>

        <order-debug-modal
            v-if="posState.showDebugInformations"
            :local-order="posState.showDebugInformations"
            @close="posState.showDebugInformations = null"
        ></order-debug-modal>

        <order-transfer-to-iot
            v-if="posState.showTransferOrderMenu"
            :pos-state="posState"
            :local-order="posState.showTransferOrderMenu"
            @close="posState.showTransferOrderMenu = null"
        ></order-transfer-to-iot>

        <reservit-room-transfer
            v-if="posState.showReservitTransferOrderMenu"
            :local-order="posState.showReservitTransferOrderMenu"
            :pos-state="posState"
            @close="posState.showReservitTransferOrderMenu = null"
        ></reservit-room-transfer>

<!--        <cash-drawer-cash-count></cash-drawer-cash-count>-->

        <div class="right-modal-dimmer" v-if="posState.linkCustomerToOrder"></div>
        <customers-list-component
            v-if="posState.linkCustomerToOrder"
            @selected-customer="updateOrderCustomer(posState.linkCustomerToOrder, $event)"
            @close="posState.linkCustomerToOrder = null"
        ></customers-list-component>

        <template v-if="lockedProfileSwitchState">
            <div class="security-code-dimmer" @click="cancelLockedProfile()"></div>
            <div class="security-code">
                <keypad :simple-mode="true" @clicked-key="clickedSecurityCodeKey($event)">
                    <template v-slot:display>
                        <div class="code">
                            <template v-for="(number, index) in lockedProfileSwitchState.profile.securityCode" >
                                <i v-if="currentSecurityCode.length <= index" class="fa-regular fa-circle-small"></i>
                                <i v-else class="fa-solid fa-circle-small"></i>
                            </template>
                        </div>
                    </template>
                </keypad>
            </div>
        </template>
    </div>

    <div id="mobile-pos" v-else>
        <pos-error v-if="error" :error="error"></pos-error>

        <div class="loading" v-else-if="loading">
            <div class="loader"></div>
            Chargement...
        </div>
        <template v-else>
            <div class="mobile-content">
                <sell :pos-profile="posProfile" :pos-state="posState" v-if="posState.currentPage === 'sell'"></sell>
                <mobile-order-list :pos-profile="posProfile" :pos-state="posState" v-if="posState.currentPage === 'orders'"></mobile-order-list>
                <mobile-statistics :pos-profile="posProfile" :pos-state="posState" v-if="posState.currentPage === 'statistics'"></mobile-statistics>
                <tables :pos-profile="posProfile" :pos-state="posState" v-if="posState.currentPage === 'tables'"></tables>
                <quick-actions :pos-state="posState" v-if="posState.currentPage === 'settings' && !posState.showQuickActions"></quick-actions>
                <settings :pos-profile="posProfile" :pos-state="posState" v-if="posState.currentPage === 'settings' && posState.showQuickActions"></settings>
            </div>
            <mobile-navigation
                :pos-state="posState"
                :pos-profile="posProfile"
            ></mobile-navigation>

            <modal-or-drawer
                :state="posState.showCashStatementModal"
                @close="posState.showCashStatementModal = false"
            >
                <closing-cash-statement v-if="posState.showCashStatementModal" :pos-state="posState" @close=""></closing-cash-statement>
            </modal-or-drawer>

            <account-switch :pos-profile="posProfile" :pos-state="posState" v-if="posState.forceProfileSwitch && !lockedProfileSwitchState"></account-switch>

            <toast-manager :pos-state="posState"></toast-manager>

            <div class="displayed-receipt" v-if="posState.displayedReceipt" @click="posState.displayedReceipt = null">
                <img :src="posState.displayedReceipt">
            </div>

            <order-debug-modal
                v-if="posState.showDebugInformations"
                :local-order="posState.showDebugInformations"
                @close="posState.showDebugInformations = null"
            ></order-debug-modal>

            <order-transfer-to-iot
                v-if="posState.showTransferOrderMenu"
                :pos-state="posState"
                :local-order="posState.showTransferOrderMenu"
                @close="posState.showTransferOrderMenu = null"
            ></order-transfer-to-iot>

            <reservit-room-transfer
                v-if="posState.showReservitTransferOrderMenu"
                :local-order="posState.showReservitTransferOrderMenu"
                :pos-state="posState"
                @close="posState.showReservitTransferOrderMenu = null"
            ></reservit-room-transfer>

            <div class="right-modal-dimmer" v-if="posState.linkCustomerToOrder"></div>
            <customers-list-component
                v-if="posState.linkCustomerToOrder"
                @selected-customer="updateOrderCustomer(posState.linkCustomerToOrder, $event)"
                @close="posState.linkCustomerToOrder = null"
            ></customers-list-component>

            <template v-if="lockedProfileSwitchState">
                <div class="security-code-dimmer" @click="cancelLockedProfile()"></div>
                <div class="security-code">
                    <keypad :simple-mode="true" @clicked-key="clickedSecurityCodeKey($event)">
                        <template v-slot:display>
                            <div class="code">
                                <template v-for="(number, index) in lockedProfileSwitchState.profile.securityCode" >
                                    <i v-if="currentSecurityCode.length <= index" class="fa-regular fa-circle-small"></i>
                                    <i v-else class="fa-solid fa-circle-small"></i>
                                </template>
                            </div>
                        </template>
                    </keypad>
                </div>
            </template>
        </template>
    </div>
</template>