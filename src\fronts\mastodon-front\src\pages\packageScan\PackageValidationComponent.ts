import {Component, Prop, Vue} from "vue-facing-decorator";
import {AutoWired, FetchError, genericFetch} from "@groupk/horizon2-core";
import {AirtableRecord} from "./packageScan";
import {BarcodeExternalNative_QRCodeReaderReturn, VibratorNative} from "@groupk/native-bridge";
import {BarcodeScannerManager} from "./BarcodeScannerManager";
import CameraComponent from "./CameraComponent.vue";
import ItemActionsComponent from "./ItemActionsComponent.vue";
import {ItemAction} from "./ItemActionsComponent";

@Component({
    components: {
        'camera': CameraComponent,
        'item-actions': ItemActionsComponent
    },
    emits: ['close', 'updated', 'updated-object']
})
export default class PackageValidationComponent extends Vue {
    @Prop({required: true}) packageData!: AirtableRecord;
    @Prop({required: true}) objectsInPackage!: AirtableRecord[];
    @Prop({required: true}) objectTranslations!: AirtableRecord[];

    notIn: AirtableRecord|null = null;
    scanState: 'ERROR'|'SUCCESS'|null = null;

    showActionsForObject: AirtableRecord|null = null;
    showCamera: { opened: boolean, forObject: AirtableRecord }|null = null;
    showObjectImage: AirtableRecord|null = null;
    showObjectReturnImage: AirtableRecord|null = null;

    scanning: boolean = false;
    saving: boolean = false;

    @AutoWired(BarcodeScannerManager) accessor barcodeScannerManager!: BarcodeScannerManager;
    @AutoWired(VibratorNative) accessor vibratorNative!: VibratorNative;

    async mounted() {
        this.barcodeScannerManager.customCallback = this.barCodeRead;
    }

    get notScannedObjects() {
        return this.objectsInPackage.filter((object) => !object.fields.return);
    }

    get scannedObjects() {
        return this.objectsInPackage.filter((object) => object.fields.return);
    }

    async barCodeRead(data: BarcodeExternalNative_QRCodeReaderReturn) {
        if(this.scanning) return;
        this.scanning = true;

        const correspondingAirtableObject = this.isIn(data.content);
        if(correspondingAirtableObject === null) {
            this.notIn = correspondingAirtableObject; // Not used for now
            this.scanState = 'ERROR';
        }
        else {
            if(correspondingAirtableObject.fields.return) {
                this.scanState = 'SUCCESS';
            } else {
                const response = await genericFetch({
                    url: 'https://api.airtable.com/v0/app9UWPLidwgXu3jB/objects',
                    method: 'PATCH',
                    headers: {
                        'Authorization': 'Bearer pat5zDgVSdUwXg2IJ.3c32972e7593e4dd64c45b24b65d57a07ddb30ccb2c56a2d7fc97bc247af9515',
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        "records": [
                            {
                                "id": correspondingAirtableObject.id,
                                "fields": {
                                    "code": correspondingAirtableObject.fields.code,
                                    "packageId": correspondingAirtableObject.fields.packageId,
                                    "return": true,
                                    "returnImage": correspondingAirtableObject.fields.returnImage,
                                    "image": correspondingAirtableObject.fields.image
                                }
                            }
                        ]
                    })
                });

                if(response instanceof FetchError) {
                    this.scanState = 'ERROR';
                } else {
                    const json = await response.json();
                    this.$emit('updated-object', json.records[0]);
                    this.scanState = 'SUCCESS';
                }
            }
        }

        setTimeout(() => this.scanState = null, 200);

        this.scanning = false;
    }

    isIn(objectId: string): AirtableRecord|null {
        return this.objectsInPackage.find((object) => object.fields.code === objectId) ?? null;
    }

    async validatePackage() {
        this.saving = true;

        const response = await genericFetch({
            url: 'https://api.airtable.com/v0/app9UWPLidwgXu3jB/package',
            method: 'PATCH',
            headers: {
                'Authorization': 'Bearer pat5zDgVSdUwXg2IJ.3c32972e7593e4dd64c45b24b65d57a07ddb30ccb2c56a2d7fc97bc247af9515',
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                "records": [
                    {
                        "id": this.packageData.id,
                        "fields": {
                            "name": this.packageData.fields.name,
                            "shipping": this.packageData.fields.shipping,
                            "return": this.packageData.fields.return,
                            "validated": true
                        }
                    }
                ]
            })
        });

        if(response instanceof FetchError) {
        } else {
            const json = await response.json();
            this.$emit('updated', json.records[0]);
        }

        this.saving = false;
        this.close();
    }

    getObjectTranslation(objectId: string) {
        const translationObject = this.objectTranslations.find((object) => object.fields.code === objectId);
        if(translationObject) {
            return translationObject.fields.brand + ' ' + translationObject.fields.name + ' - ' + objectId;
        } else {
            return objectId;
        }
    }

    async actionClicked(action: ItemAction) {
        if(!this.showActionsForObject) return;
        if(action.id === 'take-picture') {
            this.showCamera = {opened: true, forObject: this.showActionsForObject};
        } else if(action.id === 'show-return-image') {
            this.showObjectReturnImage = this.showActionsForObject;
        } else if(action.id === 'show-image') {
            this.showObjectImage = this.showActionsForObject;
        }
    }

    getObjectActions(objectData: AirtableRecord) {
        const actions: ItemAction[] = [
            {id: 'take-picture', icon: 'fa-regular fa-camera', text: 'Image de retour'},
        ];

        if(objectData.fields.returnImage) {
            actions.unshift({id: 'show-return-image', icon: 'fa-regular fa-image', text: 'Visualiser l\'image de retour'});
        }

        if(objectData.fields.image) {
            actions.unshift({id: 'show-image', icon: 'fa-regular fa-image', text: 'Visualiser l\'image'});
        }

        return actions;
    }


    async photoCaptured(dataUrl: string) {
        if(!this.showCamera) return;
        this.scanning = true;

        const response = await genericFetch({
            url: 'https://api.airtable.com/v0/app9UWPLidwgXu3jB/objects',
            method: 'PATCH',
            headers: {
                'Authorization': 'Bearer pat5zDgVSdUwXg2IJ.3c32972e7593e4dd64c45b24b65d57a07ddb30ccb2c56a2d7fc97bc247af9515',
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                "records": [
                    {
                        "id": this.showCamera.forObject.id,
                        "fields": {
                            "code": this.showCamera.forObject.fields.code,
                            "packageId": this.showCamera.forObject.fields.packageId,
                            "image": this.showCamera.forObject.fields.image,
                            "returnImage": dataUrl,
                            "return": this.showCamera.forObject.fields.return
                        }
                    }
                ]
            })
        });

        if(response instanceof FetchError) {
        } else {
            this.$emit('updated-object', (await response.json()).records[0]);
        }

        this.showCamera = null;
        this.scanning = false;
    }

    close() {
        this.$emit('close');
    }
}