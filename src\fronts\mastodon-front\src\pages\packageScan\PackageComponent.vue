<script lang="ts" src="./PackageComponent.ts"/>

<style lang="sass" scoped>
@import './PackageComponent.scss'
</style>

<template>
    <div class="package-component">
        <div class="loading-container" v-if="loading">
            <div class="loader"></div>
        </div>
        <div v-else class="container">
            <div class="actions">
                <div class="action-group" @click="close()">
                    <div class="action">
                        <i class="fa-regular fa-arrow-left"></i>
                    </div>
                    <span class="text"> retour </span>
                </div>

                <div class="action-group" @click="showValidation = true;">
                    <div class="action">
                        <i class="fa-regular fa-question-circle"></i>
                    </div>
                    <span class="text"> valider le colis </span>
                </div>

                <div class="action-group" @click="showForm = true;">
                    <div class="action">
                        <i class="fa-regular fa-pen-line"></i>
                    </div>
                    <span class="text"> modifier </span>
                </div>
            </div>

            <h3> Objets dans le colis ({{ objectsInPackage.length }})</h3>

            <div class="objects">
                <div class="object" v-for="objectData of objectsInPackage" @click="showActionsForObject = objectData">
                    {{ getObjectTranslation(objectData.fields.code) }}

                    <div class="images">
                        <i class="fa-regular fa-image" v-if="objectData.fields.image"></i>
                        <i class="fa-regular fa-image" v-if="objectData.fields.returnImage"></i>
                    </div>
                </div>
            </div>
        </div>

        <div class="scanning-modal already-in" v-if="alreadyIn">
            <div class="container">
                <div class="title"> Ce code est déjà présent dans le colis </div>
                <div class="buttons">
                    <button class="red button" :class="{loading: deleting, disabled: deleting}" @click="deleteObjectFromPackage(alreadyIn.id)">
                        Supprimer du colis
                    </button>
                    <button class="grey button" @click="alreadyIn = null;">
                        Continuer
                    </button>
                </div>
            </div>
        </div>

        <div class="scanning-modal" v-else-if="scanning">
            <div class="container">
                <div class="loading-container">
                    <div class="loader"></div>
                </div>
            </div>
        </div>

        <package-validation
            v-if="showValidation"
            :package-data="packageData"
            :objects-in-package="objectsInPackage"
            :object-translations="objectTranslations"
            @updated="updatePackage($event, true)"
            @updated-object="updateObject($event)"
            @close="showValidation = false; setupListener()"
        ></package-validation>

        <package-form
            v-if="showForm"
            :editing-package="packageData"
            @updated="updatePackage($event)"
            @close="showForm = false; setupListener()"
        ></package-form>

        <item-actions
            v-if="showActionsForObject"
            :actions="getObjectActions(showActionsForObject)"
            @action-clicked="actionClicked($event)"
            @close="showActionsForObject = null"
        ></item-actions>

        <camera
            v-if="showCamera && showCamera.opened"
            @photo-captured="photoCaptured($event)"
            @close="showCamera.opened = false"
        ></camera>

        <img class="object-image" v-if="showObjectImage" :src="showObjectImage.fields.image" @click="showObjectImage = null" />
        <img class="object-image" v-if="showObjectReturnImage" :src="showObjectReturnImage.fields.returnImage" @click="showObjectReturnImage = null" />
    </div>
</template>