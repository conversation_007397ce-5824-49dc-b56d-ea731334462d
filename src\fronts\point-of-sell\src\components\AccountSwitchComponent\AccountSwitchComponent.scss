.account-switch-component {
    position: fixed;
    z-index: 1005;
    inset: 0;
    background: white;
    display: flex;

    .left {
        width: 400px;
        display: flex;
        flex-direction: column;
        gap: 20px;
        overflow: auto;
        flex-shrink: 0;
        padding: 40px;

        @media (max-width: 900px) {
            width: 100%;
            padding: var(--safe-area-top) 20px var(--safe-area-bottom) 20px;
            box-sizing: border-box;
        }

        .account {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 30px;
            background: var(--secondary-hover-color);
            border-radius: 8px;
            font-weight: bold;
            font-size: 18px;
        }
    }

    .right {
        height: 100%;
        flex-grow: 2;
        background: black;
        background-image: url('/img/login.png');
        background-size: cover;
        background-position: left;


        @media (max-width: 900px) {
           display: none;
        }
    }
}
