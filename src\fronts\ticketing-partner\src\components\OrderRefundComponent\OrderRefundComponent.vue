<script lang="ts" src="./OrderRefundComponent.ts"/>

<style lang="sass" scoped>
@import './OrderRefundComponent.scss'
</style>

<template>
    <div class="order-refund-component">
        <form-modal-or-drawer
            :state="opened"
            :title="'Rembourser la commande'"
            :subtitle="`Remboursez des billets ou une somme personnalisée (reste ${$filters.Money(getLeftToRefundAmount())} à rembourser)`"
            @close="close()"
        >
            <template v-slot:content>

                <div class="section">
                    <h3> Billet à rembourser </h3>

                    <table class="data-table">
                        <thead>
                        <tr>
                            <td> Billet </td>
                            <td> Prix </td>
                        </tr>
                        </thead>
                        <tbody>
                        <tr v-if="fullOrder.order.purchases.length === 0">
                            <td class="empty" colspan="100%"> Aucun billet </td>
                        </tr>
                        <tr v-for="purchase of fullOrder.order.purchases">
                            <td @click="togglePurchase(purchase)">
                                <div class="ticket-data">
                                    <div class="checkbox" :class="{selected: isPurchaseSelected(purchase)}">
                                        <i v-if="isPurchaseSelected(purchase)" class="fa-regular fa-check"></i>
                                    </div>
                                    <div class="ticket-icon">
                                        <i class="fa-regular fa-ticket-simple"></i>
                                    </div>
                                    <div class="ticket-name">
                                        x{{ purchase.items.reduce((total, item) => total + item.quantity, 0) }}
                                        {{ requireProductRevisionWithUid(purchase.productRevisionUid).name }}
                                    </div>
                                </div>
                            </td>
                            <td>
                                {{ $filters.Money(orderExecutorModel.getPurchasePrice(fullOrder.order, purchase).withTaxes) }}
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>

                <!-- Custom amount section -->
                <div class="custom-amount-section" v-if="!showCustomValueInput" @click="showCustomValueInput = true">
                    <div class="custom-amount-header">
                        <span> Autre montant </span>
                        <div class="custom-amount-subtitle"> Saisir un montant personnalisé </div>
                    </div>

                    <i class="fa-regular fa-chevron-right"></i>
                </div>

                <div class="section" v-if="showCustomValueInput">
                    <h3> Montant personnalisé </h3>

                    <div class="input-group">
                        <label> Vous pouvez sélectionner des billets ET spécifier un montant personnalisé </label>
                        <div class="input flex">
                            <price-input
                                :p="refundingAmount"
                                @change="refundingAmount = $event"
                                :options="{numeralPositiveOnly: false}">
                            </price-input>

                            <button class="grey button" @click="refundingAmount = getLeftToRefundAmount()"> Max </button>
                        </div>
                    </div>
                </div>

                <!-- Refund method section -->
<!--                <div class="section">-->
<!--                    <h3>Moyen de remboursement</h3>-->
<!--                    <div class="refund-methods">-->
<!--                        <div class="method" @click="selectRefundMethod('online')">-->
<!--                            <div class="radio" :class="{selected: selectedRefundMethod === 'online'}">-->
<!--                                <div class="center"></div>-->
<!--                            </div>-->
<!--                            <span>En ligne</span>-->
<!--                        </div>-->
<!--                        <div class="method" @click="selectRefundMethod('automatic')">-->
<!--                            <div class="radio" :class="{selected: selectedRefundMethod === 'automatic'}">-->
<!--                                <div class="center"></div>-->
<!--                            </div>-->
<!--                            <span>Somme remboursée automatiquement via le prestataire de paiement</span>-->
<!--                        </div>-->
<!--                    </div>-->
<!--                </div>-->

                <div class="section">
                    <h3> Commentaire </h3>

                    <div class="input-group">
                        <textarea
                            v-model="comment"
                            placeholder="Ajouter un commentaire..."
                            rows="4"
                        ></textarea>
                    </div>
                </div>


                <!-- Refund options section -->
                <div class="section">
                    <h3> Options de remboursement </h3>

                    <div class="toggle-input">
                        <div class="infos">
                            <span class="title"> Annuler les tickets </span>
                        </div>
                        <toggle :default-toggled-value="cancelTickets" @toggled="cancelTickets = $event"></toggle>
                    </div>

                    <div class="toggle-input">
                        <div class="infos">
                            <span class="title"> Remettre en stock les tickets </span>
                        </div>
                        <toggle :default-toggled-value="putTicketsBackInStock" @toggled="putTicketsBackInStock = $event"></toggle>
                    </div>
                </div>

                <!-- Warning message -->
<!--                <div class="warning-message">-->
<!--                    Veuillez sélectionner une somme à rembourser et une méthode de paiement-->
<!--                </div>-->

                <div class="form-warning" v-if="refundingAmount > getLeftToRefundAmount()">
                    <i class="fa-solid fa-exclamation-circle"></i>
                    <div class="details">
                        <span class="title"> Remboursement supérieur au total payé </span>
                        <span class="description"> La somme remboursée ne peut pas être supérieure au total payé ({{ $filters.Money(getLeftToRefundAmount()) }}) </span>
                    </div>
                </div>
            </template>
            <template v-slot:buttons>
                <button class="primary button refund-button" :class="{disabled: refundingAmount === 0 || refundingAmount > getLeftToRefundAmount()}" @click="processRefund()">
                    Rembourser {{ $filters.Money(refundingAmount) }}
                </button>
            </template>
        </form-modal-or-drawer>
    </div>
</template>