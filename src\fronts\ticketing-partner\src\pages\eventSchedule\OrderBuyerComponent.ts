import { Component, Prop, Vue } from "vue-facing-decorator";
import { DropdownButtonComponent } from "@groupk/vue3-interface-sdk";
import {
    OrderApiOut,
    TicketApiOut,
    CustomerApiOut,
    TemplateTicketApiOut,
    MetadataDescriptorApiOut, UuidScopeProduct_templateTicket, UuidScopeCustomer_customer, UuidScopeMetadata_descriptor
} from "@groupk/mastodon-core";
import {
    VisualScopedUuid,
    UuidUtils
} from "@groupk/horizon2-core";
import TicketDropdownActionsComponent from "../../components/TicketDropdownActionsComponent/TicketDropdownActionsComponent.vue";
import { EstablishmentUrlBuilder } from "../../../../../shared/utils/EstablishmentUrlBuilder";

@Component({
    components: {
        'dropdown-button': DropdownButtonComponent,
        'ticket-dropdown-actions': TicketDropdownActionsComponent,
    },
    emits: ['orderDropdownClicked']
})
export default class OrderBuyerComponent extends Vue {
    @Prop({ required: true }) order!: OrderApiOut;
    @Prop({ required: true }) tickets!: TicketApiOut[];
    @Prop({ required: true }) customers!: CustomerApiOut[];
    @Prop({ required: true }) templateTickets!: TemplateTicketApiOut[];
    @Prop({ required: true }) metadataDescriptors!: MetadataDescriptorApiOut[];
    @Prop({ required: true }) formattedTemporalDatetime!: string;

    displayOtherTickets: boolean = false;

    getOrderTickets(order: OrderApiOut, matchingTemporalDatetime: boolean = true): TicketApiOut[] {
        return this.tickets.filter((ticket) => {
            if(ticket.groupUid !== order.uid) return false;

            const ticketValidityStartDatetimeWithoutZ = ticket.validityStartDatetime?.replace('Z', '') ?? null;
            if(matchingTemporalDatetime) return ticketValidityStartDatetimeWithoutZ === this.formattedTemporalDatetime || ticketValidityStartDatetimeWithoutZ === null;
            else return ticketValidityStartDatetimeWithoutZ !== this.formattedTemporalDatetime;
        });
    }

    requireCustomerWithUid(customerUid: VisualScopedUuid<UuidScopeCustomer_customer>): CustomerApiOut {
        const customer = this.customers.find((customer) => customer.uid === customerUid);
        if (!customer) throw new Error('missing_customer');
        return customer;
    }

    requireTemplateTicketWithUid(templateTicketUid: VisualScopedUuid<UuidScopeProduct_templateTicket>): TemplateTicketApiOut {
        const templateTicket = this.templateTickets.find((templateTicket) => templateTicket.uid === templateTicketUid);
        if (!templateTicket) throw new Error('missing_template_ticket');
        return templateTicket;
    }

    getCustomerUrl(customerUid: VisualScopedUuid<UuidScopeCustomer_customer>): string {
        return EstablishmentUrlBuilder.buildUrl('/customers?uid=' + UuidUtils.visualToUuid(customerUid));
    }

    requireMetadataDescriptorWithUid(descriptorUid: VisualScopedUuid<UuidScopeMetadata_descriptor>): MetadataDescriptorApiOut {
        const descriptor = this.metadataDescriptors.find((descriptor) => descriptor.uid === descriptorUid);
        if (!descriptor) throw new Error('missing_descriptor');
        return descriptor;
    }

    orderDropdownClicked(order: OrderApiOut, action: any): void {
        this.$emit('orderDropdownClicked', order, action);
    }
}
