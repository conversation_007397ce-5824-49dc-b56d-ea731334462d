
<script lang="ts" src="./SettingsAccountsComponent.ts">
</script>

<style lang="sass">
@use './SettingsAccountsComponent.scss' as *
</style>

<template>
    <div class="accounts-settings-component">
        <layout
            :drawer-opened="selectedEstablishmentAccount !== null"
        >
            <template v-slot:content>
                <content-header :parameters="headerParameters" @search="search = $event"></content-header>

                <div class="main-content">
                    <div class="setting-group">
                        <div class="content accounts">
                            <div class="table-scroll-wrapper">
                                <table class="data-table">
                                    <thead>
                                    <tr>
                                        <td> Prénom </td>
                                        <td class="mobile-hidden"> Email </td>
                                        <td class="mobile-hidden"> Rôle </td>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <tr v-if="establishmentAccounts.length === 0">
                                        <td class="empty-row" colspan="100%"> Aucun compte disponible </td>
                                    </tr>
                                    <tr v-if="filteredEstablishmentAccounts.length === 0">
                                        <td class="empty-row" colspan="100%"> Aucun résultat </td>
                                    </tr>
                                    <tr :class="{selected: selectedEstablishmentAccount && selectedEstablishmentAccount.uid === account.uid}" v-for="account of filteredEstablishmentAccounts" @click="toggleSelectedEstablishmentAccount(account)">
                                        <td>
                                            <div class="account-name">
                                                <div class="profile-picture" v-if="account.firstname === 'auto-generated-profile' || account.type === 'DEVICE'">
                                                    <i class="fa-regular fa-mobile-phone" />
                                                </div>
                                                <div class="profile-picture" v-else-if="account.type === 'RESELLER'">
                                                    <i class="fa-regular fa-address-book"></i>
                                                </div>
                                                <div class="profile-picture" v-else>
                                                    <template v-if="account.firstname && account.firstname[0]"> {{ account.firstname[0] }}</template>
                                                    <template v-if="account.lastname && account.lastname[0]"> {{ account.lastname[0] }}</template>
                                                </div>
                                                <div class="data">
                                                    <template v-if="account.firstname === 'auto-generated-profile' || account.type === 'DEVICE'">
                                                          <span class="name" v-if="account.firstname === 'auto-generated-profile'">
                                                            Terminal cashless
                                                        </span>
                                                        <span class="email">
                                                            <template v-if="account.type === 'RESELLER'"> (Revendeur) </template>
                                                            {{ account.lastname }}
                                                        </span>
                                                    </template>
                                                    <template v-else>
                                                         <span class="name">
                                                            <template v-if="account.type === 'RESELLER'"> (Revendeur) </template>
                                                            {{ account.firstname }}
                                                            {{ account.lastname }}
                                                        </span>
                                                        <span class="email">
                                                            {{ account.email ?? '-' }}
                                                        </span>
                                                    </template>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="mobile-hidden"> {{account.email ?? '-'}} </td>
                                        <td class="mobile-hidden"> {{ getRole(account) ? $filters.Role(getRole(account)??'') : '-' }} </td>
                                    </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </template>
            <template v-slot:right>
                <div class="selected-establishment-account" v-if="selectedEstablishmentAccount">
                    <div class="close" @click="selectedEstablishmentAccount = null">
                        <i class="fa-regular fa-xmark"></i>
                        <span> Fermer </span>
                    </div>

                    <div class="header">
                        <div class="left">
                            <h2> {{ selectedEstablishmentAccount.firstname }} {{ selectedEstablishmentAccount.lastname }} </h2>
                        </div>
                    </div>

                    <div class="content">
                        <slot name="settings" :account="selectedEstablishmentAccount"></slot>

                        <span class="title"> Droits d'accès </span>

                        <div class="roles">
                            <div class="role" :class="{selected: role.id === getRole(selectedEstablishmentAccount)}" v-for="role in roles" @click="updateAccountRole(selectedEstablishmentAccount, role.id)">
                                <div class="top">
                                    <span class="title"> {{ role.title }} </span>
                                    <i class="fa-regular fa-check" v-if="role.id === getRole(selectedEstablishmentAccount)"></i>
                                </div>
                                <span class="description">
                                        {{ role.description }}
                                    </span>
                                <div class="bottom" v-if="role.id !== null">
                                    <button class="tertiary button" @click.stop="showRoleDetailsModal = role.id">
                                        <i class="fa-regular fa-info-circle"></i>
                                        Détails
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </template>
        </layout>

        <form-modal-or-drawer
            :state="showRoleDetailsModal"
            @close="showRoleDetailsModal = null"
            title="Details du rôle"
            subtitle="Liste précise des droits accordés par ce rôle"
        >
            <template v-slot:content>
                <div class="permissions" v-if="showRoleDetailsModal">
                    <div class="permission" :class="{ok: EstablishmentAccountPermissionModel.hasPermissionsFromList(permissionModel.getBackPermissionsWithFrontRole(frontAppId, showRoleDetailsModal??''), {list: [{applicationId: appId, id: permission}]})}" v-for="(translation, permission) of frontPermissionsTranslations">
                        <i class="fa-regular fa-check fa-fw" v-if="EstablishmentAccountPermissionModel.hasPermissionsFromList(permissionModel.getBackPermissionsWithFrontRole(frontAppId, showRoleDetailsModal??''), {list: [{applicationId: appId, id: permission}]})"></i>
                        <i class="fa-regular fa-xmark fa-fw" v-else></i>
                        {{ translation }}
                    </div>
                </div>
            </template>
            <template v-slot:buttons>
                <button class="button" @click="showRoleDetailsModal = null">
                    Fermer
                </button>
            </template>
        </form-modal-or-drawer>

        <establishment-account-form
            v-if="showAccountCreationModal"
            :establishment-uid="appState.requireUrlEstablishmentUid()"
            @created="createdAccount($event)"
            @close="showAccountCreationModal = false"
        ></establishment-account-form>

        <form-modal-or-drawer
            title="Générer un lien d'invitation"
            subtitle="Partagez ce lien avec la personne que vous souhaitez inviter"
            :state="showJoinRequestModal"
            @close="showJoinRequestModal = null"
        >
            <template v-slot:content>

                <div class="right-icon input-group copy-group" v-if="showJoinRequestModal">
                    <label> Lien d'invitation </label>
                    <div class="icon-group" @click="copyJoinRequestUrl()">
                        <input :value="computeJoinRequestUrl(showJoinRequestModal)" readonly />
                        <i class="fa-regular fa-copy"></i>
                    </div>
                </div>
            </template>
            <template v-slot:buttons>
                <button type="button" class="white button" @click="showJoinRequestModal = null"> Fermer </button>
            </template>
        </form-modal-or-drawer>
    </div>

</template>