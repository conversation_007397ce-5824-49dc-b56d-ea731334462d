#password-reset-page {
    height: 100%;
    width: 100%;
    display: flex;

    .step {
        padding: 80px;
        box-sizing: border-box;

        @media (max-width: 900px) {
            padding: 80px 20px;
            width: 100%;
            max-width: 100%;
        }
    }

    >.left {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: stretch;
        width: 100%;
        max-width: 600px;

        .loading-container {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .title-group {
            display: flex;
            align-items: flex-start;
            gap: 4px;

            .logo {
                height: 60px;
                margin-bottom: 20px;
            }


            h3 {
                font-size: 20px;
            }
            span  {
                font-size: 15px;
            }
        }

        input {
            border: solid 1px #A1A1A1;
        }

        .form {
            width: 100%;
        }

        .signup-link {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 5px;
            margin-top: 30px;

            .title {
                font-size: 15px;
            }

            .link {
                all: unset;
                cursor: pointer;
                font-size: 16px;
                font-weight: 600;
                text-decoration-line: underline;
                padding: 10px 20px;
                border-radius: 8px;

                &:hover {
                    background: var(--secondary-hover-color);
                }
            }
        }
    }

    >.right {
        flex-grow: 2;
        background-position: left;
        background-size: cover;
        z-index: 300;

        @media (max-width: 900px) {
            display: none;
        }
    }

    .littl-preview {
        flex-grow: 2;
        background: #F0FAF4;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 80px;

        @media (max-width: 900px) {
            display: none;
        }

        .littl-container {
            max-width: min(100%, 600px);
            max-height: 100%;

            .head {
                display: flex;
                flex-direction: column;
                gap: 4px;

                .title {
                    font-size: 22px;
                    font-weight: 700;
                }

                .subtitle {
                    font-size: 18px;
                    font-weight: 500;
                }
            }
        }

        video {
            max-width: 100%;
            max-height: 100%;
        }
    }

    .six-inputs {
        display: grid;
        grid-template-columns: 1fr 1fr 1fr 20px 1fr 1fr 1fr;
        grid-gap: 10px;

        .minus-separator {
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 22px;
        }

        input {
            font-size: 28px;
            text-align: center;
        }
    }
}