import {Component, Vue} from "vue-facing-decorator";
import KeypadComponent from "../KeypadComponent/KeypadComponent.vue";
import {KeypadKey} from "../KeypadComponent/KeypadComponent";

@Component({
    components: {
        'keypad': KeypadComponent
    }
})
export default class CashDrawerCashCountComponent extends Vue {
    selectedAmount: number = 0.01;

    coinsState: {amount: number, quantity: number}[] = [
        {amount: 0.01, quantity: 0},
        {amount: 0.05, quantity: 0},
        {amount: 0.1, quantity: 0},
        {amount: 0.2, quantity: 0},
        {amount: 0.5, quantity: 0},
        {amount: 1, quantity: 0},
        {amount: 2, quantity: 0},
    ];

    notesState: {amount: number, quantity: number}[] = [
        {amount: 5, quantity: 0},
        {amount: 10, quantity: 0},
        {amount: 20, quantity: 0},
        {amount: 50, quantity: 0},
        {amount: 100, quantity: 0},
        {amount: 200, quantity: 0},
        {amount: 500, quantity: 0},
    ];


    updateAmount(key: Keypad<PERSON><PERSON>) {
        let currentState = this.coinsState.find((state) => state.amount === this.selectedAmount);
        if(currentState === undefined) currentState = this.notesState.find((state) => state.amount === this.selectedAmount)
        if(currentState === undefined) return;

        if (key < 10) {
            if (currentState.quantity === 0) {
                currentState.quantity = key;
            } else {
                currentState.quantity = parseInt(currentState.quantity + '' + key);
            }
        } else {
            if (key === KeypadKey.BACKSPACE) {
                if ((currentState.quantity + "").length === 1) {
                    currentState.quantity = 0;
                } else {
                    currentState.quantity = parseInt((currentState.quantity + "").slice(0, -1));
                }
            } else  if (key === KeypadKey.NEXT) {
                this.goToNextState();
            }
        }
    }

    goToNextState() {
        let currentStateIndex = this.coinsState.findIndex((state) => state.amount === this.selectedAmount);
        if(currentStateIndex !== -1) {
            if((currentStateIndex + 1) === this.coinsState.length) this.selectedAmount = this.notesState[0].amount;
            else this.selectedAmount = this.coinsState[currentStateIndex + 1].amount;
        } else {
            let currentStateIndex = this.notesState.findIndex((state) => state.amount === this.selectedAmount);
            if(currentStateIndex !== -1) {
                if((currentStateIndex + 1) !== this.notesState.length) this.selectedAmount = this.notesState[currentStateIndex + 1].amount;
            }
        }
    }

    get totalCashAmount() {
        return this.coinsState.reduce((total, state) => total + state.amount * state.quantity * 100, 0)
            + this.notesState.reduce((total, state) => total + state.amount * state.quantity * 100, 0);
    }
}