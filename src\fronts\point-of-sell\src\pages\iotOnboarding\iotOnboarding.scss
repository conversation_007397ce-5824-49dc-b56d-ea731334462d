#iot-onboarding-page {
    height: 100%;
    display: grid;
    grid-template-columns: 1fr 1fr;

    >.left, >.right {
        flex-grow: 2;
    }

    >.left {
        display: flex;
        flex-direction: column;
        gap: 20px;
        background: white;
        align-items: center;
        justify-content: center;
        padding: 80px;
        box-sizing: border-box;

        h2 {
            margin: 0;
        }

        .big.button {
            padding: 15px 20px;
            width: 100%;
            font-weight: bold;
            box-sizing: border-box;

            &.margin-top {
                margin-top: 20px;
            }

            &.green {
                background: #1D9F42;;
            }
        }

        .form-error {
            text-align: initial;
        }
    }

    .bottom {
        position: absolute;
        bottom: 20px;
        font-size: 14px;
        color: grey;
    }

    .right {
        padding: 80px;
        background: #F2F4F7;
        overflow: auto;
        display: flex;
        flex-direction: column;
        justify-content: center;
        gap: 20px;

        h2 {
            margin: 0;
        }

        .status {
            display: flex;
            align-items: center;
            gap: 15px;
            padding: 20px;
            border-radius: 12px;
            background: white;

            &.green {
                background: #1D9F42;
                color: white;

                i {
                    color: white;
                }
            }

            &.red {
                background: #E12727;
                color: white;

                i {
                    color: white;
                }
            }

            .left {
                display: flex;
                flex-direction: column;
                gap: 4px;

                .title {
                    font-size: 16px;
                    font-weight: 700;
                }

                .description {
                    font-size: 15px;
                }

                .progress-group {
                    display: flex;
                    flex-direction: column;
                    gap: 4px;

                    .quota {
                        font-size: 14px;
                        margin-top: 10px;
                    }

                    .progress-bar {
                        border-radius: 6px;
                        height: 6px;
                        background: #D9D9D9;
                        width: 100%;

                        .progress {
                            height: 6px;
                            border-radius: 6px;
                            background: #1D9F42;
                        }
                    }
                }
            }

            i {
                font-size: 30px;
                color: #1D9F42;
            }


            &.color-red {
                .progress {
                    background: #E12727 !important;
                }

                i {
                    font-size: 30px;
                    color: #E12727;
                }
            }

        }
    }

    @media (max-width: 900px) {
        grid-template-columns: 1fr;


        >.left, >.right {
            padding: 20px !important;
        }

        .right {
            display: none;
        }
    }
}