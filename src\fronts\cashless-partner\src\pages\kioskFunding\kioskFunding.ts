import {Component, Vue} from "vue-facing-decorator";
import {FilterTableLayoutComponent, HoverableInfosComponent, SavedFilter} from "@groupk/vue3-interface-sdk";
import {
	ApplicationPermission, CashlessHttpCurrencyContract, CashlessHttpKioskFundingContract,
	CashlessHttpKioskFundingContractSearchConfig, CashlessHttpProfileContract,
	CashlessHttpSimpleProductCategoryContract,
	CashlessHttpSimpleProductContract,
	CurrencyApiOut,
	EstablishmentAccountPermissionModel,
	KioskFundingApiOut,
	UuidScopeCashless_transaction
} from "@groupk/mastodon-core";
import {ContentHeaderParameters, TableColumn, TablePagination, FilterParameters} from "@groupk/vue3-interface-sdk";
import {
	AutoWired,
	ScopedUuid,
	TypedQuerySearch, Uuid,
	UuidUtils,
	VisualScopedUuid
} from "@groupk/horizon2-core";
import {TableColumnsRepository} from "../../../../../shared/repositories/TableColumnsRepository";
import {OptionBuilder} from "vue-facing-decorator/dist/optionBuilder";
import {uuidScopeEstablishment, UuidScopeEstablishment} from "@groupk/mastodon-core";
import {randomUUID, Router} from "@groupk/horizon2-front";
import SidebarStateListener from "../../../../../shared/utils/SidebarStateListener";
import {CurrencyRepository} from "../../../../../shared/repositories/CurrencyRepository";
import {KioskFondingRepository} from "../../../../../shared/repositories/KioskFondingRepository";
import {SavedFiltersRepository} from "../../../../../shared/repositories/SavedFiltersRepository";
import {EstablishmentUrlBuilder} from "../../../../../shared/utils/EstablishmentUrlBuilder";
import {ComponentUtils} from "../../../../../shared/utils/ComponentUtils";

@Component({
	components: {
		'filter-table-layout': FilterTableLayoutComponent,
		'hoverable-infos': HoverableInfosComponent
	}
})
export default class KioskFoundingView extends Vue {
	establishmentUid!: VisualScopedUuid<UuidScopeEstablishment>;

	kiosksFunding: KioskFundingApiOut[] = [];
	selectedKioskFunding: KioskFundingApiOut|null = null;
	currencies: CurrencyApiOut[] = [];

	forbidden: boolean = false;
	loading: boolean = false;

	headerParameters: ContentHeaderParameters = {
		header: 'Rechargements sur la borne',
		subtitle: 'Liste des rechargements de support Cashless via une borne de recharge',
		actions: [],
		hideSearch: false,
		searchPlaceholder: 'Rechercher un support'
	}

	allowedFilters = CashlessHttpKioskFundingContractSearchConfig;
	filters: TypedQuerySearch<typeof CashlessHttpKioskFundingContractSearchConfig> = {};
	appliedFilters: TypedQuerySearch<typeof CashlessHttpKioskFundingContractSearchConfig> = {};

	filterParameters: { [filterName: string]: FilterParameters } = {
		'publicCreditedChipId': {
			translation: 'ID public de la puce',
			type: 'UNKNOWN',
			validation: (value: unknown) => {
				return typeof value === 'string' && value.trim() !== '' && value.length >= 8;
			}
		}
	}

	tableKey = 'cashless-kiosks-funding';
	tableColumns: TableColumn[] = [{
		title: 'ID', name: 'uid', displayed: false, mobileHidden: true
	}, {
		title: 'Devise', name: 'currency', displayed: true, mobileHidden: false
	}, {
		title: 'Montant fiat', name: 'fiatAmount', displayed: true, mobileHidden: false
	}, {
		title: 'Montant Cashless', name: 'cashlessAmount', displayed: true, mobileHidden: false
	}, {
		title: 'ID de Borne', name: 'creatorDevice', displayed: false, mobileHidden: true
	}, {
		title: 'ID du point de vente', name: 'profileUid', displayed: false, mobileHidden: true
	}, {
		title: 'Méthode de paiement', name: 'fiatPaymentMethod', displayed: true, mobileHidden: true
	}, {
		title: 'Status du paiement', name: 'fiatPaymentStatus', displayed: true, mobileHidden: false
	}, {
		title: 'Date de création', name: 'creationDatetime', displayed: false, mobileHidden: true
	}, {
		title: 'Date du statut final', name: 'finalStateDatetime', displayed: false, mobileHidden: true
	}];

	kiosks: string[] = ['Borne bar', 'Accueil', 'Partenaires']
	transactions: { id: string, kiosk: string, hour: string, amount: string }[] = [{
		id: randomUUID(),
		kiosk: this.kiosks[0],
		hour: '11h20',
		amount: '35,00 €'
	}];

	pagination: TablePagination = {
		totalResults: 1,
		resultsPerPage: 50,
		currentPage: 1,
		estimateTotal: false
	}

	savedFilters: SavedFilter[] = [];

	@AutoWired(TableColumnsRepository) accessor tableColumnsRepository!: TableColumnsRepository
	@AutoWired(CurrencyRepository) accessor currencyRepository!: CurrencyRepository
	@AutoWired(KioskFondingRepository) accessor kioskFondingRepository!: KioskFondingRepository
	@AutoWired(SidebarStateListener) accessor sidebarStateListener!: SidebarStateListener
	@AutoWired(SavedFiltersRepository) accessor savedFiltersRepository!: SavedFiltersRepository
	@AutoWired(Router) accessor router!: Router

	constructor(optionBuilder: OptionBuilder, vueInstance: any) {
		super(optionBuilder, vueInstance);

		this.loading = true;

		this.sidebarStateListener.setHiddenSidebar(false);
		this.sidebarStateListener.setMinimizedSidebar(false);

		let regexMatch = this.router.lastRouteRegexMatches;

		if (regexMatch && regexMatch[1]) {
			this.establishmentUid = UuidUtils.scopedToVisual<UuidScopeEstablishment>(regexMatch[1] as ScopedUuid<UuidScopeEstablishment>, uuidScopeEstablishment);
		}

		let savedPreferences = this.tableColumnsRepository.getColumnPreferences(this.tableKey, this.tableColumns);
		if(savedPreferences) this.tableColumns = savedPreferences;

		this.savedFilters = this.savedFiltersRepository.getFilters(this.tableKey) ?? [];
	}

	addTransaction() {
		const hour = Math.round(Math.random() * 23);

		this.transactions.push({
			id: randomUUID(),
			kiosk: this.kiosks[Math.round(Math.random() * 2)],
			hour: (hour < 10 ? '0' : '') + hour + 'h' + Math.round(Math.random() * 49 + 10),
			amount: Math.round(Math.random() * 150 + 1) + ',00 €'
		})

		setTimeout(() => {
			this.addTransaction();
		}, Math.round(Math.random() * 2500) + 500);
	}

	async mounted() {
		this.addTransaction();

		if(!ComponentUtils.hasPermissions((ownedPermissions: ApplicationPermission[]) => {
			return EstablishmentAccountPermissionModel.hasPermissionsForContracts(ownedPermissions, [
				CashlessHttpKioskFundingContract.search,
				CashlessHttpCurrencyContract.list,
			])
		})) {
			this.forbidden = true;
			this.loading = false;
			return;
		}

		this.kiosksFunding = (await this.kioskFondingRepository.callContract('search', {establishmentUid: this.establishmentUid}, {})).success();
		this.currencies = (await this.currencyRepository.callContract('list', {establishmentUid: this.establishmentUid}, undefined)).success();

		if(this.kiosksFunding.length === 0) {
			this.headerParameters.hideSearch = true;
			this.headerParameters.header = 'Borne de recharge';
			this.headerParameters.subtitle = 'Installez une ou des bornes de recharge automatiques 24h/24';
		}

		this.loading = false;
	}

	saveColumnPreferences(columns: TableColumn[]) {
		this.tableColumnsRepository.saveColumnsPreferences(this.tableKey, columns);
	}

	async searchKiosksFunding(filters: TypedQuerySearch<typeof CashlessHttpKioskFundingContractSearchConfig>, cursor: {after?: Uuid, before?: Uuid}|null = null) {
		this.loading = true;
		this.filters = {...filters};

		if(!cursor) {
			const data = (await this.kioskFondingRepository.callContract('searchCount', {establishmentUid: this.establishmentUid}, filters)).success();
			this.pagination.totalResults = data.rows ?? 0;
			this.pagination.currentPage = 1;
			this.pagination.estimateTotal = data.estimate;
		}

		filters.elementsPerPage = this.pagination.resultsPerPage;
		if(cursor && cursor.after) filters.cursorAfter = cursor.after;
		if(cursor && cursor.before) filters.cursorBefore = cursor.before;
		this.kiosksFunding = (await this.kioskFondingRepository.callContract('search', {establishmentUid: this.establishmentUid}, filters)).success();
		this.appliedFilters = filters;
		this.loading = false;
	}

	getCorrespondingCurrency(currencyId:  number) {
		const currency = this.currencies.find((currency) => currency.id === currencyId);
		if(!currency) throw new Error('missing_currency');
		return currency;
	}

	nextPage() {
		this.pagination.currentPage++;
		this.searchKiosksFunding(this.filters, {after: this.kiosksFunding[this.transactions.length - 1].uid})
	}

	previousPage() {
		this.pagination.currentPage--;
		this.searchKiosksFunding(this.filters, {before: this.kiosksFunding[0].uid})
	}

	saveFilter(name: string) {
		if(this.filters.filter) {
			this.savedFilters.push({
				name: name,
				filter: this.filters.filter
			});
			this.savedFiltersRepository.saveFilters(this.tableKey, this.savedFilters);
		}
	}

	selectFilter(savedFilter: SavedFilter) {
		this.filters.filter = savedFilter.filter as any;
		this.searchKiosksFunding(this.filters);
	}

	deleteFilter(index: number) {
		this.savedFilters.splice(index, 1);
		this.savedFiltersRepository.saveFilters(this.tableKey, this.savedFilters);
	}

	goToChipTransactions(transactionUid: VisualScopedUuid<UuidScopeCashless_transaction>) {
		window.open(EstablishmentUrlBuilder.buildUrl('/transactions?transaction=' + transactionUid), '_blank');
	}

}