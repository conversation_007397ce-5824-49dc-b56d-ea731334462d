import {Component, Prop, Vue} from "vue-facing-decorator";
import {
    MetadataDescriptorApiOut,
    MetadataDescriptorTemplateStringApiOut,
    MetadataDescriptorTemplateBoolApiOut,
    MetadataDescriptorTemplateStringListApiOut
} from "@groupk/mastodon-core";
import StringDescriptorComponent from "../StringDescriptorComponent/StringDescriptorComponent.vue";
import BoolDescriptorComponent from "../BoolDescriptorComponent/BoolDescriptorComponent.vue";
import StringListDescriptorComponent from "../StringListDescriptorComponent/StringListDescriptorComponent.vue";

@Component({
    components: {
        'string-descriptor': StringDescriptorComponent,
        'bool-descriptor': BoolDescriptorComponent,
        'string-list-descriptor': StringListDescriptorComponent,
    },
    emits: ['validated', 'close']
})
export default class MetadataDescriptorFormComponent extends Vue {
    @Prop({required: true}) metadataDescriptor!: MetadataDescriptorApiOut;
    @Prop({default: null}) defaultValue!: any|null;
    @Prop({default: true}) closable!: boolean;

    MetadataDescriptorTemplateStringApiOut = MetadataDescriptorTemplateStringApiOut;
    MetadataDescriptorTemplateBoolApiOut = MetadataDescriptorTemplateBoolApiOut;
    MetadataDescriptorTemplateStringListApiOut = MetadataDescriptorTemplateStringListApiOut;

    validated(value: any) {
        this.$emit('validated', value);
    }

    close() {
        this.$emit('close');
    }
}