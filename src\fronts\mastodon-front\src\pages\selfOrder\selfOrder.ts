import {Component, Vue} from "vue-facing-decorator";
import {AutoWired, VisualScopedUuid} from "@groupk/horizon2-core";
import {SimpleProductApiOut, UuidScopeIot_deviceApp} from "@groupk/mastodon-core";
import {ProductsRepository} from "../../../../../shared/repositories/ProductsRepository";
import {AppState} from "../../../../../shared/AppState";

interface SelfOrderProduct {
    idSimple: number,
    visualId: number,
    timer: number|null,
    iotUid: VisualScopedUuid<UuidScopeIot_deviceApp>|null
}

@Component({})
export default class selfOrderPage extends Vue {

    simpleProducts: SimpleProductApiOut[] = [];
    currentId: number = 0;

    selectedProduct: SimpleProductApiOut|null = null;

    config: {
        objectName: string,
        objectGender: 'M'|'F',
    } = {
        objectName: 'machine',
        objectGender: 'F'
    }

    products: SelfOrderProduct[] = [{
        idSimple: 3,
        visualId: 5,
        timer: null,
        iotUid: null
    }];

    error: string|null = null;

    @AutoWired(ProductsRepository) accessor productsRepository!: ProductsRepository;
    @AutoWired(AppState) accessor appState!: AppState;

    async mounted() {
        addEventListener('keydown', this.keyPressedHandler);

        this.simpleProducts = (await this.productsRepository.callContract('list', {establishmentUid: this.appState.requireUrlEstablishmentUid()}, undefined)).success()
    }

    unmounted() {
        removeEventListener('keyup', this.keyPressedHandler);
    }

    keyPressedHandler(event: KeyboardEvent) {
        if(this.error) {
            if(event.key === 'Backspace') {
                this.reset();
            }
        } else if(this.selectedProduct) {
            if(event.key === 'Backspace') {
                this.reset();
            }
            if(event.key === 'Enter') {
                // PAYER
            }
        } else {
            const numberKeys = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];
            if(numberKeys.includes(event.key)) {
                if ((this.currentId + "").length === 2) return;
                this.currentId = parseInt(this.currentId + event.key);
            } else {
                // Determine sunmi delete key
                if(event.key === 'Backspace') {
                    if ((this.currentId + "").length === 1) {
                        this.currentId = 0;
                    } else {
                        this.currentId = parseInt((this.currentId + "").slice(0, -1));
                    }
                }

                // Determine sunmi validate key
                if(event.key === 'Enter') {
                    this.choseProduct();
                }

                if(event.key === '.') {
                    window.location.reload();
                }

                if(event.key === '*') {
                    document.documentElement.requestFullscreen();
                }
            }
        }
    }

    choseProduct() {
        const correspondingProduct = this.products.find((product) => product.visualId === this.currentId);
        if(correspondingProduct === undefined) {
            this.error = `Le numéro ne correspond à aucun${this.config.objectGender === 'F' ? 'e' : ''} ${this.config.objectName}`;
            return;
        }

        const correspondingSimpleProduct = this.simpleProducts.find((simpleProduct) => simpleProduct.id === correspondingProduct.idSimple);
        if(correspondingSimpleProduct === undefined) {
            this.error = `Une erreur de configuration empêche l'utilisation de ce${this.config.objectGender === 'F' ? 'tte' : ''} ${this.config.objectName}`;
            return;
        }

        this.selectedProduct = correspondingSimpleProduct;
    }

    reset() {
        this.selectedProduct = null;
        this.error = null;
        this.currentId = 0;
    }

}