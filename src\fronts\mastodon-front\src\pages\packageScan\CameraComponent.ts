import {Component, Ref, Vue} from "vue-facing-decorator";

@Component({
    components: {},
    emits: ['photo-captured', 'close']
})
export default class CameraComponent extends Vue {
    stream: MediaStream|null = null;

    @Ref() video!: HTMLVideoElement;

    mounted() {
        this.startCamera();
    }

    unmounted() {
        if(this.stream) this.stream.getTracks().forEach(function(track) {
            track.stop();
        });

    }

    async startCamera() {
        try {
            this.stream = await navigator.mediaDevices.getUserMedia({ video: true });
            this.video.srcObject = this.stream;
        } catch (err) {
            console.error("Error accessing the camera", err);
        }
    }

    capturePhoto() {
        const canvas = document.createElement('canvas');
        canvas.width = this.video.videoWidth;
        canvas.height = this.video.videoHeight;
        const context = canvas.getContext('2d')!;
        context.drawImage(this.video, 0, 0, canvas.width, canvas.height);
        const dataUrl = canvas.toDataURL('image/jpeg');
        this.$emit('photo-captured', dataUrl);
        this.close();
    }

    close() {
        this.$emit('close');
    }
}