import {Component, Vue} from "vue-facing-decorator";
import {SidebarComponent, SidebarHeader, SidebarMenu, ApplicationsSwitcherComponent} from "@groupk/vue3-interface-sdk";
import {randomUUID, Router} from "@groupk/horizon2-front";
import {AutoWired, ScopedUuid, UuidUtils, VisualScopedUuid} from "@groupk/horizon2-core";
import {OptionBuilder} from "vue-facing-decorator/dist/optionBuilder";
import {PlatformDescriptorApiOut, uuidScopeEstablishment, UuidScopeEstablishment} from "@groupk/mastodon-core";
import {EstablishmentRepository} from "../../../../../shared/repositories/EstablishmentRepository";
import {PlatformRepository} from "../../../../../shared/repositories/PlatformRepository";
import SidebarStateListener from "../../../../../shared/utils/SidebarStateListener";
import {EstablishmentUrlBuilder} from "../../../../../shared/utils/EstablishmentUrlBuilder";
import EstablishmentSwitchComponent
	from "../../../../../shared/components/EstablishmentSwitchComponent/EstablishmentSwitchComponent.vue";

@Component({
	components: {
		sidebar: SidebarComponent,
		'application-switcher': ApplicationsSwitcherComponent,
		'establishment-switch': EstablishmentSwitchComponent
	},
})
export default class SidebarView extends Vue {
	opened: boolean = false;
	minimized: boolean = false;
	hidden: boolean = true;
	establishmentUid!: VisualScopedUuid<UuidScopeEstablishment>;

	displayMobileMenu!: boolean;
	header: SidebarHeader | undefined = {
		title: "Mastodon configuration",
		icon: "fa-square-c",
		subtitle: "Votre plateforme",
	};

	menus: SidebarMenu[] = [
		{
			separator: true,
			title: "Configuration",
			navigations: [
				{icon: "fa-euro", title: "Paiement", url: EstablishmentUrlBuilder.buildUrl('/payment-methods'), name: EstablishmentUrlBuilder.buildUrl('/payment-methods')},
				{icon: "fa-building", title: "Établissements", url: EstablishmentUrlBuilder.buildUrl('/establishments'), name: EstablishmentUrlBuilder.buildUrl('/establishments')},
				{icon: "fa-truck", title: "Tracking", url: EstablishmentUrlBuilder.buildUrl('/package-tracking'), name: EstablishmentUrlBuilder.buildUrl('/package-tracking')},
				{icon: "fa-code", title: "eCommerceConfig", url: EstablishmentUrlBuilder.buildUrl('/config'), name: EstablishmentUrlBuilder.buildUrl('/config')},
				{icon: "fa-cog", title: "Générateur de rôle", url: EstablishmentUrlBuilder.buildUrl('/role-generator'), name: EstablishmentUrlBuilder.buildUrl('/role-generator')},
				{icon: "fa-cog", title: "Metadata", url: EstablishmentUrlBuilder.buildUrl('/metadata'), name: EstablishmentUrlBuilder.buildUrl('/metadata')},
				// {icon: "fa-circle-question", title: "Aide", url: EstablishmentUrlBuilder.buildUrl('/help'), name: "configuration"}
			],
		}
		// {
		// 	separator: true,
		// 	title: "Paramètres",
		// 	navigations: [
		// 		{icon: "fa-cog", title: "Paramètres", url: EstablishmentUrlBuilder.buildUrl('/parameters'), name: EstablishmentUrlBuilder.buildUrl('/parameters')},
		// 	],
		// },
		// {
		// 	separator: true,
		// 	title: "On verra pour le titre",
		// 	navigations: [
		// 		{icon: "fa-mobile-signal-out", title: "Transactions", url: "/transactions", name: "transactions"},
		// 		{icon: "fa-mobile-signal-out", title: "Monnaie", url: "/currencies", name: "currencies"},
		// 	],
		// },
	];

	bottom: SidebarMenu[] = [
		{
			separator: false,
			navigations: [{
				icon: "fa-sign-out-alt", title: "Déconnexion",
				url: '/cas-redirect?disconnect=true',
				name: "disconnect"
			}],
		},
	];

	platformDescriptor!: PlatformDescriptorApiOut|null;
	selectedNavigation: string | undefined = undefined;

	/** UUID **/
	uid: string = randomUUID();

	@AutoWired(SidebarStateListener) accessor sidebarStateListener!: SidebarStateListener;
	@AutoWired(PlatformRepository) accessor platformRepository!: PlatformRepository;
	@AutoWired(EstablishmentRepository) accessor establishmentRepository!: EstablishmentRepository;
	@AutoWired(Router) accessor router!: Router;

	constructor(optionBuilder: OptionBuilder, vueInstance: any) {
		super(optionBuilder, vueInstance);

		this.opened = this.sidebarStateListener.openedSidebar;
		this.hidden = this.sidebarStateListener.hiddenSidebar;
		this.minimized = this.sidebarStateListener.minimizedSidebar;

		this.sidebarStateListener.listen(this.stateChanged);
	}

	stateChanged(event: {type: string; value: any}){
		if (event.type === "openedSidebar") {
			this.opened = event.value;
		} else if (event.type === "minimizedSidebar") {
			this.minimized = event.value;
		} else if (event.type === "hiddenSidebar") {
			this.hidden = event.value;
		}
	}

	async mounted() {
		this.selectedNavigation = window.location.pathname;

		this.router.hookOnAsync('newPageLoaded', (data) => {
			if (data.newRoute.location !== null) {
				this.selectedNavigation = window.location.pathname;
			}
			return Promise.resolve(data);
		});


		let regexMatch = this.router.lastRouteRegexMatches;

		if (regexMatch && regexMatch[1]) {
			this.establishmentUid = UuidUtils.scopedToVisual<UuidScopeEstablishment>(regexMatch[1] as ScopedUuid<UuidScopeEstablishment>, uuidScopeEstablishment);
			try {
				const establishment = (await this.establishmentRepository.callContract('get', {establishmentUid: this.establishmentUid}, undefined)).success()
				if(this.header) this.header.subtitle = establishment.name;
			} catch(er) {}
		}
	}

	async loadPlatform() {
		if(!this.platformDescriptor) this.platformDescriptor = (await this.platformRepository.callContract('get', undefined, undefined)).success();
	}

	navigationClicked(_value: string) {}
}
