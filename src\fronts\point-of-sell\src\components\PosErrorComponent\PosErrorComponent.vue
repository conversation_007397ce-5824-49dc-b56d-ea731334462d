<script lang="ts" src="./PosErrorComponent.ts">
</script>

<style lang="sass" scoped>
@import './PosErrorComponent.scss'
</style>

<template>
    <div class="pos-error-component">
        <i class="fa-regular fa-triangle-exclamation"></i>

        <template v-if="error === 'CANCEL_STATUS'">
            <h3> Problème de configuration </h3>
            <span class="details">
                Les status d'annulation n'ont pas été configurés correctement dans cet établissement
            </span>

            <button class="big black button" @click="back()">
                <i class="fa-regular fa-arrow-left"></i>
                Retour
            </button>
        </template>

        <template v-if="error === 'ESTABLISHMENT_ACCOUNT'">
            <h3> Compte introuvable pour cet établissement </h3>
            <span class="details">
                Le compte sur lequel cette caisse est connectée ne semble pas avoir accès à l'établissement actuel
            </span>

            <button class="big black button" @click="goToAuthentication()">
                <i class="fa-regular fa-arrow-right-from-bracket"></i>
                Changer de compte
            </button>
        </template>

        <template v-if="error === 'POINT_OF_SALE'">
            <h3> Cet appareil n'est lié à aucun point de vente </h3>
            <span class="details">
                Vous pouvez assigner cet appareil à un point de vente dans votre interface d'administration
            </span>

            <button class="big black button" @click="refresh()">
                <i class="fa-regular fa-arrows-rotate"></i>
                Recharger
            </button>

            <span> {{ serial }} </span>
        </template>

        <template v-if="error === 'AUTHENTICATION'">
            <h3> Accès refusé </h3>
            <span class="details">
                Une authentification est requise pour accéder à cette caisse
            </span>

            <button class="big black button" @click="goToAuthentication()">
                <i class="fa-regular fa-arrow-right-to-bracket"></i>
                Me connecter
            </button>
        </template>

        <template v-if="error === 'UNKNOWN_ERROR'">
            <h3> Erreur de chargement </h3>
            <span class="details">
                 Des données requises pour le fonctionnement de la caisse n'ont pas pu être chargées
            </span>
        </template>
    </div>
</template>