import {Component, Prop, Vue} from "vue-facing-decorator";
import {
	ContentHeaderComponent,
	ContentHeaderParameters,
	DropdownComponent,
	FormModalOrDrawerComponent,
	LayoutContentWithRightPanelComponent,
	ToggleComponent
} from "@groupk/vue3-interface-sdk";
import {AutoWired, SearchUtils, UuidUtils} from "@groupk/horizon2-core";
import {
	ApplicationFrontPermissionContract,
	ApplicationPermission,
	EstablishmentAccountApiOut,
	EstablishmentAccountPermission,
	EstablishmentAccountPermissionModel,
	EstablishmentAccountPermissionsApiIn,
	EstablishmentJoiningApiOut,
	EstablishmentJoiningCreateApiIn,
	MastodonEstablishmentAccountContractAggregate,
	MastodonPermissions,
} from "@groupk/mastodon-core";
import {PaymentPermissions} from "@groupk/mastodon-core";
import {CleaveDirective} from "../../directives/CleaveDirective";
import {EstablishmentAccountRepository} from "../../repositories/EstablishmentAccountRepository";
import {PosProfileRepository} from "../../repositories/PosProfileRepository";
import {AppState} from "../../AppState";
import {MainConfig} from "../../MainConfig";
import {ComponentUtils} from "../../utils/ComponentUtils";
import EstablishmentAccountFormComponent, {
	EstablishmentAccountFormComponentHasRequiredPermissions
} from "../EstablishmentAccountFormComponent/EstablishmentAccountFormComponent";
import {ApplicationPermissionContract} from "@groupk/mastodon-core";

@Component({
	directives: {
		cleave: CleaveDirective,
	},
	components: {
		'layout': LayoutContentWithRightPanelComponent,
		'dropdown': DropdownComponent,
		'toggle': ToggleComponent,
		'establishment-account-form': EstablishmentAccountFormComponent,
		'form-modal-or-drawer': FormModalOrDrawerComponent,
		'content-header': ContentHeaderComponent
	}
})
export default class SettingsAccountsComponent extends Vue {
	@Prop({required: true}) establishmentAccounts!: EstablishmentAccountApiOut[];
	@Prop({required: true}) frontAppId!: string;
	@Prop({required: true}) appId!: string;
	@Prop({required: true}) backPermissions!: ApplicationPermissionContract;
	@Prop({required: true}) frontPermissions!: ApplicationFrontPermissionContract;
	@Prop({required: true}) frontPermissionsTranslations!: Record<string, string>;
	@Prop({required: true}) roles!: {id: string|null, title: string, description: string}[];

	headerParameters: ContentHeaderParameters = {
		header: 'Comptes',
		subtitle: 'Ajouter des employés et gérer leurs droits d\'accès',
		actions: [{
			id: 'create',
			type: 'SIMPLE_ACTION',
			name: 'Ajouter un employé',
			icon: 'fa-regular fa-circle-plus',
			callback: this.addAccount
		}, {
			id: 'get-join-link',
			type: 'SIMPLE_ACTION',
			name: 'Obtenir un lien de partage',
			icon: 'fa-regular fa-link-simple',
			callback: this.generateInvitationLink
		}],
		searchPlaceholder: 'Rechercher un compte'
	}

	search: string = '';
	showAccountCreationModal: boolean = false;
	showJoinRequestModal: EstablishmentJoiningApiOut|null = null;
	selectedEstablishmentAccount: EstablishmentAccountApiOut|null = null;

	showRoleDetailsModal: string|null = null;

	permissionModel!: EstablishmentAccountPermissionModel;

	EstablishmentAccountPermissionModel = EstablishmentAccountPermissionModel;

	@AutoWired(EstablishmentAccountRepository) accessor establishmentAccountRepository!: EstablishmentAccountRepository;
	@AutoWired(PosProfileRepository) accessor posProfileRepository!: PosProfileRepository;
	@AutoWired(AppState) accessor appState!: AppState;
	@AutoWired(MainConfig) accessor mainConfig!: MainConfig;

	beforeMount() {
		this.permissionModel = new EstablishmentAccountPermissionModel();
		this.permissionModel.registerBackPermissionContract(MastodonPermissions);
		this.permissionModel.registerBackPermissionContract(PaymentPermissions);
		this.permissionModel.registerBackPermissionContract(this.backPermissions);
		this.permissionModel.registerFrontPermissionContract(this.frontPermissions);

		if(!ComponentUtils.hasPermissions(EstablishmentAccountFormComponentHasRequiredPermissions)) {
			ComponentUtils.disableActions(this.headerParameters, ['create']);
		}

		if(!ComponentUtils.hasPermissions((ownedPermissions: ApplicationPermission[]) => {
			return EstablishmentAccountPermissionModel.hasPermissionsForContracts(ownedPermissions, [
				MastodonEstablishmentAccountContractAggregate.createJoinRequest,
			])
		})) {
			ComponentUtils.disableActions(this.headerParameters, ['get-join-link']);
		}
	}

	toggleSelectedEstablishmentAccount(establishmentAccount: EstablishmentAccountApiOut) {
		if(!this.selectedEstablishmentAccount || this.selectedEstablishmentAccount.uid !== establishmentAccount.uid) {
			this.selectedEstablishmentAccount = establishmentAccount;
		} else {
			this.selectedEstablishmentAccount = null;
		}
	}

	createdAccount(account: EstablishmentAccountApiOut) {
		this.establishmentAccounts.push(account);
		this.showAccountCreationModal = false;
	}

	get filteredEstablishmentAccounts() {
		return SearchUtils.searchInTab(this.establishmentAccounts, (establishmentAccount) => {
			return [establishmentAccount.firstname, establishmentAccount.lastname, establishmentAccount.email ?? ''];
		}, this.search);
	}

	async generateInvitationLink() {
		const response = await this.establishmentAccountRepository.callContract('createJoinRequest', {establishmentUid: this.appState.requireUrlEstablishmentUid()}, new EstablishmentJoiningCreateApiIn({
			validityDays: 3,
			comment: undefined
		}));

		if(response.isSuccess()) {
			this.showJoinRequestModal = response.success();
		} else {
			// this.appBus.emit('emit-toast', {
			// 	title: 'La génération du lien à échoué',
			// 	description: 'Veuillez réessayer plus tard',
			// 	duration: 3000,
			// 	type: 'ERROR',
			// 	closable: true
			// })
		}
	}

	computeJoinRequestUrl(joining: EstablishmentJoiningApiOut) {
		return this.mainConfig.configuration.casFrontUrl + 'establishment/' + UuidUtils.visualToScoped(this.appState.requireUrlEstablishmentUid()) + '/login?platform=' + this.appState.requirePlatformId() + '&establishmentInvitation=' + joining.uid;
	}

	async updateAccountRole(establishmentAccount: EstablishmentAccountApiOut, roleId: string|null) {
		const permissionApiIn = new EstablishmentAccountPermissionsApiIn({});

		const permissionModel = new EstablishmentAccountPermissionModel();
		permissionModel.registerBackPermissionContract(MastodonPermissions);
		permissionModel.registerBackPermissionContract(this.backPermissions);
		permissionModel.registerBackPermissionContract(PaymentPermissions);
		permissionModel.registerFrontPermissionContract(this.frontPermissions);

		for(let permission of establishmentAccount.roles) {
			if(permissionModel.doesRoleExist(permission.application, permission.name)) {
				permissionApiIn.rolesToRemove.push(permission);
			}
		}

		if(roleId) {
			permissionApiIn.rolesToAdd.push(new EstablishmentAccountPermission({
				application: this.frontPermissions.applicationId,
				name: roleId
			}));
		}

		const response = await this.establishmentAccountRepository.callContract('update_permissions', {establishmentUid: this.appState.requireUrlEstablishmentUid(), establishmentAccountUid: establishmentAccount.uid}, permissionApiIn);
		if(response.isSuccess()) {
			const index = this.establishmentAccounts.findIndex(account => account.uid === response.success().uid);
			if (index !== -1) {
				this.establishmentAccounts.splice(index, 1, response.success());
			}
			if(this.selectedEstablishmentAccount && this.selectedEstablishmentAccount.uid === response.success().uid) {
				this.selectedEstablishmentAccount = response.success();
			}
		}
	}

	getRole(establishmentAccount: EstablishmentAccountApiOut): string|null {
		const roles = this.frontPermissions.roles.map((role) => role.id) as string[];
		for(let permission of establishmentAccount.roles) {
			if(permission.application === this.frontPermissions.applicationId && roles.includes(permission.name)) {
				return permission.name;
			}
		}
		return null;
	}

	addAccount() {
		this.showAccountCreationModal = true
	}

	copyJoinRequestUrl() {
		if(!this.showJoinRequestModal) return;
		try {
			console.log(this.computeJoinRequestUrl(this.showJoinRequestModal));
			navigator.clipboard.writeText(this.computeJoinRequestUrl(this.showJoinRequestModal));

			// this.appBus.emit('emit-toast', {
			// 	title: 'Copié !',
			// 	description: 'Le lien d\'invitation à été copié.',
			// 	duration: 1500,
			// 	type: 'SUCCESS',
			// 	closable: true
			// });
		} catch(err) {
			// this.appBus.emit('emit-toast', {
			// 	title: 'Impossible de copier',
			// 	description: 'Votre navigateur semble interdire de copier des éléments',
			// 	duration: 3000,
			// 	type: 'ERROR',
			// 	closable: true
			// });
		}
	}
}