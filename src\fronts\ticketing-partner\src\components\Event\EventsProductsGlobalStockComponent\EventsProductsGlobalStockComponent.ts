import {Component, Prop, Vue} from "vue-facing-decorator";
import {FormModalOrDrawerComponent} from "@groupk/vue3-interface-sdk";
import {EventFull} from "../../../pages/event/event";
import {StockRepository} from "../../../../../../shared/repositories/StockRepository";
import {AutoWired, ScopedUuid, UuidUtils} from "@groupk/horizon2-core";
import {AppState} from "../../../../../../shared/AppState";
import {
    ProductHttpStockSimpleContract,
    StockMovementApiIn,
    StockSimpleApiIn,
    StockType, UuidScopeProduct_stockSimple,
    uuidScopeProduct_stockSimple
} from "@groupk/mastodon-core";
import {translateResponseError} from "../../../../../../shared/RepositoryExtensions";
import {EventRepository} from "../../../../../../shared/repositories/EventRepository";
import {EventData} from "../../../../../../shared/mastodonCoreFront/product/EventData";

@Component({
    components: {
        'form-modal-or-drawer': FormModalOrDrawerComponent,
    },
    emits: ['close', 'created-stock', 'updated-stock']
})
export default class EventsProductsGlobalStockComponent extends Vue {
    @Prop({required: true}) eventFull!: EventFull;

    opened: boolean = false;
    saving: boolean = false;

    globalStock: number = 0;
    error: string|null = null;

    @AutoWired(StockRepository) accessor stockRepository!: StockRepository;
    @AutoWired(EventRepository) accessor eventRepository!: EventRepository;
    @AutoWired(AppState) accessor appState!: AppState;

    mounted() {
        setTimeout(() => this.opened = true, 0);

        this.globalStock = this.getCurrentGlobalStockQuantity()
    }

    private getGlobalStockUid(): ScopedUuid<UuidScopeProduct_stockSimple> | null {
        const stockUidList = this.eventFull.event.indicativeGlobalStockUidList;
        if (stockUidList.length === 0) return null;

        const firstStockUid = stockUidList[0];
        return UuidUtils.isScoped(firstStockUid, uuidScopeProduct_stockSimple)
            ? firstStockUid
            : null;
    }

    private getCurrentGlobalStockQuantity(): number {
        const globalStockUid = this.getGlobalStockUid();
        if (!globalStockUid) return 0;

        const visualUid = UuidUtils.scopedToVisual(globalStockUid, uuidScopeProduct_stockSimple);
        const stock = this.eventFull.stockSimpleList.find(stock => stock.uid === visualUid);
        return stock?.currentQuantity ?? 0;
    }

    async save() {
        this.error = null;
        this.saving = true;

        if(this.globalStock < 0) {
            this.error = 'Le stock global ne peut pas être négatif';
            return;
        }

        const globalStockUid = this.getGlobalStockUid();
        if(globalStockUid) {
            const response = await this.stockRepository.callContract(
                'createStockMovementWithoutInventory',
                {establishmentUid: this.appState.requireUrlEstablishmentUid(), stockUid: globalStockUid},
                new StockMovementApiIn({
                    stockUid: UuidUtils.scopedToVisual<UuidScopeProduct_stockSimple>(globalStockUid, uuidScopeProduct_stockSimple),
                    quantity: this.globalStock,
                    set: true
                })
            );

            if(response.isError()) {
                this.error = translateResponseError<typeof ProductHttpStockSimpleContract, 'createStockMovementWithoutInventory'>(response, {});
                this.saving = false;
                return;
            }

            // Emit event for updated global stock
            this.$emit('updated-stock', {
                stockUid: UuidUtils.scopedToVisual<UuidScopeProduct_stockSimple>(globalStockUid, uuidScopeProduct_stockSimple),
                newQuantity: this.globalStock
            });
        } else {
            const response = await this.stockRepository.callContract('createStockSimple', {establishmentUid: this.appState.requireUrlEstablishmentUid()}, new StockSimpleApiIn({
                type: StockType.SIMPLE,
                initialQuantity: this.globalStock,
                minQuantity: 0
            }));

            if(response.isError()) {
                this.error = translateResponseError<typeof ProductHttpStockSimpleContract, 'createStockSimple'>(response, {});
                this.saving = false;
                return;
            }

            const createdStock = response.success();
            this.eventFull.event.indicativeGlobalStockUidList.push(UuidUtils.visualToScoped<UuidScopeProduct_stockSimple>(createdStock.uid));

            await this.eventRepository.callContract(
                'update',
                {establishmentUid: this.appState.requireUrlEstablishmentUid(), eventUid: this.eventFull.event.uid},
                new EventData(this.eventFull.event).toApi()
            );

            // Emit event for created stock
            this.$emit('created-stock', createdStock);
        }

        this.saving = false;

        this.close();
    }

    close() {
        this.opened = false;
        setTimeout(() => this.$emit('close'), 300);
    }
}