<script lang="ts" src="./LinkTagsFormComponent.ts">
</script>

<style lang="sass">
@use './LinkTagsFormComponent.scss' as *
</style>

<template>
    <div class="link-tags-form-component">
        <div class="label-form-dimmer" @click="close()"></div>
        <div class="label-form">
            <template v-if="existingTags.length > 0">
                <span class="title"> Labels existants </span>
                <div class="existing-labels">
                    <div class="small label" :class="{grey: !selectedTags.includes(tag)}" v-for="tag in existingTags" @click="toggleTag(tag)"> {{ tag }} </div>
                </div>
            </template>

            <span class="title"> Nouveau </span>

            <div class="input-group">
                <input v-model="newTag" placeholder="Nom du label" />
            </div>

            <div class="form-error" v-if="error">
                <i class="fa-solid fa-exclamation-circle"></i>
                <div class="details">
                    <span class="title"> Erreur </span>
                    <span class="description"> Sauvegarde impossible </span>
                </div>
            </div>

            <button class="button" :class="{loading: saving, disabled: saving}" @click="save()"> Valider </button>
        </div>
    </div>
</template>