import {Component, Vue} from "vue-facing-decorator";
import {GetInstance} from "@groupk/horizon2-core";
import {BluetoothManager} from "../../class/BluetoothManager";
import {NativeInterface} from "@groupk/native-bridge";

@Component({
	emits: ['bluetooth-scanned']
})
export default class BluetoothDeviceComponent extends Vue {
	isNative: boolean = false;

	// No AutoWired because with no default value, var is not watched by vue
	bluetoothManager: BluetoothManager = GetInstance(BluetoothManager);

	beforeMount() {
		this.isNative = NativeInterface.instance.available();
	}
}