import {Component, Vue} from "vue-facing-decorator";
import {
	FilterTableLayoutComponent,
} from "@groupk/vue3-interface-sdk";
import {ContentHeaderParameters, TableColumn} from "@groupk/vue3-interface-sdk";
import {
	AutoWired,
} from "@groupk/horizon2-core";
import {TableColumnsRepository} from "../../../../../shared/repositories/TableColumnsRepository";
import {Router} from "@groupk/horizon2-front";
import {PaymentMethodApiOut} from "@groupk/mastodon-core";
import {PaymentMethodsRepository} from "../../../../../shared/repositories/PaymentMethodsRepository";
import PaymentMethodFormComponent from "../../components/PaymentMethodFormComponent/PaymentMethodFormComponent.vue";
import SidebarStateListener from "../../../../../shared/utils/SidebarStateListener";
import {AppState} from "../../../../../shared/AppState";

@Component({
	components: {
		'filter-table-layout': FilterTableLayoutComponent,
		'payment-method-form': PaymentMethodFormComponent

	}
})
export default class PaymentMethodsView extends Vue {
	paymentMethods: PaymentMethodApiOut[] = [];
	selectedPaymentMethod: PaymentMethodApiOut|null = null;
	editingMethod: PaymentMethodApiOut|null = null;

	headerParameters: ContentHeaderParameters = {
		header: 'Moyens de paiement',
		subtitle: 'Liste des moyens de paiement',
		actions: [{
			type: 'SIMPLE_ACTION',
			name: 'Nouvelle méthode',
			icon: 'fa-regular fa-circle-plus',
			callback: this.createMethod
		}],
		hideSearch: true,
		searchPlaceholder: 'Rechercher un moyen de paiement'
	}

	allowedFilters = {};

	tableKey = 'mastodon-payment-methods';
	tableColumns: TableColumn[] = [{
		title: 'Identifiant', name: 'uid', displayed: false, mobileHidden: true
	}, {
		title: 'Nom', name: 'name', displayed: true, mobileHidden: false
	}, {
		title: 'Type', name: 'type', displayed: true, mobileHidden: false
	}, {
		title: 'Date de création', name: 'creationDatetime', displayed: true, mobileHidden: true
	}];

	showCreationModal: boolean = false;
	loading: boolean = false;

	@AutoWired(PaymentMethodsRepository) accessor paymentMethodsRepository!: PaymentMethodsRepository
	@AutoWired(TableColumnsRepository) accessor tableColumnsRepository!: TableColumnsRepository
	@AutoWired(SidebarStateListener) accessor sidebarStateListener!: SidebarStateListener
	@AutoWired(AppState) accessor appState!: AppState
	@AutoWired(Router) accessor router!: Router

	beforeMount() {
		this.loading = true;

		this.sidebarStateListener.setHiddenSidebar(false);
		this.sidebarStateListener.setMinimizedSidebar(false);

		let savedPreferences = this.tableColumnsRepository.getColumnPreferences(this.tableKey, this.tableColumns);
		if(savedPreferences) this.tableColumns = savedPreferences;
	}

	async mounted() {
		this.paymentMethods = (await this.paymentMethodsRepository.callContract('list', {establishmentUid: this.appState.requireUrlEstablishmentUid()}, {})).success();
		this.loading = false;
	}

	toggleSelectedMethod(paymentMethod: PaymentMethodApiOut, event?: Event) {
		if (event && event.type === "dblclick") {
			this.editingMethod = paymentMethod;
			this.showCreationModal = true;
			return;
		}

		const selection = window.getSelection();
		if (selection && !selection.isCollapsed) {
			return;
		}
	}

	saveColumnPreferences(columns: TableColumn[]) {
		this.tableColumnsRepository.saveColumnsPreferences(this.tableKey, columns);
	}

	createMethod() {
		this.showCreationModal = true;
	}

	updatedMethod(updatedPaymentMethod: PaymentMethodApiOut) {
		const index = this.paymentMethods.findIndex((paymenMethod) => paymenMethod.uid == updatedPaymentMethod.uid);
		if(index !== -1) {
			this.paymentMethods.splice(index, 1, updatedPaymentMethod);
			if(this.selectedPaymentMethod && this.selectedPaymentMethod.uid === updatedPaymentMethod.uid) this.selectedPaymentMethod = updatedPaymentMethod;
		} else {
			this.paymentMethods.push(updatedPaymentMethod);
		}
	}

}