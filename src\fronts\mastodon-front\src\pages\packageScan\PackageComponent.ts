import {Component, Prop, Vue} from "vue-facing-decorator";
import {AutoWired, FetchError, genericFetch} from "@groupk/horizon2-core";
import {AirtableRecord} from "./packageScan";
import {BarcodeExternalNative_QRCodeReaderReturn, VibratorNative} from "@groupk/native-bridge";
import PackageValidationComponent from "./PackageValidationComponent.vue";
import PackageFormComponent from "./PackageFormComponent.vue";
import {BarcodeScannerManager} from "./BarcodeScannerManager";
import ItemActionsComponent, {ItemAction} from "./ItemActionsComponent.vue";
import CameraComponent from "./CameraComponent.vue";

@Component({
    components: {
        'package-validation': PackageValidationComponent,
        'package-form': PackageFormComponent,
        'item-actions': ItemActionsComponent,
        'camera': CameraComponent
    },
    emits: ['close']
})
export default class PackageComponent extends Vue {
    @Prop({required: true}) packageData!: AirtableRecord;
    @Prop({required: true}) objectTranslations!: AirtableRecord[];

    objectsInPackage: AirtableRecord[] = [];

    deleting: boolean = false;
    scanning: boolean = false;
    showValidation: boolean = false;
    showForm: boolean = false;
    showActionsForObject: AirtableRecord|null = null;
    showCamera: { opened: boolean, forObject: AirtableRecord }|null = null;
    showObjectImage: AirtableRecord|null = null;
    showObjectReturnImage: AirtableRecord|null = null;
    alreadyIn: AirtableRecord|null = null;
    loading: boolean = true;

    @AutoWired(BarcodeScannerManager) accessor barcodeScannerManager!: BarcodeScannerManager;
    @AutoWired(VibratorNative) accessor vibratorNative!: VibratorNative;

    async mounted() {
        const response = await genericFetch({
            url: 'https://api.airtable.com/v0/app9UWPLidwgXu3jB/objects?maxRecords=80&view=Grid%20view',
            method: 'GET',
            headers: {
                'Authorization': 'Bearer pat5zDgVSdUwXg2IJ.3c32972e7593e4dd64c45b24b65d57a07ddb30ccb2c56a2d7fc97bc247af9515'
            }
        });

        if(response instanceof FetchError) {
        } else {
            const json = await response.json();
            this.objectsInPackage = json.records.filter((object: AirtableRecord) => object.fields.packageId[0] === this.packageData.id);
        }

        this.setupListener();

        this.loading = false;
    }

    setupListener() {
        this.barcodeScannerManager.customCallback = this.barCodeRead;
    }

    async barCodeRead(data: BarcodeExternalNative_QRCodeReaderReturn) {
        if(this.scanning) return;

        if(this.alreadyIn) {
            this.vibratorNative.vibrate(1000).catch(() => {});
            return;
        }

        this.scanning = true;
        await this.addObjectToPackage(data.content, this.packageData.id);
    }

    async addObjectToPackage(objectId: string, packageId: string) {

        if(this.isAlreadyIn(objectId)) {
            this.alreadyIn = this.isAlreadyIn(objectId);
            this.scanning = false;
            return;
        }

        const response = await genericFetch({
            url: 'https://api.airtable.com/v0/app9UWPLidwgXu3jB/objects',
            method: 'POST',
            headers: {
                'Authorization': 'Bearer pat5zDgVSdUwXg2IJ.3c32972e7593e4dd64c45b24b65d57a07ddb30ccb2c56a2d7fc97bc247af9515',
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                "records": [
                    {
                        "fields": {
                            "code": objectId,
                            "packageId": [packageId]
                        }
                    }
                ]
            })
        });

        if(response instanceof FetchError) {
        } else {
            const json = await response.json();
            this.objectsInPackage.push(json.records[0]);
        }

        this.scanning = false;
    }

    async deleteObjectFromPackage(airtableId: string) {
        this.deleting = true;

        const response = await genericFetch({
            url: 'https://api.airtable.com/v0/app9UWPLidwgXu3jB/objects?records[]=' + airtableId,
            method: 'DELETE',
            headers: {
                'Authorization': 'Bearer pat5zDgVSdUwXg2IJ.3c32972e7593e4dd64c45b24b65d57a07ddb30ccb2c56a2d7fc97bc247af9515',
                'Content-Type': 'application/json'
            }
        });

        if(response instanceof FetchError) {
        } else {
            const index = this.objectsInPackage.findIndex((object) => object.id === airtableId);
            if(index !== -1) {
                this.objectsInPackage.splice(index, 1);
            }
        }

        this.alreadyIn = null;
        this.deleting = false;
    }

    async actionClicked(action: ItemAction) {
        if(!this.showActionsForObject) return;
        if(action.id === 'delete') {
            this.scanning = true;
            await this.deleteObjectFromPackage(this.showActionsForObject.id);
            this.scanning = false;
        } else if(action.id === 'take-picture') {
            this.showCamera = {opened: true, forObject: this.showActionsForObject};
        } else if(action.id === 'show-image') {
            this.showObjectImage = this.showActionsForObject;
        } else if(action.id === 'show-return-image') {
            this.showObjectReturnImage = this.showActionsForObject;
        }
    }

    async photoCaptured(dataUrl: string) {
        if(!this.showCamera) return;
        this.scanning = true;

        // Extract image type from dataUrl for filename
        const imageType = dataUrl.split(';')[0].split('/')[1] || 'png';
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const filename = `captured_image_${timestamp}.${imageType}`;

        const responseFile = await genericFetch({
            url: `https://content.airtable.com/v0/app9UWPLidwgXu3jB/${this.showCamera.forObject.id}/images/uploadAttachment`,
            method: 'POST',
            headers: {
                'Authorization': 'Bearer pat5zDgVSdUwXg2IJ.3c32972e7593e4dd64c45b24b65d57a07ddb30ccb2c56a2d7fc97bc247af9515',
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                "contentType": "image/" + imageType,
                "file": dataUrl,
                "filename": filename
            })
        });

        if(responseFile instanceof FetchError) {
        } else {}

        this.showCamera = null;
        this.scanning = false;
    }

    updatePackage(packageData: AirtableRecord, closeThen: boolean = false) {
        this.packageData.fields = packageData.fields;
        this.$emit('updated-package', packageData);

        if(closeThen) this.close();
    }

    updateObject(objectData: AirtableRecord) {
        const index = this.objectsInPackage.findIndex((object) => object.id === objectData.id);
        if(index !== -1) {
            this.objectsInPackage.splice(index, 1, objectData);
        }
    }

    isAlreadyIn(objectId: string): AirtableRecord|null {
        return this.objectsInPackage.find((object) => object.fields.code === objectId) ?? null;
    }

    getObjectTranslation(objectId: string) {
        const translationObject = this.objectTranslations.find((object) => object.fields.code === objectId);
        if(translationObject) {
            return translationObject.fields.brand + ' ' + translationObject.fields.name + ' - ' + objectId;
        } else {
            return objectId;
        }
    }

    getObjectActions(objectData: AirtableRecord) {
        const actions: ItemAction[] = [
            {id: 'take-picture', icon: 'fa-regular fa-camera', text: 'Ajouter une image'},
            {id: 'delete', icon: 'fa-regular fa-trash-alt', text: 'Supprimer'}
        ];

        if(objectData.fields.returnImages) {
            actions.unshift({id: 'show-return-image', icon: 'fa-regular fa-image', text: 'Visualiser les images de retour'});
        }

        if(objectData.fields.images) {
            actions.unshift({id: 'show-image', icon: 'fa-regular fa-image', text: 'Visualiser les images'});
        }

        return actions;
    }

    close() {
        this.$emit('close');
    }
}