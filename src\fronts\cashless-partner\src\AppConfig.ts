export default class AppConfig {
	// Plateform /
	pl_name: string | null = null;
	pl_apiUrl?: string;
	pl_serviceUrl?: string;

	// Identity /
	identityGateway_apiUrl?: string;
	identityGateway_serviceUrl?: string;
	identityGateway_clientId?: string;

	// Invoice /
	invoiceGateway_apiUrl?: string;
	invoiceGateway_serviceUrl?: string;
	invoiceGateway_clientId?: string;

	// Cashless /
	cashlessGateway_apiUrl?: string;

	// Gaia /
	gaiaGateway_apiUrl?: string;
	gaiaUser_serviceUrl?: string;

	// uri /
	uri?: {qrCode?: string};

	// MDM /
	mdm_apiUrl?: string;
	mdm_serviceUrl?: string;

	sentryDsn?: string;
	sentryEnvironment?: string;

	version?: string;
	isDevelop() {
		return this.version === "develop";
	}

	constructor(obj: any = {}) {
		if (typeof obj === "string") {
			obj = JSON.parse(obj);
		}
		AppConfig.buildFromJson(this, obj);
	}

	private static buildFromJson(instance: any, json: any) {
		if (json) {
			for (let prop in json) {
				if (json.hasOwnProperty(prop)) {
					instance[prop] = json[prop];
				}
			}
		}
	}

	static getFromServer(url: string = "/config.json"): Promise<AppConfig> {
		const injectedContent = document.getElementById("config")?.innerHTML.trim();
		let prom;
		if (injectedContent === undefined || injectedContent === '"__INJECTED_CONFIG__"') {
			prom = fetch(url).then((response) => {
				return response.json();
			});
		} else {
			prom = Promise.resolve(JSON.parse(injectedContent));
		}

		return prom.then((json) => new AppConfig(json));
	}

	isValid(): boolean {
		return false;
	}
}
