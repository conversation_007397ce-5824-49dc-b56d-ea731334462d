import {Component, Vue} from "vue-facing-decorator";
import {
	ContentHeaderComponent,
	LayoutContentWithRightPanelComponent,
	ModalOrDrawerComponent,
	ContentHeaderParameters, ShortcutsManager,
	ForbiddenMessageComponent
} from "@groupk/vue3-interface-sdk";
import {
	AutoWired,
	ScopedUuid,
	Uuid, UuidUtils,
	VisualScopedUuid
} from "@groupk/horizon2-core";
import {
	CashlessHttpEstablishmentAccountProfileContract,
	CashlessHttpEstablishmentContract,
	CashlessHttpProfileContract,
	CashlessHttpSimpleProductCategoryContract,
	EstablishmentAccountProfileApi,
	EstablishmentDeviceApiOut, IotHttpEstablishmentDeviceContract,
	uuidScopeEstablishment,
	UuidScopeEstablishment
} from "@groupk/mastodon-core";
import {
	CashlessEstablishmentApi,
	ProfileApiIn,
	ProfileApiOut,
	SimpleProductCategoryApiOut,
	ApplicationPermission,
	EstablishmentAccountPermissionModel,
} from "@groupk/mastodon-core";
import {randomUUID, Router} from "@groupk/horizon2-front";
import {Draggable, Sortable, SortableStopEvent} from "@shopify/draggable";
import {CategoriesRepository} from "../../../../../shared/repositories/CategoriesRepository";
import {TableColumnsRepository} from "../../../../../shared/repositories/TableColumnsRepository";
import SidebarStateListener from "../../../../../shared/utils/SidebarStateListener";
import {OptionBuilder} from "vue-facing-decorator/dist/optionBuilder";
import {ProfilesRepository} from "../../../../../shared/repositories/ProfilesRepository";
import ProfileFormComponent from "../../components/ProfileFormComponent/ProfileFormComponent.vue";
import ImportProfileFormComponent from "../../components/ImportProfileModalComponent/ImportProfileModalComponent.vue";
import {ProfileImportExportHelper} from "../../../../../shared/utils/ProfileImportExportHelper";
import {EstablishmentUrlBuilder} from "../../../../../shared/utils/EstablishmentUrlBuilder";
import CashlessEstablishmentFormComponent from "../../components/CashlessEstablishmentFormComponent/CashlessEstablishmentFormComponent.vue";
import {
	CashlessEstablishmentFormComponentHasRequiredPermissions
} from "../../components/CashlessEstablishmentFormComponent/CashlessEstablishmentFormComponent";
import {CashlessEstablishmentRepository} from "../../../../../shared/repositories/CashlessEstablishmentRepository";
import {AppState} from "../../../../../shared/AppState";
import {EstablishmentDevicesRepository} from "../../../../../shared/repositories/EstablishmentDevicesRepository";
import {
	EstablishmentAccountProfileRepository
} from "../../../../../shared/repositories/EstablishmentAccountProfileRepository";
import {ComponentUtils} from "../../../../../shared/utils/ComponentUtils";
import {ProfileFormComponentHasRequiredPermissions} from "../../components/ProfileFormComponent/ProfileFormComponent";
import {ImportProfileModalComponentHasRequiredPermissions} from "../../components/ImportProfileModalComponent/ImportProfileModalComponent";

@Component({
	components: {
		'layout': LayoutContentWithRightPanelComponent,
		'content-header': ContentHeaderComponent,
		'modal-or-drawer': ModalOrDrawerComponent,
		'profile-form': ProfileFormComponent,
		'import-profile-modal': ImportProfileFormComponent,
		'cashless-establishment-form': CashlessEstablishmentFormComponent,
		'forbidden-message': ForbiddenMessageComponent,
	}
})
export default class ProfilesView extends Vue {
	forbidden: boolean = false;
	establishmentUid!: VisualScopedUuid<UuidScopeEstablishment>;

	profiles: ProfileApiOut[] = [];
	categories: SimpleProductCategoryApiOut[] = [];
	devices: EstablishmentDeviceApiOut[] = [];
	accountProfiles: EstablishmentAccountProfileApi[] = [];
	cashlessEstablishment!: CashlessEstablishmentApi;

	profilesSearch: string = '';
	categorySearch: string = '';

	selectedProfile: ProfileApiOut|null = null;

	headerParameters: ContentHeaderParameters = {
		header: 'Points de vente',
		subtitle: 'Créez des points de vente pour organiser votre événement',
		actions: [{
			id: 'new',
			type: 'SIMPLE_ACTION',
			name: 'Nouveau point de vente',
			icon: 'fa-regular fa-circle-plus',
			callback: this.createProfile
		}, {
			type: 'SIMPLE_ACTION',
			name: 'Importer',
			icon: 'fa-regular fa-arrow-up-to-line',
			callback: this.importProfiles
		}, {
			type: 'SIMPLE_ACTION',
			name: 'Exporter',
			icon: 'fa-regular fa-arrow-down-to-line',
			callback: this.exportProfiles
		}, {
			id: 'settings',
			type: 'SIMPLE_ACTION',
			name: 'Paramètres',
			icon: 'fa-regular fa-cog',
			callback: this.toggleSettingsModal
		}],
		hideSearch: false,
		searchPlaceholder: 'Rechercher un point de vente'
	}

	refreshKey: string = randomUUID();

	editingProfile: ProfileApiOut|null = null;
	showCreationModal: boolean = false;
	showProfilesImportModal: boolean = false;
	showSettingsModal: boolean = false;

	tableDisplay: boolean = true;

	draggable: Draggable | undefined = undefined;
	sortable: Sortable | undefined = undefined;

	loading: boolean = false;

	@AutoWired(ProfilesRepository) accessor profilesRepository!: ProfilesRepository;
	@AutoWired(CategoriesRepository) accessor categoriesRepository!: CategoriesRepository;
	@AutoWired(TableColumnsRepository) accessor tableColumnsRepository!: TableColumnsRepository;
	@AutoWired(EstablishmentDevicesRepository) accessor establishmentDevicesRepository!: EstablishmentDevicesRepository;
	@AutoWired(EstablishmentAccountProfileRepository) accessor establishmentAccountProfileRepository!: EstablishmentAccountProfileRepository;
	@AutoWired(CashlessEstablishmentRepository) accessor cashlessEstablishmentRepository!: CashlessEstablishmentRepository;
	@AutoWired(SidebarStateListener) accessor sidebarStateListener!: SidebarStateListener;
	@AutoWired(AppState) accessor appState!: AppState;
	@AutoWired(ShortcutsManager) accessor shortcutsManager!: ShortcutsManager;
	@AutoWired(Router) accessor router!: Router;

	constructor(optionBuilder: OptionBuilder, vueInstance: any) {
		super(optionBuilder, vueInstance);
		let regexMatch = this.router.lastRouteRegexMatches;

		this.sidebarStateListener.setMinimizedSidebar(false);
		this.sidebarStateListener.setHiddenSidebar(false);

		if(!this.appState.advancedInterfaces) {
			this.headerParameters.actions.splice(1, 2);
		}

		// Check permissions for form components and disable actions accordingly
		if (!ComponentUtils.hasPermissions(ProfileFormComponentHasRequiredPermissions)) {
			ComponentUtils.disableActions(this.headerParameters, ['new']);
		}

		if (!ComponentUtils.hasPermissions(ImportProfileModalComponentHasRequiredPermissions)) {
			ComponentUtils.disableActions(this.headerParameters, ['import']);
		}

		if (!ComponentUtils.hasPermissions(CashlessEstablishmentFormComponentHasRequiredPermissions)) {
			ComponentUtils.disableActions(this.headerParameters, ['settings']);
		}

		if (regexMatch && regexMatch[1]) {
			this.establishmentUid = UuidUtils.scopedToVisual<UuidScopeEstablishment>(regexMatch[1] as ScopedUuid<UuidScopeEstablishment>, uuidScopeEstablishment);

			this.loading = true;
		}
	}

	async mounted() {
		// Check for essential page functionality
		if(!ComponentUtils.hasPermissions((ownedPermissions: ApplicationPermission[]) => {
			return EstablishmentAccountPermissionModel.hasPermissionsForContracts(ownedPermissions, [
				CashlessHttpProfileContract.list,
				CashlessHttpSimpleProductCategoryContract.list,
				CashlessHttpEstablishmentContract.get,
				IotHttpEstablishmentDeviceContract.list,
				CashlessHttpEstablishmentAccountProfileContract.list,
			])
		})) {
			this.forbidden = true;
			this.loading = false;
			return;
		}

		try {
			this.profiles = (await this.profilesRepository.callContract('list', {establishmentUid: this.establishmentUid}, undefined)).success();
			this.categories =  (await this.categoriesRepository.callContract('list', {establishmentUid: this.establishmentUid}, undefined)).success();
			this.cashlessEstablishment = (await this.cashlessEstablishmentRepository.callContract('get', {establishmentUid: this.appState.requireUrlEstablishmentUid()}, undefined)).success();
			this.devices = (await this.establishmentDevicesRepository.callContract('list', {establishmentUid: this.appState.requireUrlEstablishmentUid()}, undefined)).success();
			this.accountProfiles = (await this.establishmentAccountProfileRepository.callContract('list', {establishmentUid: this.appState.requireUrlEstablishmentUid()}, undefined)).success();
		} catch(err) {
			console.log(err);
		}

		setTimeout(() => {
			this.initDraggable();
			this.initSortable();
		}, 0)

		this.loading = false;
	}

	initSortable() {
		this.sortable = new Sortable(document.querySelectorAll('.swappable'), {
			draggable: '.category.copy',
		});

		let currentContainerDeleteZone: Element|null = null;
		this.sortable.on('drag:over:container', (e) => {
			const categoryUid = e.overContainer.dataset.uid;
			if(categoryUid) {
				if (currentContainerDeleteZone) currentContainerDeleteZone.classList.remove('displayed');
				currentContainerDeleteZone = null;

				const deleteZone = document.querySelectorAll(`[data-group=a${categoryUid}]`)[0];
				currentContainerDeleteZone = deleteZone;
				deleteZone.classList.add('displayed');
			}
		});

		this.sortable.on('sortable:stop', async (e: SortableStopEvent) => {
			this.sortable?.destroy();
			this.sortable = undefined;

			const oldProfile = this.getProfileWithUid(e.oldContainer.dataset.uid as Uuid);

			if(e.newContainer.classList.contains('delete-zone')) {
				// Find real new index considering parent categories are not displayed
				let parentCategoriesBeforeMovedCategory = 0;
				for(let i = 0; i < oldProfile.allowedProductCategories.length; i++) {
					const category = this.getCategoryWithUid(oldProfile.allowedProductCategories[i])
					if(category.parent === null && i <= e.oldIndex) parentCategoriesBeforeMovedCategory++;
				}

				// Dropzone is delete-zone
				oldProfile.allowedProductCategories.splice(e.oldIndex + parentCategoriesBeforeMovedCategory, 1);
			} else {
				const newProfile = this.getProfileWithUid(e.newContainer.dataset.uid as Uuid);

				const categoryUid = this.getProfileCategories(oldProfile)[e.oldIndex].uid;

				// Find real old index considering parent categories are not displayed
				const oldContainerCategoryIndex = oldProfile.allowedProductCategories.indexOf(categoryUid);
				if(oldContainerCategoryIndex === -1) throw new Error('category_should_have_been_in_old_profile_categories');
				oldProfile.allowedProductCategories.splice(oldContainerCategoryIndex, 1);

				// Find real new index considering parent categories are not displayed
				let parentCategoriesBeforeMovedCategory = 0;
				for(let i = 0; i < newProfile.allowedProductCategories.length; i++) {
					const category = this.getCategoryWithUid(newProfile.allowedProductCategories[i])
					if(category.parent === null && i <= e.newIndex) parentCategoriesBeforeMovedCategory++;
				}
				const realNewIndex = e.newIndex + parentCategoriesBeforeMovedCategory;

				if(!newProfile.allowedProductCategories.includes(categoryUid)) {
					if (realNewIndex >= newProfile.allowedProductCategories.length) {
						newProfile.allowedProductCategories.push(categoryUid);
					} else {
						newProfile.allowedProductCategories.splice(realNewIndex, 0, categoryUid);
					}
					const category = this.getCategoryWithUid(categoryUid);
					if(category && category.parent && !newProfile.allowedProductCategories.includes(category.parent)) {
						newProfile.allowedProductCategories.push(category.parent);
					}
				}

				try {
					this.saveProfile(newProfile);
				} catch(err) {}
			}

			try {
				this.saveProfile(oldProfile);
			} catch(err) {}

			if(currentContainerDeleteZone) {
				currentContainerDeleteZone.classList.remove('displayed');
				currentContainerDeleteZone = null;
			}

			this.refreshKey = randomUUID();

			this.$nextTick(() => {
				this.initSortable();
				this.initDraggable();
			});
		});
	}

	initDraggable() {
		if (this.draggable) this.draggable.destroy();
		const dropzones = document.querySelectorAll('.drag-container');

		this.draggable = new Draggable(dropzones, {
			draggable: '.category.og',
		});

		let currentContainer: HTMLElement|null = null;
		this.draggable.on('drag:over:container', (e) => {
			currentContainer = e.overContainer;
			currentContainer.classList.add('hovered');
		});

		this.draggable.on('drag:out:container', (e) => {
			if(currentContainer) currentContainer.classList.remove('hovered');
			currentContainer = null;
		});

		this.draggable.on('drag:stop', async (e) => {
			if(!e.originalSource.dataset.id) return;
			const categoryUid = e.originalSource.dataset.id as Uuid;
			const category = this.getCategoryWithUid(categoryUid);
			if(currentContainer) {
				const profile = this.getProfileWithUid(currentContainer.dataset.uid as Uuid);
				if(!profile.allowedProductCategories.includes(categoryUid)) {
					profile.allowedProductCategories.push(categoryUid);

					if(category.parent && !profile.allowedProductCategories.includes(category.parent)) {
						profile.allowedProductCategories.push(category.parent);
					}
				}

				currentContainer.classList.remove('hovered');
				currentContainer = null;

				try {
					this.saveProfile(profile);
				} catch(err) {}
			}

			this.$nextTick(() => {
				this.initDraggable();
			});
		});
	}

	createProfile() {
		this.showCreationModal = true;
	}

	getProfileCategories(profile: ProfileApiOut) {
		return profile.allowedProductCategories.map((categoryUid) => {
			return this.getCategoryWithUid(categoryUid);
		}).filter((category) => category.parent !== null);
	}

	getProfileDevices(profile: ProfileApiOut) {
		return this.devices.filter((device) => {
			const deviceAccountProfile = this.accountProfiles.find((accountProfile) => accountProfile.establishmentAccountUid === device.establishmentAccountUid);
			return deviceAccountProfile && deviceAccountProfile.profileUid === profile.uid;
		});
	}

	getFilteredCategories() {
		return this.categories.filter((category) => category.parent !== null && category.name.toLowerCase().includes(this.categorySearch.toLowerCase()));
	}

	getFilteredProfiles() {
		return this.profiles
			.filter((profile) => profile.name.toLowerCase().includes(this.profilesSearch.toLowerCase()))
			.sort((a, b) => a.name.localeCompare(b.name));
	}

	async toggleCategoryInProfile(profile: ProfileApiOut, category: SimpleProductCategoryApiOut) {
		const index = profile.allowedProductCategories.indexOf(category.uid);
		if(index !== -1) {
			profile.allowedProductCategories.splice(index, 1);
		} else {
			profile.allowedProductCategories.push(category.uid);

			if(category.parent && !profile.allowedProductCategories.includes(category.parent)) {
				profile.allowedProductCategories.push(category.parent);
			}
		}

		this.saveProfile(profile);
	}

	createdProfile(profile: ProfileApiOut) {
		this.profiles.unshift(profile);
		this.showCreationModal = false;
		this.editingProfile = null;

		this.initSortable();
		this.initDraggable();
	}

	updatedProfile(updatedProfile: ProfileApiOut) {
		const index = this.profiles.findIndex((profile) => profile.uid === updatedProfile.uid);
		if(index !== -1) {
			this.profiles.splice(index, 1, updatedProfile);
		} else this.profiles.push(updatedProfile);

		this.showCreationModal = false;
		this.editingProfile = null;

		this.initSortable();
		this.initDraggable();
	}

	async saveProfile(profile: ProfileApiOut) {
		const profileApiIn = new ProfileApiIn({
			name: profile.name,
			allowBalanceReading: profile.allowBalanceReading,
			resetBalanceOptionOnBalanceReading: profile.resetBalanceOptionOnBalanceReading,
			allowDeviceHistoricReading: profile.allowDeviceHistoricReading,
			allowTerminalProduct: profile.allowTerminalProduct,
			allowedProductCategories: profile.allowedProductCategories,
			terminalKeypadDebit: profile.terminalKeypadDebit,
			terminalKeypadCredit: profile.terminalKeypadCredit,
			allowDevicePosManaged: profile.allowDevicePosManaged,
			allowDeviceKiosk: profile.allowDeviceKiosk,
			creditChipId: profile.creditChipId,
			debitChipId: profile.debitChipId,
			txHistoryDelay: profile.txHistoryDelay,
			demoMode: profile.demoMode,
			adminPassword: profile.adminPassword,
			adminPublicChipIds: profile.adminPublicChipIds,
			kiosk: profile.kiosk
		});

		await this.profilesRepository.callContract('update', {establishmentUid: this.establishmentUid, deviceProfileUid: profile.uid}, profileApiIn);
	}

	getCategoriesInProfile(profile: ProfileApiOut) {
		let categories: SimpleProductCategoryApiOut[] = [];
		if(profile.terminalProduct) {
			categories = this.categories.filter((category) => profile.terminalProduct!.allowedProductCategoryList.includes(category.uid));
		} else {
			categories = this.categories.filter((category) => profile.allowedProductCategories.includes(category.uid));
		}
		return categories.filter((category) => category.parent !== null);
	}

	getProfileWithUid(profileUid: Uuid): ProfileApiOut {
		const profile = this.profiles.find((profile) => profile.uid === profileUid);
		if(!profile) throw new Error('missing_profile');
		return profile;
	}

	getCategoryWithUid(categoryUid: Uuid): SimpleProductCategoryApiOut {
		const category = this.categories.find((category) => category.uid === categoryUid);
		if(!category) throw new Error('missing_category');
		return category;
	}

	importProfiles() {
		this.showProfilesImportModal = true;
	}

	exportProfiles() {
		const exportHelper = new ProfileImportExportHelper(this.appState.requireUrlEstablishmentUid());
		exportHelper.exportProfiles();
	}

	importedProfiles(importedProfiles: ProfileApiOut[]) {
		for(let importedProfile of importedProfiles) {
			const index = this.profiles.findIndex((profile) => profile.uid === importedProfile.uid);
			if(index !== -1) this.profiles.splice(index, 1, importedProfile);
			else this.profiles.push(importedProfile);
		}
	}

	getEditUrl(profile: ProfileApiOut) {
		return EstablishmentUrlBuilder.buildUrl('/profile/' + UuidUtils.visualToScoped(profile.uid));
	}

	toggleSettingsModal() {
		this.showSettingsModal = true;
	}
}