<script lang="ts" src="./refill.ts">
</script>

<style lang="sass">
@import './refill.scss'
</style>

<template>
    <div id="refill-page" :class="{'small-device': smallDevice}">

        <div class="top" v-if="specificState.currentWallet">
            <div class="back-button grey button" @click="router.previous()">
                <i class="fa-regular fa-arrow-left"></i>
                Retour
            </div>
            <span> Ajoutez de l'argent sur votre support <b> {{ $filters.Chip(specificState.currentWallet.chipVisualId) }} </b>  </span>
        </div>

        <div class="bottom">
            <div class="amount-container" :class="{small: amount.includes(',') || formatNumber().length > 3, tiny: formatNumber().length > 6}">
                <div class="amount"> {{ formatNumber() }} </div>
                <div class="currency"> € </div>
            </div>

            <div class="prefilled-amounts">
                <div class="amount button" @click="amount = '50,00'; cursorPosition = 3"> 50,00 € </div>
                <div class="amount button" @click="amount = '20,00'; cursorPosition = 3"> 20,00 € </div>
                <div class="amount button" @click="amount = '10,00'; cursorPosition = 3"> 10,00 € </div>
            </div>

            <div class="refill-button" :class="{disabled: amount === '0'}" @click="validateAmount()">
                Valider le montant
            </div>

            <div class="error" v-if="error">
                {{ error }}
            </div>

            <div class="mobile-keyboard">
                <div @click="clickKey('1')"> 1 </div>
                <div @click="clickKey('2')"> 2 </div>
                <div @click="clickKey('3')"> 3 </div>
                <div @click="clickKey('4')"> 4 </div>
                <div @click="clickKey('5')"> 5 </div>
                <div @click="clickKey('6')"> 6 </div>
                <div @click="clickKey('7')"> 7 </div>
                <div @click="clickKey('8')"> 8 </div>
                <div @click="clickKey('9')"> 9  </div>
                <div @click="clickKey('COMMA')"> , </div>
                <div @click="clickKey('0')"> 0 </div>
                <div @click="clickKey('BACK')">
                    <i class="fa-regular fa-arrow-left-long"></i>
                </div>
            </div>
        </div>

        <form-modal-or-drawer
            :state="showMissingDataModal"
            title="Informations requises"
            subtitle="Veuillez remplir ces informations requises avant de recharger votre bracelet"
        >
            <template v-slot:content>
                <div class="input-group" v-if="!specificState.customer?.firstname">
                    <label> Prénom </label>
                    <input placeholder="Prénom" v-model="missingCustomerData.firstname" />
                </div>

                <div class="input-group" v-if="!specificState.customer?.lastname">
                    <label> Nom </label>
                    <input placeholder="Nom" v-model="missingCustomerData.lastname" />
                </div>

                <div class="input-group" v-if="!specificState.customer?.email">
                    <label> Email </label>
                    <input placeholder="Email" v-model="missingCustomerData.email" />
                </div>

                <div class="error" v-if="error">
                    {{ error }}
                </div>
            </template>

            <template v-slot:buttons>
                <button class="grey button" @click="showMissingDataModal = false"> Annuler </button>
                <button class="button" @click="validateAmount()"> Payer {{ formatNumber() }}€ </button>
            </template>
        </form-modal-or-drawer>

        <div class="fixed-payment-modal" v-if="onlineFunding">
            <mangopay-checkout-payment-component
                v-if="onlineFunding.onlinePayment.protocol === PaymentProtocol.WEB_MANGOPAY"
                :online-payment="onlineFunding.onlinePayment"
                @payment-success="paymentSuccess()"
            ></mangopay-checkout-payment-component>

            <test-checkout-payment-component
                v-if="onlineFunding.onlinePayment.protocol === PaymentProtocol.WEB_TEST"
                :online-payment="onlineFunding.onlinePayment"
                @payment-success="paymentSuccess()"
            ></test-checkout-payment-component>

            <stripe-checkout-payment-component
                v-if="onlineFunding.onlinePayment.protocol === PaymentProtocol.WEB_STRIPE"
                :online-payment="onlineFunding.onlinePayment"
                @payment-success="paymentSuccess()"
            ></stripe-checkout-payment-component>
        </div>
    </div>
</template>