<script lang="ts" src="./establishments.ts">
</script>

<style lang="sass">
@import './establishments.scss'
</style>

<template>
    <div id="establishments-page" class="page">
        <filter-table-layout
            :header-parameters="headerParameters"
            :allowed-filters="{filters: [], sorts: availableSorts}"
            :table-columns="tableColumns"
            :filters="{}"
            :drawer-opened="selectedEstablishment !== null"
            @search="search($event)"
            @changed-column-preferences="saveColumnPreferences($event)"
            @sorted="sorted($event)"
        >
            <template v-slot:actions-left>
                <dropdown
                    class="dropdown small white button"
                    :values="applicationDropdownValues"
                    :default-selected="applicationFilter"
                    @update="applicationFilter = $event; filter();"
                ></dropdown>
            </template>
            <template v-slot:table-data>
                <div class="table-dimmer" v-if="loading">
                    <div class="loader-container">
                        <div class="loader"></div>
                    </div>
                </div>
                <tr class="table-no-data" v-else-if="establishments.length === 0">
                    <td colspan="100%"> Aucune donnée </td>
                </tr>
                <tr class="table-no-data" v-else-if="filteredEstablishments.length === 0">
                    <td colspan="100%"> Aucun établissement ne correspond à la recherche </td>
                </tr>
                <tr v-else v-for="establishment in filteredEstablishments" :class="{selected: selectedEstablishment && establishment.uid === selectedEstablishment.uid}" @click="toggleSelectedEstablishment(establishment)">
                    <td :class="{'mobile-hidden': column.mobileHidden}" v-for="column in tableColumns.filter((column) => column.displayed)">
                        <template v-if="column.name === 'uid'"> {{ establishment.uid }} </template>
                        <template v-else-if="column.name === 'name'"> {{ establishment.name }} </template>
                        <template v-else-if="establishment.backApplications.includes(column.name) || establishment.frontApplications.includes(column.name)">
                            <div class="status">
                                <div class="square" :class="{green: establishment.backApplications.includes(column.name) || establishment.frontApplications.includes(column.name)}">
                                    <i class="fa-regular fa-check" v-if="establishment.backApplications.includes(column.name) || establishment.frontApplications.includes(column.name)"></i>
                                    <i class="fa-regular fa-times" v-else></i>
                                </div>
                            </div>
                        </template>
                        <template v-else>
                            {{ getDeviceNumberForEstablishmentWithType(establishment, column.name) }}
                        </template>
                    </td>
                </tr>
            </template>
            <template v-slot:right>
                <div v-if="!selectedEstablishment" class="empty-right-panel">
                    <img src="../../assets/img/select-hint.svg" />
                    Cliquer sur un support pour <br/> le sélectionner
                </div>
                <div v-else class="selected-establishment">
                    <div class="close" @click="toggleSelectedEstablishment(selectedEstablishment)">
                        <i class="fa-regular fa-xmark"></i>
                        <span> Fermer </span>
                    </div>

                    <div class="header">
                        <div class="left">
                            <h2> {{ selectedEstablishment.name }} </h2>
                        </div>
                    </div>

                    <div class="content">
                        <div class="iot-devices" v-for="(deviceApps, type) of getDeviceAppsBySettingsType(selectedEstablishment)">

                            <div class="top">
                                <span class="title"> {{ getSettingsTypeTranslation(type) }} ({{ deviceApps.length }}) </span>
                            </div>

                            <div class="iot-device" v-for="deviceApp of deviceApps">
                                {{ requireDeviceWithUid(deviceApp.deviceUid).brand }}
                                {{ requireDeviceWithUid(deviceApp.deviceUid).model }}
                            </div>
                        </div>
                    </div>
                </div>
            </template>
        </filter-table-layout>
    </div>
</template>