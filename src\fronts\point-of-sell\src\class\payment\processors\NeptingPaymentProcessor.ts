import {App2AppNativeRequest, AutoWired, EventEmitter} from "@groupk/horizon2-core";
import {PhysicalPaymentProtocol} from "./PhysicalPaymentProtocol";
import {OrderPaymentApiOut} from "@groupk/mastodon-core";
import {NeptingBridge, NeptingBridgeListener} from "../../NeptingBridge";
import {PaymentMethodDataPhysicalNeptingApp2App} from "@groupk/mastodon-core";
import {ErrorHandler} from "../../ErrorHandler";
import {randomUUID} from "@groupk/horizon2-front";

export class NeptingPaymentProcessor extends EventEmitter<{
	'totalPaidChanged': number,
	'totalRefundChanged': number,
	'done': boolean,
	'neptingResult': PaymentMethodDataPhysicalNeptingApp2App,
	'neptingError': any
}> implements PhysicalPaymentProtocol {
	config = {
		displayLivePaidAmount: false,
		autoCloseOnSuccess: false,
		cancelable: false
	};

	@AutoWired(NeptingBridge) accessor neptingBridge!: NeptingBridge;
	@AutoWired(ErrorHandler) accessor errorHandler!: ErrorHandler;

	async initiatePayment(payment: OrderPaymentApiOut) {
		const self = this;

		const amount = -payment.amount;
		this.neptingBridge.createPaymentRequest(amount, randomUUID(), new class implements NeptingBridgeListener {
			error(err: any, extras : {request: App2AppNativeRequest|undefined}): void {
				self.emit('neptingError', err);

				self.errorHandler.logToSentry({
					title: 'Nepting payment error',
					description: JSON.stringify(extras)
				});
			}
		}).then((result)=>{
			this.emit('neptingResult', result.unified);
		});
	}

	cancel() {
		this.neptingBridge.forceCancelCurrent();
	}
}