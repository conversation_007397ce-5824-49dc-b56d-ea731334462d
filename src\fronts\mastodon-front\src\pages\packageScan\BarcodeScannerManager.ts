import {BarcodeExternalNative_QRCodeReaderReturn} from "@groupk/native-bridge";

export class BarcodeScannerManager {
    customCallback: ((data: BarcodeExternalNative_QRCodeReaderReturn) => void)|null = null;

    constructor() {
        console.log(this.customCallback);
    }

    callback(data: BarcodeExternalNative_QRCodeReaderReturn) {
        console.log('callback');
        console.log(this.customCallback);
        if(this.customCallback) this.customCallback(data);
    }
}
