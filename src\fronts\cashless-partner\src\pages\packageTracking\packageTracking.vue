<script lang="ts" src="./packageTracking.ts" />

<style lang="sass" scoped>
@import './packageTracking.scss'
</style>

<template>
    <div id="package-tracking-page" class="page">
        <filter-table-layout
            :header-parameters="headerParameters"
            :allowed-filters="allowedFilters"
            :table-columns="tableColumns"
            :filters="filters"
            :drawer-opened="selectedPackage !== null"
            @changed-column-preferences="saveColumnPreferences($event)"
        >
            <template v-slot:table-data>
                <tr class="table-dimmer" :class="{relative: packages.length === 0}" v-if="loading">
                    <td colspan="100%">
                        <div class="dimmer">
                            <div class="loader-container">
                                <div class="loader"></div>
                            </div>
                        </div>
                    </td>
                </tr>
                <tr class="table-no-data" v-else-if="packages.length === 0">
                    <td colspan="100%">Aucun colis</td>
                </tr>
                <tr v-for="postalPackage in packages" :class="{selected: selectedPackage && selectedPackage.uid === postalPackage.uid}">
                    <td :class="{'mobile-hidden': column.mobileHidden, grow: column.name === 'name'}" v-for="column in tableColumns.filter((column) => column.displayed)" @click="selectedPackage = postalPackage">
                        <template v-if="column.name === 'trackingId'"> {{ postalPackage.trackingId ?? '-' }} </template>
                        <template v-if="column.name === 'provider'"> {{ $filters.PostalTransporter(postalPackage.transportProvider) }} </template>
                        <template v-if="column.name === 'comment'"> {{ postalPackage.comment || '-' }} </template>
                    </td>
                </tr>
            </template>
            <template v-slot:right>
                <div v-if="!selectedPackage" class="empty-right-panel">
                    <img src="../../assets/img/select-hint.svg" />
                    Cliquer sur un colis pour <br/> le sélectionner
                </div>
                <div v-else class="selected-tracking">
                    <div class="close" @click="selectedPackage = null">
                        <i class="fa-regular fa-xmark"></i>
                        <span> Fermer </span>
                    </div>

                    <div class="header">
                        <div class="left">
                            <h2> {{ $filters.PostalTransporter(selectedPackage.transportProvider) }} </h2>
                            <span> {{ selectedPackage.comment || '-' }} </span>
                        </div>
                    </div>

                    <div class="updates">
                        <div class="update" v-for="trackingHistory in selectedPackage.trackingHistoryList">
                            <div class="point"></div>
                            <span class="datetime"> {{ $filters.Day(trackingHistory.datetime) }} </span>

                            <div class="data">
                                <span class="status"> {{ $filters.PostalPackageState(trackingHistory.state) }} </span>
                                <span class="comment"> {{ trackingHistory.comment || '-' }} </span>
                            </div>
                        </div>
                    </div>

                </div>
            </template>
        </filter-table-layout>
    </div>
</template>