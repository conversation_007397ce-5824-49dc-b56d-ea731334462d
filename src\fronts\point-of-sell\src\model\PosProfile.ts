import {AutoWired, VisualScopedUuid} from "@groupk/horizon2-core";
import {UuidScopeEstablishmentAccount} from "@groupk/mastodon-core";
import {
	PosProfileApiIn,
	PosProfileApiOut,
	PosProfilePaymentMethodSettings,
} from "@groupk/mastodon-core";
import {PosState} from "./PosState";

export class PosProfile {
	profiles: PosProfileApiOut[]|null = null;

	@AutoWired(PosState) accessor posState!: PosState;

	constructor(profiles: PosProfileApiOut[]|null = null) {
		this.profiles = profiles;
	}

	get color(): string|null {
		return this.getFirstNotNullValue('color', this.posState.posProfiles) ?? null;
	}

	get displayProductsSearch() : boolean {
		return this.getFirstNotNullValue('displayProductsSearch', this.posState.posProfiles) ?? true;
	}

	get displayQuickPay() : boolean {
		return this.getFirstNotNullValue('displayQuickPay', this.posState.posProfiles) ?? true;
	}

	get displayProductImage() : boolean {
		return this.getFirstNotNullValue('displayProductImage', this.posState.posProfiles) ?? true;
	}

	get allowPurchaseDiscounts() : boolean {
		return this.getFirstNotNullValue('allowPurchaseDiscounts', this.posState.posProfiles) ?? true;
	}

	get performanceMode() : boolean {
		return this.getFirstNotNullValue('performanceMode', this.profiles ?? this.posState.posProfiles) ?? false;
	}

	get paymentMethodConfigs(): PosProfilePaymentMethodSettings[] {
		return this.getFirstNotNullValue('paymentMethodConfigs', this.profiles ?? this.posState.posProfiles) ?? [];
	}

	get securityCode(): string|null {
		return this.getFirstNotNullValue('securityCode', this.profiles ?? this.posState.posProfiles) ?? null;
	}

	get enableWorkClock(): boolean|null {
		return this.getFirstNotNullValue('enableWorkClock', this.profiles ?? this.posState.posProfiles) ?? null;
	}

	getFirstNotNullValue<K extends keyof PosProfileApiOut>(key: K, profiles: PosProfileApiOut[]): PosProfileApiOut[K]|null {
		const sortedProfilesByMaxMatchingKeys = profiles
			.filter((profile) => profile.establishmentAccountUid === null || profile.establishmentAccountUid === this.posState.currentEstablishmentAccountUid)
			.sort((p1, p2) => {
				return this.getProfileMatchKeysQuantity(p1) > this.getProfileMatchKeysQuantity(p2) ? -1 : 1;
			});

		for(const profile of sortedProfilesByMaxMatchingKeys) {
			if(profile[key] !== null) return profile[key];
		}
		return null;
	}

	getProfileMatchKeysQuantity(profile: PosProfileApiOut): number {
		let matchingKeys: number = 0;
		if(profile.iotDeviceUid !== null) matchingKeys++;
		if(profile.establishmentAccountUid !== null) matchingKeys++;
		return matchingKeys;
	}

	static apiOutToApiIn(posProfileApiOut: PosProfileApiOut) {
		return new PosProfileApiIn({
			establishmentAccountUid: posProfileApiOut.establishmentAccountUid,
			iotDeviceUid: posProfileApiOut.iotDeviceUid,
			color: posProfileApiOut.color,
			displayProductsSearch: posProfileApiOut.displayProductsSearch,
			displayQuickPay: posProfileApiOut.displayQuickPay,
			displayProductImage: posProfileApiOut.displayProductImage,
			allowPurchaseDiscounts: posProfileApiOut.allowPurchaseDiscounts,
			performanceMode: posProfileApiOut.performanceMode,
			paymentMethodConfigs: posProfileApiOut.paymentMethodConfigs,
			securityCode: posProfileApiOut.securityCode,
			enableWorkClock: posProfileApiOut.enableWorkClock
		});
	}
}