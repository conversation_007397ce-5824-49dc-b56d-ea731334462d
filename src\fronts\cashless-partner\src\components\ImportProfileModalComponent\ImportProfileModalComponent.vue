<script lang="ts" src="./ImportProfileModalComponent.ts">
</script>

<style lang="sass">
@use './ImportProfileModalComponent.scss' as *
</style>

<template>
    <modal-or-drawer :state="opened" @close="close()">
        <table-import
            v-if="opened"
            :expected-columns="csvImportColumns"
            :entity-type="csvImportEntityType"
            :entity-builder="(a: any, b: any)=>profileImportExportHelper.entityBuilder(a, b,profiles??[])"
            :entity-saver="(a: any)=>profileImportExportHelper.entitySaver(a, emitImportedProfiles)"
            @close="close()"
        ></table-import>
    </modal-or-drawer>
</template>