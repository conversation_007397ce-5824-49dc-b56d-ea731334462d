import {Component, Prop, Vue} from "vue-facing-decorator";

export type ItemAction = {
    id: string,
    icon: string,
    text: string
}

@Component({
    components: {},
    emits: ['close', 'action-clicked']
})
export default class ItemActionsComponent extends Vue {
    @Prop() actions!: ItemAction[];

    opened: boolean = false;

    mounted() {
        setTimeout(() => this.opened = true, 0);
    }

    clickedAction(action: ItemAction) {
        this.$emit('action-clicked', action);
        this.close();
    }

    close() {
        this.opened = false;
        setTimeout(() => this.$emit('close'), 300);
    }
}