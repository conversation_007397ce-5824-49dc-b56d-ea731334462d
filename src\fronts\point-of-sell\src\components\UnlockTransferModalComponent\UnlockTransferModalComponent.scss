.unlock-transfer-modal-component {
    position: fixed;
    inset: 0;
    display: flex;
    align-items: flex-start;
    justify-content: center;
    background: rgba(0, 0, 0, 0.7);
    padding: 60px 16px;
    z-index: 501;

    .modal {
        display: flex;
        flex-direction: column;
        gap: 10px;
        width: 100%;
        padding: 20px;
        max-width: max(30%, 300px);
        background: #F2F4F7;
        border-radius: 12px;

        .head {
            display: flex;
            align-items: center;
            justify-content: space-between;

            .title {
                font-size: 18px;
                font-weight: bold;
            }

            .close {
                display: flex;
                align-items: center;
                justify-content: center;
                height: 42px;
                width: 42px;
                background: #E8E8E8;
                border-radius: 50%;
            }
        }

        textarea {
            height: 200px;
            padding: 20px;
            font-size: 18px;
            font-weight: 500;
            resize: none;
        }

        .button {
            all: unset;
            display: flex;
            align-items: center;
            justify-content: center;
            height: 56px;
            width: 100%;
            border-radius: 8px;
            background: var(--primary-color);
            color: var(--primary-text-color);
            font-family: Montserrat, sans-serif;
            font-size: 15px;
            font-weight: 700;
            cursor: pointer;

            &:hover {
                background: var(--primary-button-hover-color);
                color: var(--primary-text-color);
            }

            &.disabled {
                opacity: 0.5;
                pointer-events: none;
            }
        }
    }

    .keypad-component {
        background: white;

        .code {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr 1fr;
            grid-gap: 20px;
            padding: 20px;
            box-sizing: border-box;

            &.red {
                background: rgba(235, 87, 87, 0.2);

                .character {
                    background: rgba(235, 87, 87, 0.2);
                    color: var(--error-color)
                }
            }

            .character {
                display: flex;
                align-items: center;
                justify-content: center;
                height: 60px;
                background: #f2f4f7;
                border-radius: 8px;
                font-size: 24px;
                font-weight: bold;
            }
        }
    }
}