import {Component, Vue} from "vue-facing-decorator";
import {CleaveDirective} from "../../directives/CleaveDirective";
import {DropdownComponent, FormModalOrDrawerComponent, InputDateTimeComponent} from "@groupk/vue3-interface-sdk";
import UploadInputComponent from "../UploadInputComponent/UploadInputComponent.vue";
import {AppApiOut_type, AppStripeCreationApiIn, AppStripeEditAccountApiIn, AppType,} from "@groupk/mastodon-core";
import {AutoWired} from "@groupk/horizon2-core";
import {AppState} from "../../AppState";
import {StripeAppRepository} from "../../repositories/StripeAppRepository";
import {AppRepository} from "../../repositories/AppRepository";

@Component({
    directives: {
        cleave: CleaveDirective,
    },
    components: {
        'date-time-input': InputDateTimeComponent,
        'form-modal-or-drawer': FormModalOrDrawerComponent,
        'upload-input': UploadInputComponent,
        'dropdown': DropdownComponent
    },
    emits: ['created', 'updated']
})
export default class StripeAppSelectorComponent extends Vue {
    apps: AppApiOut_type[] = [];

    selectedApp: AppApiOut_type|null = null;

    opened: boolean = false;
    connectingAccount: boolean = false;
    loading: boolean = false;
    error: string|null = null;

    @AutoWired(StripeAppRepository) private accessor stripeAppRepository!: StripeAppRepository;
    @AutoWired(AppRepository) private accessor appRepository!: AppRepository;
    @AutoWired(AppState) private accessor appState!: AppState;

    async mounted() {
        setTimeout(() => this.opened = true, 0);

        this.apps = (await this.appRepository.callContract(
            'list',
            {establishmentUid: this.appState.requireUrlEstablishmentUid()},
            undefined)
        ).success().list.filter((app) => app.type === AppType.STRIPE);
    }

    async connectWithStripe() {
        this.connectingAccount = true;

        let appName = 'Stripe';
        if(this.apps.length > 0) {
            appName += ` ${this.apps.length + 1}`;
        }

        const stripeApp = (await this.stripeAppRepository.callContract(
            'create',
            {establishmentUid: this.appState.requireUrlEstablishmentUid()},
            new AppStripeCreationApiIn({name: appName, type: AppType.STRIPE}))
        ).success();

        const redirectUrl = new URL(window.location.href);
        redirectUrl.searchParams.set('from', 'stripe_account_binding')

        const data = (await this.stripeAppRepository.callContract(
            'accountEdit',
            {establishmentUid: this.appState.requireUrlEstablishmentUid(), appUid: stripeApp.item.uid},
            new AppStripeEditAccountApiIn({
                refreshUrl: redirectUrl.toString(),
                returnUrl: redirectUrl.toString()
            }))
        ).success();

        window.location.href = data.redirectUrl;
    }

    validate() {
        this.$emit('selected', this.selectedApp);
        this.close();
    }

    close() {
        this.opened = false;
        setTimeout(() => this.$emit('close'), 300);
    }


}