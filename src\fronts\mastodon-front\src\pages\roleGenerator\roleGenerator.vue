<script lang="ts" src="./roleGenerator.ts">
</script>

<style lang="sass">
@import './roleGenerator.scss'
</style>

<template>
    <div id="role-generator-page" class="page">
        <div class="input-group right-icon">
            <input type="text" placeholder="Coller le json" @paste="pastedJson($event)" />
        </div>

        <div class="form-error" v-if="parseError">
            <i class="fa-solid fa-exclamation-circle"></i>
            <div class="details">
                <span class="title"> Erreur </span>
                <span class="description">{{ parseError }}</span>
            </div>
        </div>

        <div class="container">
            <div class="button" @click="addContract()">
                <i class="fa-regular fa-circle-plus"></i>
                Ajouter un contrat
            </div>

            <div class="button" @click="exportContracts()">
                <i class="fa-regular fa-circle-plus"></i>
                Exporter
            </div>

            <div class="contracts">
                <div class="contract" v-for="contract of applicationFrontContracts">
                    <input v-model="contract.applicationId" />

                    <div class="permissions-groups">
                        <div class="head">
                            Groupes de permissions
                            <i v-if="!closedContractGroup.includes(contract.applicationId)" class="fa-regular fa-chevron-up" @click="closedContractGroup.push(contract.applicationId)"></i>
                            <i v-else class="fa-regular fa-chevron-down" @click="closedContractGroup.splice(closedContractGroup.indexOf(contract.applicationId), 1)"></i>
                        </div>

                        <template v-if="!closedContractGroup.includes(contract.applicationId)">
                            <div class="permissions-group" v-for="group of contract.permissions">
                                <input placeholder="Nom du groupe" v-model="group.id" />

                                <div class="permissions">
                                    <div class="two-inputs">
                                        <div class="permission" v-for="permission of group.backPermissions">
                                            {{ permission.applicationId }} - {{ permission.id }}
                                        </div>
                                    </div>
                                </div>

                                <button class="grey button" @click="editingGroup = group">
                                    <i class="fa-regular fa-pen-line"></i>
                                    Modifier
                                </button>
                            </div>

                            <button class="white button" @click="addGroup(contract)">
                                <i class="fa-regular fa-circle-plus"></i>
                                Ajouter un groupe
                            </button>
                        </template>
                    </div>

                    <div class="permissions-groups">
                        <div class="head">
                            Rôles
                            <i v-if="!closedContractRoles.includes(contract.applicationId)" class="fa-regular fa-chevron-up" @click="closedContractRoles.push(contract.applicationId)"></i>
                            <i v-else class="fa-regular fa-chevron-down" @click="closedContractRoles.splice(closedContractRoles.indexOf(contract.applicationId), 1)"></i>
                        </div>

                        <template v-if="!closedContractRoles.includes(contract.applicationId)">
                            <div class="permissions-group" v-for="role of contract.roles">
                                <input placeholder="Nom du role" v-model="role.id" />

                                <div class="front-permissions">
                                    <div class="two-inputs">
                                        <div class="permission" v-for="permission of role.frontPermissions">
                                            {{ permission }}
                                        </div>
                                    </div>
                                </div>

                                <div class="permissions">
                                    <div class="two-inputs">
                                        <div class="permission" v-for="permission of role.backPermissions">
                                            {{ permission.applicationId }} - {{ permission.id }}
                                        </div>
                                    </div>
                                </div>

                                <button class="grey button" @click="editingGroup = role">
                                    <i class="fa-regular fa-pen-line"></i>
                                    Modifier les droits
                                </button>

<!--                                <button class="grey button" @click="editingGroup = group">-->
<!--                                    <i class="fa-regular fa-pen-line"></i>-->
<!--                                    Modifier les groupes de droits-->
<!--                                </button>-->
                            </div>

                            <button class="white button" @click="addRole(contract)">
                                <i class="fa-regular fa-circle-plus"></i>
                                Ajouter un rôle
                            </button>
                        </template>
                    </div>

                    <permission-group-form
                        v-if="editingGroup"
                        :current-permissions="editingGroup.backPermissions"
                        :current-front-permissions="'frontPermissions' in editingGroup ? editingGroup.frontPermissions : null"
                        :available-front-permissions="contract.permissions.map((permission) => permission.id)"
                        @edit-permissions="editedPermission($event)"
                        @close="editingGroup = null"
                    ></permission-group-form>
                </div>
            </div>
        </div>

    </div>
</template>
