import {Component, Prop, Vue} from "vue-facing-decorator";
import {
    EstablishmentApiOut,
    PostalHttpPackageContract,
    PostalPackageApiOut,
    PostalPackageTransportProvider
} from "@groupk/mastodon-core";
import {
    DropdownComponent, DropdownValue,
    FormModalOrDrawerComponent
} from "@groupk/vue3-interface-sdk";
import {PostalPackageData} from "../../../../../shared/mastodonCoreFront/postal/PostalPackageData";
import {AutoWired, EnumUtils} from "@groupk/horizon2-core";
import StringOrNullInputComponent
    from "../../../../../shared/components/StringOrNullInputComponent/StringOrNullInputComponent.vue";
import {PostalPackageRepository} from "../../../../../shared/repositories/PostalPackageRepository";
import {AppState} from "../../../../../shared/AppState";
import {translateResponseError} from "../../../../../shared/RepositoryExtensions";
import {EstablishmentRepository} from "../../../../../shared/repositories/EstablishmentRepository";

@Component({
    components: {
        'form-modal-or-drawer': FormModalOrDrawerComponent,
        'dropdown': DropdownComponent,
        'string-or-null-input': StringOrNullInputComponent
    },
    emits: ['close', 'created']
})
export default class PostalPackageFormComponent extends Vue {
    @Prop({default: null}) editingPostalPackage!: PostalPackageApiOut|null;

    postalPackage: PostalPackageData = new PostalPackageData();

    saving: boolean = false;
    opened: boolean = false;
    error: string|null = null;

    @AutoWired(PostalPackageRepository) private accessor postalPackageRepository!: PostalPackageRepository;
    @AutoWired(EstablishmentRepository) private accessor establishmentRepository!: EstablishmentRepository;
    @AutoWired(AppState) private accessor appState!: AppState;

    async mounted() {
        if(this.editingPostalPackage) this.postalPackage = new PostalPackageData(this.editingPostalPackage);

        setTimeout(() => this.opened = true, 0);
    }

    getTransporterProviderDropdownValues(): DropdownValue[] {
        return EnumUtils.values(PostalPackageTransportProvider).map((value) => {
            return {
                name: value,
                value: value
            }
        });
    }

    async create() {
        this.error = null;
        this.saving = true;

        if(!this.editingPostalPackage) {
            const response = await this.postalPackageRepository.callContract('create', {establishmentUid: this.appState.requireUrlEstablishmentUid()}, this.postalPackage.toApi());
            if(response.isSuccess()) {
                this.$emit('created', response.success());
                this.close();
            } else {
                this.error = translateResponseError<typeof PostalHttpPackageContract, 'create'>(response, {});
            }
        } else {

        }

        this.saving = false;
    }

    close() {
        this.opened = false;
        setTimeout(() => this.$emit('close'), 300);
    }
}