<script lang="ts" src="./WalletDeclareStateFormComponent.ts">
</script>

<style lang="sass">
@use './WalletDeclareStateFormComponent.scss' as *
</style>

<template>
    <div class="wallet-declare-state-form-component">
        <form-modal-or-drawer
            :state="opened"
            :title="`Déclarer la puce ${
                reason === WalletInactiveReason.LOST ?
                    'perdue' : reason === WalletInactiveReason.STOLEN ?
                    'volée' : 'cassée'
            } ?`"
            subtitle="Attention lors de cette opération le solde de la puce va être remis à zéro"
            @close="close()"
        >
            <template v-slot:content>

                <div class="loading-container" v-if="loading">
                    <div class="loader"></div>
                </div>
                <template v-else>
                    <div class="form-warning">
                        <i class="fa-solid fa-exclamation-circle"></i>
                        <div class="details">
                            <span class="title"> Information </span>
                            <span class="description">
                            Attention, malgré la mise à jour du solde à distance, il est possible que la puce soit
                            acceptée sur des terminaux hors ligne tant qu'ils n'ont pas retrouvé de connexion internet.
                        </span>
                        </div>
                    </div>

                    <div class="input-group">
                        <label> Choisir le point de vente dans lequel la transaction de réinitialisation apparaîtra </label>
                        <dropdown
                            placeholder="Point de vente"
                            :searchable="true"
                            :values="profilesDropdownValues"
                            @update="selectedProfile = $event"
                        ></dropdown>
                    </div>

                    <!--                <div class="input-group">-->
                    <!--                    <label for="title"> Choisir la méthode de paiement de vidage </label>-->
                    <!--                    <dropdown-->
                    <!--                        class="white"-->
                    <!--                        placeholder="Méthode de paiement"-->
                    <!--                        :values="paymentMethodsDropdownValues"-->
                    <!--                        @update="selectedPaymentMethod = $event"-->
                    <!--                    ></dropdown>-->
                    <!--                </div>-->

                    <div class="form-error" v-if="error">
                        <i class="fa-solid fa-exclamation-circle"></i>
                        <div class="details">
                            <span class="title"> {{ error.title }} </span>
                            <span class="description">
                            {{ error.description }}
                        </span>
                        </div>
                    </div>
                </template>
            </template>

            <template v-slot:buttons>
                <button class="white button" :class="{disabled: saving}" @click="close()"> Annuler </button>
                <button class="red button" :class="{loading: saving, disabled: saving}" @click="validate()">
                    Déclarer
                    <template v-if="reason === WalletInactiveReason.LOST"> perdue </template>
                    <template v-if="reason === WalletInactiveReason.BROKEN"> cassée </template>
                    <template v-if="reason === WalletInactiveReason.STOLEN"> volée </template>
                </button>
            </template>
        </form-modal-or-drawer>
    </div>
</template>