import {ProfileKioskProduct} from "@groupk/mastodon-core";
import {ProfileKioskProductItemData} from "./ProfileKioskProductItemData";

export class ProfileKioskProductData {
    products: ProfileKioskProductItemData[] = [];

    constructor(profileKioskProduct: ProfileKioskProduct|null = null) {
        if(profileKioskProduct) {
            this.products = profileKioskProduct.products.map((product) => new ProfileKioskProductItemData(product));
        }
    }

    toProfileKioskProduct() {
        return new ProfileKioskProduct({
            products: this.products.map((product) => product.toProfileKioskProductItem())
        });
    }
}