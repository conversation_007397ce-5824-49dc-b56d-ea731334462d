<script lang="ts" src="./MobileOrderCardComponent.ts">
</script>

<style lang="sass">
@import './MobileOrderCardComponent.scss'
</style>

<template>
    <div class="mobile-order-card-component" :class="{selected: posState.currentOrder && posState.currentOrder.order.uid === localOrder.order.uid}">
        <div class="top">
            <span class="date">
                {{ $filters.Day(localOrder.order.creationDatetime) }}
                {{ $filters.Hour(localOrder.order.creationDatetime) }}<br/>
                <span class="ago"> ({{ $filters.Since(localOrder.order.creationDatetime) }}) </span>
            </span>
            <div class="label orange" v-if="havePendingPayment()">
                En attente
            </div>
            <div class="label orange" v-else-if="getOrderTotals.leftToPay > 0">
                {{ $filters.Money(getOrderTotals.leftToPay) }}
            </div>
            <div class="label green" v-else>
                Payé
            </div>

            <img class="reservit-logo" :src="$assets.reservit" v-if="localOrder.order.transferredTo">
        </div>
        <div class="bottom">
            <div class="data">
                <span class="value"> {{ $filters.Money(getOrderTotals.purchases.withTaxesBeforeDiscount) }} </span>
                <span class="title"> Total TTC </span>
            </div>
<!--            <div class="data">-->
<!--                <span class="value">  {{ getOrderPaymentMethods().join(', ') || '-'}} </span>-->
<!--                <span class="title"> Payé en </span>-->
<!--            </div>-->
            <div class="data">
                <span class="value">
                 {{ localOrder.order.sellerEstablishmentAccountUid ? posState.getEstablishmentAccountWithUid(localOrder.order.sellerEstablishmentAccountUid).firstname : 'En ligne' }}
                </span>
                <span class="title"> Vendeur </span>
            </div>
            <div class="actions">
                <div class="quick-edit" @click="quickEdit()">
                    <i class="fa-regular fa-pen-line"></i>
                </div>

                <div class="quick-edit">
                    <dropdown-button
                        class="custom-dropdown-button"
                        icon="fa-regular fa-ellipsis-vertical"
                        :touch-compatible="true"
                        :icon-only="true"
                        alignment="RIGHT"
                        :actions="[{
                            title: '',
                            actions: OrderUtils.getOrderDropdownActions(localOrder)
                        }]"
                        @clicked="dropdownClicked($event)"
                    ></dropdown-button>
                </div>
            </div>

        </div>

        <div class="transferring-hover" v-if="localOrderTransfer && !localOrderTransfer.canceled" @click.stop="">
            En cours de transfert...

            <dropdown-button
                class="custom-dropdown-button"
                icon="fa-regular fa-ellipsis-h"
                :touch-compatible="true"
                :icon-only="true"
                alignment="RIGHT"
                :actions="[{
                title: '',
                actions: getTransferDropdownValues(localOrderTransfer)
            }]"
                @clicked="transfertDropdownClicked($event, localOrderTransfer)"
            ></dropdown-button>
        </div>
    </div>
</template>