<script lang="ts" src="./refundBatches.ts"/>

<style lang="sass" scoped>
@import './refundBatches.scss'
</style>

<template>
    <div id="refund-batches-page">
        <layout :drawer-opened="selectedBatch">
            <template v-slot:content>
                <content-header
                    :parameters="headerParameters"
                ></content-header>

                <div class="page-content">
                    <div class="loading-container" v-if="loading">
                        <div class="loader"></div>
                    </div>

                    <div class="table-scroll-wrapper" v-else>
                        <table class="data-table">
                            <thead>
                            <tr>
                                <td> ID </td>
                                <td> Nombre de puces </td>
                                <td> Montant remboursé </td>
                                <td> Montant débité </td>
                                <td> Date </td>
                            </tr>
                            </thead>
                            <tbody>
                            <tr v-if="batches.length === 0">
                                <td class="empty" colspan="100%"> Aucun remboursement </td>
                            </tr>
                            <tr
                                v-for="batch of batches"
                                :class="{selected: selectedBatch && selectedBatch.item.uid === batch.uid}"
                                @click="toggleSelectedBatch(batch)"
                            >
                                <td>
                                    <div class="ticket-data">
                                        <div class="ticket-icon">
                                            <i class="fa-regular fa-file"></i>
                                        </div>
                                        <span class="ticket-name"> {{ batch.uid.slice(-8) }} </span>
                                    </div>
                                </td>
                                <td> {{ batch.count }} </td>
                                <td> {{ $filters.Money(batch.totalAmount) }} </td>
                                <td> {{ $filters.Money(batch.totalDebitedAmount) }} </td>
                                <td> {{ $filters.Date(batch.creationDatetime) }} </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </template>

            <template v-slot:right>
                <div v-if="loadingBatch" class="loading-container right-loading">
                    <div class="loader"></div>
                </div>
                <div v-else-if="!selectedBatch" class="empty-right-panel">
                    <img src="../../assets/img/select-hint.svg" />
                    Cliquez sur un remboursement pour <br/> le sélectionner
                </div>
                <div v-else class="selected-batch">
                    <div class="close" @click="toggleSelectedBatch(selectedBatch.item)">
                        <i class="fa-regular fa-xmark"></i>
                        <span> Fermer </span>
                    </div>

                    <div class="content">
                        <div class="header">
                            <div class="left">
                                <h2> {{ selectedBatch.item.uid.slice(-8) }} </h2>
                            </div>
                            <dropdown-button
                                :button-class="'tertiary icon button'"
                                :icon="'fa-regular fa-ellipsis-vertical'"
                                :alignment="'RIGHT'"
                                @clicked="dropdownClicked($event)"
                                :actions="[{title: '', actions: [{
                                    'id': 'export',
                                    'name': 'Exporter les demandes en CSV',
                                    'icon': 'fa-regular fa-file-csv fa-fw'
                                }, {
                                    'id': 'download',
                                    'name': 'Télécharger le fichier de virement',
                                    'icon': 'fa-regular fa-arrow-down-to-line fa-fw'
                                }]}]"
                            ></dropdown-button>
                        </div>

                        <div class="properties-table">
                            <div class="row">
                                <span class="title"> Nombre de demandes </span>
                                {{ selectedBatch.item.count }}
                            </div>
                            <div class="row">
                                <span class="title"> Montant remboursé </span>
                                {{ $filters.Money(selectedBatch.item.totalAmount) }}
                            </div>

                            <div class="row">
                                <span class="title"> Date </span>
                                {{ $filters.Date(selectedBatch.item.creationDatetime) }}
                            </div>
                        </div>

                        <div class="divider"></div>

                        <div class="block">
                            <h3> Liste des demandes </h3>

                            <div class="table-scroll-wrapper">
                                <table class="data-table">
                                    <thead>
                                    <tr>
                                        <td> Utilisateur </td>
                                        <td class="no-break"> Montant remboursé </td>
                                        <td class="no-break"> N° de support </td>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <tr v-if="selectedBatch.refundList.length === 0">
                                        <td class="empty" colspan="100%"> Aucune demande </td>
                                    </tr>
                                    <tr v-for="refund of selectedBatch.refundList">
                                        <td>
                                            <div class="ticket-data">
                                                <div class="ticket-icon">
                                                    {{ refund.lastname[0] }}{{ refund.firstname[0] }}
                                                </div>
                                                <div class="ticket-name">
                                                    {{ refund.lastname }}
                                                    {{ refund.firstname }}
                                                    <span class="ticket-description"> {{ refund.email }} </span>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="no-break">
                                            {{ $filters.Money(refund.amount ?? 0) }}
                                        </td>
                                      <td class="no-break">
                                        <span class="link" @click="goToChipTransactions(refund.chipVisualId)">{{ refund.chipVisualId}}</span>
                                      </td>
                                    </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </template>
        </layout>
    </div>
</template>