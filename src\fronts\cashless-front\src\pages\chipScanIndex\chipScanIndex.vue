<script lang="ts" src="./chipScanIndex.ts" />

<style lang="sass" scoped>
@import './chipScanIndex.scss'
</style>

<template>
    <div id="chip-scan-index">
        <div class="redirecting" v-if="redirecting">
            Redirection en cours...
        </div>
        <div class="redirecting" v-else-if="error">
            {{ error }}
        </div>
        <div v-else class="main-container">
            <div class="big-actions">
                <a :href="getRefillPageUrl()" class="action" v-if="areOnlineFundingsAvailable">
                    Recharger mon support
                </a>

                <a :href="getRefundPageUrl()"  class="action" v-if="areRefundsAvailable">
                    Demander un remboursement
                </a>
            </div>
        </div>
    </div>
</template>