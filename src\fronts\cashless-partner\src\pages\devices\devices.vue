<script lang="ts" src="./devices.ts">
</script>

<style scoped lang="sass">
@use './devices.scss' as *
</style>

<template>
    <div id="devices-page" class="page">
        <forbidden-message v-if="forbidden"></forbidden-message>
        <layout v-else :drawerOpened="drawerOpened" :display-on-mid-screen="false">
            <template v-slot:content>
                <content-header
                    :parameters="headerParameters"
                ></content-header>

                <div class="page-content">
                    <div class="order-banner">
                        <div class="left">
                            <div class="title"> Besoin de plus de terminaux ? </div>
                            <div class="description"> Augmentez votre capacité de service en ajoutant des terminaux supplémentaires. Contactez nous pour plus de détails. </div>
                        </div>

                        <a href="https://littl.fr/masto-cashless-contact?source=more-devices" target="_blank" class="button">
                            <i class="fa-regular fa-message-middle"></i>
                            Contact
                        </a>
                    </div>


                    <div class="data-actions left-right">
                        <div class="action-group mobile-hidden">
                            <button class="small white button" @click="showColumnsOrganization = true">
                                <i class="fa-regular fa-columns-3"></i>
                                Organiser les colonnes
                            </button>

                            <table-columns-organization
                                v-if="showColumnsOrganization"
                                :columns="devicesTableColumns"
                                table-id="littl-links"
                                @close="showColumnsOrganization = false"
                                @changed-columns="saveColumnPreferences($event)"
                            ></table-columns-organization>
                        </div>

                        <div class="right">
                            <div class="pages">
							<span>
								1-{{ deviceApps.length }}
								sur {{ deviceApps.length }}
							</span>
                                <div class="small disabled white button">
                                    <i class="fa-regular fa-arrow-left" ></i>
                                </div>
                                <div class="small disabled white button">
                                    <i class="fa-regular fa-arrow-right"></i>
                                </div>
                            </div>

                            <!--						<button class="small white button" @click="alternateLines = !alternateLines">-->
                            <!--							<i v-if="!alternateLines" class="fa-regular fa-square"></i>-->
                            <!--							<i v-else class="fa-regular fa-square-check"></i>-->
                            <!--							Alterner les lignes-->
                            <!--						</button>-->
                        </div>
                    </div>

                    <div class="table-scroll-wrapper">
                        <table class="data-table">
                            <thead>
                            <tr>
                                <td class="toggle-all">
                                    <div class="checkbox" @click="toggleAll()" :class="{'half-selected': isOneDeviceSelected(), selected: areAllDevicesSelected()}">
                                        <i v-if="areAllDevicesSelected()" class="fa-regular fa-check"></i>
                                        <i v-else-if="isOneDeviceSelected()" class="fa-regular fa-minus"></i>
                                    </div>

                                    <span class="selected-number" v-if="selectedDevices.length > 0"> ({{ selectedDevices.length }}) </span>
                                </td>
                                <td :class="{ 'mobile-hidden': column.mobileHidden }" v-for="column in devicesTableColumns.filter((column) => column.displayed)">
                                    <div class="flex-container" @click="sortRow(column.name)">
                                        {{ column.title }}
                                        <template v-if="sortedRow && sortedRow.name === column.name">
                                            <i v-if="sortedRow.direction === 'asc'" class="fa-regular fa-sort-up"></i>
                                            <i v-else class="fa-regular fa-sort-down"></i>
                                        </template>
                                        <i v-else class="fa-regular fa-sort"></i>
                                    </div>
                                </td>
                                <td></td>
                            </tr>
                            </thead>
                            <tbody>
                            <tr class="table-dimmer" :class="{relative: deviceApps.length === 0}" v-if="loading">
                                <td colspan="100%">
                                    <div class="dimmer">
                                        <div class="loader-container">
                                            <div class="loader"></div>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            <tr class="table-no-data" v-else-if="deviceApps.length === 0">
                                <td colspan="100%">
                                    Aucune donnée
                                </td>
                            </tr>
                            <tr v-else v-for="device in deviceApps" :class="{selected: isDeviceSelected(device)}" @click="toggleDevice($event, device)" @mousedown="unselectAll()">
                                <td>
                                    <div class="checkbox" :class="{selected: isDeviceSelected(device)}">
                                        <i class="fa-regular fa-check"></i>
                                    </div>
                                </td>
                                <td :class="{ 'mobile-hidden': column.mobileHidden }" v-for="column in devicesTableColumns.filter((column) => column.displayed)">
                                    <template v-if="column.name === 'uid'">
                                        {{ device.uid }}
                                    </template>
                                    <template v-if="column.name === 'hardwareId'">
                                        {{ requireDeviceForApp(device).hardwareId }}
                                    </template>
                                    <template v-else-if="column.name === 'profile'">
                                        {{ device.establishmentAccountUid && getLinkedProfile(device.establishmentAccountUid) ? getLinkedProfile(device.establishmentAccountUid)?.name : 'Aucun' }}
                                    </template>

                                    <template v-else-if="column.name === 'lastHeartbeat'">
                                        {{ getCashlessDeviceStateWithUid(device.uid)?.lastAllTransactionsSentNetworkDatetime }}
                                    </template>

                                    <template v-else-if="column.name === 'batteryLevel'">
                                        {{ getCashlessDeviceStateWithUid(device.uid)?.batteryLevel }}
                                    </template>

                                    <template v-else-if="column.name === 'creationDatetime'">
                                        {{ device.creationDatetime }}
                                    </template>
                                    <template v-else-if="column.name === 'applicationVersion'">
                                        {{getCashlessDeviceStateWithUid(device.uid)?.applicationVersionCode}}
                                    </template>
                                </td>

                                <td>
                                    <dropdown-button
                                        v-if="getCashlessDeviceWithUid(device.uid)"
                                        button-class="tertiary icon button"
                                        icon="fa-regular fa-ellipsis-vertical"
                                        alignment="RIGHT"
                                        @clicked="showDeviceStateModal = requireCashlessDeviceWithUid(device.uid)"
                                        :actions="[{title: '', actions: [{
                                            id: 'open-ticket',
                                            name: 'Afficher l\'état',
                                            icon: 'fa-regular fa-circle-question fa-fw'
                                        }]}]"
                                    ></dropdown-button>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>

                    <button class="mobile-floating big button" @click="drawerOpened = true" v-if="selectedDevices.length > 0">
                        <i class="fa-regular fa-check"></i>
                        Valider la sélection
                    </button>

<!--                    <div class="hotkeys">-->
<!--                        <div class="hotkey">-->
<!--                            Selection multiple-->

<!--                            <div class="key"> Shift </div>-->
<!--                            +-->
<!--                            <div class="key"> Clic </div>-->
<!--                        </div>-->
<!--                    </div>-->
                </div>
            </template>

            <template v-slot:right>
                <div v-if="selectedDevices.length === 0" class="empty-right-panel">
                    <img :src="$assets.selectHint" />
                    Cliquer sur un appareil pour <br/> le sélectionner
                </div>
                <div class="right-view" v-else>
                    <div class="close" @click="drawerOpened = false">
                        <i class="fa-regular fa-xmark"></i>
                        <span> Fermer </span>
                    </div>

                    <div class="header">
                        <div class="left">
                            <h2> Point de vente par défaut </h2>
                            <span> Lier un point de vente par défaut à tous les appareils sélectionnés </span>
                        </div>
                    </div>

                    <div class="selected-devices">
                        <div class="form">
                            <div class="input-group">
                                <label for="title"> Nom </label>
                                <dropdown
                                    :default-selected="selectedProfile"
                                    :values="getProfilesDropdownValues()"
                                    :placeholder="'Point de vente par défaut'"
                                    @update="selectedProfile = $event"
                                ></dropdown>
                            </div>
                        </div>

                        <div class="buttons">
                            <button class="black button" :class="{loading: linkingDevices, disabled: linkingDevices}" @click="linkDevicesWithAccount()">
                                <i class="fa-regular fa-check"></i>
                                Lier
                            </button>
                        </div>
                    </div>
                </div>
            </template>
        </layout>

<!--        <modal-or-drawer :state="creatingDeviceData !== null" @close="creatingDeviceData = null">-->
<!--            <div class="form" v-if="creatingDeviceData">-->
<!--                <div class="title-group">-->
<!--                    <h3> Ajouter un appareil </h3>-->
<!--                    <span> Ajouter un appareil à ma flotte  </span>-->
<!--                </div>-->

<!--                <div class="input-group">-->
<!--                    <label for="title"> Fabricant </label>-->
<!--                    <dropdown-->
<!--                        placeholder="Fabricant de l'appareil"-->
<!--                        :values="getDeviceBrandsValues()"-->
<!--                        :default-selected="creatingDeviceData.brand"-->
<!--                        @update="creatingDeviceData.brand = $event"-->
<!--                    ></dropdown>-->
<!--                </div>-->

<!--                <div class="input-group">-->
<!--                    <label for="title"> ID de l'appareil </label>-->
<!--                    <div class="ui input">-->
<!--                        <input v-model="creatingDeviceData.serial" type="text" placeholder="ID de l'appareil " />-->
<!--                    </div>-->
<!--                </div>-->

<!--                <div class="form-error" v-if="linkError">-->
<!--                    <i class="fa-solid fa-exclamation-circle"></i>-->
<!--                    <div class="details">-->
<!--                        <span class="title"> Erreur </span>-->
<!--                        <span class="description">{{ linkError }}</span>-->
<!--                    </div>-->
<!--                </div>-->

<!--&lt;!&ndash;                <div class="form-error" v-if="appState.requireEstablishment().params.maxEstablishmentAccounts <= accountProfiles.length">&ndash;&gt;-->
<!--&lt;!&ndash;                    <i class="fa-solid fa-exclamation-circle"></i>&ndash;&gt;-->
<!--&lt;!&ndash;                    <div class="details">&ndash;&gt;-->
<!--&lt;!&ndash;                        <span class="title"> Quota atteint </span>&ndash;&gt;-->
<!--&lt;!&ndash;                        <span class="description">&ndash;&gt;-->
<!--&lt;!&ndash;                            Vous avez atteint le quota maximum de {{appState.requireEstablishment().params.maxEstablishmentAccounts}} appareils autorisés&ndash;&gt;-->
<!--&lt;!&ndash;                        </span>&ndash;&gt;-->
<!--&lt;!&ndash;                    </div>&ndash;&gt;-->
<!--&lt;!&ndash;                </div>&ndash;&gt;-->

<!--                <div class="buttons">-->
<!--                    <button class="ui white button" @click="creatingDeviceData = null">-->
<!--                        <i class="fa-regular fa-xmark" style="margin-top: 3px"></i>-->
<!--                        Annuler-->
<!--                    </button>-->
<!--                    <button class="button" @click="addDevice()" :class="{loading: creatingDevice, disabled: creatingDevice}">-->
<!--                        <i class="fa-regular fa-check" style="margin-top: 3px"></i>-->
<!--                        Ajouter-->
<!--                    </button>-->
<!--                </div>-->
<!--            </div>-->
<!--        </modal-or-drawer>-->

<!--        <device-import-->
<!--            :state="showImportModal"-->
<!--            @imported-devices="importedDevices($event)"-->
<!--            @close="showImportModal = false"-->
<!--        ></device-import>-->

        <device-state
            v-if="showDeviceStateModal"
            :device-state="showDeviceStateModal"
            @close="showDeviceStateModal = null"
        ></device-state>

        <device-onboarding-scan v-if="showScanModal" @imported="loadDevices()" @close="showScanModal = false"></device-onboarding-scan>

        <form-modal-or-drawer
            :state="showManualLinkModal"
            :title="''"
            :subtitle="''"
            @close="showManualLinkModal = null"
        >
            <template v-slot:content>
                <div class="input-group" v-if="showManualLinkModal">
                    <label> QuickLinkUid </label>
                    <input v-model="showManualLinkModal.quickLinkUid" placeholder="QuickLinkUid" />
                </div>

                <div class="buttons">
                    <button v-if="isNative" class="grey button" @click="readCodeWithCamera($event)">
                        <i class="fa-regular fa-qrcode"></i>
                        Caméra
                    </button>
                    <button v-else class="grey button" @click="readCodeWithWebCamera()">
                        <i class="fa-regular fa-qrcode"></i>
                        Caméra
                    </button>
                </div>

                <div class="form-error" v-if="showManualLinkModal && showManualLinkModal.error">
                    <i class="fa-regular fa-circle-exclamation"></i>
                    <div class="details">
                        <span class="title"> Erreur </span>
                        <span class="description">{{ showManualLinkModal.error }}</span>
                    </div>
                </div>
            </template>

            <template v-slot:buttons>
                <button class="white button" @click="showManualLinkModal = null"> Annuler </button>
                <button class="button" @click="manualImportQuickLink()"> Valider </button>
            </template>
        </form-modal-or-drawer>

        <toast-manager></toast-manager>

        <web-qr-code-reader
            v-if="!isNative"
            ref="webQrReader"
            :prompt-camera-on-scan="true"
            @scanned="importWithQrCode($event)"
            @not-configured="webQrReader.showCameraChooser = false"
            @close=""
        ></web-qr-code-reader>
    </div>
</template>