.icon-bank-component {
    display: flex;
    height: 400px;
    border-radius: 12px;
    overflow: hidden;
    z-index: 999;
    box-shadow: 0 4px 86px 0 rgba(0, 0, 0, 0.13);
    width: 500px;

    @media (max-width: 900px) {
        width: auto;
    }

    .navigation {
        flex-shrink: 0;
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 5px;
        padding: 20px 10px;
        background: #E3E3E3;
        overflow: auto;

        .item {
            width: 100%;
            flex-shrink: 0;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 10px;
            gap: 4px;
            cursor: pointer;
            border-radius: 8px;
            box-sizing: border-box;

            &.selected {
                background: #D9D9D9;
            }

            &:hover {
                background: #D9D9D9;
            }

            i {
                font-size: 18px;
            }

            .name {
                font-size: 14px;
            }
        }
    }

    .content {
        flex-grow: 2;
        display: flex;
        flex-direction: column;
        gap: 20px;
        padding: 15px;
        background: white;
        box-sizing: border-box;
        overflow: auto;

        .top {
            position: sticky;
            top: -15px;
            background: white;
            margin: -15px;
            padding: 15px;

            display: flex;
            gap: 10px;

            .input-group {
                flex-grow: 2;
            }

            button {
                flex-shrink: 0;
            }
        }

        .icon.input-group  {
            i {
                color: grey;
            }
        }

        .icons {
            display: grid;
            grid-gap: 10px;
            grid-template-columns: repeat(6, 1fr);

            @media (max-width: 900px) {
                grid-template-columns: repeat(5, 1fr);
            }

            .icon {
                padding: 5px;
                width: 100%;
                border-radius: 6px;
                cursor: pointer;
                box-sizing: border-box;

                &:hover {
                    background: var(--secondary-hover-color);
                }
            }
        }
    }
}