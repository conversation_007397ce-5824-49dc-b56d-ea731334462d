import {Component, Vue} from "vue-facing-decorator";
import {
    ContentHeaderComponent, ContentHeaderParameters, DropdownButtonAction,
    DropdownButtonComponent,
    LayoutContentWithRightPanelComponent
} from "@groupk/vue3-interface-sdk";
import ToastManagerComponent from "../../components/ToastManagerComponent/ToastManagerComponent.vue";
import {RefundBatchApiOut} from "@groupk/mastodon-core";
import {AutoWired} from "@groupk/horizon2-core";
import {AppState} from "../../../../../shared/AppState";
import {AppBus} from "../../config/AppBus";
import SidebarStateListener from "../../../../../shared/utils/SidebarStateListener";
import {RefundRepository} from "../../../../../shared/repositories/RefundRepository";
import {RefundBatchItemApiOut} from "@groupk/mastodon-core";
import {MultiFormatExporter} from "../../../../../shared/utils/MultiFormatExporter";
import {EstablishmentUrlBuilder} from "../../../../../shared/utils/EstablishmentUrlBuilder";
import {Router} from "@groupk/horizon2-front";

@Component({
    components: {
        'content-header': ContentHeaderComponent,
        'layout': LayoutContentWithRightPanelComponent,
        'toast-manager': ToastManagerComponent,
        'dropdown-button': DropdownButtonComponent,
    }
})
export default class RefundBatches extends Vue {
    headerParameters: ContentHeaderParameters = {
        header: 'Remboursements effectués',
        subtitle: 'Liste des remboursements effectués',
        actions: [{
            type: 'SIMPLE_ACTION',
            name: 'Voir les demandes de remboursement',
            icon: 'fa-regular fa-arrow-up-right-from-square',
            callback: this.goToRefundBatches
        }],
        hideSearch: true,
        searchPlaceholder: ''
    }

    selectedBatch: RefundBatchItemApiOut|null = null;
    batches: RefundBatchApiOut[] = [];
    loadingBatch: boolean = false;
    loading: boolean = true;

    @AutoWired(RefundRepository) accessor refundRepository!: RefundRepository;
    @AutoWired(AppState) accessor appState!: AppState;
    @AutoWired(SidebarStateListener) accessor sidebarStateListener!: SidebarStateListener;
    @AutoWired(AppBus) accessor appBus!: AppBus;
    @AutoWired(Router) accessor router!: Router;


    beforeMount() {
        this.sidebarStateListener.setMinimizedSidebar(false);
        this.sidebarStateListener.setHiddenSidebar(false);
    }

    async mounted() {
        this.batches = (await this.refundRepository.callContract('listBatch', {establishmentUid: this.appState.requireUrlEstablishmentUid()}, undefined)).success().list;
        this.loading = false;
    }

    async toggleSelectedBatch(batch: RefundBatchApiOut) {
        if(this.selectedBatch && this.selectedBatch.item.uid === batch.uid) {
            this.selectedBatch = null;
        } else {
            this.selectedBatch = null;
            this.loadingBatch = true;
            try {
                this.selectedBatch = (await this.refundRepository.callContract('getOneBatch', {establishmentUid: this.appState.requireUrlEstablishmentUid(), refundBatchUid: batch.uid}, undefined)).success();
            } catch(err) {}
            this.loadingBatch = false;
        }
    }

    async dropdownClicked(action: DropdownButtonAction) {
        if(!this.selectedBatch) return;
        if(action.id === 'export') {
            const jsonData: Record<string, string|number>[] = [];

            for(const refund of this.selectedBatch.refundList) {
                jsonData.push({
                    'Nom': refund.lastname,
                    'Prenom': refund.firstname,
                    'Email': refund.email,
                    'Telephone':refund.phoneNumber,
                    'Montant rembourse': (refund.amount ?? 0) / 100,
                    'iban':refund.iban,
                    'bic':refund.bic
                });
            }

            MultiFormatExporter.downloadData([
                {name: 'Nom', type: 'AUTO'},
                {name: 'Prenom', type: 'AUTO'},
                {name: 'Email', type: 'AUTO'},
                {name: 'Telephone', type: 'AUTO'},
                {name: 'Montant rembourse', type: 'AUTO'},
                {name: 'iban', type: 'AUTO'},
                {name: 'bic', type: 'AUTO'},
            ], jsonData, 'csv');
        } else if(action.id === 'download') {
            let response = (await this.refundRepository.callContract('exportSepa', {
                establishmentUid: this.appState.requireUrlEstablishmentUid(),
                refundBatchUid: this.selectedBatch.item.uid
            }, undefined));

            if(response.isSuccess()) {
                const sepa = response.success();
                const blob = new Blob([sepa]);
                const link= document.createElement('a');
                link.href = window.URL.createObjectURL(blob);
                link.download = "export." + 'xml';
                link.click();
                window.URL.revokeObjectURL(link.href);
                link.remove();
            } else {
                this.appBus.emit('emit-toast', {
                    title: 'L\'export a échoué',
                    description: 'Une erreur technique est survenue.',
                    duration: 3000,
                    type: 'ERROR',
                    closable: true
                });
            }
        }
    }
    goToRefundBatches() {
        this.router.changePage(EstablishmentUrlBuilder.buildUrl('/refund-requests'));
    }
    goToChipTransactions(publicChipId: string) {
        window.open(EstablishmentUrlBuilder.buildUrl('/transactions?chip=' + publicChipId), '_blank');
    }
}