<script lang="ts" src="./settings.ts">
</script>

<style lang="sass">
@use './settings.scss' as *
</style>

<template>
    <div id="settings-page" class="page">
        <left-navigation
            page-title="Paramètres"
            :navigation="navigation"
            @changed-page="currentPage = $event"
        >
            <layout :drawer-opened="selectedEstablishmentAccount !== null">
                <template v-slot:content>
                    <accounts-settings
                        v-if="currentPage === 'partners'"
                        :establishment-accounts="establishmentAccounts"
                        :app-id="CashlessAppId"
                        :front-app-id="CashlessFrontAppId"
                        :back-permissions="CashlessPermissions"
                        :front-permissions="CashlessFrontPermissions"
                        :front-permissions-translations="permissionTranslations"
                        :roles="roles"
                    ></accounts-settings>

                    <div class="main-content" v-else>
                        <div class="setting-group">
                            <div class="title"> Cashless </div>

                            <div class="form">
                                <div class="input-group right-icon">
                                    <label for="title"> Mot de passe admin </label>
                                    <template v-if="cashlessEstablishment.adminPassword !== undefined">
                                        <div class="icon-group">
                                            <i class="fa-regular fa-xmark" @click="cashlessEstablishment.adminPassword = undefined"></i>
                                            <div class="ui input" v-if="cashlessEstablishment.adminPassword !== undefined">
                                                <input v-model="cashlessEstablishment.adminPassword" type="text" />
                                            </div>
                                        </div>
                                    </template>

                                    <button class="button" @click="cashlessEstablishment.adminPassword = ''" v-else>
                                        <i class="fa-regular fa-plus"></i>
                                        Ajouter un mot de passe admin global
                                    </button>
                                </div>

                                <div class="sub-form">
                                    <div class="header">
                                        <span class="title"> Puces admin </span>
                                    </div>

                                    <div class="two-inputs">
                                        <span v-if="!cashlessEstablishment.adminPublicChipIds || cashlessEstablishment.adminPublicChipIds.length === 0" class="empty-sub-form"> Aucune puce admin </span>
                                        <template v-else>
                                            <div class="input-group" v-for="(chipId, index) in cashlessEstablishment.adminPublicChipIds">
                                                <label for="title"> Numéro de puce </label>
                                                <div class="action-input">
                                                    <input maxlength="8" class="white" :value="cashlessEstablishment.adminPublicChipIds[index]" @input="setAdminPublicChip($event, index)"  type="text" />
                                                    <i @click="cashlessEstablishment.adminPublicChipIds.splice(index, 1)" class="fa-regular fa-trash-alt"></i>
                                                </div>
                                            </div>
                                        </template>
                                    </div>

                                    <div>
                                        <button class="small transparent button" @click="addAdminChip()">
                                            <i class="fa-regular fa-plus"></i>
                                            Ajouter une puce
                                        </button>
                                    </div>
                                </div>

                                <div class="sub-form">
                                    <div class="header">
                                        <span class="title"> Dates de remboursement </span>
                                    </div>

                                    <div v-if="!cashlessEstablishment.refunds">
                                        Désactivé

                                        <button class="button" @click="enableRefunds()"> Activer </button>
                                    </div>
                                    <template v-else>
                                        <div class="two-inputs">
                                            <div class="input-group">
                                                <label for="title"> Date d'ouverture </label>
                                                <input-date
                                                    :value="cashlessEstablishment.refunds.startingDatetime"
                                                    @change="cashlessEstablishment.refunds.startingDatetime = $event"
                                                ></input-date>
                                                <div class="error" v-if="refundErrors['startingDatetime']"> {{ refundErrors['startingDatetime'] }} </div>
                                            </div>

                                            <div class="input-group">
                                                <label for="title"> Date de fin </label>
                                                <input-date
                                                    :value="cashlessEstablishment.refunds.endingDatetime"
                                                    @change="cashlessEstablishment.refunds.endingDatetime = $event"
                                                ></input-date>
                                                <div class="error" v-if="refundErrors['endingDatetime']"> {{ refundErrors['endingDatetime'] }} </div>
                                            </div>
                                        </div>

                                        <div class="two-inputs">
                                            <div class="input-group">
                                                <label for="title"> Frais de résiliation </label>
                                                <input class="white" type="text" v-model.number="cashlessEstablishment.refunds.feesAmount" />
                                            </div>

                                            <div class="input-group">
                                                <label for="title"> Montant minimal pour remboursement </label>
                                                <input class="white" type="text" v-model.number="cashlessEstablishment.refunds.minAmount" />
                                            </div>
                                        </div>
                                    </template>
                                </div>

                                <div class="buttons">
                                    <button class="button" :class="{disabled: savingCashlessEstablishment, loading: savingCashlessEstablishment}" @click="saveCashlessEstablishment()"> Sauvegarder </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </template>
                <template v-slot:right>
                    <div class="selected-establishment-account" v-if="selectedEstablishmentAccount">
                        <div class="close" @click="selectedEstablishmentAccount = null">
                            <i class="fa-regular fa-xmark"></i>
                            <span> Fermer </span>
                        </div>

                        <div class="header">
                            <div class="left">
                                <h2> {{ selectedEstablishmentAccount.firstname }} {{ selectedEstablishmentAccount.lastname }} </h2>
                            </div>
                        </div>

                        <div class="content">
                            <div class="roles">
                                <div class="role" :class="{selected: role.id === selectedRole}" v-for="role in roles" @click="selectedRole = role.id">
                                    <div class="top">
                                        <span class="title"> {{ role.title }} </span>
                                        <i class="fa-regular fa-check" v-if="role.id === selectedRole"></i>
                                    </div>
                                    <span class="description">
                                        {{ role.description }}
                                    </span>
                                    <div class="bottom" v-if="role.id !== null">
                                        <button class="tertiary button" @click.stop="showRoleDetailsModal = role.id">
                                            <i class="fa-regular fa-info-circle"></i>
                                            Détails
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <div class="form-error" v-if="updateError">
                                <i class="fa-solid fa-exclamation-circle"></i>
                                <div class="details">
                                    <span class="title"> Erreur </span>
                                    <span class="description"> {{ updateError }} </span>
                                </div>
                            </div>

                            <div class="save-button button" :class="{loading: updatingAccount, disabled: updatingAccount}" @click="updateAccountRole(selectedEstablishmentAccount)"> Sauvegarder </div>
                        </div>
                    </div>
                </template>
            </layout>
        </left-navigation>

        <form-modal-or-drawer
            :state="showRoleDetailsModal"
            @close="showRoleDetailsModal = null"
            title="Details du rôle"
            subtitle="Liste précise des droits accordés par ce rôle"
        >
            <template v-slot:content>
                <div class="permissions" v-if="showRoleDetailsModal">
                    <div class="permission" :class="{ok: EstablishmentAccountPermissionModel.hasPermissionsFromList(permissionModel.getBackPermissionsWithFrontRole(CashlessFrontAppId, showRoleDetailsModal??''), {list: {applicationId: CashlessAppId, id: permission}})}" v-for="(translation, permission) of permissionTranslations">
                        <i class="fa-regular fa-check fa-fw" v-if="EstablishmentAccountPermissionModel.hasPermissionsFromList(permissionModel.getBackPermissionsWithFrontRole(CashlessFrontAppId, showRoleDetailsModal??''), {list: {applicationId: CashlessAppId, id: permission}})"></i>
                        <i class="fa-regular fa-xmark fa-fw" v-else></i>
                        {{ translation }}
                    </div>
                </div>
            </template>
            <template v-slot:buttons>
                <button class="button" @click="showRoleDetailsModal = null">
                    Fermer
                </button>
            </template>
        </form-modal-or-drawer>

        <establishment-account-form
            v-if="showAccountCreationModal"
            :establishment-uid="appState.requireUrlEstablishmentUid()"
            @created="createdAccount($event)"
            @close="showAccountCreationModal = false"
        ></establishment-account-form>

        <toast-manager></toast-manager>
    </div>
</template>