import {Component, Prop, Vue} from 'vue-facing-decorator';
import {LinkApiIn, LinkApiOut, LinkTarget, LittlLinkTargetContract} from '@groupk/mastodon-core';
import {AutoWired, VisualScopedUuid} from '@groupk/horizon2-core';
import {LinkRepository} from '../../repositories/LinkRepository';
import {randomUUID} from '@groupk/horizon2-front';
import TargetLinkComponent from '../TargetLinkComponent/TargetLinkComponent.vue';
import {UuidScopeEstablishment} from "@groupk/mastodon-core";
import {AppBus} from "../../config/AppBus";
import {LinkData} from "../../../../../shared/mastodonCoreFront/littl/LinkData";
import {translateResponseError} from "../../../../../shared/RepositoryExtensions";

@Component({
	emits: ['saved', 'cancel', 'error', 'loading-start', 'loading-end'],
	components: {
		'targets': TargetLinkComponent
	}
})
export default class LinkComponent extends Vue {
	@Prop() readonly linkApiOut: LinkApiOut | undefined;
	@Prop() readonly establishmentUid!: VisualScopedUuid<UuidScopeEstablishment>;
	@Prop() readonly focus!: boolean;
	link: LinkData = new LinkData([new LinkTarget('')]);

	linkDefinition = LinkApiOut.__entityDefinition;

	error: string | null = null;
	loading: boolean = false;

	id: string = 'component-' + randomUUID();

	@AutoWired(LinkRepository) accessor linkRepository!: LinkRepository;
	@AutoWired(AppBus) accessor appBus!: AppBus;

	mounted() {
		if (this.linkApiOut) {
			this.link.id = this.linkApiOut.id;
			this.link.targets = this.linkApiOut.targets;
		}

		if (this.focus) {
			let inputSelector = document.querySelector('#' + this.id + ' [name=\'title\']');
			if (inputSelector) {
				(inputSelector as HTMLElement).focus();
			}
		}
	}

	async create() {
		this.error = null;

		if(this.link.id.length <= 4) {
			this.error = 'Le lien court doit faire minimum 5 caractères';
			return;
		}

		if(this.link.id.length > (this.linkDefinition.getFieldString('id').maxLength ?? 256)) {
			this.error = `Le lien court doit faire moins de ${this.linkDefinition.getFieldString('id').maxLength ?? 256} caractères`;
			return;
		}

		if(this.link.targets[0].targetUri.length > (LinkTarget.__entityDefinition.getFieldString('targetUri').maxLength ?? 256)) {
			this.error = `Le lien de redirection doit faire moins de ${LinkTarget.__entityDefinition.getFieldString('targetUri').maxLength ?? 256} caractères`;
			return;
		}

		this.$emit('loading-start');
		this.loading = true;

		let linkApiIn = new LinkApiIn();
		linkApiIn.targets = this.link.targets;

		let response = await this.linkRepository.create(this.establishmentUid, this.link.id, linkApiIn);

		if (response.isSuccess()) {
			let linkApiOut = response.success();
			this.link.targets = linkApiOut.targets;
			this.appBus.emit('linkCreated', linkApiOut);
			this.$emit('saved', linkApiOut);
		} else if (response.isError() && response.errorResponse) {
			this.error = translateResponseError<typeof LittlLinkTargetContract, 'update'>(response, {
				invalid_target: 'Le lien ne semble plus exister',
				id_already_used: 'Ce Littl est déjà utilisé',
				max_link_limit_reach: 'Vous avez atteint le nombre de lien maximum. Pensez à changer de plan de facturation',
				invalid_data: 'Données invalides',
			})
			this.$emit('error');
		}
		this.$emit('loading-end');
		this.loading = false;
	}

	cancel() {
		this.$emit('cancel');
	}
}
