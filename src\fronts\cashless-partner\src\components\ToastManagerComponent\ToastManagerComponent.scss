.toast-manager-component {
    position: fixed;
    bottom: 40px;
    right: 40px;
    z-index: 1500;

    display: flex;
    flex-direction: column;
    gap: 10px;

    .toast {
        display: flex;
        align-items: flex-start;
        padding: 15px;
        gap: 15px;
        background: #161618;
        border-radius: 12px;
        max-width: 350px;
        user-select: none;
        box-shadow: 0 4px 50px 0 rgba(0, 0, 0, 0.25);
        z-index: 999;

        animation: .2s ease-out 0s 1 slideIn;

        .right, .left {
            flex-shrink: 0;

            .fa-circle-check {
                font-size: 20px;
                color: #4BD264;
            }
            .fa-circle-xmark {
                font-size: 18px;
                color: #fa4848;
                cursor: pointer;
            }

            .fa-xmark {
                font-size: 18px;
                color: #9a9a9a;
                cursor: pointer;
            }
        }

        .center {
            flex-grow: 2;
            display: flex;
            flex-direction: column;
            gap: 2px;

            .title {
                color: #ffffff;
                font-size: 14px;
                font-weight: 500;
            }

            .description {
                color: #c0c0c0;
                font-size: 13px;
            }
        }
    }
}

@-webkit-keyframes slideIn {
    from {
        transform: translateX(40%);
    }
    to {
        transform: translateX(0)
    }
}