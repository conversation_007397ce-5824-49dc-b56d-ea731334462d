import {Component, Vue} from "vue-facing-decorator";
import {
	ContentHeaderParameters,
	DropdownButtonAction,
	DropdownButtonComponent, DropdownComponent,
	FilterParameters,
	FilterTableLayoutComponent,
	FormModalOrDrawerComponent,
	InputDateComponent,
	SavedFilter,
	ShortcutsManager,
	TableColumn,
	TablePagination,
	ForbiddenMessageComponent
} from "@groupk/vue3-interface-sdk";
import {
	CashlessHttpTransactionContractSearchConfig,
	CurrencyApiOut,
	CustomerChipApiOut,
	ProfileApiOut,
	SimpleProductApiOut,
	TransactionApiOut,
	TransactionFixApiIn,
	TransactionStatus,
	uuidScopeCashless_profile,
	UuidScopeCashless_profile,
	uuidScopeCashless_transaction,
	UuidScopeCashless_transaction,
	UuidScopeIot_deviceApp,
	ApplicationPermission,
	EstablishmentAccountPermissionModel,
	CashlessHttpTransactionContract,
	IotHttpEstablishmentDeviceContract,
	CashlessHttpProfileContract,
	CashlessHttpSimpleProductContract,
	PaymentHttpMethodContract,
	CashlessHttpCurrencyContract,
} from "@groupk/mastodon-core";
import {
	AutoWired,
	dehydrate,
	EnumUtils,
	QueryFilterGroupClause,
	QueryOperator,
	ScopedUuid,
	TypedQuerySearch,
	Uuid,
	UuidUtils,
	VisualScopedUuid
} from "@groupk/horizon2-core";
import {TableColumnsRepository} from "../../../../../shared/repositories/TableColumnsRepository";
import {OptionBuilder} from "vue-facing-decorator/dist/optionBuilder";
import {uuidScopeEstablishment, UuidScopeEstablishment} from "@groupk/mastodon-core";
import {Router} from "@groupk/horizon2-front";
import SidebarStateListener from "../../../../../shared/utils/SidebarStateListener";
import {TransactionsRepository} from "../../../../../shared/repositories/TransactionsRepository";
import {AppBus} from "../../config/AppBus";
import {ProfilesRepository} from "../../../../../shared/repositories/ProfilesRepository";
import {CurrencyRepository} from "../../../../../shared/repositories/CurrencyRepository";
import {SavedFiltersRepository} from "../../../../../shared/repositories/SavedFiltersRepository";
import {EstablishmentDevicesRepository} from "../../../../../shared/repositories/EstablishmentDevicesRepository";
import {EstablishmentDeviceApiOut} from "@groupk/mastodon-core";
import {PaymentMethodApiOut, UuidScopePayment_method} from "@groupk/mastodon-core";
import {PaymentMethodsRepository} from "../../../../../shared/repositories/PaymentMethodsRepository";
import {ProductsRepository} from "../../../../../shared/repositories/ProductsRepository";
import {CustomerChipRepository} from "../../../../../shared/repositories/CustomerChipRepository";
import {EstablishmentUrlBuilder} from "../../../../../shared/utils/EstablishmentUrlBuilder";
import {PaymentMethodFilter} from "../../../../../shared/filters/CashlessFilters";
import DateUtils from "../../../../../shared/utils/DateUtils";
import TransactionMethodFormComponent
	from "../../components/TransactionMethodFormComponent/TransactionMethodFormComponent.vue";
import {UuidScopeCustomer_customer} from "@groupk/mastodon-core";
import {AppState} from "../../../../../shared/AppState";
import {QueryFilterUtils} from "../../../../../shared/utils/QueryFilterUtils";
import {ExportFileType, MultiFormatExporter} from "../../../../../shared/utils/MultiFormatExporter";
import {ComponentUtils} from "../../../../../shared/utils/ComponentUtils";
import {TransactionMethodFormComponentHasRequiredPermissions} from "../../components/TransactionMethodFormComponent/TransactionMethodFormComponent";

@Component({
	components: {
		'filter-table-layout': FilterTableLayoutComponent,
		'form-modal-or-drawer': FormModalOrDrawerComponent,
		'date-input': InputDateComponent,
		'dropdown-button': DropdownButtonComponent,
		'dropdown': DropdownComponent,
		'transaction-method-form': TransactionMethodFormComponent,
		'forbidden-message': ForbiddenMessageComponent,
	},
	emits: ['updated', 'close'],
})
export default class TransactionsView extends Vue {
	establishmentUid!: VisualScopedUuid<UuidScopeEstablishment>;

	transactions: TransactionApiOut[] = [];
	profiles: ProfileApiOut[] = [];
	paymentMethods: PaymentMethodApiOut[] = [];
	products: SimpleProductApiOut[] = [];
	devices: EstablishmentDeviceApiOut[] = [];
	currencies: CurrencyApiOut[] = [];
	selectedTransaction: TransactionApiOut|null = null;
	selectedTransactionCustomerChip: CustomerChipApiOut|null = null;
	newTransactions: TransactionApiOut[] = [];

	loading: boolean = false;
	forbidden: boolean = false;
	disabledActions: string[] = [];

	headerParameters: ContentHeaderParameters = {
		header: 'Transactions',
		subtitle: 'Liste des flux d\'argent sur votre plateforme Cashless',
		actions: [{
			type: 'SIMPLE_ACTION',
			name: 'Exporter',
			icon: 'fa-regular fa-arrow-down-to-line',
			callback: this.showExportModalFnc
		}],
		hideSearch: false,
		searchPlaceholder: 'Rechercher une transaction'
	}

	TransactionStatus = TransactionStatus;
	allowedFilters = CashlessHttpTransactionContractSearchConfig;
	filters: TypedQuerySearch<typeof CashlessHttpTransactionContractSearchConfig> = {};
	appliedFilters: TypedQuerySearch<typeof CashlessHttpTransactionContractSearchConfig> = {};

	filterParameters: { [filterName: string]: FilterParameters } = {
		'uid': {
			translation: 'ID de transaction',
			type: 'UNKNOWN',
			validation: (value: unknown) => {
				return typeof value === 'string' && UuidUtils.isVisual(value, uuidScopeCashless_transaction);
			}
		},
		'publicDebitedChipId': {
			translation: 'Puce débitée',
			type: 'UNKNOWN',
			validation: (value: unknown) => {
				return typeof value === 'string' && value.trim() !== '' && value.length >= 8;
			}
		},
		'publicCreditedChipId': {
			translation: 'Puce créditée',
			type: 'UNKNOWN',
			validation: (value: unknown) => {
				return typeof value === 'string' && value.trim() !== '' && value.length >= 8;
			}
		},
		'chipVisualId': {
			translation: 'Numéro de puce',
			type: 'UNKNOWN',
			validation: (value: unknown) => {
				return typeof value === 'string' && value.trim() !== '' && value.length >= 8;
			}
		},
		'currency': {
			translation: 'Devise',
			type: 'DROPDOWN',
			dropdownValues: [],
			validation: QueryFilterUtils.simpleStringValidation
		},
		'amount': {
			translation: 'Montant',
			type: 'DECIMAL',
			transformFunction: QueryFilterUtils.priceTransformFunction(),
			validation: QueryFilterUtils.amountValidation
		},
		'profileUid': {
			translation: 'Point de vente',
			type: 'DROPDOWN',
			validation: QueryFilterUtils.simpleStringValidation
		},
		'establishmentAccountUid': {
			translation: 'Compte émetteur',
			type: 'UUID',
			validation: QueryFilterUtils.simpleStringValidation
		},
		'creationDatetimeOnServer': {
			translation: 'Date de création serveur',
			type: 'DATETIME',
			validation: QueryFilterUtils.dateValidation
		},
		'finalStateDatetimeOnServer': {
			translation: 'Date du status final serveur',
			type: 'DATETIME',
			validation: QueryFilterUtils.dateValidation
		},
		'creationDatetime': {
			translation: 'Date de création',
			type: 'DATETIME',
			validation: QueryFilterUtils.dateValidation
		},
		'finalStateDatetime': {
			translation: 'Date du status final',
			type: 'DATETIME',
			validation: QueryFilterUtils.dateValidation
		},
		'status': {
			translation: 'Statut',
			type: 'DROPDOWN',
			dropdownValues: EnumUtils.values(TransactionStatus).map((value) => {
				return  {name: value, value: value};
			}),
			validation: QueryFilterUtils.simpleStringValidation
		},
	}

	pagination: TablePagination = {
		totalResults: 0,
		resultsPerPage: 50,
		currentPage: 1,
		estimateTotal: false
	}
	// Puce (soit débitée soit créditée) price is not virtual (chipModel.isVirutal)
	// Prix négatif si débité
	// Colonne point de vente par défaut

	tableKey = 'cashless-transaction';
	tableColumns: TableColumn[] = [{
		title: 'ID', name: 'uid', displayed: false, mobileHidden: true
	}, {
		title: 'Puce', name: 'chip', displayed: true, mobileHidden: false
	}, {
		title: 'Montant', name: 'amount', displayed: true, mobileHidden: false
	}, {
		title: 'Point de vente', name: 'profileUid', displayed: true, mobileHidden: false
	}, {
		title: 'Devise', name: 'currency', displayed: false, mobileHidden: true
	}, {
		title: 'Status', name: 'status', displayed: true, mobileHidden: false
	}, {
		title: 'Date', name: 'creationDatetime', displayed: true, mobileHidden: false
	}, {
		title: '', name: 'action', displayed: true, mobileHidden: false
	}];

	savedFilters: SavedFilter[] = [];

	showPaymentMethodFormModal: TransactionApiOut|null = null;
	showExportModal: boolean = false;
	exporting: boolean = false;
	exportError: string|null = null;
	exportDates: { start: string, end: string } = {start: '', end: ''};

	@AutoWired(TransactionsRepository) accessor transactionsRepository!: TransactionsRepository
	@AutoWired(ProfilesRepository) accessor profilesRepository!: ProfilesRepository
	@AutoWired(PaymentMethodsRepository) accessor paymentMethodsRepository!: PaymentMethodsRepository
	@AutoWired(ProductsRepository) accessor productsRepository!: ProductsRepository
	@AutoWired(EstablishmentDevicesRepository) accessor establishmentDevicesRepository!: EstablishmentDevicesRepository;
	@AutoWired(CustomerChipRepository) accessor customerChipRepository!: CustomerChipRepository;
	@AutoWired(CurrencyRepository) accessor currencyRepository!: CurrencyRepository
	@AutoWired(TableColumnsRepository) accessor tableColumnsRepository!: TableColumnsRepository
	@AutoWired(SavedFiltersRepository) accessor savedFiltersRepository!: SavedFiltersRepository
	@AutoWired(SidebarStateListener) accessor sidebarStateListener!: SidebarStateListener
	@AutoWired(Router) accessor router!: Router
	@AutoWired(AppBus) accessor appBus!: AppBus
	@AutoWired(ShortcutsManager) accessor shortcutsManager!: ShortcutsManager;
	@AutoWired(AppState) accessor appState!: AppState;

	constructor(optionBuilder: OptionBuilder, vueInstance: any) {
		super(optionBuilder, vueInstance);

		this.loading = true;

		this.sidebarStateListener.setHiddenSidebar(false);
		this.sidebarStateListener.setMinimizedSidebar(false);

		let regexMatch = this.router.lastRouteRegexMatches;

		if (regexMatch && regexMatch[1]) {
			this.establishmentUid = UuidUtils.scopedToVisual<UuidScopeEstablishment>(regexMatch[1] as ScopedUuid<UuidScopeEstablishment>, uuidScopeEstablishment);
		}

		let savedPreferences = this.tableColumnsRepository.getColumnPreferences(this.tableKey, this.tableColumns);
		if(savedPreferences) this.tableColumns = savedPreferences;

		this.savedFilters = this.savedFiltersRepository.getFilters(this.tableKey) ?? [];

		// Check permissions for form components and disable actions accordingly
		if (!ComponentUtils.hasPermissions(TransactionMethodFormComponentHasRequiredPermissions)) {
			this.disabledActions.push('change-method');
		}

		if (!ComponentUtils.hasPermissions((ownedPermissions: ApplicationPermission[]) => {
			return EstablishmentAccountPermissionModel.hasPermissionsForContracts(ownedPermissions, [
				CashlessHttpTransactionContract.fix,
			])
		})) {
			this.disabledActions.push('fix');
		}

		this.appBus.on('transactionReceived', this.updateTransactions)
	}

	updateTransactions(transaction: TransactionApiOut) {
		if(!this.newTransactions.map((transaction) => transaction.uid).includes(transaction.uid)) {
			this.newTransactions.push(transaction);
		}
	}

	async mounted() {
		// Check for essential page functionality
		if(!ComponentUtils.hasPermissions((ownedPermissions: ApplicationPermission[]) => {
			return EstablishmentAccountPermissionModel.hasPermissionsForContracts(ownedPermissions, [
				CashlessHttpTransactionContract.search,
				CashlessHttpTransactionContract.searchCount,
				IotHttpEstablishmentDeviceContract.list,
				CashlessHttpProfileContract.list,
				CashlessHttpSimpleProductContract.list,
				PaymentHttpMethodContract.list,
				CashlessHttpCurrencyContract.list,
			])
		})) {
			this.forbidden = true;
			this.loading = false;
			return;
		}

		const urlParams = new URLSearchParams(window.location.search);
		if(urlParams.has('chip')) {
			this.filters = {
				filter: {
					name: 'chipIdPublic',
					value: urlParams.get('chip') as string,
					operator: QueryOperator.EQUAL
				}
			}
		} else if(urlParams.has('transaction')) {
			this.filters = {
				filter: {
					name: 'uid',
					value: urlParams.get('transaction') as VisualScopedUuid<UuidScopeCashless_transaction>,
					operator: QueryOperator.EQUAL
				}
			}
		}

		this.devices = (await this.establishmentDevicesRepository.callContract('list', {establishmentUid: this.establishmentUid}, undefined)).success();
		this.profiles = (await this.profilesRepository.callContract('list', {establishmentUid: this.establishmentUid}, undefined)).success();
		this.products = (await this.productsRepository.callContract('list', {establishmentUid: this.establishmentUid}, undefined)).success();
		this.paymentMethods = (await this.paymentMethodsRepository.callContract('list', {establishmentUid: this.establishmentUid}, undefined)).success();
		this.filterParameters['profileUid'].dropdownValues = this.profiles.map((profile) => {
			return {
				name: profile.name,
				value: profile.uid
			}
		});

		this.currencies = (await this.currencyRepository.callContract('list', {establishmentUid: this.establishmentUid}, undefined)).success();
		this.filterParameters['currency'].dropdownValues = this.currencies.map((currency) => {
			return {
				name: currency.name,
				value: currency.id
			}
		});

		await this.searchTransactions(this.filters);
		this.setupShortcutListeners();
	}

	beforeUnmount() {
		this.shortcutsManager.off('ArrowUp', this.handleKeypress);
		this.shortcutsManager.off('ArrowDown', this.handleKeypress);
		this.shortcutsManager.off('Escape', this.handleKeypress)
	}

	setupShortcutListeners() {
		this.shortcutsManager.on('ArrowUp', this.handleKeypress);
		this.shortcutsManager.on('ArrowDown', this.handleKeypress);
		this.shortcutsManager.on('Escape', this.handleKeypress);
	}

	handleKeypress(e: KeyboardEvent) {
		if (e.code === 'ArrowUp' || e.code === 'ArrowDown') {
			if (!this.selectedTransaction) {
				this.selectedTransaction = this.transactions[0] ?? null;
			} else {
				const currentIndex = this.transactions.findIndex((transaction) => transaction.uid === this.selectedTransaction?.uid);

				if (e.code === 'ArrowUp') {
					if (currentIndex > 0) {
						this.selectedTransaction = this.transactions[currentIndex - 1];
					}
				} else if (e.code === 'ArrowDown') {
					if (currentIndex < this.transactions.length - 1) {
						this.selectedTransaction = this.transactions[currentIndex + 1];
					}
				}
			}
		} else if (e.code === 'Escape') {
			this.selectedTransaction = null;
		}
	}

	async searchTransactions(filters: TypedQuerySearch<typeof CashlessHttpTransactionContractSearchConfig>, cursor: {after?: Uuid, before?: Uuid}|null = null) {
		this.loading = true;
		this.filters = {...filters};

		if(!cursor) {
			const data = (await this.transactionsRepository.callContract('searchCount', {establishmentUid: this.establishmentUid}, filters)).success();
			this.pagination.totalResults = data.rows;
		 	this.pagination.currentPage = 1;
			this.pagination.estimateTotal = data.estimate;
		}

		filters.elementsPerPage = this.pagination.resultsPerPage;
		if(cursor && cursor.after) filters.cursorAfter = cursor.after;
		if(cursor && cursor.before) filters.cursorBefore = cursor.before;
		this.transactions = (await this.transactionsRepository.callContract('search', {establishmentUid: this.establishmentUid}, filters)).success();
		this.appliedFilters = filters;
		this.newTransactions = [];
		this.loading = false;
	}

	async selectTransaction(transaction: TransactionApiOut) {
		if(this.selectedTransaction && this.selectedTransaction.uid === transaction.uid) {
			this.selectedTransaction = null;
		} else {
			this.selectedTransaction = transaction;
			this.selectedTransactionCustomerChip = null;

			const transactionChips = (await this.customerChipRepository.callContract('search', {establishmentUid: this.establishmentUid}, {
				filter: {
					group: QueryFilterGroupClause.AND,
					filters: [{
						name: 'publicChipId',
						value: transaction.chipVisualId ?? '',
						operator: QueryOperator.EQUAL
					}, {
						name: 'startingDatetime',
						value: this.selectedTransaction.creationDatetime,
						operator: QueryOperator.LESS_OR_EQUAL
					}, {
						group: QueryFilterGroupClause.OR,
						filters: [{
							name: 'endingDatetime',
							value: null,
							operator: QueryOperator.EQUAL
						}, {
							name: 'endingDatetime',
							value: this.selectedTransaction.creationDatetime,
							operator: QueryOperator.MORE_OR_EQUAL
						}]
					}]
				}
			})).success();

			this.selectedTransactionCustomerChip = transactionChips[0] ?? null;
		}
	}

	saveColumnPreferences(columns: TableColumn[]) {
		this.tableColumnsRepository.saveColumnsPreferences(this.tableKey, columns);
	}

	async search(search: string) {
		const filters = [];
		const split = search.split('-');
		const splitSpace = search.split(' ');
		if(
			search.length === 8
			|| (split.length === 2 && split[0].length === 4 && split[1].length === 4)
			|| (splitSpace.length === 2 && splitSpace[0].length === 4 && splitSpace[1].length === 4)
		) {
			filters.push({
				name: 'publicDebitedChipId',
				value: search,
				operator: QueryOperator.EQUAL
			});
			filters.push({
				name: 'publicCreditedChipId',
				value: search,
				operator: QueryOperator.EQUAL
			});
		}

		if(parseInt(search, 10)) {
			const searchedPrice = parseFloat(search.replace(',', '.'));
			let searchPriceCeil: number|null = null;
			const modulus = searchedPrice % 1;
			if(modulus === 0) {
				searchPriceCeil = searchedPrice + 1;
			} else {
				const decimalNumber = (searchedPrice + '').split('.')[1].length;
				searchPriceCeil = searchedPrice + (1 / Math.pow(10, decimalNumber));
			}

			filters.push({
				group: QueryFilterGroupClause.AND,
				filters: [{
					name: 'amount',
					value: Math.ceil(searchedPrice * 100),
					operator: QueryOperator.MORE_OR_EQUAL
				}, {
					name: 'amount',
					value: Math.ceil(searchPriceCeil * 100),
					operator: QueryOperator.LESS
				}]
			});
		}

		if(filters.length === 0) {
			await this.searchTransactions({});
		} else if(filters.length === 1) {
			await this.searchTransactions({
				filter: filters[0] as any
			});
		} else {
			await this.searchTransactions({
				filter: {
					group: QueryFilterGroupClause.OR,
					filters: filters as any
				}
			});
		}
	}

	nextPage() {
		this.pagination.currentPage++;
		this.searchTransactions(this.filters, {after: this.transactions[this.transactions.length - 1].uid})
	}

	previousPage() {
		this.pagination.currentPage--;
		this.searchTransactions(this.filters, {before: this.transactions[0].uid})
	}

	saveFilter(name: string) {
		if(this.filters.filter) {
			this.savedFilters.push({
				name: name,
				filter: this.filters.filter
			});
			this.savedFiltersRepository.saveFilters(this.tableKey, this.savedFilters);
		}
	}

	selectFilter(savedFilter: SavedFilter) {
		this.filters.filter = savedFilter.filter as any;
		this.searchTransactions(this.filters);
	}

	deleteFilter(index: number) {
		this.savedFilters.splice(index, 1);
		this.savedFiltersRepository.saveFilters(this.tableKey, this.savedFilters);
	}

	async showExportModalFnc() {
		this.showExportModal = true;
	}

	selectedFileType: ExportFileType = 'xlsx';

	async exportTransactions() {
		if(!this.exportDates.start || !this.exportDates.end) {
			this.exportError = 'Les dates sont invalides';
			return;
		}

		this.exporting = true;

		let transactions = await this.loadAllTransactions();

		const jsonTractions = transactions.map((transaction) => {
			return {
				'Uid': transaction.uid,
				'Type': transaction.signedAmount > 0 ? 'Rechargement' : 'Paiement',
				'Montant': transaction.signedAmount / 100,
				'Moyen de paiement': transaction.paymentMethod === null ? '-' : PaymentMethodFilter(transaction.paymentMethod,this.paymentMethods)?.name,
				'Date': DateUtils.formatDateFrFormat(transaction.creationDatetime),
				'Heure': DateUtils.formatDateHour(transaction.creationDatetime, ':'),
				'Point de vente': this.requireProfileWithUid(transaction.profileUid).name,
				'Statut': transaction.status,
				'Raison du refus': transaction.refusedReason ?? '-',
				'Produits': transaction.products ? JSON.stringify(transaction.products.map((product) => dehydrate(product))) : '-',
				'Device': this.requireDeviceNameWithUid(transaction.finalStateDeviceUid),
			};
		})

		MultiFormatExporter.downloadData([
			{name: 'Uid', type: 'AUTO'},
			{name: 'Type', type: 'AUTO'},
			{name: 'Montant', type: 'MONEY'},
			{name: 'Moyen de paiement', type: 'AUTO'},
			{name: 'Date', type: 'AUTO'},
			{name: 'Heure', type: 'AUTO'},
			{name: 'Point de vente', type: 'AUTO'},
			{name: 'Statut', type: 'AUTO'},
			{name: 'Raison du refus', type: 'AUTO'},
			{name: 'Produits', type: 'AUTO'},
			{name: 'Device', type: 'AUTO'},
		], jsonTractions, this.selectedFileType);

		this.showExportModal = false;
		this.exporting = false;
	}

	async loadAllTransactions() {
		let allTransactions: TransactionApiOut[] = [];
		let transactions: TransactionApiOut[] = [];
		do {
			let params: TypedQuerySearch<typeof CashlessHttpTransactionContractSearchConfig> = {
				filter: {
					group: QueryFilterGroupClause.AND,
					filters: [{
						name: 'creationDatetime',
						operator: QueryOperator.MORE_OR_EQUAL,
						value: this.exportDates.start
					}, {
						name: 'creationDatetime',
						operator: QueryOperator.LESS_OR_EQUAL,
						value: this.exportDates.end
					}]
				}
			} as any;

			if(transactions[49]) {
				params.cursorAfter = transactions[49].uid
			}

			transactions = (await this.transactionsRepository.callContract('search', {establishmentUid: this.establishmentUid}, params)).success();
			allTransactions = allTransactions.concat(transactions);
		} while(transactions.length === 50);

		return allTransactions;
	}

	requireProfileWithUid(profileUid: VisualScopedUuid<UuidScopeCashless_profile>): ProfileApiOut|{name: string} {
		if(profileUid === UuidUtils.scopedToVisual(ProfileApiOut.PLATFORM_UID, uuidScopeCashless_profile)) {
			return {
				name: 'Rechargement en ligne'
			}
		}
		const profile = this.profiles.find((profile) => profile.uid === profileUid);
		if(!profile) throw new Error('missing_profile(' + profileUid + ')');
		return profile;
	}

	requirePaymentMethodWithUid(paymentMethodUid: VisualScopedUuid<UuidScopePayment_method>) {
		const paymentMethod = this.paymentMethods.find((paymentMethod) => paymentMethod.uid === paymentMethodUid);
		if(!paymentMethod) throw new Error('missing_payment_method(' + paymentMethodUid + ')');
		return paymentMethod;
	}

	requireProductWithUid(productId: number) {
		const product = this.products.find((product) => product.id === productId);
		if(!product) throw new Error('missing_product(' + productId + ')');
		return product;
	}

	requireDeviceNameWithUid(deviceUid: VisualScopedUuid<UuidScopeIot_deviceApp>) {
		if(deviceUid === TransactionApiOut.CREATOR_SERVER_UUID_VISUAL) return 'Serveur';
		const device = this.devices.find((device) => device.uid === deviceUid);
		if(!device) return 'Inconnu';
		return device.hardwareId;
	}

	getCustomerUrl(customerUid: VisualScopedUuid<UuidScopeCustomer_customer>) {
		return EstablishmentUrlBuilder.buildUrl('/customers?uid=' + UuidUtils.visualToUuid(customerUid));
	}

	get transactionDropdownActions(): DropdownButtonAction[] {
		const actions: DropdownButtonAction[] = [{
			id: 'fix',
			name: 'Passer en WRITTEN',
			icon: 'fa-regular fa-check'
		}];

		return actions.filter((action) => !this.disabledActions.includes(action.id));
	}

	get transactionSimpleDropdownActions(): DropdownButtonAction[] {
		const actions: DropdownButtonAction[] =  [{
			id: 'change-method',
			name: 'Changer le moyen de paiement',
			icon: 'fa-regular fa-arrow-right-arrow-left'
		}];

		return actions.filter((action) => !this.disabledActions.includes(action.id));
	}

	dropdownClicked(action: DropdownButtonAction) {
		if(action.id === 'fix') this.fixTransaction();
	}

	async fixTransaction(){
		if(!this.selectedTransaction) throw 'no_selected_transaction';
		if(!this.establishmentUid) throw 'no_establishmentUid';

		if(this.selectedTransaction.status===TransactionStatus.WRITTEN) throw 'already_written';

		let transactionFix= new TransactionFixApiIn({
			status:TransactionStatus.WRITTEN
		});
		let t = (await this.transactionsRepository.callContract('fix',{establishmentUid:this.establishmentUid,transactionUid:this.selectedTransaction.uid},transactionFix)).success();
	}

	transactionDropdownClicked(action: DropdownButtonAction, transaction: TransactionApiOut) {
		if(action.id === 'change-method') {
			this.showPaymentMethodFormModal = transaction;
		}
	}

	updatedTransaction(updatedTransaction: TransactionApiOut) {
		const index = this.transactions.findIndex((transaction) => updatedTransaction.uid === transaction.uid);
		if(index === -1) this.transactions.push(updatedTransaction);
		else this.transactions.splice(index, 1);
		if(this.selectedTransaction && this.selectedTransaction.uid === updatedTransaction.uid) this.selectedTransaction = updatedTransaction;
	}
}
