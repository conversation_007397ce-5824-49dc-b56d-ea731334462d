import {VisualScopedUuid} from "@groupk/horizon2-core";
import {UuidScopeProductProduct} from "@groupk/mastodon-core";

export class LocalFavoriteStockRepository {
	lsKey = 'favorite-stocks';

	get(): VisualScopedUuid<UuidScopeProductProduct>[] {
		const rawFavorites = localStorage.getItem(this.lsKey);
		if(rawFavorites) {
			try {
				return JSON.parse(rawFavorites);
			} catch (err) {
				return [];
			}
		} else {
			return [];
		}
	}

	save(productUids: VisualScopedUuid<UuidScopeProductProduct>[]) {
		localStorage.setItem(this.lsKey, JSON.stringify(productUids));
	}
}