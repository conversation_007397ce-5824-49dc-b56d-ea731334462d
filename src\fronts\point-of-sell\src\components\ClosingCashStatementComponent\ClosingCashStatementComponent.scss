.closing-cash-statement-component {

    .key-numbers {
        display: grid;
        grid-template-columns: 1fr 1fr;
        grid-gap: 10px;

        .key-number {
            display: flex;
            flex-direction: column;
            gap: 10px;
            padding: 25px;
            border-radius: 8px;
            background: white;
            border: 1px solid var(--border-color);

            &.full {
                grid-column: 1/3;
            }

            .type {
                font-size: 14px;
            }

            .value {
                font-size: 22px;
                font-weight: 700;
            }

            .indicator {
                display: flex;
                align-items: center;
                gap: 4px;
                color: #079455;
                font-weight: 600;
                font-size: 14px;

                span {
                    color: #545454;
                    font-size: 12px;
                    font-weight: 500;
                }
            }
        }
    }

    .center-aligned {
        display: flex;
        flex-direction: column;
        align-items: center;

        > i {
            font-size: 56px;
            margin-bottom: 10px;
        }

        h1 {
            margin: 0 0 5px 0;
            text-align: center;
            font-size: 20px;
            font-weight: 700;
        }

        span {
            text-align: center;
            font-size: 15px;
            margin-bottom: 45px;

            &.highlighted {
                font-weight: bold;
                text-decoration: underline;
            }
        }

        .tertiary.button {
            margin-top: 20px;

            i {
                margin-top: 2px;
            }
        }
    }

    .table-scroll-wrapper {
        overflow: auto;
    }

    .recap {
        display: flex;
        flex-direction: column;
        gap: 20px;

        .title-group {
            display: flex;
            gap: 5px;

            .title {
                font-size: 20px;
                font-weight: 700;
            }

            .subtitle {
                font-size: 15px;
                font-weight: 400;
            }
        }

        .warning {
            text-align: center;
            color: var(--error-color);
            font-size: 15px;
            font-style: italic;
        }

        .buttons {
            margin-top: 20px;
            display: flex;
            justify-content: flex-end;
            align-items: stretch;
        }
    }

    .see-canceled-button {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        gap: 10px;
    }
}