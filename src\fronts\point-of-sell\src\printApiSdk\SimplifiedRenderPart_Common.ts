import CanvasRendererSDK from "./CanvasRendererSDK";
import {SimplifiedPrintingPartRendererConfig} from "./SimplifiedPrintingPartData";

export class SimplifiedRenderPart_Common{
	protected config : SimplifiedPrintingPartRendererConfig;

	constructor(config : SimplifiedPrintingPartRendererConfig) {
		this.config = config;
	}

	getRenderLinePosition(heightInPixelsPerLine: number, y: number) {
		return {
			x: this.config.margins.left * heightInPixelsPerLine,
			y: y,
		};
	}

	getRenderLineSize(renderer: CanvasRendererSDK, heightInPixelsPerLine: number) {
		let rendererConfig = renderer.getConfig();
		return {
			width: rendererConfig.targetSize.width - (this.config.margins.left + this.config.margins.right) * heightInPixelsPerLine,
			height: heightInPixelsPerLine,
		};
	}

}
