<script lang="ts" src="./MobileBottomCartComponent.ts">
</script>

<style lang="sass">
@import './MobileBottomCartComponent.scss'
</style>

<template>
    <div class="mobile-bottom-cart-component" :class="{opened: opened}">
        <div class="quantity">
            {{getPurchasesQuantity}}
        </div>
        <div class="center" v-if="posState.currentOrder">
            <span class="amount"> {{ $filters.Money(getOrderLeftToPay) }} </span>
            <span class="title"> {{ (posState.currentOrder.order.diningExtra && posState.currentOrder.diningExtra.tableUid) ? posState.requireTableWithUid(posState.currentOrder.diningExtra.tableUid).name : 'Aucune table' }} </span>
        </div>

        <div class="actions">
            <div class="action" @click.stop="posState.currentOrder = null;">
                <i class="fa-regular fa-hourglass"></i>
            </div>

            <div class="action">
                <i class="fa-regular fa-chevron-up"></i>
            </div>
        </div>
    </div>
</template>