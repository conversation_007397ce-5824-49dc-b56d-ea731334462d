.group-option-component {
    position: relative;
    display: flex;
    flex-direction: column;
    gap: 20px;

    .header {
        display: flex;
        flex-direction: column;
        gap: 4px;

        .title {
            font-size: 20px;
            font-weight: 700;
            margin: 0;
        }

        .subtitle {
            font-size: 15px;
        }
    }

    .steps {
        display: flex;
        align-items: center;
        gap: 30px;

        .step {
            display: flex;
            align-items: center;
            gap: 5px;

            .number {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 24px;
                height: 24px;
                font-size: 14px;
                color: black;
                background: var(--secondary-hover-color);
                border-radius: 50%;
            }

            .name {
                font-size: 14px;
            }

            &.active {
                .number {
                    font-weight: bold;
                    color: var(--primary-text-color);
                    background: var(--primary-color);
                }

                .name {
                    font-weight: bold;
                }
            }
        }
    }

    .restrictions {
        font-size: 15px;
        font-weight: 600;
    }

    .options {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        grid-gap: 10px;

        @media (max-width: 900px) {
            grid-template-columns: repeat(2, 1fr);
        }

        .option {
            display: flex;
            flex-direction: column;
            cursor: pointer;
            position: relative;

            &.disabled {
                opacity: 0.5;
                pointer-events: none;
            }

            &:hover {
                .top {
                    background: #e1e1e1;
                }
            }

            .quantity {
                position: absolute;
                display: flex;
                align-items: center;
                justify-content: center;
                top: 5px;
                right: 5px;
                height: 20px;
                width: 20px;
                border-radius: 50%;
                background: var(--primary-color);
                color: var(--primary-text-color);
                font-size: 13px;
                font-weight: 500;
            }

            .top {
                flex-grow: 2;
                padding: 10px 5px;
                border-radius: 4px 4px 0 0;
                display: flex;
                align-items: center;
                justify-content: center;
                text-align: center;
                background: white;
                font-weight: 500;
                font-size: 14px;
                min-height: 60px;
                border: 1px solid #E8E8E8;
                word-break: break-all;
            }

            .bottom {
                display: flex;
                align-items: center;
                justify-content: center;
                background: #E8E8E8;
                padding: 10px;
                border-radius: 0 0 4px 4px;
                font-size: 14px;
            }
        }
    }

    .buttons {
        display: flex;
        justify-content: flex-end;
        align-items: flex-end;
        flex-grow: 2;

        &.spaced {
            justify-content: space-between;
            position: sticky;
            bottom: -20px;
            margin: 0 -20px -20px -20px;
            padding: 10px 20px;
            background: white;

            >.button {
                @media (max-width: 900px) {
                    span {
                        display: none;
                    }
                }
            }

            //@media (max-width: 900px) {
            //    flex-direction: column-reverse;
            //    justify-content: flex-end;
            //    align-items: flex-start;
            //    gap: 10px;
            //
            //    .buttons {
            //        flex-grow: 0;
            //    }
            //}
        }
    }
}