<script lang="ts" src="./TablesComponent.ts" />

<style lang="sass">
@import './TablesComponent.scss'
</style>

<template>
    <div class="tables-component">
        <div class="left">
            <div class="content" ref="contentContainer">
                <div class="head">
                    <div class="top">
                        <h2> Tables </h2>

                        <div class="red label" v-if="isOffline" @click="showNetworkModal = true">
                            <i class="fa-solid fa-triangle-exclamation"></i>
                            Hors ligne
                        </div>

                        <div class="right-container">
                            <button class="big white button" :class="{disabled:loading}" @click="!loading?loadOrders():''">
                                <i class="fa-regular fa-arrows-rotate"></i>
                                <PERSON><PERSON>ichir
                            </button>
                        </div>
                    </div>


                    <div class="button-group" v-if="posState.diningAreas.length > 1">
                        <button v-for="area of posState.diningAreas" @click="selectedArea = area" :class="{selected: selectedArea && selectedArea.uid === area.uid}">
                            {{ area.name }}
                        </button>
                    </div>
                </div>

                <div class="tables-area">
                    <div class="legend-container">
                        <div class="sessions">
                            <div class="grey label" v-if="isOffline">
                                <i class="fa-regular fa-mobile"></i>
                                Cette caisse uniquement
                            </div>

                            <div class="grey label" v-else-if="loadingCashStatement">
                                Chargement des appareils...
                            </div>

                            <div class="grey label" v-else @click="loadCurrentCashStatement()">
                                <i class="fa-regular fa-mobile"></i>
                                {{ posState.currentCashStatement.sessions .filter((session) => session.closingDatetime === null).length }} appareils

                                <i class="fa-regular fa-arrows-rotate"></i>
                            </div>
                        </div>
                        <div class="legend">
                            <div class="color"></div>
                            Libre
                        </div>

                        <div class="legend">
                            <div class="blue color"></div>
                            Occupé
                        </div>

                    </div>

                    <div class="loading-container" v-if="loading">
                        <div class="loader"></div>
                    </div>
                    <div v-else-if="!selectedArea">
                        Aucune table
                    </div>
                    <template v-else>
                        <div class="tables-group" v-for="(tables, places) of tablesByHeadCountByArea[selectedArea.uid]">
                            <span class="capacity"> Tables de {{ places }} </span>
                            <div class="tables">

                                <table-view
                                    v-for="table in tables"
                                    :key="table.uid"
                                    :table="table"
                                    :local-orders="mapOrders[table.uid].localOrders"
                                    :server-orders="mapOrders[table.uid].serverOrders"
                                    :order-transfers="orderTransfers"
                                    :pos-state="posState"
                                    @show-modal-for-table="showModalForTable = $event"
                                    @animate-mobile-cart="animateMobileCart = true"
                                    @show-mobile-cart="showMobileCart = true"
                                ></table-view>

<!--                                <div class="table" -->
<!--                                    :class="{blue: data.orders.length > 0, opened: data.orders.length > 1 && openedTable === data.table}"-->
<!--                                    v-for="data in tableData"-->
<!--                                    @click="data.orders.length > 1 ? openedTable = data.table : createOrderForTable(data.table)"-->
<!--                                    @mousedown="handleLongPressStart(data.table, $event)"-->
<!--                                    @touchstart="handleLongPressStart(data.table, $event)"-->
<!--                                    @mouseup="handleLongPressEnd()"-->
<!--                                    @touchend="handleLongPressEnd()"-->
<!--                                    @mouseleave="handleLongPressEnd()"-->
<!--                                >-->
<!--                                    <span class="name">-->
<!--                                        {{ data.table.name }}-->
<!--                                    </span>-->

<!--                                    <span-->
<!--                                        v-if="data.orders.length > 0"-->
<!--                                        class="orders-count"-->
<!--                                        :class="{orange: data.orders.length === 1 && data.orders[0].cashStatementSessionUid !== posState.currentCashStatementSession.uid}"-->
<!--                                    >-->
<!--                                        {{ data.orders.length }}-->
<!--                                    </span>-->

<!--                                    <div class="guests">-->
<!--                                        <i class="fa-regular fa-user"></i>-->
<!--                                        <div class="infos">-->
<!--                                            <span class="number"> {{ data.orders[0]?.diningExtra?.guestCount ?? 0 }}/{{ places }} </span>-->
<!--                                        </div>-->
<!--                                    </div>-->

<!--                                    <span class="hour" v-if="data.orders.length === 0"> &#45;&#45;:&#45;&#45; </span>-->
<!--                                    <span class="hour" v-else> {{ getTimeSinceDate(data.orders[0].creationDatetime) }} </span>-->

<!--                                    <div ref="tableOrders" class="orders" v-if="data.orders.length > 1">-->
<!--                                        <div-->
<!--                                            class="order"-->
<!--                                            v-for="order in data.orders"-->
<!--                                            @click.stop="selectOrder(order); openedTable = null;"-->
<!--                                            @mousedown.stop="handleLongPressStart(data.table, $event)"-->
<!--                                            @touchstart.stop="handleLongPressStart(data.table, $event)"-->
<!--                                            @mouseup.stop="handleLongPressEnd()"-->
<!--                                            @touchend.stop="handleLongPressEnd()"-->
<!--                                            @mouseleave.stop="handleLongPressEnd()"-->
<!--                                        >-->
<!--                                            <div v-if="order.cashStatementSessionUid !== posState.currentCashStatementSession.uid" class="orange label">-->
<!--                                                Distant-->
<!--                                            </div>-->

<!--                                            <div class="head">-->
<!--                                                <span class="time"> {{ $filters.Hour(order.creationDatetime) }} </span>-->
<!--                                                <span class="account"> {{ order.sellerEstablishmentAccountUid ? posState.getEstablishmentAccountWithUid(order.sellerEstablishmentAccountUid).firstname : 'En ligne' }} </span>-->
<!--                                            </div>-->

<!--                                            {{ $filters.Money(getOrderTotals(order).purchases.withTaxesBeforeDiscount) }}-->
<!--                                        </div>-->
<!--                                    </div>-->
<!--                                </div>-->
                            </div>
                        </div>
                    </template>
                </div>
            </div>

            <mobile-bottom-cart
                v-if="getPurchasesQuantity > 0"
                :pos-state="posState"
                :opened="animateMobileCart"
                @click="toggleMobileCart()"
            ></mobile-bottom-cart>
        </div>

        <div class="right" :class="{opened: showMobileCart && posState.currentOrder}">
            <order-sidebar :pos-state="posState" :pos-profile="posProfile" @close="closeMobileCart()"></order-sidebar>
        </div>

        <div class="modal-dimmer" v-if="showNetworkModal" @click="showNetworkModal = false">
            <div class="modal" @click.stop>
                <p>Cette caisse n'a <strong>actuellement pas accès à internet</strong> et vous n'avez donc accès
                    <strong>qu'aux commandes actuellement gérées par cette caisse</strong>.</p>
                <p>Il est possible que certaines tables qui n'affichent pas de commande aient <strong>en réalité une
                    commande en cours créée par un autre appareil</strong>.</p>
            </div>
        </div>

        <div class="right-modal-dimmer" v-if="showModalForTable"></div>
        <right-modal
            v-if="showModalForTable"
            bottom-button-text="Fermer"
            @bottom-button-clicked="showModalForTable = null"
        >
            <div class="table-long-press-modal">
                <div class="head">
                    <span class="title"> Table {{ showModalForTable.name }} </span>
                    <span class="description"> Historique des commandes sur {{ showModalForTable.name }} </span>
                </div>

                <div class="orders">
                    <div
                        v-for="localOrder of orders.filter((order) => showModalForTable && order.diningExtra.tableUid === showModalForTable.uid)"
                        class="order"
                    >
                        <mobile-order-card
                            :pos-state="posState"
                            :local-order="localOrder"
                            :local-order-transfer="orderTransfers.find((transfer) => transfer.order.uid === localOrder.uid && !transfer.canceled)"
                        ></mobile-order-card>
                    </div>

                </div>
            </div>
        </right-modal>
    </div>
</template>
