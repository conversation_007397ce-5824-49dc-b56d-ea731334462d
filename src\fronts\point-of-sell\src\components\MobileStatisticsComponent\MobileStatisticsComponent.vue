<script lang="ts" src="./MobileStatisticsComponent.ts">
</script>

<style lang="sass">
@import './MobileStatisticsComponent.scss'
</style>

<template>
    <div class="mobile-statistics-component">
        <div class="key-numbers">
            <div class="key-number">
                <span class="type"> CA Total </span>
                <span class="value"> {{ $filters.Money(ordersStatistics.totals.withTaxes) }} </span>
            </div>
            <div class="key-number">
                <span class="type"> Nombre de commandes </span>
                <span class="value"> {{ orders.length }} </span>
            </div>
            <div class="key-number full">
                <span class="type"> <PERSON>ier moyen </span>
                <span class="value" v-if="orders.length > 0"> {{ $filters.Money(ordersStatistics.totals.withTaxes / orders.length) }} </span>
                <span class="value" v-else> {{ $filters.Money(0) }} </span>
            </div>
        </div>

        <h3> Par moyen de paiement </h3>

        <div class="key-numbers">
            <div class="key-number" v-for="method of posState.paymentMethods">
                <span class="type"> {{ method.name }}</span>
                <span class="value">
                    <template v-if="selectedEmployee === 'global'">
                        {{ $filters.Money(ordersStatistics.perPaymentMethod[method.uid] ? ordersStatistics.perPaymentMethod[method.uid].success : 0) }}
                    </template>
                     <template v-else>
                        {{ $filters.Money(ordersStatistics.perSeller[selectedEmployee].perPaymentMethod[method.uid] ? ordersStatistics.perSeller[selectedEmployee].perPaymentMethod[method.uid].success : 0) }}
                    </template>
                </span>
            </div>
        </div>
    </div>
</template>