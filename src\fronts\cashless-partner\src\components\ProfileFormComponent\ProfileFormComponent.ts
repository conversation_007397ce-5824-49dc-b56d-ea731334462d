import {Component, Prop, Vue} from "vue-facing-decorator";
import {FormModalOrDrawerComponent, ToggleComponent} from "@groupk/vue3-interface-sdk";
import {AutoWired, Uuid, VisualScopedUuid} from "@groupk/horizon2-core";
import {
	UuidScopeEstablishment,
	ApplicationPermission,
	EstablishmentAccountPermissionModel,
	PaymentHttpMethodContract,
	CashlessHttpProfileContract
} from "@groupk/mastodon-core";
import {ProfileApiOut} from "@groupk/mastodon-core";
import {PaymentMethodApiOut} from "@groupk/mastodon-core";
import {ProfilesRepository} from "../../../../../shared/repositories/ProfilesRepository";
import ProfilePaymentMethodsEditorComponent
	from "../ProfilePaymentMethodsEditorComponent/ProfilePaymentMethodsEditorComponent.vue";
import {PaymentMethodsRepository} from "../../../../../shared/repositories/PaymentMethodsRepository";
import {OptionBuilder} from "vue-facing-decorator/dist/optionBuilder";
import {EstablishmentAccountProfileRepository} from "../../../../../shared/repositories/EstablishmentAccountProfileRepository";
import {EstablishmentAccountRepository} from "../../../../../shared/repositories/EstablishmentAccountRepository";
import {DropdownComponent} from "@groupk/vue3-interface-sdk";
import {HoverableInfosComponent} from "@groupk/vue3-interface-sdk";
import {ProfileData} from "../../../../../shared/mastodonCoreFront/cashless/ProfileData";
import {ProfileTerminalKeypadData} from "../../../../../shared/mastodonCoreFront/cashless/ProfileTerminalKeypadData";
import {ProfilePosData} from "../../../../../shared/mastodonCoreFront/cashless/ProfilePosData";
import {ProfileKioskData} from "../../../../../shared/mastodonCoreFront/cashless/ProfileKioskData";
import {AppState} from "../../../../../shared/AppState";

export function ProfileFormComponentHasRequiredPermissions(ownedPermissions: ApplicationPermission[]): boolean {
	return EstablishmentAccountPermissionModel.hasPermissionsForContracts(ownedPermissions, [
		CashlessHttpProfileContract.create,
		CashlessHttpProfileContract.update,
		PaymentHttpMethodContract.list,
	]);
}

@Component({
	components: {
		'form-modal-or-drawer': FormModalOrDrawerComponent,
		'profile-payment-editor': ProfilePaymentMethodsEditorComponent,
		'dropdown': DropdownComponent,
		'hoverable-info': HoverableInfosComponent,
		'toggle': ToggleComponent
	},
	emits: ['saved']
})
export default class ProfileFormComponent extends Vue {
	@Prop({required: true}) establishmentUid!: VisualScopedUuid<UuidScopeEstablishment>;
	@Prop({required: true, default: null}) editingProfile!: ProfileApiOut|null;
	paymentMethods: PaymentMethodApiOut[] = [];

	profile: ProfileData = new ProfileData();

	showCreditEditModal: boolean = false;
	showDebitEditModal: boolean = false;

	loading: boolean = false;
	saving: boolean = false;
	error: string|null = null;

	@AutoWired(ProfilesRepository) accessor profilesRepository!: ProfilesRepository
	@AutoWired(PaymentMethodsRepository) accessor paymentMethodsRepository!: PaymentMethodsRepository;
	@AutoWired(EstablishmentAccountProfileRepository) accessor establishmentAccountProfileRepository!: EstablishmentAccountProfileRepository;
	@AutoWired(EstablishmentAccountRepository) accessor establishmentAccountRepository!: EstablishmentAccountRepository;
	@AutoWired(AppState) accessor appState!: AppState;

	constructor(optionBuilder: OptionBuilder, vueInstance: any) {
		super(optionBuilder, vueInstance);
		this.loading = true;
	}

	async beforeMount() {
		this.profile = new ProfileData(this.editingProfile);
		this.paymentMethods = (await this.paymentMethodsRepository.callContract('list', {establishmentUid: this.establishmentUid}, undefined)).success();
		this.loading = false;
	}

	getMethodWithUid(methodUid: Uuid): PaymentMethodApiOut {
		const paymentMethod = this.paymentMethods.find((method) => method.uid === methodUid);
		if(!paymentMethod) throw new Error('missing_payment_method');
		return paymentMethod;
	}

	setAdminPublicChip(event: Event, index: number) {
		if(event.target && event.target instanceof HTMLInputElement){
			this.profile.adminPublicChipIds[index] = event.target.value.toUpperCase();
		}
	}

	getPaymentMethodsDropdownValues() {
		return this.paymentMethods.map((method) => {
			return {
				name: method.name,
				value: method.uid
			}
		});
	}

	addDebitKeypad() {
		this.profile.terminalKeypadDebit = new ProfileTerminalKeypadData();
	}

	addCreditKeypad() {
		this.profile.terminalKeypadCredit = new ProfileTerminalKeypadData()
	}

	addPos() {
		this.profile.pos = new ProfilePosData();
	}

	addKiosk() {
		this.profile.kiosk = new ProfileKioskData();
	}

	async save() {
		this.saving = true;
		this.error = null;

		try {
			const apiIn = this.profile.toApiIn();

			if(this.profile.uid === null) {
				const response = await this.profilesRepository.callContract('create', {establishmentUid: this.establishmentUid}, apiIn);
				if(response.isSuccess()) {
					const profile = response.success();
					this.$emit('created', profile);
				} else {
					const error = response.error();
					if(error && 'error' in error) {
						this.error = error.error;
					} else {
						this.error = 'Erreur inconnue'
					}
				}
			} else {
				const response = await this.profilesRepository.callContract('update', {establishmentUid: this.establishmentUid, deviceProfileUid: this.profile.uid}, apiIn);
				if(response.isSuccess()) {
					const profile = response.success();
					this.$emit('updated', profile);
				} else {
					const error = response.error();
					if(error && 'error' in error) {
						this.error = error.error;
					} else {
						this.error = 'Erreur inconnue'
					}
				}
			}
		} catch(err) {
			// form errors
			console.log(err);
		}

		this.saving = false;
	}
}