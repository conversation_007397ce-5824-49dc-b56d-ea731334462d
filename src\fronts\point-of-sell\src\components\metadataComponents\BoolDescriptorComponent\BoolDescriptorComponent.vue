<script lang="ts" src="./BoolDescriptorComponent.ts" />

<style lang="sass" scoped>
@import './BoolDescriptorComponent.scss'
</style>

<template>
    <div class="bool-descriptor-component">
        <div class="input-group">
            <label> {{ metadataDescriptorTemplate.name }} </label>
            <input v-model="value" type="checkbox" />
        </div>

        <button class="primary button" @click="validate()"> Valider </button>
    </div>
</template>