import {Component, Prop, Vue} from "vue-facing-decorator";
import {BrowserQRCodeReader} from "@zxing/library";
import {randomUUID, Router} from "@groupk/horizon2-front";
import {FormModalOrDrawerComponent} from "@groupk/vue3-interface-sdk";
import {AutoWired} from "@groupk/horizon2-core";

@Component({
	components: {
		'form-modal-or-drawer': FormModalOrDrawerComponent
	},
	emits: ['scanned', 'not-configured']
})
export default class WebQrCodeReaderComponent extends Vue {
	@Prop({default: false}) promptCameraOnScan!: boolean;

	uuid: string = randomUUID();
	codeReader!: BrowserQRCodeReader;

	availableVideoDevices: MediaDeviceInfo[] = [];
	selectedVideoDeviceId: string|null = null;

	showCameraChooser: boolean = false;

	loading: boolean = false;
	opened: boolean = false;

	@AutoWired(Router) accessor router!: Router;

	async mounted() {
		if(!this.promptCameraOnScan) await this.initCameraChooser();
	}

	async initCameraChooser() {
		this.loading = true;

		this.showCameraChooser = true;

		try {
			this.codeReader = new BrowserQRCodeReader();
			const defaultStream = await navigator.mediaDevices.getUserMedia({video: true});
			defaultStream.getTracks().forEach(function(track) {
				track.stop();
			});

			this.availableVideoDevices = (await navigator.mediaDevices.enumerateDevices()).filter((device) => {
				return device.label !== '' && device.kind === 'videoinput'
			});

			if(this.availableVideoDevices.length === 1) {
				this.selectedVideoDeviceId = this.availableVideoDevices[0].deviceId;
				if(this.promptCameraOnScan) this.start();
			}
		} catch(err) {}

		this.loading = false;
	}

	selectCamera(device: MediaDeviceInfo) {
		this.selectedVideoDeviceId = device.deviceId;
		this.showCameraChooser = false;
		if(this.promptCameraOnScan) this.start();
	}

	beforeUnmount() {
		if(this.codeReader) this.codeReader.reset();
	}

	start() {
		console.log('start');
		if(this.selectedVideoDeviceId === null) {
			this.initCameraChooser();
			return;
		}
		this.opened = true;

		this.codeReader.decodeOnceFromVideoDevice(this.selectedVideoDeviceId, 'video-' + this.uuid).then((result) => {
			this.$emit('scanned', result);
			this.done();
		}).catch((err) => {
			console.log(err);
			this.done();
		});
	}

	done() {
		this.codeReader.reset();
		this.opened = false;
	}

	getDeviceName(device: MediaDeviceInfo) {
		let name = device.label;
		name = name.replace('facing back', 'arrière');
		name = name.replace('facing front', 'avant');
		return name;
	}

	back() {
		this.$emit('not-configured');
	}
}