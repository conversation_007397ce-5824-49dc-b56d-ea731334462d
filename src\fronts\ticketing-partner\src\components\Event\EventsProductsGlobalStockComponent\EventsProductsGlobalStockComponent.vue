<script lang="ts" src="./EventsProductsGlobalStockComponent.ts"/>

<style lang="sass" scoped>
@import './EventsProductsGlobalStockComponent.scss'
</style>

<template>
    <div class="events-products-global-stock-component">
        <form-modal-or-drawer
            :state="opened"
            :title="'Stock global'"
            :subtitle="'Gérer le stock global des produits'"
            @close="close()"
        >
            <template v-slot:content>
                <div class="input-group">
                    <label> Stock global </label>
                    <input placeholder="Stock global" v-model.number="globalStock" />
                </div>

                <div class="form-error" v-if="error">
                    <i class="fa-solid fa-exclamation-circle"></i>
                    <div class="details">
                        <span class="title"> Erreur </span>
                        <span class="description">{{ error }}</span>
                    </div>
                </div>
            </template>

            <template v-slot:buttons>
                <button type="button" class="white button" @click="close()"> Annuler </button>
                <button type="button" class="button" :class="{loading: saving, disabled: saving}" @click="save()">
                    <i class="fa-regular fa-check"></i>
                    Valider
                </button>
            </template>
        </form-modal-or-drawer>
    </div>
</template>