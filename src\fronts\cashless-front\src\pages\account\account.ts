import {Component, Vue} from "vue-facing-decorator";
import {AutoWired, EmailUtils} from "@groupk/horizon2-core";
import {Router} from "@groupk/horizon2-front";
import {DropdownComponent, FormModalOrDrawerComponent} from "@groupk/vue3-interface-sdk";
import SpecificState from "../../SpecificState";
import {CleaveDirective} from "../../../../../shared/directives/CleaveDirective";
import {RefundRepository} from "../../../../../shared/repositories/RefundRepository";
import {CashlessEstablishmentRepository} from "../../../../../shared/repositories/CashlessEstablishmentRepository";
import {
	BankAccountUtils, CashlessHttpChipRefundContract,
	PublicCashlessEstablishmentApiOut,
	PublicRefundApiIn
} from "@groupk/mastodon-core";
import {AppState} from "../../../../../shared/AppState";
import {translateResponseError} from "../../../../../shared/RepositoryExtensions";

@Component({
	directives: {
		cleave: CleaveDirective,
	},
	components: {
		'form-modal-or-drawer': FormModalOrDrawerComponent,
		'dropdown': DropdownComponent
	}
})
export default class account extends Vue {
	publicEstablishment!: PublicCashlessEstablishmentApiOut;

	data: {
		firstname: string,
		lastname: string,
		email: string,
		phone: string,
		chipPublicId: string,
		iban: string
		bic: string
	} = {
		firstname: '',
		lastname: '',
		email: '',
		phone: '',
		chipPublicId: '',
		iban: '',
		bic: ''
	};

	cleaveIban = {
		blocks: [4, 4, 4, 4, 4, 4, 4, 4, 4],
		delimiter: ' ',
		uppercase: true
	}

	cleavePhone = {
		blocks: [2, 2, 2, 2, 2],
		delimiter: ' ',
	}

	error: string|null = null;
	areRefundsAvailable: boolean = false;
	showRefundModal: boolean = false;
	creatingRefund: boolean = false;
	createdRefund: boolean = false;

	PublicRefundApiInDefinition = PublicRefundApiIn.__entityDefinition;

	@AutoWired(CashlessEstablishmentRepository) accessor cashlessEstablishmentRepository!: CashlessEstablishmentRepository;
	@AutoWired(RefundRepository) accessor refundRepository!: RefundRepository;
	@AutoWired(Router) accessor router!: Router;
	@AutoWired(AppState) accessor appState!: AppState;
	@AutoWired(SpecificState) accessor specificState!: SpecificState;

	async mounted() {
		this.data.chipPublicId = this.specificState.currentWallet?.chipVisualId ?? '';

		if(this.specificState.customer) {
			this.data.email = this.specificState.customer.email ?? '';
			this.data.firstname = this.specificState.customer.firstname ?? '';
			this.data.lastname = this.specificState.customer.lastname ?? '';
		}

		this.publicEstablishment = (await this.cashlessEstablishmentRepository.callContract('getPublic', {establishmentUid: this.appState.requireUrlEstablishmentUid()}, undefined)).success()

		if(this.publicEstablishment.refunds) {
			const startDate = new Date(this.publicEstablishment.refunds.startingDatetime);
			const endDate = new Date(this.publicEstablishment.refunds.endingDatetime);
			const now = new Date();

			if(now.getTime() >= startDate.getTime() && now.getTime() < endDate.getTime()) this.areRefundsAvailable = true;
		}
	}

	async sendRefundRequest() {
		this.error = null;

		if(!EmailUtils.isEmailValid(this.data.email.trim())) {
			this.error = 'Veuillez renseigner un email valide';
			return;
		}

		const phoneRegex = /^(?:\d{10}|\d{3}[\s.-]?\d{3}[\s.-]?\d{4}|\(\d{3}\)[\s.-]?\d{3}[\s.-]?\d{4}|\d{2}[\s.-]?\d{3}[\s.-]?\d{3}|\d{4}[\s.-]?\d{3}[\s.-]?\d{3})$/;
		if(!phoneRegex.test(this.data.phone.replaceAll(' ', ''))) {
			this.error = 'Veuillez renseigner un numéro de tél valide';
			return;
		}

		const ibanInfos = BankAccountUtils.extractInfoFromIban(this.data.iban);
		if(!ibanInfos) {
			this.error = 'Veuillez renseigner un IBAN valide';
			return;
		}

		const bicInfos = BankAccountUtils.extractInfoFromBic(this.data.bic);
		if(!bicInfos) {
			this.error = 'Veuillez renseigner un BIC valide';
			return;
		}

		this.creatingRefund = true;

		const response = await this.refundRepository.callContract(
			'createPublic',
			{establishmentUid: this.publicEstablishment.uid},
			new PublicRefundApiIn({
				firstname: this.data.firstname,
				lastname: this.data.lastname,
				email: this.data.email,
				bic: bicInfos.sanitized,
				phoneNumber: this.data.phone,
				publicChipId: this.data.chipPublicId,
				iban: ibanInfos.sanitized
			})
		);

		if(response.isSuccess()) {
			this.createdRefund = true;
			this.showRefundModal = false;
		} else {
			this.error = translateResponseError<typeof CashlessHttpChipRefundContract, 'createPublic'>(response, {
				invalid_chip_id: 'Le numéro de puce donné est invalide',
				refunds_not_setup: 'Les remboursement ne sont pas encore actifs pour cet établissement',
				refunds_creation_not_yet_allowed: 'Les remboursement ne sont pas encore actifs pour cet établissement'
			});
		}

		this.creatingRefund = false;
	}
}