import {Component, Vue} from "vue-facing-decorator";
import {AutoWired, ScopedUuid, SearchUtils, UuidUtils, VisualScopedUuid} from "@groupk/horizon2-core";
import {AuthRepository} from "../../../../../shared/repositories/AuthRepository";
import {
	UuidScopeEstablishment,
	AuthCredentialsApiIn,
	uuidScopeEstablishment,
	EstablishmentAuthTokenApiOut,
	AccountAuthTokenApiOut,
	EstablishmentAccountApiOut,
	AccountApiOut,
	EstablishmentApiOut,
	PlatformDescriptorApiOut,
	EstablishmentCreationApiIn,
	uuidScopeMastodon_quickConnect,
	QuickConnectAuthApiIn,
	UuidScopeMastodon_quickConnect,
	MastodonEstablishmentAccountContractAggregate,
	PublicEstablishmentJoiningItemApiOut, UuidScopeEstablishmentJoining, uuidScopeEstablishmentJoining
} from "@groupk/mastodon-core";
import {CrossDomainTracker, GoogleTracker, Router} from "@groupk/horizon2-front";
import {EstablishmentAccountRepository} from "../../../../../shared/repositories/EstablishmentAccountRepository";
import {AccountRepository} from "../../../../../shared/repositories/AccountRepository";
import {EstablishmentRepository} from "../../../../../shared/repositories/EstablishmentRepository";
import {PlatformRepository} from "../../../../../shared/repositories/PlatformRepository";
import {PlatformDescriptorFrontApiOut} from "@groupk/mastodon-core";
import {FormModalOrDrawerComponent} from "@groupk/vue3-interface-sdk";
import {BarcodeCameraReaderNative, NativeInterface} from "@groupk/native-bridge";
import {AuthStateModel} from "../../../../../shared/AuthStateModel";
import {translateResponseError} from "../../../../../shared/RepositoryExtensions";
import {EstablishmentUrlBuilder} from "../../../../../shared/utils/EstablishmentUrlBuilder";

@Component({
	components: {
		'form-modal-or-drawer': FormModalOrDrawerComponent
	}
})
export default class LoginView extends Vue {
	establishmentUid: VisualScopedUuid<UuidScopeEstablishment>|null = null;

	platforms!: PlatformDescriptorApiOut;
	currentPlatform!: PlatformDescriptorFrontApiOut;
	redirectAfter: string|null = null;
	establishmentJoining: PublicEstablishmentJoiningItemApiOut|null = null;

	currentToken: EstablishmentAuthTokenApiOut|AccountAuthTokenApiOut|null = null;
	connectedAccount: EstablishmentAccountApiOut|AccountApiOut|null = null;
	latestUsedEmail: string|null = null;

	step: 'LOGIN'|'SELECT_ESTABLISHMENT' = 'LOGIN';

	credentials: { email: string, password: string} = {email: '', password: ''};

	establishments: EstablishmentApiOut[] = [];
	loadingEstablishments: boolean = false;
	establishmentsError: string|null = null;
	selectingEstablishment: EstablishmentApiOut|null = null;
	establishmentSearch: string = '';

	loading: boolean = false;
	showEstablishmentCreationModal: EstablishmentCreationApiIn|null = null;
	creatingEstablishment: boolean = false;
	creatingEstablishmentError: string|null = null;
	connecting: boolean = false;
	redirecting: boolean = false;
	error: string|null = null;
	joiningError: string|null = null;

	isNative: boolean = false;

	@AutoWired(AuthRepository) private accessor authRepository!: AuthRepository;
	@AutoWired(EstablishmentAccountRepository) private accessor establishmentAccountRepository!: EstablishmentAccountRepository;
	@AutoWired(AccountRepository) private accessor accountRepository!: AccountRepository;
	@AutoWired(EstablishmentRepository) private accessor establishmentRepository!: EstablishmentRepository;
	@AutoWired(BarcodeCameraReaderNative) private accessor barcodeCameraReader!: BarcodeCameraReaderNative;
	@AutoWired(PlatformRepository) private accessor platformRepository!: PlatformRepository;
	@AutoWired(AuthStateModel) private accessor authStateModel!: AuthStateModel;
	@AutoWired(Router) private accessor router!: Router;

	async beforeMount() {
		this.loading = true;

		this.currentToken = this.authStateModel.getStateSync();
		const url = new URL(window.location.href);

		let regexMatch = this.router.lastRouteRegexMatches;
		if (regexMatch && regexMatch[1]) {
			this.establishmentUid = UuidUtils.scopedToVisual<UuidScopeEstablishment>(regexMatch[1] as ScopedUuid<UuidScopeEstablishment>, uuidScopeEstablishment);
		}

		if(url.searchParams.get('disconnect')) {
			this.authStateModel.disconnect();
		}

		this.latestUsedEmail = localStorage.getItem('latest-used-email') ?? null;

		if(url.searchParams.has('establishmentInvitation')) {
			const establishmentJoiningUid: VisualScopedUuid<UuidScopeEstablishmentJoining> = UuidUtils.scopedToVisual<UuidScopeEstablishmentJoining>(url.searchParams.get('establishmentInvitation') as ScopedUuid<UuidScopeEstablishmentJoining>, uuidScopeEstablishmentJoining);
			const joiningResponse = await this.establishmentAccountRepository.callContract('getJoinRequestPublic', {joiningRequestUid: establishmentJoiningUid}, undefined);

			if(joiningResponse.isSuccess()) {
				const joining = joiningResponse.success();
				if(new Date(joining.item.expiresDatetime) < new Date()) {
					this.joiningError = 'Ce lien a expiré';
				} else if(joining.item.usedDatetime) {
					this.joiningError = 'Ce lien a déjà été utilisé';
				} else {
					this.establishmentJoining = joiningResponse.success();
				}
			} else {
				this.joiningError = translateResponseError<typeof MastodonEstablishmentAccountContractAggregate, 'getJoinRequestPublic'>(joiningResponse, {});
			}
		}

		try {
			this.platforms = (await this.platformRepository.callContract('get', undefined, undefined)).success();

			if(url.searchParams.has('redirectAfter')) this.redirectAfter = url.searchParams.get('redirectAfter');

			const platformName = url.searchParams.get('platform') ?? '';
			const platform = this.platforms.fronts.find((platform) => platform.id.toLowerCase() === platformName.toLowerCase());
			if(!platform) {
				if(this.platforms.fronts.length === 1) this.currentPlatform = this.platforms.fronts[0];
				else {
					window.location.href = '/not-found';
					return;
				}
			} else {
				this.currentPlatform = platform;
			}

			this.isNative = NativeInterface.instance.available();
		} catch(err) {
			// ??
			console.log(err);
		}

		this.getConnectedAccount();

		this.loading = false;
	}

	get filteredEstablishments() {
		return SearchUtils.searchInTab(this.establishments, (establishment) => {
			return [establishment.name, establishment.reseller?.name ?? '', establishment.reseller?.contactEmail ?? '']
		}, this.establishmentSearch)
	}

	async getConnectedAccount() {
		if(this.currentToken) {
			if('accountUid' in this.currentToken) {
				try {
					this.connectedAccount = (await this.accountRepository.callContract('get', {accountUid: this.currentToken.accountUid}, undefined)).success();
				} catch(err){}
			} else {
				try {
					this.connectedAccount = (await this.establishmentAccountRepository.callContract('get_one', {establishmentAccountUid: this.currentToken.establishmentAccountUid}, undefined)).success();
				} catch(err){}
			}
		}
	}

	async login() {
		this.error = null;
		this.connecting = true;

		GoogleTracker.instance.event('login');

		try {
			const authCredentialsApiIn = new AuthCredentialsApiIn(this.credentials.email, this.credentials.password);

			let response;
			if(this.establishmentUid) {
				response = await this.authStateModel.authEstablishmentAccount(this.establishmentUid, authCredentialsApiIn);
				if(!response.isSuccess()) {
					response = await this.authStateModel.authAccount(authCredentialsApiIn);
				}
			} else {
				response = await this.authStateModel.authAccount(authCredentialsApiIn);
			}

			if(response.isSuccess()) {
				const token = response.success();
				localStorage.setItem('latest-used-email', this.credentials.email);
				this.currentToken = token;
				await this.continueWithAccount();
			} else {
				this.error = translateResponseError(response, {
					account_invalid_password: 'Mot de passe invalide',
					account_password_not_set: 'Vous ne pouvez pas vous connecter sur ce compte : aucun mot de passe configuré',
					unknown_account: 'Email inconnu'
				});
			}
		} catch(err) {
			console.error(err);
			this.error = 'Une erreur est survenue';
		}

		this.connecting = false;
	}

	async continueWithAccount() {
		if(this.establishmentJoining) {
			const response = await this.establishmentAccountRepository.callContract('useJoinRequest', {joiningRequestUid: this.establishmentJoining.item.uid}, undefined);
			if(!response.isSuccess()) {
				this.error = translateResponseError<typeof MastodonEstablishmentAccountContractAggregate, 'useJoinRequest'>(response, {
					establishmentAccount_limitReached: 'Le nombre de comptes liés à cet établissement est atteint',
					establishmentAccount_already_exists: 'Ce compte est déjà lié à cet établissement'
				});
				return;
			}
		}

		if(this.establishmentUid === null || !this.doesUrlEstablishmentUidMatchCurrentAccount()) {
			this.loadingEstablishments = true;

			const response = await this.establishmentRepository.callContract('list', undefined, undefined);
			if(response.isSuccess()) {
				this.establishments = response.success();
				this.loadingEstablishments = false;

				if(this.establishments.length === 1) {
					this.selectEstablishment(this.establishments[0]);
				} else {
					this.step = 'SELECT_ESTABLISHMENT';
				}
			} else {
				console.debug(response);
				this.step = 'SELECT_ESTABLISHMENT';
				this.establishmentsError = 'Une erreur inconnue est survenue';
				this.loadingEstablishments = false;
			}
		} else {
			this.redirectToEstablishment(this.establishmentUid);
		}
	}

	showNewEstablishmentModal() {
		this.showEstablishmentCreationModal = new EstablishmentCreationApiIn({
			name: ''
		});
	}

	async createNewEstablishment() {
		this.creatingEstablishment = true;
		this.creatingEstablishmentError = null;

		if(!this.showEstablishmentCreationModal) return;

		const response = await this.establishmentRepository.callContract('create', undefined, this.showEstablishmentCreationModal);
		if(response.isSuccess()) {
			const establishment = response.success();
			this.redirectToEstablishment(establishment.uid);
		} else {
			const error = response.error();

			if(error && 'error' in error && error.error === 'establishment_ownTooMany') {
				this.creatingEstablishmentError = 'Vous avez atteint le nombre maximum d\'établissements';
			} else if(error && 'error' in error && error.error === 'unknown_reseller') {
				this.creatingEstablishmentError = 'Le revendeur semble invalide';
			} else if(error && 'error' in error && error.error === 'invalid_data') {
				this.creatingEstablishmentError = 'Données invalides : ' + error.error_details;
			} else {
				this.creatingEstablishmentError = 'Une erreur inconnue est survenue';
			}
		}

		this.creatingEstablishment = false;
	}

	selectEstablishment(establishment: EstablishmentApiOut) {
		this.selectingEstablishment = establishment;
		this.redirectToEstablishment(establishment.uid);
	}

	redirectToEstablishment(establishmentUid: VisualScopedUuid<UuidScopeEstablishment>) {
		const token = this.authStateModel.getStateSync();
		if(!token) {
			this.error = 'Impossible de trouver un identifiant de connexion';
			return;
		}

		// Optimize, may be too long for url, but we'll have to fetch full token in targeted platform
		if(token instanceof AccountAuthTokenApiOut) token.establishmentAccounts = [];
		this.redirecting = true;
		window.location.href = CrossDomainTracker.instance.addTrackingToLink((this.currentPlatform.url + 'establishment/' + UuidUtils.visualToScoped(establishmentUid) + '/auth?token=' + btoa(JSON.stringify(token)) + (this.redirectAfter ? '&redirectAfter=' + this.redirectAfter : '')));
	}

	doesUrlEstablishmentUidMatchCurrentAccount() {
		if(!this.establishmentUid || !this.currentToken) return true;
		if('establishmentUid' in this.currentToken) return true;
		else {
			return this.currentToken.establishmentAccounts.map((account) => account.establishmentUid).includes(this.establishmentUid);
		}
	}

	goToSignup() {
		window.location.href = '/signup' + window.location.search;
	}

	getPasswordResetUrl() {
		return EstablishmentUrlBuilder.buildUrl('/password-reset' + window.location.search);
	}

	async nativeScanCredentialsQr() {
		const data = await this.barcodeCameraReader.read({side: 'back'});
		if(data.status) {
			if(UuidUtils.isVisual(data.content, uuidScopeMastodon_quickConnect)) {
				this.error = null;
				this.connecting = true;

				GoogleTracker.instance.event('login');

				const response = await this.authRepository.callContract('quickConnect', undefined, new QuickConnectAuthApiIn({
					uid: data.content as VisualScopedUuid<UuidScopeMastodon_quickConnect>
				}));

				if(response.isSuccess()) {
					const token = response.success();
					localStorage.setItem('latest-used-email', this.credentials.email);
					this.currentToken = token;
					await this.continueWithAccount();
				} else if(response.isError()) {
					this.error = translateResponseError(response, {
						quickconnect_expired: 'Ce code a expiré'
					});
				} else {
					this.error = 'Une erreur est survenue';
				}

				this.connecting = false;
			} else {
				const url =  new URL(data.content);
				const id = url.searchParams.get('id');
				const password = url.searchParams.get('password');

				if(!id || !password) return;

				this.credentials.email = id;
				this.credentials.password = password;
				this.login();
			}
		}
	}
}
