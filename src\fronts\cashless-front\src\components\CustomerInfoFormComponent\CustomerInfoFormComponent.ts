import {Component, Vue} from "vue-facing-decorator";
import {AutoWired, buildHttpRoutePathWithArgs, EmailUtils} from "@groupk/horizon2-core";
import SpecificState from "../../SpecificState";
import {AppState} from "../../../../../shared/AppState";
import {CleaveDirective} from "../../../../../shared/directives/CleaveDirective";
import {translateResponseError} from "../../../../../shared/RepositoryExtensions";
import {
    CashlessHttpCustomerContract,
    MastodonHttpImagesContract,
    PublicEstablishmentApiOut
} from "@groupk/mastodon-core";
import {CashlessCustomerRepository} from "../../../../../shared/repositories/CashlessCustomerRepository";
import {EstablishmentRepository} from "../../../../../shared/repositories/EstablishmentRepository";
import {MainConfig} from "../../../../../shared/MainConfig";

export type CashlessManualData = {
    firstname: string,
    lastname: string,
    email: string,
    idPublic: string
}

export const CashlessManualDataLSKey = 'specific-state';

@Component({
    directives: {
        cleave: CleaveDirective,
    },
    emits: ['updated']
})
export default class CustomerInfoFormComponent extends Vue {

    establishment!: PublicEstablishmentApiOut;

    manualData: CashlessManualData = {
        firstname: '',
        lastname: '',
        email: '',
        idPublic: ''
    }

    cleaveChip = {
        blocks: [4, 4],
        delimiter: '-',
        uppercase: true
    }

    loading: boolean = true;
    error: string|null = null;
    validating: boolean = false;

    @AutoWired(CashlessCustomerRepository) accessor cashlessCustomerRepository!: CashlessCustomerRepository;
    @AutoWired(EstablishmentRepository) accessor establishmentRepository!: EstablishmentRepository;
    @AutoWired(SpecificState) accessor specificState!: SpecificState;
    @AutoWired(AppState) accessor appState!: AppState;
    @AutoWired(MainConfig) accessor mainConfig!: MainConfig;

    async beforeMount() {
        this.establishment = (await this.establishmentRepository.callContract('getPublic', {
            establishmentUid: this.appState.requireUrlEstablishmentUid()
        }, undefined)).success();

        this.loading = false;
    }

    async validate() {
        if(!EmailUtils.isEmailValid(this.manualData.email.trim())) {
            this.error = 'Veuillez renseigner un email valide';
            return;
        }

        this.validating = true;

        this.manualData.email = this.manualData.email.trim();

        const response = await this.cashlessCustomerRepository.callContract('getWallet', {
            establishmentUid: this.appState.requireUrlEstablishmentUid(),
            chipVisualId: this.manualData.idPublic
        }, undefined);

        if(response.isSuccess()) {
            const wallet = response.success();

            this.specificState.customer = {
                firstname: this.manualData.firstname,
                lastname: this.manualData.lastname,
                email: this.manualData.email,
            }

            this.specificState.currentWallet = wallet.item;

            localStorage.setItem(CashlessManualDataLSKey, JSON.stringify(this.manualData));

            this.$emit('updated');
        }else if(response.responseCode === 404){
            this.error = "Numéro de support non initialisé sur la plateforme."
        } else {
            this.error = translateResponseError<typeof CashlessHttpCustomerContract, 'getWallet'>(response, {
                invalid_data: undefined,
            });
        }

        this.validating = false;
    }

    getEstablishmentImageUrl() {
        if(!this.establishment || !this.establishment.mainImageUpload) return null;
        return this.mainConfig.configuration.mastodonApiEndpoint + buildHttpRoutePathWithArgs(MastodonHttpImagesContract.getPublicImage, {
            establishmentUid: this.establishment.uid,
            imageId: this.establishment.mainImageUpload.uid,
            options: {},
            dimensions: {width: 128},
            extension: 'png',
            resizeType: 'contain',
            revision: this.establishment.mainImageUpload.lastUpdateDatetime
        });
    }
}