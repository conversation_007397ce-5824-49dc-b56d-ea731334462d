import {Component, Vue} from "vue-facing-decorator";
import {
	ApplicationsSwitcherComponent,
	ContentHeaderComponent,
	ContentHeaderParameters, DropdownButtonComponent,
	DropdownComponent,
	DropdownValue,
	FormModalOrDrawerComponent,
	LayoutContentWithRightPanelComponent,
	SidebarComponent
} from "@groupk/vue3-interface-sdk";
import {OptionBuilder} from "vue-facing-decorator/dist/optionBuilder";
import {
	AutoWired, QueryFilterGroupClause, QueryOperator,
	ScopedUuid, SearchUtils, TypedQuerySearch, Uuid, UuidUtils,
	VisualScopedUuid
} from "@groupk/horizon2-core";
import {uuidScopeEstablishment, UuidScopeEstablishment} from "@groupk/mastodon-core";
import {ProfilesRepository} from "../../../../../shared/repositories/ProfilesRepository";
import {CategoriesRepository} from "../../../../../shared/repositories/CategoriesRepository";
import {TableColumnsRepository} from "../../../../../shared/repositories/TableColumnsRepository";
import SidebarStateListener from "../../../../../shared/utils/SidebarStateListener";
import {Router} from "@groupk/horizon2-front";
import {StatsRepository} from "../../../../../shared/repositories/StatsRepository";
import {
	ProfileApiOut, SimpleProductApiOut,
	TransactionStats,
	UuidScopeCashless_profile,
	TransactionStatsPoint, SimpleProductCategoryApiOut
} from "@groupk/mastodon-core";
import CustomCalendarComponent from "../../components/CustomCalendarComponent/CustomCalendarComponent.vue";
import DateUtils, {Range} from "../../../../../shared/utils/DateUtils";
import {HourFilter, MonthFilter, ShortDayFilter} from "../../../../../shared/filters/DateFilter";
import LineChartComponent from "../../components/ChartComponents/LineChartComponent/LineChartComponent.vue";
import {PaymentMethodApiOut, UuidScopePayment_method} from "@groupk/mastodon-core";
import {PaymentMethodsRepository} from "../../../../../shared/repositories/PaymentMethodsRepository";
import {CurrencyRepository} from "../../../../../shared/repositories/CurrencyRepository";
import DoughChartComponent from "../../components/ChartComponents/DoughChartComponent/DoughChartComponent.vue";
import {ProductsRepository} from "../../../../../shared/repositories/ProductsRepository";
import {MoneyFilter} from "../../../../../shared/filters/Money";
import {CashlessHttpTransactionsStatsContractConfig} from "@groupk/mastodon-core";
import ExportChoiceComponent, {ChosenExport} from "../../components/ExportChoiceComponent/ExportChoiceComponent.vue";
import {ExportFileType, MultiFormatExporter} from "../../../../../shared/utils/MultiFormatExporter";
import {jsx} from "vue/jsx-runtime";

export type ProfileAggregate = Record<VisualScopedUuid<UuidScopeCashless_profile>, {
	totalWithTaxes: {
		refills: number,
		sells: number
	},
	transactionsCount: {
		refills: number,
		sells: number
	},
	perPaymentMethod: Record<VisualScopedUuid<UuidScopePayment_method>|string, {
		refills: number,
		sells: number
	}>,
	perProduct: Record<number|string, {
		refills: number,
		sells: number,
		quantity: number,
		perPaymentMethod: Record<VisualScopedUuid<UuidScopePayment_method>|string, {
			refills: number,
			sells: number
		}>,
	}>
}>

export type ProductAggregate = Record<number|string, {
	totalWithTaxes: {
		refills: number,
		sells: number
	},
	quantity: number,
	transactionsCount: {
		refills: number,
		sells: number
	},
	perProfile: Record<VisualScopedUuid<UuidScopeCashless_profile>, {
		refills: number,
		sells: number,
		quantity: number,
	}>
}>

@Component({
	components: {
		layout: LayoutContentWithRightPanelComponent,
		dropdown: DropdownComponent,
		'custom-calendar': CustomCalendarComponent,
		'line-chart': LineChartComponent,
		'doughnut-chart': DoughChartComponent,
		'content-header': ContentHeaderComponent,
		sidebar: SidebarComponent,
		'application-switcher': ApplicationsSwitcherComponent,
		'form-modal-or-drawer': FormModalOrDrawerComponent,
		'dropdown-button': DropdownButtonComponent,
		'export-choice-modal': ExportChoiceComponent
	},
})
export default class TransactionsStatisticsView extends Vue {
	establishmentUid!: VisualScopedUuid<UuidScopeEstablishment>;
	profiles: ProfileApiOut[] = [];
	paymentMethods: PaymentMethodApiOut[] = [];
	products: SimpleProductApiOut[] = [];
	categories: SimpleProductCategoryApiOut[] = [];
	statistics!: TransactionStats;

	selectedProfile: ProfileApiOut|null = null;
	selectedKiosk: ProfileApiOut|null = null;
	selectedProduct: SimpleProductApiOut|'none'|null = null;
	search: string = '';

	headerParameters: ContentHeaderParameters = {
		header: 'Statistiques',
		subtitle: 'Analysez vos ventes',
		actions: [{
			type: 'SIMPLE_ACTION',
			name: 'Imprimer le rapport',
			icon: 'fa-regular fa-print',
			callback: this.initPrintReport
		}, {
			type: 'SIMPLE_ACTION',
			name: 'Exporter les données',
			icon: 'fa-regular fa-arrow-down-to-line',
			callback: this.toggleExportModal
		}],
		hideSearch: true,
		searchPlaceholder: '',
		tabs: [{
			id: 'GLOBAL',
			name: 'CA global',
		}, {
			id: 'PROFILES',
			name: 'Points de vente',
		}, {
			id: 'PRODUCTS',
			name: 'Produits',
		}]
	}

	currentView: 'GLOBAL'|'PROFILES'|'PRODUCTS'|'KIOSKS' = 'GLOBAL';
	profileDetailView: 'PAYMENT_METHODS'|'PRODUCTS' = 'PAYMENT_METHODS';

	showCalendarModal: boolean = false;
	showReportOptionsModal: boolean = false;
	showExportPerCetagoriesModal: boolean = false;
	exportingPerCategories: boolean = false;

	showExportModal: boolean = false;
	selectedExport: boolean = false;

	selectedRange: Range = {start: new Date(), end: null, color: 'default', chained: true, disabled: false}
	calendarRange: Range|null = null;

	selectedDropdownGroupBy: 'fifteen'|'hour'|'day'|'weekly'|'monthly' = 'hour'
	groupByDropdownValues: DropdownValue[] = [{
		name: '15 minutes',
		value: 'fifteen'
	}, {
		name: 'Chaque heure',
		value: 'hour'
	}];

	selectedDropdownPeriod: string = 'today';
	periodsDropdownValues: DropdownValue[] = [{
		name: 'Aujourd\'hui',
		value: 'today',
	}, {
		name: '7 derniers jours',
		value: 'last-seven-days',
	}, {
		name: '4 dernières semaines',
		value: 'last-four-weeks',
	}, {
		name: '3 derniers mois',
		value: 'last-three-months',
	}, {
		name: 'Début du mois',
		value: 'start-of-month',
	}, {
		name: 'Début du trimestre',
		value: 'start-of-quarter',
	}, {
		name: 'Début de l\'année',
		value: 'start-of-year',
	}, {
		name: 'Personnalisé',
		value: 'custom',
	}];

	DateUtils = DateUtils;

	loading: boolean = false;
	downloadingProfilesStatistics: boolean = false;
	downloadingProductStatistics: boolean = false;
	printingReport: boolean = false;
	reportExcludedPaymentMethods: VisualScopedUuid<UuidScopePayment_method>[] = [];
	exportCategories: Uuid[] = [];

	@AutoWired(ProfilesRepository) accessor profilesRepository!: ProfilesRepository;
	@AutoWired(CategoriesRepository) accessor categoriesRepository!: CategoriesRepository;
	@AutoWired(ProductsRepository) accessor productsRepository!: ProductsRepository;
	@AutoWired(PaymentMethodsRepository) accessor paymentMethodsRepository!: PaymentMethodsRepository;
	@AutoWired(CurrencyRepository) accessor currencyRepository!: CurrencyRepository;
	@AutoWired(TableColumnsRepository) accessor tableColumnsRepository!: TableColumnsRepository;
	@AutoWired(SidebarStateListener) accessor sidebarStateListener!: SidebarStateListener;
	@AutoWired(StatsRepository) accessor statsRepository!: StatsRepository;
	@AutoWired(Router) accessor router!: Router;

	constructor(optionBuilder: OptionBuilder, vueInstance: any) {
		super(optionBuilder, vueInstance);
		let regexMatch = this.router.lastRouteRegexMatches;

		this.sidebarStateListener.setHiddenSidebar(false);

		addEventListener("resize", (event) => {
			if(window.innerWidth < 1400) this.sidebarStateListener.setMinimizedSidebar(true);
			else this.sidebarStateListener.setMinimizedSidebar(false);
		});

		if (regexMatch && regexMatch[1]) {
			this.establishmentUid = UuidUtils.scopedToVisual<UuidScopeEstablishment>(regexMatch[1] as ScopedUuid<UuidScopeEstablishment>, uuidScopeEstablishment);

			this.loading = true;
		}
	}

	async mounted() {
		(await this.currencyRepository.callContract('list', {establishmentUid: this.establishmentUid}, undefined)).success();
		this.profiles = (await this.profilesRepository.callContract('list', {establishmentUid: this.establishmentUid}, undefined)).success();
		this.paymentMethods = (await this.paymentMethodsRepository.callContract('list', {establishmentUid: this.establishmentUid}, undefined)).success();
		this.products = (await this.productsRepository.callContract('list', {establishmentUid: this.establishmentUid}, undefined)).success();
		this.categories = (await this.categoriesRepository.callContract('list', {establishmentUid: this.establishmentUid}, undefined)).success();
		await this.selectedDropdownValue('today');

		this.loading = false;
	}

	async fetchStatistics() {
		let datesFilter: TypedQuerySearch<typeof CashlessHttpTransactionsStatsContractConfig> = {};
		const startDate: Date|null = this.selectedRange.start ? new Date(this.selectedRange.start.getTime()) : null;
		let endDate: Date|null = this.selectedRange.end ? new Date(this.selectedRange.end.getTime()) : null;
		if(startDate) {
			if(!endDate) {
				datesFilter = {
					filter: {
						name: 'creationDatetime',
						value: startDate.toISOString(),
						operator: QueryOperator.MORE_OR_EQUAL
					}
				}
			} else {
				datesFilter = {
					filter: {
						group: QueryFilterGroupClause.AND,
						filters: [{
							name: 'creationDatetime',
							value: startDate.toISOString(),
							operator: QueryOperator.MORE_OR_EQUAL
						}, {
							name: 'creationDatetime',
							value: endDate.toISOString(),
							operator: QueryOperator.LESS_OR_EQUAL
						}]
					}
				}
			}
		}

		this.loading = true;
		this.statistics = (await this.statsRepository.callContract('fetch', {establishmentUid: this.establishmentUid}, datesFilter)).success();
		this.loading = false;
	}

	getTimeDeltaForGroupByAndDate(date: Date): number {
		if(this.selectedDropdownGroupBy === 'fifteen') {
			return 900000;
		} else if(this.selectedDropdownGroupBy === 'hour') {
			return 3600000;
		} else if(this.selectedDropdownGroupBy === 'day') {
			return 86400000;
		} else if(this.selectedDropdownGroupBy === 'weekly') {
			return 604800000;
		} else if(this.selectedDropdownGroupBy === 'monthly') {
			const lastDay= new Date(date.getFullYear(), date.getMonth()+1, 0);
			const daysNumber = lastDay.getDate();
			return daysNumber * 86400000;
		}
		return 0;
	}

	get scaleForRangeAndGroupBy(): Date[] {
		let endDate = this.selectedRange.end;

		if(this.selectedDropdownGroupBy === 'fifteen') {
			if(!this.selectedRange.start) throw new Error('invalid_group_by');
			return DateUtils.getFifteenMinutesInRange(this.selectedRange.start, endDate);
		} else if(this.selectedDropdownGroupBy === 'hour') {
			if(!this.selectedRange.start) throw new Error('invalid_group_by');
			return DateUtils.getHoursInRange(this.selectedRange.start, endDate);
		} else if(this.selectedDropdownGroupBy === 'day') {
			if(!this.selectedRange.start || !endDate) throw new Error('invalid_group_by');
			return DateUtils.getDatesInRange(this.selectedRange.start, endDate);
		} else if(this.selectedDropdownGroupBy === 'weekly') {
			if(!this.selectedRange.start || !endDate) throw new Error('invalid_group_by');
			return DateUtils.getWeeksInRange(this.selectedRange.start, endDate);
		} else if(this.selectedDropdownGroupBy === 'monthly') {
			if(!this.selectedRange.start || !endDate) throw new Error('invalid_group_by');
			return DateUtils.getMonthsInRange(this.selectedRange.start, endDate);
		}
		return [];
	}

	get labelsForRangeAndGroupBy() {
		const scale = this.scaleForRangeAndGroupBy;
		if(this.selectedDropdownGroupBy === 'hour' || this.selectedDropdownGroupBy === 'fifteen') {
			return scale.map((date) => {
				const dateString = date.toISOString();
				return (scale.length > 24 ? (ShortDayFilter(dateString) + ' ') : '') + HourFilter(dateString);
			});
		} else if(this.selectedDropdownGroupBy === 'day') {
			return scale.map((date) => ShortDayFilter(date.toISOString()));
		} else if(this.selectedDropdownGroupBy === 'weekly') {
			return scale.map((date) => 'Sem. du ' + ShortDayFilter(date.toISOString()));
		} else if(this.selectedDropdownGroupBy === 'monthly') {
			return scale.map((date) => MonthFilter(date.toISOString()));
		}
		return [];
	}

	getDataIndex(dataDatetime: string): number|null {
		const dataDateAsTime = new Date(dataDatetime).getTime();
		const scale = this.scaleForRangeAndGroupBy;
		for(let index = 0; index < scale.length; index++) {
			let timeDelta = this.getTimeDeltaForGroupByAndDate(scale[index]);
			if(scale[index] && scale[index + 1]) timeDelta = scale[index + 1].getTime() - scale[index].getTime();
			if(dataDateAsTime >= scale[index].getTime() && dataDateAsTime < scale[index].getTime() + timeDelta) {
				return index;
			}
		}
		return null;
	}
	getRefillChartData() {
		const labels = this.labelsForRangeAndGroupBy;
		const refillData = new Array(labels.length).fill(0);
		const sellsData = new Array(labels.length).fill(0);

		for(let point of this.statistics.points) {
			if(!point.datetime) continue;
			const index = this.getDataIndex(point.datetime);
			const totals = this.getAggregateTotal(point);
			if(index !== null) refillData[index] += totals.refills / 100;
			if(index !== null) sellsData[index] += totals.sells / 100;
		}
		return {
			labels: labels,
			datasets: [{
				label: 'Rechargements',
				data: refillData,
				borderColor: '#003dff',
				tension: 0.3,
				fill: true
			}, {
				label: 'Dépenses',
				data: sellsData,
				borderColor: '#bbc9fd',
				tension: 0.3,
				fill: true
			}]
		};
	}

	getAggregateTotal(aggregate: TransactionStatsPoint, filter: { profileUid: VisualScopedUuid<UuidScopeCashless_profile>|null, productId: number|'none'|null }|null = null): { sells: number, refills: number, sellTransactions: number, refillTransactions: number } {
		let total: { sells: number, refills: number, sellTransactions: number, refillTransactions: number } = { sells: 0, refills: 0, sellTransactions: 0, refillTransactions: 0 };
		for(let perProfile of aggregate.perProfile) {
			if(filter && filter.profileUid && perProfile.profileUid !== filter.profileUid) continue;
			for(let perEstablishmentAccount of perProfile.perEstablishmentAccount) {
				for(let perPaymentMethod of perEstablishmentAccount.perPaymentMethod) {
					for(let perProduct of perPaymentMethod.perProduct) {
						if(filter && filter.productId !== null && (perProduct.productId ?? 'none') !== filter.productId) continue;
						for(let perPrice of perProduct.perPrice) {
							for(let perCurrency of perPrice.perCurrency) {
								if(perCurrency.currency === 0) {
									if(perCurrency.totalAmount > 0) {
										total.refills += perCurrency.totalAmount;
										total.refillTransactions += perCurrency.transactionCount;
									} else {
										total.sells += perCurrency.totalAmount * -1;
										total.sellTransactions += perCurrency.transactionCount;
									}
								}
							}
						}
					}
				}
			}
		}
		return total;
	}

	get profileTotal(): {refills: number, sells: number} {
		const total: {refills: number, sells: number} = {refills: 0, sells: 0}
		const aggregate = this.aggregateProfileTotal;
		for (const [profileUid, data] of Object.entries(aggregate)) {
			total.refills += data.totalWithTaxes.refills;
			total.sells += data.totalWithTaxes.sells;
		}
		return total;
	}

	get productTotal(): {refills: number, sells: number, quantity: number} {
		const total: {refills: number, sells: number, quantity: number} = {refills: 0, sells: 0, quantity: 0}
		const aggregate = this.aggregateProductTotal;
		for (const [productUid, data] of Object.entries(aggregate)) {
			total.refills += data.totalWithTaxes.refills;
			total.sells += data.totalWithTaxes.sells;
			total.quantity += data.quantity;
		}
		return total;
	}

	get aggregateProductTotal(): ProductAggregate {
		let totalPerProduct: ProductAggregate = {};

		for(let point of this.statistics.points) {
			if(!point.datetime) continue;
			if(this.isDataInRange(new Date(point.datetime))) {
				for (let perProfile of point.perProfile) {
					for(let perEstablishmentAccount of perProfile.perEstablishmentAccount) {
						for(let perPaymentMethod of perEstablishmentAccount.perPaymentMethod) {
							for(let perProduct of perPaymentMethod.perProduct) {
								const productKey = perProduct.productId ?? 'none';
								if(!totalPerProduct[productKey]) {
									const perProfile: Record<VisualScopedUuid<UuidScopeCashless_profile>|string, {
										refills: number,
										sells: number,
										quantity: number
									}> = {};

									for(let profile of this.profiles) {
										perProfile[profile.uid] = {
											refills: 0,
											sells: 0,
											quantity: 0,
										}
									}

									totalPerProduct[productKey] = {
										totalWithTaxes: {
											refills: 0,
											sells: 0
										},
										transactionsCount: {
											refills: 0,
											sells: 0
										},
										quantity: 0,
										perProfile: perProfile
									}
								}

								for(let perPrice of perProduct.perPrice) {
									for(let perCurrency of perPrice.perCurrency) {
										if(perCurrency.currency === 0) {
											totalPerProduct[productKey].quantity += perPrice.totalCount; // WARNING MULTIPLE CURRENCIES
											totalPerProduct[productKey].perProfile[perProfile.profileUid].quantity += perPrice.totalCount;

											if(perCurrency.totalAmount > 0) {
												totalPerProduct[productKey].totalWithTaxes.refills += perCurrency.totalAmount;
												totalPerProduct[productKey].transactionsCount.refills += perCurrency.transactionCount;
												totalPerProduct[productKey].perProfile[perProfile.profileUid].refills += perCurrency.totalAmount;
											} else {
												totalPerProduct[productKey].totalWithTaxes.sells += perCurrency.totalAmount * -1;
												totalPerProduct[productKey].transactionsCount.sells += perCurrency.transactionCount;
												totalPerProduct[productKey].perProfile[perProfile.profileUid].sells += perCurrency.totalAmount * -1;
											}
										}
									}
								}
							}
						}
					}
				}
			}
		}
		console.log(totalPerProduct);
		return totalPerProduct;
	}

	get aggregateProfileTotal(): ProfileAggregate {
		let totalPerProfile: ProfileAggregate = {};

		for(let point of this.statistics.points) {
			if(!point.datetime) continue;
			if(this.isDataInRange(new Date(point.datetime))) {
				for (let perProfile of point.perProfile) {
					if (!totalPerProfile[perProfile.profileUid]) {
						const perPaymentMethod: Record<VisualScopedUuid<UuidScopePayment_method>|string, {
							refills: number,
							sells: number,
						}> = {
							'none': {
								refills: 0,
								sells: 0,
							}
						};
						for (let method of this.paymentMethods) {
							perPaymentMethod[method.uid] = {
								refills: 0,
								sells: 0,
							};
						}

						const perProduct: Record<number|string, {
							refills: number,
							sells: number,
							quantity: number,
							perPaymentMethod: Record<VisualScopedUuid<UuidScopePayment_method>|string, {
								refills: number,
								sells: number,
							}>
						}> = {
							'none': {
								refills: 0,
								sells: 0,
								quantity: 0,
								perPaymentMethod: JSON.parse(JSON.stringify(perPaymentMethod))
							}
						};
						for (let product of this.products) {
							perProduct[product.id] = {
								refills: 0,
								sells: 0,
								quantity: 0,
								perPaymentMethod: JSON.parse(JSON.stringify(perPaymentMethod))
							};
						}

						totalPerProfile[perProfile.profileUid] = {
							totalWithTaxes: {
								refills: 0,
								sells: 0
							},
							transactionsCount: {
								refills: 0,
								sells: 0
							},
							perPaymentMethod: perPaymentMethod,
							perProduct: perProduct
						};
					}

					for(let perEstablishmentAccount of perProfile.perEstablishmentAccount) {
						for(let perPaymentMethod of perEstablishmentAccount.perPaymentMethod) {
							for(let perProduct of perPaymentMethod.perProduct) {
								for(let perPrice of perProduct.perPrice) {
									for(let perCurrency of perPrice.perCurrency) {
										if(perCurrency.currency === 0) {
											if(perCurrency.totalAmount > 0) {
												totalPerProfile[perProfile.profileUid].totalWithTaxes.refills += perCurrency.totalAmount;
												totalPerProfile[perProfile.profileUid].transactionsCount.refills += perCurrency.transactionCount;

												const paymentIndex = perPaymentMethod.paymentMethodUid ?? 'none';
												totalPerProfile[perProfile.profileUid].perPaymentMethod[paymentIndex].refills += perCurrency.totalAmount;

												const productIndex = perProduct.productId ?? 'none';
												totalPerProfile[perProfile.profileUid].perProduct[productIndex].refills += perCurrency.totalAmount;
												totalPerProfile[perProfile.profileUid].perProduct[productIndex].quantity += perPrice.totalCount;
												totalPerProfile[perProfile.profileUid].perProduct[productIndex].perPaymentMethod[paymentIndex].refills += perCurrency.totalAmount;
											} else {
												totalPerProfile[perProfile.profileUid].totalWithTaxes.sells += perCurrency.totalAmount * -1;
												totalPerProfile[perProfile.profileUid].transactionsCount.sells += perCurrency.transactionCount;
												const paymentIndex = perPaymentMethod.paymentMethodUid ?? 'none';
												totalPerProfile[perProfile.profileUid].perPaymentMethod[paymentIndex].sells += perCurrency.totalAmount * -1;

												const productIndex = perProduct.productId ?? 'none';
												totalPerProfile[perProfile.profileUid].perProduct[productIndex].sells += perCurrency.totalAmount * -1;
												totalPerProfile[perProfile.profileUid].perProduct[productIndex].quantity += perPrice.totalCount;
												totalPerProfile[perProfile.profileUid].perProduct[productIndex].perPaymentMethod[paymentIndex].sells += perCurrency.totalAmount * -1;
											}
										}
									}
								}
							}
						}
					}
				}
			}
		}
		return totalPerProfile;
	}

	getProfileRefillChartData() {
		const data: number[] = [];

		const aggregate = this.aggregateProfileTotal;

		for(const profile of this.profiles) {
			if(aggregate[profile.uid]) {
				data.push(aggregate[profile.uid].totalWithTaxes.refills / 100);
			} else {
				data.push(0);
			}
		}

		return  {
			labels: this.profiles.map((profile) => profile.name),
			datasets: [{
				label: 'Rechargements',
				data: data,
				backgroundColor: ['#2a5af8', '#4c76ff', '#83a1ff', '#b3c4ff', '#c6d3ff'],
				borderColor: 'rgba(0, 0, 0, 0)'
			}]
		}
	}

	getProfileSellsChartData() {
		const data: number[] = [];

		const aggregate = this.aggregateProfileTotal;

		for(const profile of this.profiles) {
			if(aggregate[profile.uid]) {
				data.push(aggregate[profile.uid].totalWithTaxes.sells / 100);
			} else {
				data.push(0);
			}
		}

		return  {
			labels: this.profiles.map((profile) => profile.name),
			datasets: [{
				label: 'Ventes',
				data: data,
				backgroundColor: ['#2a5af8', '#4c76ff', '#83a1ff', '#b3c4ff', '#c6d3ff'],
				borderColor: 'rgba(0, 0, 0, 0)'
			}]
		}
	}

	getSelectedProfileChartData() {
		const labels = this.labelsForRangeAndGroupBy;
		const refillData = new Array(labels.length).fill(0);
		const sellsData = new Array(labels.length).fill(0);

		for(let point of this.statistics.points) {
			if(!point.datetime) continue;
			const index = this.getDataIndex(point.datetime);
			const totals = this.getAggregateTotal(point, {profileUid: this.selectedProfile?.uid ?? null, productId: null});
			if(index !== null) refillData[index] += totals.refills / 100;
			if(index !== null) sellsData[index] += totals.sells / 100;
		}
		return {
			labels: labels,
			datasets: [{
				label: 'Rechargements',
				data: refillData,
				borderColor: '#003dff',
				tension: 0.3,
				fill: true
			}, {
				label: 'Dépenses',
				data: sellsData,
				borderColor: '#bbc9fd',
				tension: 0.3,
				fill: true
			}]
		};
	}

	getSelectedProductChartData() {
		const labels = this.labelsForRangeAndGroupBy;
		const refillData = new Array(labels.length).fill(0);
		const sellsData = new Array(labels.length).fill(0);

		for(let point of this.statistics.points) {
			if(!point.datetime) continue;
			const index = this.getDataIndex(point.datetime);
			const totals = this.getAggregateTotal(point, {profileUid: null, productId: this.selectedProduct === 'none' ? 'none' : (this.selectedProduct?.id ?? null)});
			if(index) refillData[index] += totals.refills / 100;
			if(index) sellsData[index] += totals.sells / 100;
		}
		return {
			labels: labels,
			datasets: [{
				label: 'Rechargements',
				data: refillData,
				borderColor: '#003dff',
				tension: 0.3,
				fill: true
			}, {
				label: 'Dépenses',
				data: sellsData,
				borderColor: '#bbc9fd',
				tension: 0.3,
				fill: true
			}]
		};
	}


	getCATotals(): { sells: number, refills: number, sellTransactions: number, refillTransactions: number } {
		let total: { sells: number, refills: number, sellTransactions: number, refillTransactions: number } = { sells: 0, refills: 0, sellTransactions: 0, refillTransactions: 0 };
		for(let point of this.statistics.points) {
			if(!point.datetime) continue;
			if(this.isDataInRange(new Date(point.datetime))) {
				const subTotal = this.getAggregateTotal(point);
				total.refills += subTotal.refills;
				total.sells += subTotal.sells;
				total.refillTransactions += subTotal.refillTransactions;
				total.sellTransactions += subTotal.sellTransactions;
			}
		}
		return total;
	}

	toggleCalendarModal() {
		this.calendarRange = null;
		this.showCalendarModal = !this.showCalendarModal;
	}

	selectedPeriod(ranges: Range[]) {
		console.log('selectedPeriod');
		this.calendarRange = ranges[0];
		if(this.calendarRange.start === this.calendarRange.end) {
			this.calendarRange.end = null;
		}
	}

	applyCalendarRange() {
		if(this.calendarRange) {
			console.log(JSON.stringify(this.calendarRange));
			this.selectedRange = this.calendarRange;
			this.selectedDropdownPeriod = 'custom';

			this.groupByDropdownValues = this.getGroupByDropdownValuesForRange(this.selectedRange);
			if(!this.groupByDropdownValues.map((data) => data.value).includes(this.selectedDropdownGroupBy)) {
				this.selectedDropdownGroupBy = this.groupByDropdownValues[0].value as never;
			}
		}
		this.fetchStatistics();
		this.toggleCalendarModal();
	}

	selectedGroupByDropdownValue(value: string) {
		this.selectedDropdownGroupBy = value as never;
	}

	isDataInRange(dataDate: Date) {
		if(!this.selectedRange.start) throw new Error('missing_range_start');
		if(!this.selectedRange.end) {
			return dataDate.getDate() === this.selectedRange.start.getDate() &&
				dataDate.getMonth() === this.selectedRange.start.getMonth() &&
				dataDate.getFullYear() === this.selectedRange.start.getFullYear();
		} else {
			return dataDate.getTime() > this.selectedRange.start.getTime() &&
				dataDate.getTime() < this.selectedRange.end.getTime()
		}
	}

	async selectedDropdownValue(value: string) {
		this.selectedDropdownPeriod = value;
		if(value === 'today') {
			const startOfDay = new Date();
			startOfDay.setHours(0);
			startOfDay.setMinutes(0);
			startOfDay.setSeconds(0);
			startOfDay.setMilliseconds(0);
			this.selectedRange = {start: startOfDay, end: null, color: 'default', chained: true, disabled: false};
		} else if(value === 'last-seven-days') {
			const today = new Date();
			const minusSeven = new Date();
			minusSeven.setDate(today.getDate() - 6);

			minusSeven.setHours(0);
			minusSeven.setMinutes(0);
			minusSeven.setSeconds(0);
			minusSeven.setMilliseconds(0);

			today.setHours(23);
			today.setMinutes(59);
			today.setSeconds(59);
			today.setMilliseconds(999);

			this.selectedRange = {start: minusSeven, end: today, color: 'default', chained: true, disabled: false};
		} else if(value === 'last-four-weeks') {
			const today = new Date();
			const minusFourWeeks = new Date();
			minusFourWeeks.setDate((today.getDate() - 7 * 4) + 1);

			minusFourWeeks.setHours(0);
			minusFourWeeks.setMinutes(0);
			minusFourWeeks.setSeconds(0);
			minusFourWeeks.setMilliseconds(0);

			today.setHours(23);
			today.setMinutes(59);
			today.setSeconds(59);
			today.setMilliseconds(999);

			this.selectedRange = {start: minusFourWeeks, end: today, color: 'default', chained: true, disabled: false};
		} else if(value === 'last-three-months') {
			const today = new Date();
			const minusThreeMonth = new Date();
			minusThreeMonth.setMonth(today.getMonth() - 3);
			minusThreeMonth.setDate(minusThreeMonth.getDate() + 1);

			minusThreeMonth.setHours(0);
			minusThreeMonth.setMinutes(0);
			minusThreeMonth.setSeconds(0);
			minusThreeMonth.setMilliseconds(0);

			today.setHours(23);
			today.setMinutes(59);
			today.setSeconds(59);

			this.selectedRange = {start: minusThreeMonth, end: today, color: 'default', chained: true, disabled: false};
		} else if(value === 'start-of-month') {
			const today = new Date();
			const minusThreeMonth = new Date();
			minusThreeMonth.setDate(1);

			minusThreeMonth.setHours(0);
			minusThreeMonth.setMinutes(0);
			minusThreeMonth.setSeconds(0);
			minusThreeMonth.setMilliseconds(0);

			today.setHours(23);
			today.setMinutes(59);
			today.setSeconds(59);

			this.selectedRange = {start: minusThreeMonth, end: today, color: 'default', chained: true, disabled: false};
		} else if(value === 'start-of-quarter') {
			const today = new Date();
			const quarter = new Date();
			quarter.setMonth(quarter.getMonth() - (quarter.getMonth() % 3));
			quarter.setDate(1);

			quarter.setHours(0);
			quarter.setMinutes(0);
			quarter.setSeconds(0);
			quarter.setMilliseconds(0);

			today.setHours(23);
			today.setMinutes(59);
			today.setSeconds(59);
			this.selectedRange = {start: quarter, end: today, color: 'default', chained: true, disabled: false};
		} else if(value === 'start-of-year') {
			const today = new Date();
			const year = new Date();
			year.setMonth(0);
			year.setDate(1);

			year.setHours(0);
			year.setMinutes(0);
			year.setSeconds(0);
			year.setMilliseconds(0);

			today.setHours(23);
			today.setMinutes(59);
			today.setSeconds(59);

			this.selectedRange = {start: year, end: today, color: 'default', chained: true, disabled: false};
		} else if(value === 'custom') {
			this.toggleCalendarModal();
		}

		this.groupByDropdownValues = this.getGroupByDropdownValuesForRange(this.selectedRange);
		if(!this.groupByDropdownValues.map((data) => data.value).includes(this.selectedDropdownGroupBy)) {
			this.selectedDropdownGroupBy = this.groupByDropdownValues[0].value as never;
		}

		await this.fetchStatistics();
	}

	getGroupByDropdownValuesForRange(range: Range): DropdownValue[] {
		if(!range.start) return [];
		if(!range.end) {
			return [{
				name: '15 minutes',
				value: 'fifteen'
			}, {
				name: 'Chaque heure',
				value: 'hour'
			}]
		}
		const daysInterval = this.daysBetween(range.start, range.end);
		if(daysInterval <= 2) {
			return [{
				name: '15 minutes',
				value: 'fifteen'
			}, {
				name: 'Chaque heure',
				value: 'hour'
			}];
		}
		if(daysInterval <= 7) {
			return [{
				name: 'Chaque heure',
				value: 'hour'
			}, {
				name: 'Quotidien',
				value: 'day'
			}];
		}
		if(daysInterval <= 4 * 7) {
			return [{
				name: 'Quotidien',
				value: 'day'
			}, {
				name: 'Hebdomadaire',
				value: 'weekly'
			}];
		}
		return [{
			name: 'Quotidien',
			value: 'day'
		}, {
			name: 'Hebdomadaire',
			value: 'weekly'
		}, {
			name: 'Mensuel',
			value: 'monthly'
		}];
	}

	treatAsUTC(date: Date): Date {
		const result = new Date(date);
		result.setMinutes(result.getMinutes() - result.getTimezoneOffset());
		return result;
	}

	daysBetween(startDate: Date, endDate: Date) {
		const millisecondsPerDay = 24 * 60 * 60 * 1000;
		return (this.treatAsUTC(endDate).valueOf() - this.treatAsUTC(startDate).valueOf()) / millisecondsPerDay;
	}

	getProfileWithUid(profileUid: VisualScopedUuid<UuidScopeCashless_profile>) {
		const profile = this.profiles.find((profile) => profile.uid === profileUid);
		if(!profile) throw new Error('missing_account');
		return profile;
	}

	searchInArray<T extends (SimpleProductApiOut|ProfileApiOut|PaymentMethodApiOut)>(array: T[]): T[] {
		return SearchUtils.searchInTab(array, (elem) => {
			return elem.name;
		}, this.search);
	}

	async exportProfilesStatistics(type: ExportFileType, exportZeroLines: boolean) {
		this.downloadingProfilesStatistics = true;

		this.$nextTick(() => {
			const jsonData: Record<string, string|number>[] = [];

			for (let profile of this.profiles) {
				for (let product of [...this.products, 'none']) {
					for (let paymentMethod of [...this.paymentMethods, 'none']) {
						const productIndex = typeof product !== 'string' ? product.id : 'none';
						const paymentMethodIndex = typeof paymentMethod !== 'string' ? paymentMethod.uid : 'none';

						if(
							(this.aggregateProfileTotal[profile.uid]?.perProduct[productIndex].perPaymentMethod[paymentMethodIndex].refills ?? 0) !== 0 ||
							(this.aggregateProfileTotal[profile.uid]?.perProduct[productIndex].perPaymentMethod[paymentMethodIndex].sells ?? 0) !== 0
						) {
							if(
								!exportZeroLines &&
								(this.aggregateProfileTotal[profile.uid]?.perProduct[productIndex].perPaymentMethod[paymentMethodIndex].refills ?? 0) === 0 &&
								(this.aggregateProfileTotal[profile.uid]?.perProduct[productIndex].perPaymentMethod[paymentMethodIndex].sells ?? 0) === 0
							) continue;

							jsonData.push({
								'Point de vente': profile.name,
								'Produit': typeof product !== 'string' ? product.name : 'Montant libre',
								'Moyen de paiement': typeof paymentMethod !== 'string' ? paymentMethod.name : 'Aucun',
								'Rechargements': ((this.aggregateProfileTotal[profile.uid]?.perProduct[productIndex].perPaymentMethod[paymentMethodIndex].refills ?? 0) / 100),
								'Depenses': ((this.aggregateProfileTotal[profile.uid]?.perProduct[productIndex].perPaymentMethod[paymentMethodIndex].sells ?? 0) / 100),
							})
						}
					}

				}
			}

			MultiFormatExporter.downloadData([
				{name: 'Point de vente', type: 'AUTO'},
				{name: 'Produit', type: 'AUTO'},
				{name: 'Moyen de paiement', type: 'AUTO'},
				{name: 'Rechargements', type: 'MONEY'},
				{name: 'Depenses', type: 'MONEY'},
			], jsonData, type);

			this.downloadingProfilesStatistics = false;
		});
	}

	async exportProductsStatistics(type: ExportFileType, exportZeroLines: boolean) {
		this.downloadingProductStatistics = true;

		this.$nextTick(() => {
			const jsonData: Record<string, string|number>[] = [];

			for(let product of [...this.products, 'none']) {
				for(let profile of this.profiles) {
					const productIndex = typeof product !== 'string' ? product.id : 'none';
					if(
						!exportZeroLines &&
						(this.aggregateProductTotal[productIndex]?.perProfile[profile.uid].refills ?? 0) === 0 &&
						(this.aggregateProductTotal[productIndex]?.perProfile[profile.uid].sells ?? 0) === 0
					) continue;

					jsonData.push({
						'Point de vente': profile.name,
						'Produit': typeof product !== 'string' ? product.name : 'Montant libre',
						'Quantite': this.aggregateProductTotal[productIndex]?.perProfile[profile.uid].quantity ?? 0,
						'Rechargements': (this.aggregateProductTotal[productIndex]?.perProfile[profile.uid].refills ?? 0) / 100,
						'Depenses': (this.aggregateProductTotal[productIndex]?.perProfile[profile.uid].sells ?? 0) / 100,
					})
				}
			}

			MultiFormatExporter.downloadData([
				{name: 'Point de vente', type: 'AUTO'},
				{name: 'Produit', type: 'AUTO'},
				{name: 'Quantite', type: 'AUTO'},
				{name: 'Rechargements', type: 'MONEY'},
				{name: 'Depenses', type: 'MONEY'},
			], jsonData, type);

			this.downloadingProductStatistics = false;
		});
	}

	exportCategoriesStatistics() {
		this.exportingPerCategories = true;

		this.$nextTick(() => {
			const jsonData: Record<string, string|number>[] = [];
			for(let category of this.categories) {
				if(this.exportCategories.includes(category.uid)) continue;
				let categoryTotals = {sells: 0, refills: 0};
				for(let product of this.products) {
					if(category.productIds.includes(product.id)) {
						categoryTotals.sells += this.aggregateProductTotal[product.id]?.totalWithTaxes.sells ?? 0;
						categoryTotals.refills += this.aggregateProductTotal[product.id]?.totalWithTaxes.refills ?? 0;
					}
				}
				jsonData.push({
					'Categorie': category.name,
					'Rechargements': categoryTotals.refills / 100,
					'Depenses': categoryTotals.sells / 100
				});
			}

			MultiFormatExporter.downloadData(
				[{name: 'Categorie', type: 'AUTO'}, {name: 'Rechargements', type: 'MONEY'}, {name: 'Depenses', type: 'MONEY'}],
				jsonData,
				"xlsx"
			);

			this.downloadingProductStatistics = false;
		});

		this.exportingPerCategories = false;
	}

	printReport() {
		const startDate = this.selectedRange.start ? new Date(this.selectedRange.start) : new Date();
		const endDate = this.selectedRange.end ? new Date(this.selectedRange.end) : new Date(startDate);

		startDate.setHours(0);
		startDate.setMinutes(0);
		startDate.setSeconds(0);
		startDate.setMilliseconds(0);

		endDate.setHours(23);
		endDate.setMinutes(59);
		endDate.setSeconds(59);
		endDate.setMilliseconds(999);

		this.printingReport = true;
		const printIframe = document.createElement('iframe')
		printIframe.src = window.location.origin+`/establishment/${UuidUtils.visualToUuid(this.establishmentUid)}/sample-report?after=${startDate.toISOString()}&before=${endDate.toISOString()}&excludedPaymentMethods=${JSON.stringify(this.reportExcludedPaymentMethods)}`
		if(printIframe.contentWindow) printIframe.contentWindow.document.body.style.cssText = 'padding: 0; margin: 0;';
		printIframe.style.display = 'none';
		window.document.body.appendChild(printIframe)

		window.addEventListener('message', (event) => {
			if(event.data === 'print-iframe-ready') {
				printIframe.focus()
				printIframe.contentWindow?.print();
				this.printingReport = false;
				printIframe.remove();
				this.showReportOptionsModal = false;
			}
		});
	}

	toggleReportPaymentMethod(paymentMethodUid: VisualScopedUuid<UuidScopePayment_method>) {
		const index = this.reportExcludedPaymentMethods.indexOf(paymentMethodUid);
		if(index === -1) {
			this.reportExcludedPaymentMethods.push(paymentMethodUid);
		} else {
			this.reportExcludedPaymentMethods.splice(index, 1);
		}
	}

	toggleCategory(categoryUid: Uuid) {
		const index = this.exportCategories.indexOf(categoryUid);
		if(index === -1) {
			this.exportCategories.push(categoryUid);
		} else {
			this.exportCategories.splice(index, 1);
		}
	}

	processExport(data: ChosenExport) {
		if(data.exportId === 'raw') {
			this.exportProfilesStatistics(data.format, data.exportZeroLines);
		} else if(data.exportId === 'perProfile') {
			this.exportProductsStatistics(data.format, data.exportZeroLines);
		} else if(data.exportId === 'perCategory') {
			this.showExportPerCetagoriesModal = true;
		}
	}

	initPrintReport() {
		this.showReportOptionsModal = true;
	}

	toggleExportModal() {
		this.showExportModal = true;
	}

	profileSort: {key: string, order: 'ASC'|'DESC'}|null = {key: 'quantity', order: 'DESC'};
	sortProfiles(key: string) {
		if(!this.profileSort || this.profileSort.key !== key) {
			this.profileSort = {key: key, order: 'ASC'};
		} else {
			if(this.profileSort.order === 'ASC') {
				this.profileSort.order = 'DESC';
			} else {
				this.profileSort = null;
			}
		}
	}

	get sortedProfiles() {
		return [...this.profiles].sort((p1, p2) => {
			if(!this.profileSort) return 0;
			if(this.profileSort.key === 'name') {
				if(this.profileSort.order === 'ASC') return p1.name.localeCompare(p2.name);
				else return p2.name.localeCompare(p1.name);
			} else if(this.profileSort.key === 'refills') {
				const p1Refills = this.aggregateProfileTotal[p1.uid] ? this.aggregateProfileTotal[p1.uid].totalWithTaxes.refills : 0;
				const p2Refills = this.aggregateProfileTotal[p2.uid] ? this.aggregateProfileTotal[p2.uid].totalWithTaxes.refills : 0;

				if(this.profileSort.order === 'ASC') return p1Refills > p2Refills ? 1 : -1;
				else return p1Refills > p2Refills ? -1 : 1;
			} else if(this.profileSort.key === 'sells') {
				const p1Sells = this.aggregateProfileTotal[p1.uid] ? this.aggregateProfileTotal[p1.uid].totalWithTaxes.sells : 0;
				const p2Sells = this.aggregateProfileTotal[p2.uid] ? this.aggregateProfileTotal[p2.uid].totalWithTaxes.sells : 0;

				if(this.profileSort.order === 'ASC') return p1Sells > p2Sells ? 1 : -1;
				else return p1Sells > p2Sells ? -1 : 1;
			}
			return 0;
		});
	}

	productSort: {key: string, order: 'ASC'|'DESC'}|null = {key: 'quantity', order: 'DESC'};
	sortProduct(key: string) {
		if(!this.productSort || this.productSort.key !== key) {
			this.productSort = {key: key, order: 'ASC'};
		} else {
			if(this.productSort.order === 'ASC') {
				this.productSort.order = 'DESC';
			} else {
				this.productSort = null;
			}
		}
	}

	get sortedProducts() {
		return [...this.products].sort((p1, p2) => {
			if(!this.productSort) return 0;
			if(this.productSort.key === 'name') {
				if(this.productSort.order === 'ASC') return p1.name.localeCompare(p2.name);
				else return p2.name.localeCompare(p1.name);
			} else if(this.productSort.key === 'refills') {
				const p1Refills = this.aggregateProductTotal[p1.id] ? this.aggregateProductTotal[p1.id].totalWithTaxes.refills : 0;
				const p2Refills = this.aggregateProductTotal[p2.id] ? this.aggregateProductTotal[p2.id].totalWithTaxes.refills : 0;

				if(this.productSort.order === 'ASC') return p1Refills > p2Refills ? 1 : -1;
				else return p1Refills > p2Refills ? -1 : 1;
			} else if(this.productSort.key === 'sells') {
				const p1Sells = this.aggregateProductTotal[p1.id] ? this.aggregateProductTotal[p1.id].totalWithTaxes.sells : 0;
				const p2Sells = this.aggregateProductTotal[p2.id] ? this.aggregateProductTotal[p2.id].totalWithTaxes.sells : 0;

				if(this.productSort.order === 'ASC') return p1Sells > p2Sells ? 1 : -1;
				else return p1Sells > p2Sells ? -1 : 1;
			} else if(this.productSort.key === 'quantity') {
				const p1Quantity = this.aggregateProductTotal[p1.id] ? this.aggregateProductTotal[p1.id].quantity : 0
				const p2Quantity = this.aggregateProductTotal[p2.id] ? this.aggregateProductTotal[p2.id].quantity : 0

				if(this.productSort.order === 'ASC') return p1Quantity > p2Quantity ? 1 : -1;
				else return p1Quantity > p2Quantity ? -1 : 1;
			}
			return 0;
		});
	}

	profilePaymentMethodsSort: {key: string, order: 'ASC'|'DESC'}|null = {key: 'refills', order: 'DESC'};
	sortProfilePaymentMethods(key: string) {
		if(!this.profilePaymentMethodsSort || this.profilePaymentMethodsSort.key !== key) {
			this.profilePaymentMethodsSort = {key: key, order: 'ASC'};
		} else {
			if(this.profilePaymentMethodsSort.order === 'ASC') {
				this.profilePaymentMethodsSort.order = 'DESC';
			} else {
				this.profilePaymentMethodsSort = null;
			}
		}
	}

	get sortedProfilePaymentMethods() {
		return [...this.paymentMethods].sort((p1, p2) => {
			if(!this.selectedProfile) return 0;
			if(!this.profilePaymentMethodsSort) return 0;
			if(this.profilePaymentMethodsSort.key === 'name') {
				if(this.profilePaymentMethodsSort.order === 'ASC') return p1.name.localeCompare(p2.name);
				else return p2.name.localeCompare(p1.name);
			} else if(this.profilePaymentMethodsSort.key === 'refills') {
				const p1Refills = this.aggregateProfileTotal[this.selectedProfile.uid] ? this.aggregateProfileTotal[this.selectedProfile.uid].perPaymentMethod[p1.uid].refills : 0;
				const p2Refills = this.aggregateProfileTotal[this.selectedProfile.uid] ? this.aggregateProfileTotal[this.selectedProfile.uid].perPaymentMethod[p2.uid].refills : 0;

				if(this.profilePaymentMethodsSort.order === 'ASC') return p1Refills > p2Refills ? 1 : -1;
				else return p1Refills > p2Refills ? -1 : 1;
			} else if(this.profilePaymentMethodsSort.key === 'sells') {
				const p1Sells = this.aggregateProfileTotal[this.selectedProfile.uid] ? this.aggregateProfileTotal[this.selectedProfile.uid].perPaymentMethod[p1.uid].sells : 0;
				const p2Sells = this.aggregateProfileTotal[this.selectedProfile.uid] ? this.aggregateProfileTotal[this.selectedProfile.uid].perPaymentMethod[p2.uid].sells : 0;

				if(this.profilePaymentMethodsSort.order === 'ASC') return p1Sells > p2Sells ? 1 : -1;
				else return p1Sells > p2Sells ? -1 : 1;
			}
			return 0;
		});
	}

	profileProductSort: {key: string, order: 'ASC'|'DESC'}|null = {key: 'quantity', order: 'DESC'};
	sortProfileProduct(key: string) {
		if(!this.profileProductSort || this.profileProductSort.key !== key) {
			this.profileProductSort = {key: key, order: 'ASC'};
		} else {
			if(this.profileProductSort.order === 'ASC') {
				this.profileProductSort.order = 'DESC';
			} else {
				this.profileProductSort = null;
			}
		}
	}

	get sortedProfileProducts() {
		return [...this.products].sort((p1, p2) => {
			if(!this.selectedProfile) return 0;
			if(!this.profileProductSort) return 0;
			if(this.profileProductSort.key === 'name') {
				if(this.profileProductSort.order === 'ASC') return p1.name.localeCompare(p2.name);
				else return p2.name.localeCompare(p1.name);
			} else if(this.profileProductSort.key === 'refills') {
				const p1Refills = this.aggregateProfileTotal[this.selectedProfile.uid] ? this.aggregateProfileTotal[this.selectedProfile.uid].perProduct[p1.id].refills : 0;
				const p2Refills = this.aggregateProfileTotal[this.selectedProfile.uid] ? this.aggregateProfileTotal[this.selectedProfile.uid].perProduct[p2.id].refills : 0;

				if(this.profileProductSort.order === 'ASC') return p1Refills > p2Refills ? 1 : -1;
				else return p1Refills > p2Refills ? -1 : 1;
			} else if(this.profileProductSort.key === 'sells') {
				const p1Sells = this.aggregateProfileTotal[this.selectedProfile.uid] ? this.aggregateProfileTotal[this.selectedProfile.uid].perProduct[p1.id].sells : 0;
				const p2Sells = this.aggregateProfileTotal[this.selectedProfile.uid] ? this.aggregateProfileTotal[this.selectedProfile.uid].perProduct[p2.id].sells : 0;

				if(this.profileProductSort.order === 'ASC') return p1Sells > p2Sells ? 1 : -1;
				else return p1Sells > p2Sells ? -1 : 1;
			} else if(this.profileProductSort.key === 'quantity') {
				const p1Quantity = this.aggregateProfileTotal[this.selectedProfile.uid] ? this.aggregateProfileTotal[this.selectedProfile.uid].perProduct[p1.id].quantity : 0;
				const p2Quantity = this.aggregateProfileTotal[this.selectedProfile.uid] ? this.aggregateProfileTotal[this.selectedProfile.uid].perProduct[p2.id].quantity : 0;

				if(this.profileProductSort.order === 'ASC') return p1Quantity > p2Quantity ? 1 : -1;
				else return p1Quantity > p2Quantity ? -1 : 1;
			}
			return 0;
		});
	}

	productProfileSort: {key: string, order: 'ASC'|'DESC'}|null = {key: 'quantity', order: 'DESC'};
	sortProductProfile(key: string) {
		if(!this.productProfileSort || this.productProfileSort.key !== key) {
			this.productProfileSort = {key: key, order: 'ASC'};
		} else {
			if(this.productProfileSort.order === 'ASC') {
				this.productProfileSort.order = 'DESC';
			} else {
				this.productProfileSort = null;
			}
		}
	}

	get sortedProductProfiles() {
		return [...this.profiles].sort((p1, p2) => {
			if(!this.selectedProduct) return 0;
			if(!this.productProfileSort) return 0;
			if(this.productProfileSort.key === 'name') {
				if(this.productProfileSort.order === 'ASC') return p1.name.localeCompare(p2.name);
				else return p2.name.localeCompare(p1.name);
			} else if(this.productProfileSort.key === 'refills') {
				const p1Refills = this.aggregateProductTotal[this.selectedProduct === 'none' ? 'none' : this.selectedProduct.id] ? this.aggregateProductTotal[this.selectedProduct === 'none' ? 'none' : this.selectedProduct.id].perProfile[p1.uid].refills : 0;
				const p2Refills = this.aggregateProductTotal[this.selectedProduct === 'none' ? 'none' : this.selectedProduct.id] ? this.aggregateProductTotal[this.selectedProduct === 'none' ? 'none' : this.selectedProduct.id].perProfile[p2.uid].refills : 0;

				if(this.productProfileSort.order === 'ASC') return p1Refills > p2Refills ? 1 : -1;
				else return p1Refills > p2Refills ? -1 : 1;
			} else if(this.productProfileSort.key === 'sells') {
				const p1Sells = this.aggregateProductTotal[this.selectedProduct === 'none' ? 'none' : this.selectedProduct.id] ? this.aggregateProductTotal[this.selectedProduct === 'none' ? 'none' : this.selectedProduct.id].perProfile[p1.uid].sells : 0;
				const p2Sells = this.aggregateProductTotal[this.selectedProduct === 'none' ? 'none' : this.selectedProduct.id] ? this.aggregateProductTotal[this.selectedProduct === 'none' ? 'none' : this.selectedProduct.id].perProfile[p2.uid].sells : 0;

				if(this.productProfileSort.order === 'ASC') return p1Sells > p2Sells ? 1 : -1;
				else return p1Sells > p2Sells ? -1 : 1;
			} else if(this.productProfileSort.key === 'quantity') {
				const p1Quantity = this.aggregateProductTotal[this.selectedProduct === 'none' ? 'none' : this.selectedProduct.id] ? this.aggregateProductTotal[this.selectedProduct === 'none' ? 'none' : this.selectedProduct.id].perProfile[p1.uid].quantity : 0;
				const p2Quantity = this.aggregateProductTotal[this.selectedProduct === 'none' ? 'none' : this.selectedProduct.id] ? this.aggregateProductTotal[this.selectedProduct === 'none' ? 'none' : this.selectedProduct.id].perProfile[p2.uid].quantity : 0;

				if(this.productProfileSort.order === 'ASC') return p1Quantity > p2Quantity ? 1 : -1;
				else return p1Quantity > p2Quantity ? -1 : 1;
			}
			return 0;
		});
	}
}