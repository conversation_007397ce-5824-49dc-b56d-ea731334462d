server {
	listen 80;
    client_max_body_size 2M;

    root /var/www/html/;
    access_log off;

    location /config.json{
     	root /var/www/;
        expires 10m;
    }

    location / {
        try_files ${DOLLAR}uri ${DOLLAR}uri/ /index.html;
        expires -1;
        add_header 'Cache-Control' 'no-store, no-cache, must-revalidate, proxy-revalidate, max-age=0';
    }

    location ~* \.(js|css|ttf|woff|woff2|eot|svg|png)${DOLLAR} {
        expires 365d;
    }
}
