
.profile-payment-methods-editor-component {

    .methods {
        display: grid;
        grid-template-columns: 1fr 1fr 1fr;
        grid-gap: 10px;

        .method {
            display: flex;
            align-items: center;
            justify-content: space-between;
            gap: 10px;
            padding: 10px;
            border-radius: 6px;
            border: 1px solid #D8D8D8;
            background: white;
            cursor: pointer;
        }
    }

    .dropzone {
        display: flex;
        flex-direction: column;
        gap: 15px;
        padding: 15px;
        border-radius: 8px;
        background: #D8D8D8;

        .empty {
            font-size: 13px;
            grid-column: 1 / 3;
        }

        &.final {

            &.hovered {
                background: #EEF2FF;
                border: 1px solid #4F46E5;
            }

            .method {
                &.draggable-mirror {
                    position: relative;
                    z-index: 9;
                }

                &.draggable-source--is-dragging {
                    background: #E2E2E2;
                    opacity: 0.5;
                    z-index: 0;
                }
            }
        }
    }


    .delete-zone {
        padding: 10px 15px;
        border-radius: 6px;
        font-size: 14px;
        color: black;
        border: 1px dashed black;
        display: none;

        &.displayed {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .draggable-source--is-dragging {
            display: none !important;
        }

        &.draggable-container--over {
            background: #f64e4e;
            color: white;
        }
    }
}