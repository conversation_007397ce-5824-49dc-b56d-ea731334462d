.right-modal-component {
    position: fixed;
    right: 25px;
    top: 25px;
    bottom: 25px;
    z-index: 999;
    width: max(373px, 30%) ;
    display: flex;
    flex-direction: column;
    gap: 10px;
    border-radius: 12px;
    background: white;
    // Avoid content small visual overflow
    padding: 1px;
    box-sizing: border-box;
    overflow: hidden;

    // 25px for margin
    transform: translateX(calc(100% + 25px)) scale(1);

    @media (max-width: 900px) {
        right: 12px;
        top: 12px;
        bottom: 12px;
        left: 12px;
        width: auto;
    }

    &:not(.performance-mode) {
        transition: transform .3s cubic-bezier(0.22, 1, 0.36, 1);

        &.stacked {
            transform: scale(0.9);
        }
    }

    &.opened {
        transform: none;
    }

    .fixed-bottom {
        position: absolute;
        bottom: 10px;
        left: 10px;
        right: 10px;
        display: flex;
        flex-direction: column;
        gap: 10px;
        background: white;

        .form-error {
            width: 100%;
            box-sizing: border-box;
        }

        .bottom.button {
            font-size: 15px;
            font-weight: 700;
            padding: 18px 20px;
            width: 100%;
            box-sizing: border-box;
        }
    }

}