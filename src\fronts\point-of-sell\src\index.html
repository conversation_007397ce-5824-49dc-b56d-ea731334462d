<!DOCTYPE html>
<html lang="fr" style="height: 100%;">
<head>
	<title> Caisse enregistreuse </title>
	<meta charset="UTF-8"/>
	<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no"/>
	<link href="//fonts.googleapis.com/css?family=Montserrat:thin,extra-light,light,100,200,300,400,500,600,700,800" rel="stylesheet" type="text/css">

    <style>
        #pre-loader {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            position: fixed;
            inset: 0;
            background: #F2F4F7;
            z-index: 2;

            .loading {
                display: flex;
                flex-direction: column;
                gap: 10px;
                align-items: center;
                justify-content: center;
                width: 100%;
                font-weight: 600;
                font-size: 13px;
            }

            .loader {
                position: relative;
                width: 32px;
                height: 32px;
                border-radius: 500rem;
                border: 0.2em solid rgba(0, 0, 0, 0.15);
            }

            .loader::after {
                position: absolute;
                content: '';
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                width: 32px;
                height: 32px;
                border-radius: 500rem;
                -webkit-animation: loader 0.6s infinite linear;
                animation: loader 0.6s infinite linear;
                border: 0.2em solid currentColor;
                color: #000;
                -webkit-box-shadow: 0 0 0 1px transparent;
                box-shadow: 0 0 0 1px transparent;
                border-left-color: transparent;
                border-right-color: transparent;
                border-bottom-color: transparent;
            }

        }
    </style>

</head>
<body style="height: 100%;">

<div id="pre-loader">
    <div class="loading">
        <div class="loader"></div>
        Chargement...
    </div>
</div>

<div id="oldRouterContainer"></div>
<div id="mainRouterContainer" style="height: 100%;"></div>

<script>
    if(typeof String.prototype.replaceAll === 'undefined')
        String.prototype.replaceAll = function(str1, str2, ignore){
            return this.replace(new RegExp(str1.replace(/([\/\,\!\\\^\$\{\}\[\]\(\)\.\*\+\?\|\<\>\-\&])/g,"\\$&"),(ignore?"gi":"g")),(typeof(str2)=="string")?str2.replace(/\$/g,"$$$$"):str2);
        };
</script>

<script type="module" src="./index.ts"></script>

<script id="config" type="application/json">
	"__INJECTED_CONFIG__"
</script>

</body>
</html>
