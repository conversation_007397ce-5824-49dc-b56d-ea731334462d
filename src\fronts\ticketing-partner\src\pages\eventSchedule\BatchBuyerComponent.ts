import { Component, Prop, Vue } from "vue-facing-decorator";
import { DropdownButtonComponent, DropdownButtonAction } from "@groupk/vue3-interface-sdk";
import {
    TicketBatchApiOut,
    TicketApiOut,
    CustomerApiOut,
    TemplateTicketApiOut,
    MetadataDescriptorApiOut,
    MetadataModel,
    MetadataTargetSpecific, UuidScopeCustomer_customer, UuidScopeProduct_templateTicket, UuidScopeMetadata_descriptor
} from "@groupk/mastodon-core";
import { 
    VisualScopedUuid, 
    UuidUtils
} from "@groupk/horizon2-core";
import TicketDropdownActionsComponent from "../../components/TicketDropdownActionsComponent/TicketDropdownActionsComponent.vue";
import { EstablishmentUrlBuilder } from "../../../../../shared/utils/EstablishmentUrlBuilder";

@Component({
    components: {
        'dropdown-button': DropdownButtonComponent,
        'ticket-dropdown-actions': TicketDropdownActionsComponent,
    },
    emits: ['orderDropdownClicked']
})
export default class BatchBuyerComponent extends Vue {
    @Prop({ required: true }) batch!: TicketBatchApiOut;
    @Prop({ required: true }) tickets!: TicketApiOut[];
    @Prop({ required: true }) customers!: CustomerApiOut[];
    @Prop({ required: true }) templateTickets!: TemplateTicketApiOut[];
    @Prop({ required: true }) metadataDescriptors!: MetadataDescriptorApiOut[];

    getBatchTicketsPerCustomer(batch: TicketBatchApiOut): { customer: CustomerApiOut|null, tickets: TicketApiOut[]}[] {
        const data: { customer: CustomerApiOut|null, tickets: TicketApiOut[]}[] = [];

        for(const ticket of this.tickets) {
            if(ticket.groupUid === batch.uid) {
                const existing = data.find((item) => (item.customer === null && ticket.ownerCustomerUid === null) || (item.customer && item.customer.uid === ticket.ownerCustomerUid));
                if(!existing) {
                    data.push({
                        customer: ticket.ownerCustomerUid ? this.requireCustomerWithUid(ticket.ownerCustomerUid) : null,
                        tickets: [ticket]
                    })
                } else {
                    existing.tickets.push(ticket);
                }
            }
        }

        return data;
    }

    getBatchActionsDropdownValues(): DropdownButtonAction[] {
        const actions: DropdownButtonAction[] = [{
            id: 'cancel-all',
            name: 'Annuler tout les billets',
            color: 'red',
            icon: 'fa-regular fa-trash-alt'
        }];

        const batchMetadataDescriptors = MetadataModel.applicableDescriptorOnTarget(this.metadataDescriptors, [
            {uid: MetadataTargetSpecific.TICKET_BATCH, isChildren: false}
        ]);

        for(const descriptor of batchMetadataDescriptors) {
            actions.unshift({
                id: descriptor.uid,
                name: 'Modifier ' + descriptor.descriptorTemplate.name,
                icon: 'fa-regular fa-pen-line'
            })
        }

        return actions;
    }

    requireCustomerWithUid(customerUid: VisualScopedUuid<UuidScopeCustomer_customer>): CustomerApiOut {
        const customer = this.customers.find((customer) => customer.uid === customerUid);
        if (!customer) throw new Error('missing_customer');
        return customer;
    }

    requireTemplateTicketWithUid(templateTicketUid: VisualScopedUuid<UuidScopeProduct_templateTicket>): TemplateTicketApiOut {
        const templateTicket = this.templateTickets.find((templateTicket) => templateTicket.uid === templateTicketUid);
        if (!templateTicket) throw new Error('missing_template_ticket');
        return templateTicket;
    }

    getCustomerUrl(customerUid: VisualScopedUuid<UuidScopeCustomer_customer>): string {
        return EstablishmentUrlBuilder.buildUrl('/customers?uid=' + UuidUtils.visualToUuid(customerUid));
    }

    requireMetadataDescriptorWithUid(descriptorUid: VisualScopedUuid<UuidScopeMetadata_descriptor>): MetadataDescriptorApiOut {
        const descriptor = this.metadataDescriptors.find((descriptor) => descriptor.uid === descriptorUid);
        if (!descriptor) throw new Error('missing_descriptor');
        return descriptor;
    }

    orderDropdownClicked(batch: TicketBatchApiOut, action: any): void {
        this.$emit('orderDropdownClicked', batch, action);
    }
}
