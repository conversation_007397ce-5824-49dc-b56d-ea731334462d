# Metadata Page Implementation Plan

## Overview
Create a new "metadata" page in mastodon-front project that displays a list of MetadataDescriptors using the FilterTableLayoutComponent pattern. This page will ONLY provide data visualization - no CRUD operations, forms, or editing capabilities.

## Current State Analysis
- ✅ Navigation entry already exists in sidebar (line 44 of sidebar.ts)
- ✅ MetadataRepository exists in shared/repositories 
- ❌ No metadata page exists yet
- ❌ No metadata routing defined

## Execution Plan

### Phase 1: Repository Usage Analysis
**File**: `/src/shared/repositories/MetadataRepository.ts`

**Current State**: Basic repository extending HttpAuthedRepository - this is correct
**Repository Pattern**: Uses `callContract()` method with contract-based API calls

**Usage Pattern** (following establishments page pattern):
```typescript
// In component:
@AutoWired(MetadataRepository) accessor metadataRepository!: MetadataRepository

// In mounted():
this.metadataDescriptors = (await this.metadataRepository.callContract('list', { establishmentUid }, {})).success();
```

**Key Insight**: Repository doesn't need custom methods - uses `callContract()` directly with the MetadataHttpContract methods available in @groupk/mastodon-core.

### Phase 2: Metadata Page Implementation
**Location**: `/src/fronts/mastodon-front/src/pages/metadata/`

**Files to Create**:
- `metadata.vue` - Template using FilterTableLayoutComponent  
- `metadata.ts` - Page component logic
- `metadata.scss` - Page-specific styles

**Page Structure Following Established Pattern**:
```typescript
@Component({
    components: {
        'filter-table-layout': FilterTableLayoutComponent
    }
})
export default class Metadata extends Vue {
    metadataDescriptors: MetadataDescriptorApiOut[] = []
    filteredMetadataDescriptors: MetadataDescriptorApiOut[] = []
    loading: boolean = false
    
    headerParameters: ContentHeaderParameters = {
        header: 'Métadonnées',
        subtitle: 'Liste des descripteurs de métadonnées',
        actions: [],
        hideSearch: false,
        searchPlaceholder: 'Rechercher un descripteur'
    }
    
    tableColumns: TableColumn[] = [
        { title: 'Identifiant', name: 'uid', displayed: false, mobileHidden: true },
        { title: 'Nom', name: 'name', displayed: true, mobileHidden: false },
        { title: 'Type', name: 'type', displayed: true, mobileHidden: false },
        { title: 'Requis', name: 'required', displayed: true, mobileHidden: true },
        { title: 'Description', name: 'description', displayed: true, mobileHidden: true }
    ]
    
    searchString: string = ''
    sort: { name: string, direction: 'asc'|'desc' } | null = null
    availableSorts: string[] = ['name', 'type', 'required']
    
    @AutoWired(MetadataRepository) accessor metadataRepository!: MetadataRepository
    @AutoWired(TableColumnsRepository) accessor tableColumnsRepository!: TableColumnsRepository
    @AutoWired(SidebarStateListener) accessor sidebarStateListener!: SidebarStateListener
    @AutoWired(Router) accessor router!: Router
    
    async mounted() {
        this.loading = true
        // Following establishments page pattern:
        const establishmentUid = this.router.currentPathParams?.establishmentUid
        this.metadataDescriptors = (await this.metadataRepository.callContract('list', { establishmentUid }, {})).success()
        this.filteredMetadataDescriptors = this.metadataDescriptors
        this.loading = false
    }
}
```

**Template Structure** (READ-ONLY data visualization):
```vue
<template>
    <div id="metadata-page" class="page">
        <filter-table-layout
            :header-parameters="headerParameters"
            :allowed-filters="{filters: [], sorts: availableSorts}"
            :table-columns="tableColumns"
            :filters="{}"
            :drawer-opened="false"
            @search="search($event)"
            @changed-column-preferences="saveColumnPreferences($event)"
            @sorted="sorted($event)"
        >
            <template v-slot:table-data>
                <div class="table-dimmer" v-if="loading">
                    <div class="loader-container">
                        <div class="loader"></div>
                    </div>
                </div>
                <tr class="table-no-data" v-else-if="metadataDescriptors.length === 0">
                    <td colspan="100%">Aucune donnée</td>
                </tr>
                <tr class="table-no-data" v-else-if="filteredMetadataDescriptors.length === 0">
                    <td colspan="100%">Aucun descripteur ne correspond à la recherche</td>
                </tr>
                <tr v-else v-for="metadata in filteredMetadataDescriptors" :key="metadata.uid">
                    <td v-for="column in tableColumns.filter(col => col.displayed)" :class="{'mobile-hidden': column.mobileHidden}">
                        <template v-if="column.name === 'uid'">{{ metadata.uid }}</template>
                        <template v-else-if="column.name === 'name'">{{ metadata.name }}</template>
                        <template v-else-if="column.name === 'type'">{{ metadata.type }}</template>
                        <template v-else-if="column.name === 'required'">
                            <i class="fa-regular fa-check" v-if="metadata.required"></i>
                            <i class="fa-regular fa-times" v-else></i>
                        </template>
                        <template v-else-if="column.name === 'description'">{{ metadata.description }}</template>
                    </td>
                </tr>
            </template>
        </filter-table-layout>
    </div>
</template>
```

### Phase 3: Routing Configuration
**File**: `/src/fronts/mastodon-front/src/index.ts`

**Implementation**: Add metadata route following the established pattern
```typescript
// Add this line around line 81 with other routes
router.addRoute({
    regex: new RegExp(/establishment\//.source + uuidRegex.source + /\/metadata/.source), 
    loader: () => import('./pages/metadata/metadata.vue').then((vue) => VueAuthedRouteFactory(vue.default, vueRouteOptions))
});
```

### Phase 4: Navigation Integration
**File**: `/src/fronts/mastodon-front/src/pages/sidebar/sidebar.ts`

**Current State**: Navigation entry already exists on line 44
**Status**: ✅ No changes needed - metadata navigation already configured

### Phase 5: Styling
**File**: `/src/fronts/mastodon-front/src/pages/metadata/metadata.scss`

**Implementation**: Follow existing design patterns from establishments page - minimal styling needed as FilterTableLayoutComponent handles most styling.

## Simple Implementation Summary

### Files to Create (3 files only):
1. `/src/fronts/mastodon-front/src/pages/metadata/metadata.vue` - Template with FilterTableLayoutComponent
2. `/src/fronts/mastodon-front/src/pages/metadata/metadata.ts` - Component logic following establishments pattern  
3. `/src/fronts/mastodon-front/src/pages/metadata/metadata.scss` - Basic page styling

### Files to Modify (1 file only):
1. `/src/fronts/mastodon-front/src/index.ts` - Add routing entry

### Key Implementation Notes:
- ✅ No repository modifications needed - use existing MetadataRepository with `callContract('list', { establishmentUid }, {})`
- ✅ No form components needed - READ-ONLY data visualization  
- ✅ No CRUD operations - just display the list
- ✅ Follow exact pattern from establishments page
- ✅ Use FilterTableLayoutComponent for all table functionality
- ✅ Navigation already configured in sidebar

### Estimated Time: 4-6 hours
Simple implementation focused only on data visualization using existing patterns and components.