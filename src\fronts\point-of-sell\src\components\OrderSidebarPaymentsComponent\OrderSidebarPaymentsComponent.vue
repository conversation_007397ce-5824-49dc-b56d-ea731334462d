<script lang="ts" src="./OrderSidebarPaymentsComponent.ts">
</script>

<style lang="sass">
@import './OrderSidebarPaymentsComponent.scss'
</style>

<template>
    <div class="order-sidebar-payments-component">
        <div class="quick-actions">
            <div class="action" @click="close()">
                <i class="fa-regular fa-arrow-left"></i>
                Retour
            </div>
        </div>

        <div class="hint">
            <span v-if="localOrder.order.payments.length === 0"> Aucun paiement pour cette commande </span>
            <span v-else> Paiements réalisés sur cette commande </span>
        </div>

        <div class="payments">
            <template v-for="payment in localOrder.order.payments">
                <div class="payment" v-if="!isRefundPayment(payment)">
                    <div class="top">
                        <div class="left">
                            <div class="amounts">
                                <div class="amount"> {{ $filters.Money(payment.amount) }} </div>
                                <div v-if="haveBeenOverpaid(payment)" class="overpaid">
                                    rendu {{ $filters.Money(haveBeenOverpaid(payment)) }}
                                </div>
                            </div>
                            <div class="method"> {{ getMethodWithUid(payment.method).name }} </div>
                        </div>
                        <div v-if="payment.status === 'ERROR'" class="status"> Échoué </div>
                        <div v-else-if="payment.status === 'PENDING'" class="status"> En cours </div>
                        <template v-else-if="!haveBeenRefund(payment)">
                            <div class="delete" @click="cancelPayment(payment);">
                                <i class="fa-regular fa-times"></i>
                            </div>

                            <dropdown-button
                                class="custom-dropdown-button"
                                icon="fa-regular fa-ellipsis-v"
                                button-class="order-actions"
                                :touch-compatible="true"
                                :icon-only="true"
                                alignment="RIGHT"
                                :actions="[{
                                    title: '',
                                    actions: [{
                                        id: 'print-note',
                                        name: 'Imprimer le justificatif',
                                        icon: 'fa-regular fa-receipt fa-fw'
                                    }]
                                }]"
                                @clicked="showKeypad = payment"
                            ></dropdown-button>
                        </template>
                        <div v-else class="status"> Remboursé </div>
                    </div>
                    <div class="bottom" v-if="payment.status === 'PENDING'">
                        <button class="white button" @click="manuallySetStatus(payment, OrderPaymentStatus.SUCCESS)"> Valider </button>
                        <button class="red button" @click="manuallySetStatus(payment, OrderPaymentStatus.ERROR)"> Annuler </button>
                    </div>
                </div>
            </template>

            <div class="keypad-container" v-if="showKeypad">
                <div class="keypad">
                    <div class="head">
                        <span class="title">
                            Nombre de repas
                        </span>

                        <div class="close" @click="showKeypad = null; partsAmount = 0;">
                            <i class="fa-regular fa-xmark"></i>
                        </div>
                    </div>

                    <keypad
                        :simple-mode="true"
                        @clicked-key="updatePartsAmount($event)"
                    >
                        <template v-slot:display>
                            <div class="selector">
                                <div class="minus" @click="partsAmount > 1 ? partsAmount-- : ''">
                                    <i class="fa-regular fa-minus"></i>
                                </div>

                                <span class="amount"> {{ partsAmount }} </span>

                                <div class="plus" @click="partsAmount++">
                                    <i class="fa-regular fa-plus"></i>
                                </div>
                            </div>
                        </template>
                    </keypad>

                    <button class="primary button" :class="{disabled: partsAmount < 1}" @click="printTicket(showKeypad); ">
                        Valider
                    </button>
                </div>
            </div>
        </div>
    </div>
</template>
