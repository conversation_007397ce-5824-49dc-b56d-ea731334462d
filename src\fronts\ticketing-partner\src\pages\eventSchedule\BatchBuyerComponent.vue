<script lang="ts" src="./BatchBuyerComponent.ts" />

<style lang="sass" scoped>
@import './BatchBuyerComponent.scss'
</style>

<template>
    <div class="batch-buyer-component">
        <div class="buyer">
            <div class="head">
                <div class="infos">
                    <span class="order-name">
                        Commande n°{{batch.uid.slice(-8)}}
                    </span>
                    <span class="tickets-quantity">
                        {{ getBatchTicketsPerCustomer(batch).reduce((acc, data) => acc + data.tickets.length, 0) }} billet{{ getBatchTicketsPerCustomer(batch).reduce((acc, data) => acc + data.tickets.length, 0) > 1 ? 's' : ''}}
                    </span>
                </div>

                <dropdown-button
                    button-class="grey button"
                    icon="fa-regular fa-ellipsis-vertical"
                    title=""
                    alignment="RIGHT"
                    @clicked="orderDropdownClicked(batch, $event)"
                    :actions="[{title: '', actions: getBatchActionsDropdownValues()}]"
                ></dropdown-button>
            </div>

            <div class="batch-metadata metadata" v-for="metadata of batch.metadataList" @click="orderDropdownClicked(batch, {
                id: metadata.descriptorUid,
                name: 'Modifier ',
                icon: 'fa-regular fa-pen-line'
            })">
                <div class="name">
                    {{ requireMetadataDescriptorWithUid(metadata.descriptorUid).descriptorTemplate.name }}
                    <i class="fa-regular fa-pen-line"></i>
                </div>
                <div class="value"> {{ $filters.metadataValue(metadata.value) }} </div>

            </div>

            <template v-for="data of getBatchTicketsPerCustomer(batch)">
                <a target="_blank" :href="getCustomerUrl(data.customer.uid)" class="top" v-if="data.customer">
                    <div class="profile-picture">
                        {{ (data.customer.firstname??'-')[0] }}{{ (data.customer.lastname??'-')[0] }}
                    </div>
                    <div class="data">
                        <span class="name">
                            {{ data.customer.firstname }}
                            {{ data.customer.lastname }}
                        </span>
                        <span class="email">
                            {{ data.customer.email }}
                        </span>

                        <!--                                <span class="email">-->
                        <!--                                    Commentaire de derezr erzzerzer zer zer-->
                        <!--                                </span>-->

                    </div>

                    <i class="fa-regular fa-arrow-up-right-from-square"></i>
                </a>

                <div v-else class="top">
                    <div class="data">
                        <span class="name">
                            Anonyme
                        </span>
                    </div>
                </div>

                <div class="purchases">
                    <div class="purchase" v-for="ticket of data.tickets">
                        <div class="data">
                            <span class="name"> {{ requireTemplateTicketWithUid(ticket.templateTicketUid).name }} </span>

                            <div class="red label" v-if="ticket.disabled">
                                Désactivé
                            </div>
                            <div class="green label" v-else-if="ticket.lastValidUsageEventDatetime">
                                Contrôlé
                            </div>
                            <div class="grey label" v-else>
                                Valide
                            </div>
                        </div>

                        <ticket-dropdown-actions
                            :ticket="ticket"
                        ></ticket-dropdown-actions>
                    </div>
                </div>
            </template>
        </div>
    </div>
</template>
