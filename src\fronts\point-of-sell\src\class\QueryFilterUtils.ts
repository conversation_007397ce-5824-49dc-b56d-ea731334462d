import {QueryFilter, QueryFilterGroup} from "@groupk/horizon2-core";
import {FilterParameters} from "@groupk/vue3-interface-sdk";
import {QueryFilterGroupClause} from "@groupk/horizon2-core";

export class QueryFilterUtils {
	static setFiltersKeepDisabled(existingFilters: QueryFilter | QueryFilterGroup| undefined, filtersToSet: QueryFilter | QueryFilterGroup | null, filterParameters: {
		[filterName: string]: FilterParameters
	}) {
		const disabledFilters: QueryFilter[] = [];
		if(existingFilters) {
			if ('name' in existingFilters) {
				if (filterParameters[existingFilters.name] && filterParameters[existingFilters.name].disabled) {
					disabledFilters.push(existingFilters);
				}
			} else {
				for (let filter of existingFilters.filters) {
					if ('name' in filter) {
						if (filterParameters[filter.name] && filterParameters[filter.name].disabled) {
							disabledFilters.push(filter);
						}
					}
				}
			}
		}

		if(filtersToSet) {
			if('name' in filtersToSet) {
				if(disabledFilters.length > 0) {
					return {
						group: QueryFilterGroupClause.AND,
						filters: disabledFilters.concat([filtersToSet])
					} satisfies QueryFilterGroup;
				} else {
					return filtersToSet;
				}
			} else {
				if(disabledFilters.length > 0) {
					return {
						group: QueryFilterGroupClause.AND,
						filters: (disabledFilters as (QueryFilter | QueryFilterGroup)[]).concat(filtersToSet.filters)
					} satisfies QueryFilterGroup;
				} else {
					return filtersToSet;
				}
			}
		} else {
			if(disabledFilters.length > 1) {
				return {
					group: QueryFilterGroupClause.AND,
					filters: disabledFilters
				} satisfies QueryFilterGroup;
			} else if(disabledFilters.length === 1) {
				return disabledFilters[0];
			} else {
				return undefined;
			}
		}
	}
}