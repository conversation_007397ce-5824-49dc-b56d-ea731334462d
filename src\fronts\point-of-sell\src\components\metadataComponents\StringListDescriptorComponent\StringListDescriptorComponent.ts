import {Component, Prop, Vue} from "vue-facing-decorator";
import {
    MetadataDescriptorTemplateStringListApiOut
} from "@groupk/mastodon-core";

@Component({
    emits: ['validated']
})
export default class StringListDescriptorComponent extends Vue {
    @Prop({required: true}) metadataDescriptorTemplate!: MetadataDescriptorTemplateStringListApiOut;
    @Prop({default: null}) defaultValue!: string[]|null;

    values: string[] = [];

    beforeMount() {
        if(this.defaultValue) this.values = this.defaultValue;
    }

    toggleValue(value: string) {
        if(this.metadataDescriptorTemplate.minimumValueCount === 1 && this.metadataDescriptorTemplate.maximumValueCount === 1) {
            this.values = [value];
        } else {
            const index = this.values.indexOf(value);
            if(index === -1) this.values.push(value);
            else this.values.splice(index, 1);
        }
    }

    validate() {
        this.$emit('validated', this.values);
    }
}