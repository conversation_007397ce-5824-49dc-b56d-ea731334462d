
:root {
  --primary-hover-color: #F2F2F2 !important;
  --primary-hover-text-color: black !important;
  --primary-color: #2d2d2d !important;
  --primary-text-color: white !important;
  --primary-button-hover-color: black !important;
}

html, body {
  height: 100%;
  margin: 0;
  padding: 0;
  overflow: hidden;
}

*:not(i, text) {
  font-family: 'Montserrat', sans-serif !important;
}

.page {
  overflow: hidden;
  height: 100%;
  width: 100%;
}

#mainRouterContainer {
  width: 100%;
  height: 100%;
}

// Computer
@media (min-width: 900px) {
  body, #mainRouterContainer {
    display: flex;
  }
}

// Mobile & Tablet
@media (max-width: 900px) {
  #mainRouterContainer .page {
    padding-top: 60px;
  }
}

.layout-main-content-right-panel {
  .close {
    display: none;
    margin-bottom: 20px;
    cursor: pointer;
    user-select: none;
  }

  @media screen and (max-width: 1400px) {
    .close {
      display: flex;
      gap: 10px;
      align-items: center;
    }
  }
}

.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 20px;
}