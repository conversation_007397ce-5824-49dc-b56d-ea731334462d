import {Component, Prop, Vue} from "vue-facing-decorator";
import {
    MetadataApiOut_type, MetadataBoolApiOut,
    MetadataDescriptorApiOut,
    MetadataDescriptorType, MetadataStringApiOut,
    ProductApiOut, uuidScopeMetadata_metadata
} from "@groupk/mastodon-core";
import {MetadataUtils} from "../../../../../../shared/utils/MetadataUtils";
import {PosState} from "../../../model/PosState";
import MetadataDescriptorFormComponent from "../MetadataDescriptorFormComponent/MetadataDescriptorFormComponent.vue";
import {UuidUtils, sinkNever} from "@groupk/horizon2-core";
import {
    MetadataStringListApiOut
} from "@groupk/mastodon-core";

@Component({
    components: {
        'metadata-descriptor-form': MetadataDescriptorFormComponent
    },
    emits: ['done', 'cancel']
})
export default class ProductRequiredMetadataListComponent extends Vue {
    @Prop({required: true}) product!: ProductApiOut;
    @Prop({required: true}) posState!: PosState;

    requiredMetadataDescriptorList: MetadataDescriptorApiOut[] = [];
    filledMetadataList: MetadataApiOut_type[] = [];

    fillingMetadataDescriptor!: MetadataDescriptorApiOut;

    beforeMount() {
        this.requiredMetadataDescriptorList = MetadataUtils.getRequiredMetadata(this.product, this.posState.metadataDescriptors);
        if(this.requiredMetadataDescriptorList.length === 0) throw new Error('product_has_no_required_metadata');
        this.fillNextMetadata();

    }

    fillNextMetadata() {
        if(this.requiredMetadataDescriptorList.length === 0) {
            this.$emit('done', this.filledMetadataList);
        } else {
            this.fillingMetadataDescriptor = this.requiredMetadataDescriptorList[0];
            this.requiredMetadataDescriptorList.splice(0, 1);
        }
    }

    validateFillingMetadata(value: any) {
        if(this.fillingMetadataDescriptor.descriptorTemplate.type === MetadataDescriptorType.STRING) {
            this.filledMetadataList.push(new MetadataStringApiOut({
                uid: UuidUtils.randomVisualScopedUUID(uuidScopeMetadata_metadata),
                descriptorUid: this.fillingMetadataDescriptor.uid,
                value: value as string,
                type: MetadataDescriptorType.STRING
            }));
        } else if(this.fillingMetadataDescriptor.descriptorTemplate.type === MetadataDescriptorType.BOOL) {
            this.filledMetadataList.push(new MetadataBoolApiOut({
                uid: UuidUtils.randomVisualScopedUUID(uuidScopeMetadata_metadata),
                descriptorUid: this.fillingMetadataDescriptor.uid,
                value: value as boolean,
                type: MetadataDescriptorType.BOOL
            }));
        } else if(this.fillingMetadataDescriptor.descriptorTemplate.type === MetadataDescriptorType.STRING_LIST) {
            this.filledMetadataList.push(new MetadataStringListApiOut({
                uid: UuidUtils.randomVisualScopedUUID(uuidScopeMetadata_metadata),
                descriptorUid: this.fillingMetadataDescriptor.uid,
                value: value as string[],
                type: MetadataDescriptorType.STRING_LIST
            }));
        } else {
            sinkNever(this.fillingMetadataDescriptor.descriptorTemplate);
        }

        this.fillNextMetadata();
    }

    cancel() {
        this.$emit('cancel');
    }
}