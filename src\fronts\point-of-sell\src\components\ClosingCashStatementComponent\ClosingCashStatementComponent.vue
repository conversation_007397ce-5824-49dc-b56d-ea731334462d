<script lang="ts" src="./ClosingCashStatementComponent.ts">
</script>

<style lang="sass" scoped>
@import './ClosingCashStatementComponent.scss'
</style>

<template>
    <div class="closing-cash-statement-component">
        <div v-if="state === 'LOADING'">
            <div class="loading-container">
                <div class="loader"></div>
            </div>
        </div>
        <div class="center-aligned" v-else-if="state === 'NETWORK'">
            <i class="fa-regular fa-wifi-slash"></i>
            <h1> La caisse est Hors Ligne</h1>
            <span>
                La caisse n'a pas accès à internet et la cloture du ticket Z est donc momentanément impossible.
                Assurez vous de connecter la caisse à internet puis réessayez.
            </span>

            <button class="big black button" @click="posState.showCashStatementModal = false"> Fermer </button>
        </div>
        <div class="center-aligned" v-else-if="state === 'ERROR'">
            <i class="fa-regular fa-wifi-slash"></i>
            <h1> Une erreur inconnue est survenue </h1>
            <span>
                Essayez de resynchroniser vos commandes puis retentez l'opération.
                <span class="highlighted"> Si cela ne fonctionne toujours pas contactez notre équipe technique </span>
                nous étudierons le problème au plus vite afin de finaliser cette opération.
            </span>

            <button class="big black button" @click="posState.showCashStatementModal = false"> Fermer </button>
        </div>
        <div class="center-aligned" v-else-if="state === 'ORDER_ERROR'">
            <i class="fa-regular fa-triangle-exclamation"></i>
            <h1> Une commande est en erreur </h1>
            <span>
                Essayez de resynchroniser vos commandes puis retentez l'opération.
                <span class="highlighted"> Si cela ne fonctionne toujours pas contactez notre équipe technique </span>
                nous étudierons le problème au plus vite afin de finaliser cette opération.
            </span>

            <button class="big black button" @click="posState.showCashStatementModal = false"> Fermer </button>
        </div>

        <div class="center-aligned" v-else-if="state === 'UNPAID'">
            <i class="fa-regular fa-magnifying-glass-dollar"></i>
            <h1> Commandes impayés </h1>
            <span>
                Certaines commandes restent impayés, il est nécessaire de régulariser
                toutes vos commandes avant de pouvoir fermer le ticket Z
            </span>

            <button class="big white button" @click="goToUnpaidOrders()">
                <i class="fa-regular fa-arrow-up-right-from-square"></i>
                Voir les commandes impayés
            </button>
        </div>

        <div class="center-aligned" v-else-if="state === 'TRANSFER_PENDING'">
            <i class="fa-regular fa-loader"></i>
            <h1> Transferts en attente </h1>
            <span>
                Certains transfers de commande restent en attente, il est nécessaire de débloquer
                toutes vos commandes avant de pouvoir fermer le ticket Z
            </span>

            <button class="big white button" @click="goToUnpaidOrders()">
                <i class="fa-regular fa-arrow-up-right-from-square"></i>
                Voir les commandes
            </button>
        </div>

        <div class="center-aligned" v-else-if="state === 'UNSYNC'">
            <i class="far fa-wifi-slash"></i>
            <h1> Commandes non synchronisées  </h1>
            <span>
                Certaines commandes n'on pas pu être synchronisées avec le serveur, assurez vous d'être connecté
                à internet avant de continuer
            </span>

            <button class="big white button" @click="manualOrdersReload()">
                <i class="fa-regular fa-arrows-rotate" :class="{'fa-spin': loadingOrders}"></i>
                Réessayer
            </button>
        </div>

        <div class="center-aligned" v-else-if="state === 'UNSYNC_WORK_CLOCK'">
            <i class="far fa-wifi-slash"></i>
            <h1> Pointages non synchronisés  </h1>
            <span>
                Certains pointages n'on pas pu être synchronisés avec le serveur, assurez vous d'être connecté
                à internet avant de continuer
            </span>

            <button class="big white button" @click="manualOrdersReload()">
                <i class="fa-regular fa-arrows-rotate" :class="{'fa-spin': loadingOrders}"></i>
                Réessayer
            </button>
        </div>

        <div class="center-aligned" v-else-if="state === 'AUTH'">
            <i class="far fa-wifi-slash"></i>
            <h1> Votre authentification a expiré </h1>
            <span>
               Pour fermer le ticket Z veuillez vous reconnecter à la caisse
            </span>

            <button class="big white button" @click="goToCas()"> Se reconnecter </button>
        </div>

        <div v-else-if="showPrintSettings">
            <print-settings></print-settings>
            <div class="buttons">
                <button class="big button" @click="showPrintSettings = false"> Sauvegarder </button>
            </div>
        </div>

        <div class="recap" v-else-if="state === 'READY'">
            <div class="title-group">
                <span class="title"> Tout est prêt ! </span>
                <span class="subtitle"> Récapitulatif des ventes pour le ticket Z </span>
            </div>

            <div class="key-numbers" v-if="posState.isDiningEnabled()">
                <div class="key-number">
                    <span class="type"> Nombre de commandes </span>
                    <span class="value"> {{ orders.length }} </span>
                </div>

                <div class="key-number">
                    <span class="type"> Nombre de couverts </span>
                    <span class="value"> {{ orders.reduce((acc, order) => acc + (order.diningExtra.guestCount ?? 0), 0) }} </span>
                </div>
            </div>

            <div class="table-scroll-wrapper">
                <table class="data-table">
                    <thead>
                    <tr>
                        <td> Vendeur </td>
                        <td v-for="method of posState.paymentMethods"> {{ method.name }} </td>
                        <td> Total </td>
                    </tr>
                    </thead>
                    <tbody>
                    <tr v-for="(data, sellerUid) in ordersStatistics.perSeller">
                        <td> {{ posState.getEstablishmentAccountWithUid(sellerUid).firstname }} </td>
                        <td v-for="method of posState.paymentMethods">
                            {{ $filters.Money(data.perPaymentMethod[method.uid] ? data.perPaymentMethod[method.uid].success : 0) }}
                        </td>
                        <td> {{ $filters.Money(data.withTaxes) }} </td>
                    </tr>
                    <tr>
                        <td> Total </td>
                        <td v-for="method of posState.paymentMethods">
                            {{ $filters.Money(ordersStatistics.perPaymentMethod[method.uid] ? ordersStatistics.perPaymentMethod[method.uid].success : 0) }}
                        </td>
                        <td> {{ $filters.Money(ordersStatistics.totals.withTaxes) }} </td>
                    </tr>
                    </tbody>
                </table>
            </div>

            <span class="see-canceled-button" @click="showCanceledPayments = !showCanceledPayments">
                Voir les paiements annulés
                <i class="fa-regular fa-chevron-down" v-if="!showCanceledPayments"></i>
                <i class="fa-regular fa-chevron-up" v-else></i>
            </span>

            <div class="table-scroll-wrapper" v-if="showCanceledPayments">
                <table class="data-table">
                    <thead>
                    <tr>
                        <td> Vendeur </td>
                        <td v-for="method of posState.paymentMethods"> {{ method.name }} </td>
                        <td> Total </td>
                    </tr>
                    </thead>
                    <tbody>
                    <tr v-for="(data, sellerUid) in ordersStatistics.perSeller">
                        <td> {{ posState.getEstablishmentAccountWithUid(sellerUid).firstname }} </td>
                        <td v-for="method of posState.paymentMethods">
                            {{ $filters.Money(data.perPaymentMethod[method.uid] ? data.perPaymentMethod[method.uid].canceled : 0) }}
                        </td>
                        <td> {{ $filters.Money(data.canceled) }} </td>
                    </tr>
                    <tr>
                        <td> Total </td>
                        <td v-for="method of posState.paymentMethods">
                            {{ $filters.Money(ordersStatistics.perPaymentMethod[method.uid] ? ordersStatistics.perPaymentMethod[method.uid].canceled : 0) }}
                        </td>
                        <td> {{ $filters.Money(ordersStatistics.totals.canceled) }} </td>
                    </tr>
                    </tbody>
                </table>
            </div>

            <div class="warning">
                Une fois le ticket Z fermé les commandes associées ne pourront plus être modifiées.
            </div>

            <div class="form-error message" v-if="error">
                <i class="fa-regular fa-circle-exclamation"></i>
                <div class="details">
                    <span class="title"> Impossible de fermer le ticket Z </span>
                    {{ error }}
                </div>
            </div>

            <div class="buttons">
                <button class="big white button" :class="{disabled: closing}" @click="posState.showCashStatementModal = false"> Annuler </button>
                <button class="big red button" :class="{loading: closing, disabled: closing}" @click="closeCashStatement()"> Fermer le ticket Z </button>
            </div>
        </div>

        <div class="center-aligned" v-else-if="state === 'CLOSED'">
            <i class="fa-regular fa-circle-check"></i>
            <h1> Ticket Z fermé </h1>
            <span>
                Le ticket Z a été fermé avec succès, vous pouvez dès à présent imprimer le ticket Z.
            </span>

            <button class="big black button" @click="printCashStatement()">
                <i class="fa-regular fa-receipt"></i>
                Imprimer le ticket Z
            </button>

            <button class="big tertiary button" @click="reload()">
                <i class="fa-regular fa-check"></i>
                Terminé
            </button>

        </div>
    </div>
</template>