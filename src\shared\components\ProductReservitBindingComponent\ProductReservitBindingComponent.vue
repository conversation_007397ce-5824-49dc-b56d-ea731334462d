<script lang="ts" src="./ProductReservitBindingComponent.ts" />

<style lang="sass">
@import './ProductReservitBindingComponent.scss'
</style>

<template>
    <div class="product-reservit-binding-component">
        <form-modal-or-drawer
            :state="opened"
            title="Relier les produits"
            subtitle="Reliez les produits de Vente By Weecop avec ceux de Reservit "
            :fixed-buttons="true"
            @close="close()"
        >
            <template v-slot:content>
                <div class="input-group">
                    <input type="text" v-model="search" placeholder="Rechercher un produit..."/>
                </div>

                <div v-if="loading" class="loading-container">
                    <div class="loader"></div>
                </div>

                <template v-else>
                    <div class="top">
                        <div></div>
                        <div class="pages" v-if="pagination">
                            <span>
                                {{ pagination.currentPage * pagination.resultsPerPage - pagination.resultsPerPage + 1 }}-{{ Math.min(pagination.currentPage * pagination.resultsPerPage, pagination.totalResults) }}
                                sur {{ pagination.totalResults }} <span v-if="pagination.estimateTotal"> (environ)</span>
                            </span>
                            <div class="small white button" :class="{disabled: pagination.currentPage === 1}" @click="previousPage()">
                                <i class="fa-regular fa-arrow-left" ></i>
                            </div>
                            <div class="small white button" :class="{disabled: pagination.currentPage === Math.ceil(pagination.totalResults / pagination.resultsPerPage)}" @click="nextPage()">
                                <i class="fa-regular fa-arrow-right"></i>
                            </div>
                        </div>
                    </div>

                    <table class="data-table">
                        <thead>
                        <tr>
                            <td> Produit </td>
                        </tr>
                        </thead>
                        <tbody>
                        <tr v-if="filteredProducts.length === 0">
                            <td colspan="100%"> Aucun résultat </td>
                        </tr>

                        <tr v-for="data of filteredProducts" :key="data.product.uid">
                            <td>
                                <div class="product-cell">
                                    <div class="product-name">
                                        <product-image v-if="data" class="image" :establishment-uid="appState.requireUrlEstablishmentUid()" :product="data.product"></product-image>
                                        {{ data.product.lastRevision.name }}
                                    </div>

                                    <div class="two-inputs">
                                        <div class="input-group">
                                            <dropdown
                                                :key="'vat-' + data.product.uid"
                                                placeholder="TVA"
                                                :values="getTaxValuesForProduct(data)"
                                                :default-selected="getVatNumberWithProduct(data.product)"
                                                @update="setVatBinding(data.product.uid, $event)"
                                            ></dropdown>
                                        </div>
                                        <div class="input-group">
                                            <dropdown
                                                :key="'reservit-' + data.product.uid"
                                                placeholder="Produit Reservit"
                                                :values="reservitProductDropdownValues"
                                                :default-selected="getReservitProductWithProduct(data.product)"
                                                @update="setProductBinding(data.product.uid, $event)"
                                            ></dropdown>
                                        </div>
                                    </div>
                                </div>
                            </td>
                        </tr>
                        </tbody>
                    </table>

                    <div class="pages" v-if="pagination">
                    <span>
                        {{ pagination.currentPage * pagination.resultsPerPage - pagination.resultsPerPage + 1 }}-{{ Math.min(pagination.currentPage * pagination.resultsPerPage, pagination.totalResults) }}
                        sur {{ pagination.totalResults }} <span v-if="pagination.estimateTotal"> (environ) </span>
                    </span>
                        <div class="small white button" :class="{disabled: pagination.currentPage === 1}" @click="previousPage()">
                            <i class="fa-regular fa-arrow-left" ></i>
                        </div>
                        <div class="small white button" :class="{disabled: pagination.currentPage === Math.ceil(pagination.totalResults / pagination.resultsPerPage)}" @click="nextPage()">
                            <i class="fa-regular fa-arrow-right"></i>
                        </div>
                    </div>
                </template>
            </template>

            <template v-slot:buttons>
                <div class="left">
                    <div class="shortcut">
                        <div class="key" :class="{down: lastKeyboardEvent?.ctrlKey}"> Ctrl </div>
                        <div class="key" :class="{down: lastKeyboardEvent?.shiftKey}"> Maj </div>
                        <div class="key" :class="{down: lastKeyboardEvent?.code === 'KeyV'}"> V </div>
                        <div class="title"> Coller les valeurs </div>
                    </div>
                </div>

                <button class="white button" @click="close()"> Annuler </button>
                <button class="button" @click="save()" :class="{loading: saving, disabled: saving}"> Sauvegarder </button>
            </template>
        </form-modal-or-drawer>

        <form-modal-or-drawer
            :state="showPasteModal"
            title="Liaison rapide"
            subtitle="Collez une liste de couple Nom produit, Nom compte comptable séparés par des entrées à la ligne"
            @close="showPasteModal = false"
        >
            <template v-slot:content>
                <textarea placeholder="Grande Frite, 501 - Nourriture&#10;Coca, 502 - Boisson&#10;..." v-model="pastedData"></textarea>

                <div class="form-error" v-if="pasteImportError">
                    <i class="fa-regular fa-circle-exclamation"></i>
                    <div class="details">
                        {{ pasteImportError }}
                    </div>
                </div>
            </template>

            <template v-slot:buttons>
                <button class="white button" @click="showPasteModal = false"> Annuler </button>
                <button class="button" @click="processPastedData()"> Valider </button>
            </template>
        </form-modal-or-drawer>
    </div>
</template>
