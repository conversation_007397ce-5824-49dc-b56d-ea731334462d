.accounts-settings-component {
    height: 100%;
    box-sizing: border-box;

    @media (min-width: 1400px) {
        .desktop-hidden {
            display: none;
        }
    }

    @media (max-width: 900px) {
       padding-top: 75px;
    }

    .main-content {
        padding: 20px var(--desktop-padding);

        @media (max-width: 900px) {
            padding: 20px var(--mobile-padding);
        }


        .title {
            font-size: 16px;
            font-weight: 500;
        }

        .account-name {
            display: flex;
            gap: 15px;
            align-items: center;

            .profile-picture {
                display: flex;
                align-items: center;
                justify-content: center;
                background: var(--secondary-hover-color);
                font-size: 12px;
                font-weight: 600;
                border-radius: 50%;
                height: 30px;
                width: 30px;
                text-transform: uppercase;
            }

            .data {
                display: flex;
                flex-direction: column;
                gap: 4px;

                .name {
                    font-weight: 600;
                    text-transform: capitalize;
                }

                .email {

                }
            }
        }
    }

    .password-area {
        display: flex;
        align-items: center;
        margin-top: 20px;
        gap: 20px;

        > div {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }

        .title {
            font-weight: 600;
        }

        .subtitle {
            font-size: 14px;
        }

        .password {
            font-weight: 500;
        }
    }

    .roles {
        display: flex;
        flex-direction: column;
        gap: 10px;

        .role {
            display: flex;
            flex-direction: column;
            gap: 8px;
            padding: 20px;
            border-radius: 8px;
            background: var(--secondary-hover-color);
            cursor: pointer;
            border: 1px solid transparent;

            &.selected {
                background: var(--primary-hover-color);
                border: 1px solid var(--primary-color);
            }

            .icon {
                display: flex;
                align-items: center;
                justify-content: center;
                height: 42px;
                width: 42px;
                background: white;
                border-radius: 6px;
                margin-bottom: 10px;
            }

            .top {
                display: flex;
                justify-content: space-between;

                i {
                    font-size: 18px;
                    color: var(--primary-color)
                }
            }

            .title {
                font-size: 16px;
                font-weight: bold;
            }

            .description {
                font-size: 14px;
            }

            .bottom {
                display: flex;
                justify-content: flex-end;
                margin-top: 5px;

                .tertiary:hover {
                    background: white;
                }
            }
        }
    }

    .title {
        font-weight: 600;
    }
    .permissions {
        display: grid;
        grid-template-columns: 1fr 1fr;
        grid-gap: 10px 20px;

        .permission {
            display: flex;
            gap: 10px;

            &.not-ok {
                opacity: 0.6;
            }

            i {
                margin-top: 3px;
            }

            .fa-check {
                color: green;
            }
        }
    }

    .copy-group .icon-group * {
        cursor: pointer;
    }
}