<script lang="ts" src="./ImportProductInCategoryComponent.ts">
</script>

<style lang="sass" scoped>
@use './ImportProductInCategoryComponent.scss' as *
</style>

<template>
    <form-modal-or-drawer
        :state="opened"
        :title="`Importer des produits dans : ${category.name}`"
        subtitle="Coller la liste de vos produits séparé d'un retour à la ligne"
        @close="close()"
    >
        <template v-slot:content>
            <textarea @input="importProducts($event,true)" rows="10" placeholder="Séparer les produits avec un retour à la ligne."></textarea>

            <template class="details" v-if="productMatchedToImport.length>0 && importProductsNameCount">
                <span class="details">
                    {{ productMatchedToImport.length }} produit{{ productMatchedToImport.length > 1 ? 's' : '' }}
                    trouvé{{ productMatchedToImport.length > 1 ? 's' : '' }}
                    sur {{ Object.keys(importProductsNameSplitted).length }}.&nbsp;

                    <span class="action" @click="showModalVerification=true">
                        Voir les détails
                    </span><br>
                </span>
                <span class="details">
                    Information supplémentaire : {{ importProductsNameCount - Object.keys(importProductsNameSplitted).length }} en doublon
                </span>
            </template>
            <span class="details" v-else-if="Object.keys(importProductsNameSplitted).length>0">
                Aucun produit trouvé
            </span>

            <div class="form-error" v-if="error">
                <i class="fa-solid fa-exclamation-circle"></i>
                <div class="details">
                    <span class="title"> Erreur </span>
                    <span class="description"> {{ error }} </span>
                </div>
            </div>
        </template>
        <template v-slot:buttons>
            <button type="button" class="white button" @click="close()">
                Quitter sans importer
            </button>
            <button type="button" class="button" :class="{loading: importing, disabled: importing}" @click="importProducts();">
                <i class="fa-regular fa-arrow-up-to-line"></i>
                Importer
            </button>
        </template>
    </form-modal-or-drawer>

    <modal-or-drawer :state="showModalVerification" @close="showModalVerification = false">
        <div class="header">
            <h2>Détail de l'import</h2>
        </div>

        <div class="content">
            <div class="table-scroll-wrapper">
                <table class="data-table">
                    <thead>
                    <tr>
                        <td> Texte importé </td>
                        <td> Produit trouvé </td>
                        <td> Prix </td>
                    </tr>
                    </thead>
                    <tbody>
                    <tr v-for="(value,key) of importProductsNameSplitted">
                        <td>{{ key }}</td>
                        <td>
                            <template v-if="value!==null">
                                {{ value.name }}
                            </template>
                            <template v-else>-</template>
                        </td>
                        <td>
                            <template v-if="value !== null && value.prices[0]">
                                {{ $filters.Money(value.prices[0]) }}
                            </template>
                            <template v-else>-</template>
                        </td>
                    </tr>
                    </tbody>
                </table>
            </div>
            <div class="buttons">
                <button class="black button" @click="showModalVerification=false"> Fermer</button>
            </div>
        </div>
    </modal-or-drawer>
</template>