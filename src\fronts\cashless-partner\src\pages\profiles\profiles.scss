#profiles-page {
    height: 100%;

    .layout {
        width: 100%;
    }

    .profiles {
        display: grid;
        grid-template-columns: 1fr 1fr 1fr;
        grid-gap: 20px;

        @media (max-width: 1900px) {
            grid-template-columns: 1fr 1fr;
        }

        @media (max-width: 1500px) {
            grid-template-columns: 1fr 1fr 1fr;
        }

        @media (max-width: 1300px) {
            grid-template-columns: 1fr 1fr;
        }

        @media (max-width: 900px) {
            grid-template-columns: 1fr;
        }

        .profile {
            display: flex;
            flex-direction: column;
            gap: 20px;
            padding: 20px;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            min-height: 120px;

            &.new {
                justify-content: center;
                align-items: center;
                gap: 10px;
                border: 1px dashed var(--border-color);
                cursor: pointer;

                &:hover {
                    background: var(--secondary-hover-color);
                }

                i {
                    font-size: 22px;
                    color: black
                }

                .add {
                    font-size: 14px;
                    color: black
                }

                &.disabled{
                    pointer-events: none;
                    opacity: 0.5;
                }
            }

            .top {
                display: flex;
                justify-content: space-between;
                align-items: center;
                //height: 40px;

                .title {
                    font-size: 16px;
                    font-weight: 600;
                }

                .empty {
                    font-size: 14px;
                    font-style: italic;
                }

                .employees {
                    display: flex;
                    align-items: center;

                    >div {
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        height: 28px;
                        width: 28px;
                        margin-left: -12px;
                        background-color: #e7e6e6;
                        border-radius: 50%;
                        border: 3px solid #ffffff;
                        font-size: 12px;
                        font-weight: 600;
                        text-transform: uppercase;
                    }
                }
            }

            .datas {
                display: flex;
                justify-content: center;
                align-items: center;

                .separator {
                    border-left: 1px solid var(--border-color);
                    height: 25px;
                }

                .data {
                    flex-grow: 2;
                    display: flex;
                    flex-direction: column;
                    gap: 4px;
                    align-items: center;

                    .amount {
                        font-size: 18px;
                        font-weight: 600;
                    }

                    .title {
                        font-size: 14px;
                        font-weight: 400;
                    }
                }
            }

            .buttons {
                display: flex;
                gap: 10px;
                margin-top: 0;

                .fluid {
                    flex-grow: 2;
                }

                .icon.button {
                    padding: 10px;
                }
            }
        }
    }

    .top-bar {
        display: flex;
        justify-content: flex-end;
        margin-bottom: 10px;
    }

    .button-group {
        border-radius: 8px;
        display: flex;
        border: 1px solid #E2E2E2;

        &.small {
            div {
                font-size: 12px;
                font-weight: 500;
                padding: 8px 15px;
            }
        }

        button {
            all: unset;
            padding: 10px 15px;
            border-right: 1px solid #E2E2E2;
            cursor: pointer;
            font-size: 14px;
            user-select: none;

            &:first-child {
                border-radius: 8px 0 0 8px;
            }

            &:last-child {
                border: none;
                border-radius: 0 8px 8px 0;
            }

            &:hover {
                background: var(--secondary-hover-color);
            }

            &.active {
                background: #F7F7F8;
                font-weight: 600;
            }
        }
    }

    td.name {
        font-weight: 500;
    }

    .table-scroll-wrapper {
        overflow: auto;

        .no-break {
            white-space: nowrap;
        }

        &.no-margin {
            margin-right: -40px;
            padding-right: 40px;
        }
    }
}
