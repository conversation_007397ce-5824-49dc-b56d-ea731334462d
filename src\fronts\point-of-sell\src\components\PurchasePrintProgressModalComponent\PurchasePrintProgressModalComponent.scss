.purchase-print-progress-modal-component {
    .modal {
        display: flex;
        flex-direction: column;
        gap: 20px;
        width: 450px;

        .head {
            display: flex;
            align-items: center;
            justify-content: space-between;

            .title {
                font-size: 18px;
                font-weight: bold;
            }
        }

        .progress-group {
            display: flex;
            flex-direction: column;
            gap: 10px;
            background: var(--secondary-hover-color);
            padding: 20px;
            border-radius: 8px;

            .printer-name {
                font-weight: 500;
            }

            .current-step {
                display: flex;
                align-items: center;
                justify-content: space-between;
                gap: 10px;

                .step {
                    font-weight: 600;
                }
            }

            .progress-bar {
                height: 6px;
                background: #D9D9D9;
                border-radius: 6px;
                width: 100%;

                .progress {
                    height: 6px;
                    border-radius: 6px;
                    background: var(--primary-color);
                    transition: width 1s ease-out;
                }
            }

            &.error {
                background: var(--error-color);
                color: white;

                 .progress-bar {
                     display: none;
                }

                .white.button {
                    border: 1px solid white;
                }
            }

        }
    }
}