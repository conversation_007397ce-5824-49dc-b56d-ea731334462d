# Repository Pattern Documentation

## Architecture Overview

The mastodon-fronts project implements a sophisticated repository pattern built on top of the Horizon2 framework, providing type-safe, authenticated API communication with robust offline capabilities. The architecture follows Domain-Driven Design principles with clear separation between API-based and local storage repositories.

### Repository Pattern Implementation Approach

The project uses a **Contract-First** approach where API interactions are defined through strongly-typed HTTP contracts from the `@groupk/mastodon-core` package. This ensures compile-time type safety and automatic API documentation generation.

### Project-specific Repository Conventions

- **Base Class Inheritance**: All HTTP repositories extend `HttpAuthedRepository`
- **Contract Binding**: Each repository is bound to a specific HTTP contract aggregate
- **Dependency Injection**: Uses `@AutoWired` decorator for automatic service resolution
- **Domain Organization**: Repositories are organized by business domain (Establishment, Product, Order, etc.)

### Dependency Injection and Service Integration

```typescript
// Repository injection pattern
@AutoWired(EstablishmentRepository) accessor establishmentRepository!: EstablishmentRepository;
@AutoWired(TransactionsRepository) accessor transactionsRepository!: TransactionsRepository;
@AutoWired(AuthStateModel) accessor authStateModel!: AuthStateModel;
```

The system uses Horizon2's dependency injection container with automatic service registration and singleton management.

### Data Flow Architecture and Patterns

```
Component → Repository → HTTP Contract → API Endpoint
                ↓
          Response Transform → Domain Models → Reactive State
```

## Repository Interface Reference

### Base Repository Interface Definition

#### HttpAuthedRepository

The primary base class for all authenticated HTTP repositories:

```typescript
export abstract class HttpAuthedRepository<ContractAggregate extends HttpRouteDescriptorAggregate> 
    extends AbstractHttpDescriptorRepository<ContractAggregate> {
    
    @AutoWired(AuthStateModel) accessor authStateModel!: AuthStateModel;
    
    constructor(
        contractAggregate: ContractAggregate, 
        options: Partial<AbstractHttpDescriptorRepositoryOptions> = DefaultAbstractHttpDescriptorRepositoryOptions
    ) {
        const mainConfig = GetInstance(MainConfig);
        super(mainConfig.configuration.mastodonApiEndpoint, contractAggregate, options);
    }
}
```

**Features:**
- **Automatic Authentication**: Integrates with `AuthStateModel` for token management
- **Configuration Integration**: Uses `MainConfig` for endpoint resolution
- **Type Safety**: Generic contract binding for compile-time validation
- **Error Handling**: Built-in HTTP error processing and transformation

#### EntityIndexeddbRepository

Base class for local storage repositories:

```typescript
export class LocalOrderRepository extends EntityIndexeddbRepository<LocalOrder> {
    protected static timestampIndexName = 'lastLocalUpdateDate';
    protected static syncedIndexName = 'synced';
    
    @AutoWired(AppBus) private accessor appBus!: AppBus;
    @AutoWired(PosState) private accessor posState!: PosState;
    
    constructor() {
        super(LocalOrder, 'uid', 'pos_orders', 'pos_orders', 1);
        this.addEntityBooleanIndex('synced');
    }
}
```

**Features:**
- **IndexedDB Integration**: Persistent local storage with indexing
- **Sync Tracking**: Boolean indexes for synchronization state
- **Event Integration**: Automatic event emission on data changes
- **Offline Capabilities**: Full CRUD operations without network connectivity

### Common Repository Contracts

| Repository | Contract | Purpose |
|------------|----------|---------|
| EstablishmentRepository | MastodonEstablishmentContractAggregate | Establishment management |
| TransactionsRepository | CashlessHttpTransactionContract | Transaction processing |
| ProductsRepository | CashlessHttpSimpleProductContract | Product catalog management |
| OrderRepository | ProductHttpOrderContract | Order lifecycle management |
| CustomerRepository | MastodonCustomerContractAggregate | Customer data management |
| AuthRepository | MastodonAuthContractAggregate | Authentication operations |

### Generic Type Parameters and Constraints

```typescript
// Contract constraint ensures type safety
interface Repository<TContract extends HttpRouteDescriptorAggregate> {
    callContract<TMethod extends keyof TContract>(
        method: TMethod,
        pathParams: TContract[TMethod]['pathParams'],
        body: TContract[TMethod]['body']
    ): Promise<HttpResponseWrap<TContract[TMethod]>>;
}
```

### Method Signatures and Return Types

#### Standard Repository Methods

```typescript
// Contract-based method calls
async callContract<TMethod extends keyof ContractAggregate>(
    method: TMethod,
    pathParams: ContractAggregate[TMethod]['pathParams'],
    body: ContractAggregate[TMethod]['body']
): Promise<HttpResponseWrap<ContractAggregate[TMethod]>>

// Response handling
response.success(): TSuccessType
response.error(): TErrorType
response.isSuccess(): boolean
response.isError(): boolean
response.isNetworkError(): boolean
response.isServerError(): boolean
```

## Implementation Guide

### How to Create New Repositories

#### 1. HTTP Repository Creation

```typescript
// 1. Import base class and contract
import { HttpAuthedRepository } from "./HttpAuthedRepository";
import { MyDomainContract } from "@groupk/mastodon-core";

// 2. Extend base class with contract binding
export class MyDomainRepository extends HttpAuthedRepository<typeof MyDomainContract> {
    constructor() {
        super(MyDomainContract);
    }
    
    // 3. Add domain-specific methods if needed
    async findByCustomCriteria(criteria: MyCriteria): Promise<MyEntity[]> {
        const response = await this.callContract('customSearch', 
            { establishmentUid: criteria.establishmentUid }, 
            criteria
        );
        return response.success();
    }
}
```

#### 2. Local Repository Creation

```typescript
import { EntityIndexeddbRepository } from "@groupk/horizon2-front";
import { MyLocalEntity } from "../model/MyLocalEntity";

export class LocalMyEntityRepository extends EntityIndexeddbRepository<MyLocalEntity> {
    constructor() {
        super(MyLocalEntity, 'uid', 'my_entities', 'my_entities_db', 1);
        this.addEntityBooleanIndex('synced');
        this.addEntityIndex('timestamp', 'lastModified');
    }
    
    async findUnsyncedItems(): Promise<MyLocalEntity[]> {
        const cursor = await this._findAllBooleanIndexCursor('synced', false);
        return await cursor.toArray();
    }
    
    async markAsSynced(uid: string): Promise<void> {
        const entity = await this.get(uid);
        if (entity) {
            entity.synced = true;
            await this.save(entity);
        }
    }
}
```

### Naming Conventions and File Organization

#### File Structure
```
src/
├── shared/
│   └── repositories/
│       ├── HttpAuthedRepository.ts          # Base HTTP repository
│       ├── EstablishmentRepository.ts       # Domain repositories
│       ├── TransactionsRepository.ts
│       └── [DomainName]Repository.ts
└── fronts/
    └── [frontend-name]/
        └── src/
            └── repositories/
                ├── Local[Entity]Repository.ts  # Local storage repos
                └── [Frontend]SpecificRepository.ts
```

#### Naming Conventions
- **HTTP Repositories**: `[DomainName]Repository.ts`
- **Local Repositories**: `Local[EntityName]Repository.ts`  
- **Frontend-specific**: `[Frontend][Domain]Repository.ts`
- **File Naming**: PascalCase with descriptive domain names

### Abstract Base Class Usage Patterns

```typescript
// Extending base functionality
export abstract class BaseEntityRepository<T extends Entity> extends HttpAuthedRepository<ContractType> {
    protected abstract entityType: new() => T;
    
    async findAll(establishmentUid: string): Promise<T[]> {
        const response = await this.callContract('list', { establishmentUid }, {});
        return response.success().map(data => hydrate(this.entityType, data));
    }
    
    async findById(id: string, establishmentUid: string): Promise<T | null> {
        const response = await this.callContract('get', { establishmentUid, id }, {});
        return response.isSuccess() ? hydrate(this.entityType, response.success()) : null;
    }
}
```

## CRUD Operations

### Standard CRUD Method Implementations

#### Create Operations
```typescript
// Create entity
async createProduct(establishmentUid: string, productData: ProductApiIn): Promise<ProductApiOut> {
    const response = await this.callContract('create', 
        { establishmentUid }, 
        productData
    );
    
    if (response.isError()) {
        throw new Error(translateResponseError(response, {
            'PRODUCT_NAME_ALREADY_EXISTS': 'Un produit avec ce nom existe déjà',
            'INVALID_CATEGORY': 'Catégorie invalide'
        }));
    }
    
    return response.success();
}
```

#### Read Operations
```typescript
// Search with filters
async searchTransactions(
    establishmentUid: string, 
    filters: TypedQuerySearch<typeof CashlessHttpTransactionContractSearchConfig>
): Promise<TransactionApiOut[]> {
    const response = await this.callContract('search', 
        { establishmentUid }, 
        filters
    );
    
    return response.success();
}

// Get single entity
async getTransaction(establishmentUid: string, transactionUid: string): Promise<TransactionApiOut> {
    const response = await this.callContract('get', 
        { establishmentUid, transactionUid }, 
        {}
    );
    
    return response.success();
}
```

#### Update Operations
```typescript
// Update entity
async updateProduct(
    establishmentUid: string, 
    productUid: string, 
    updates: Partial<ProductApiIn>
): Promise<ProductApiOut> {
    const response = await this.callContract('update', 
        { establishmentUid, productUid }, 
        updates
    );
    
    return response.success();
}
```

#### Delete Operations
```typescript
// Soft delete
async deleteProduct(establishmentUid: string, productUid: string): Promise<void> {
    const response = await this.callContract('delete', 
        { establishmentUid, productUid }, 
        {}
    );
    
    if (response.isError()) {
        throw new Error('Failed to delete product');
    }
}
```

### Query Building and Parameter Handling

#### Advanced Query Construction
```typescript
// Complex filter building
const searchFilters: TypedQuerySearch<typeof TransactionSearchConfig> = {
    filter: {
        group: QueryFilterGroupClause.AND,
        filters: [
            {
                name: 'amount',
                value: 10.0,
                operator: QueryOperator.GREATER_OR_EQUAL
            },
            {
                group: QueryFilterGroupClause.OR,
                filters: [
                    {
                        name: 'status',
                        value: TransactionStatus.COMPLETED,
                        operator: QueryOperator.EQUAL
                    },
                    {
                        name: 'status',
                        value: TransactionStatus.PENDING,
                        operator: QueryOperator.EQUAL
                    }
                ]
            }
        ]
    },
    sorts: [
        { name: 'creationDatetime', direction: 'desc' }
    ],
    elementsPerPage: 25,
    cursorAfter: lastTransactionUid
};
```

### Filtering and Search Patterns

#### Date Range Filtering
```typescript
const dateFilters = {
    filter: {
        group: QueryFilterGroupClause.AND,
        filters: [
            {
                name: 'creationDatetime',
                value: startDate.toISOString(),
                operator: QueryOperator.GREATER_OR_EQUAL
            },
            {
                name: 'creationDatetime', 
                value: endDate.toISOString(),
                operator: QueryOperator.LESS_OR_EQUAL
            }
        ]
    }
};
```

#### Text Search Implementation
```typescript
const textSearch = {
    filter: {
        group: QueryFilterGroupClause.OR,
        filters: [
            {
                name: 'customerName',
                value: searchTerm,
                operator: QueryOperator.LIKE
            },
            {
                name: 'chipVisualId',
                value: searchTerm,
                operator: QueryOperator.LIKE
            }
        ]
    }
};
```

### Pagination and Sorting Implementations

#### Cursor-based Pagination
```typescript
// Implementation with cursor pagination
async loadNextPage(currentCursor?: string): Promise<{ 
    items: TransactionApiOut[], 
    nextCursor?: string 
}> {
    const filters = {
        ...this.currentFilters,
        elementsPerPage: this.pageSize,
        ...(currentCursor && { cursorAfter: currentCursor })
    };
    
    const response = await this.transactionsRepository.callContract('search', 
        { establishmentUid: this.establishmentUid }, 
        filters
    );
    
    const items = response.success();
    const nextCursor = items.length === this.pageSize ? items[items.length - 1].uid : undefined;
    
    return { items, nextCursor };
}
```

#### Multi-column Sorting
```typescript
const sortedQuery = {
    sorts: [
        { name: 'creationDatetime', direction: 'desc' },
        { name: 'amount', direction: 'asc' },
        { name: 'uid', direction: 'asc' } // Stable sort fallback
    ]
};
```

### Bulk Operations and Batch Processing

#### Batch Updates
```typescript
async updateMultipleProducts(
    establishmentUid: string, 
    updates: Array<{ uid: string, data: Partial<ProductApiIn> }>
): Promise<ProductApiOut[]> {
    const results = await Promise.allSettled(
        updates.map(({ uid, data }) => 
            this.updateProduct(establishmentUid, uid, data)
        )
    );
    
    const errors = results
        .filter((result): result is PromiseRejectedResult => result.status === 'rejected')
        .map(result => result.reason);
    
    if (errors.length > 0) {
        console.warn('Some updates failed:', errors);
    }
    
    return results
        .filter((result): result is PromiseFulfilledResult<ProductApiOut> => result.status === 'fulfilled')
        .map(result => result.value);
}
```

## API Integration Patterns

### HTTP Client Configuration and Usage

#### Automatic Configuration Resolution
```typescript
// Configuration loaded from environment
export class MainConfig {
    configuration!: Configuration;
    
    async init(filepath: string = '/config.json') {
        // 1. Check for injected configuration
        const configScriptElement = document.getElementById('config');
        if (configScriptElement) {
            const content = configScriptElement.innerText.trim();
            if (content !== '"__INJECTED_CONFIG__"') {
                try {
                    this.configuration = hydrate(Configuration, JSON.parse(content));
                    return this.configuration;
                } catch (_e) {}
            }
        }
        
        // 2. Fallback to file-based configuration
        const response = await fetch(filepath);
        this.configuration = hydrate(Configuration, await response.json());
        return this.configuration;
    }
}

// Configuration entity with environment-specific settings
@EntityClass()
export class Configuration extends Entity {
    @StringField() mastodonApiEndpoint: string;
    @StringField() remoteWsEndpoint: string;
    @StringField() casFrontUrl: string;
    @StringField() gmapsKey: string;
    @BoolField() develop: boolean;
}
```

### Request/Response Interceptors

#### Automatic Response Transformation
```typescript
// Built-in hydration/dehydration
const response = await this.orderRepository.callContract('create', 
    { establishmentUid }, 
    new OrderApiIn({
        customerUid: customer.uid,
        items: orderItems.map(item => new OrderItemApiIn(item))
    })
);

// Response is automatically hydrated to typed objects
const createdOrder: OrderApiOut = response.success();
```

### Error Handling and Transformation

#### Centralized Error Translation
```typescript
// RepositoryExtensions.ts - Error translation utility
export function translateResponseError<ContractAggregate extends HttpRouteDescriptorAggregate, Contract extends string & keyof ContractAggregate>(
    response: HttpResponseWrap<ContractAggregate[Contract]>,
    translations: { [errorCode: string]: string | undefined }
): string | null {
    if (response.isSuccess()) {
        return null;
    } else if (response.isError()) {
        const error = response.error();
        
        if (response.responseCode === HttpRouteResponseCode.unauthorized) {
            return 'Vous devez être connecté pour effectuer cette action';
        } else if (response.responseCode === HttpRouteResponseCode.accessDenied) {
            return 'Vous n\'avez pas les droits pour effectuer cette action';
        } else if (response.responseCode === HttpRouteResponseCode.payloadTooLarge) {
            return 'Fichier trop volumineux';
        }
        
        // Custom error translation
        if (typeof error === 'string' && error in translations) {
            return translations[error] || error;
        }
        
        return JSON.stringify(error);
    } else if (response.isServerError()) {
        return 'Une erreur interne au serveur est survenue';
    } else if (response.isNetworkError()) {
        return 'Une erreur de réseau est survenue. Vérifiez votre connexion internet.';
    }
    
    return null;
}

// Usage in repository methods
async createProduct(data: ProductApiIn): Promise<ProductApiOut> {
    const response = await this.callContract('create', { establishmentUid }, data);
    
    if (response.isError()) {
        const errorMessage = translateResponseError(response, {
            'PRODUCT_NAME_ALREADY_EXISTS': 'Un produit avec ce nom existe déjà',
            'INVALID_CATEGORY': 'Catégorie invalide',
            'INSUFFICIENT_PERMISSIONS': 'Permissions insuffisantes'
        });
        throw new Error(errorMessage || 'Erreur inconnue');
    }
    
    return response.success();
}
```

### Authentication Token Management

#### Automatic Token Renewal
```typescript
export class AuthStateModel {
    private _authCache: AccountAuthTokenApiOut | EstablishmentAuthTokenApiOut | null = null;
    private static readonly SESSION_STORAGE_CACHE_KEY = 'mastodon_session_state';
    
    async renew(): Promise<boolean> {
        // Multi-tab coordination using cookies
        const lockCookie = 'mastodon_auth_renewal_lock';
        const lockValue = Date.now().toString();
        
        // Set lock with expiration
        document.cookie = `${lockCookie}=${lockValue}; max-age=30; path=/`;
        
        // Check if another tab is already renewing
        await new Promise(resolve => setTimeout(resolve, 100));
        const currentLock = this.getCookie(lockCookie);
        
        if (currentLock !== lockValue) {
            // Another tab is handling renewal
            return false;
        }
        
        try {
            const response = await this.authRepository.callContract('renew', {}, {});
            
            if (response.isSuccess()) {
                this._authCache = response.success();
                this.saveToSessionStorage();
                return true;
            }
        } catch (error) {
            console.error('Token renewal failed:', error);
        } finally {
            // Clear lock
            document.cookie = `${lockCookie}=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=/`;
        }
        
        return false;
    }
    
    private saveToSessionStorage(): void {
        if (this._authCache) {
            sessionStorage.setItem(
                AuthStateModel.SESSION_STORAGE_CACHE_KEY, 
                JSON.stringify(dehydrate(this._authCache))
            );
        }
    }
}
```

### Rate Limiting and Retry Logic

#### Built-in Resilience
```typescript
// Horizon2 framework provides built-in retry logic
const options: Partial<AbstractHttpDescriptorRepositoryOptions> = {
    retryCount: 3,
    retryDelay: 1000,
    timeout: 30000
};

export class ResilientRepository extends HttpAuthedRepository<typeof MyContract> {
    constructor() {
        super(MyContract, options);
    }
}
```

## Data Transformation

### Model to DTO Conversion Patterns

#### Hydration/Dehydration Pattern
```typescript
// Automatic conversion using Horizon2 entities
import { hydrate, dehydrate } from "@groupk/horizon2-core";

// Converting API response to domain model
const apiResponse = await repository.callContract('get', { id }, {});
const domainModel = hydrate(ProductEntity, apiResponse.success());

// Converting domain model to API input
const apiInput = dehydrate(domainModel);
await repository.callContract('update', { id }, apiInput);
```

#### Manual Transformation Patterns
```typescript
// Local repository with custom transformation
export class LocalOrderRepository extends EntityIndexeddbRepository<LocalOrder> {
    override formatObjectAfterFetch(object: any): LocalOrder {
        // Custom transformation after retrieval
        for (const discount of object.order.discounts) {
            discount.amount = Math.floor(discount.amount);
        }
        
        return super.formatObjectAfterFetch(object);
    }
    
    override formatObjectBeforeSave(object: LocalOrder): any {
        // Custom transformation before storage
        const serialized = super.formatObjectBeforeSave(object);
        serialized.lastLocalUpdateDate = new Date().getTime();
        return serialized;
    }
}
```

### Response Data Normalization

#### Consistent Data Structure
```typescript
// Response wrapper pattern
interface ApiResponse<T> {
    success(): T;
    error(): any;
    isSuccess(): boolean;
    isError(): boolean;
    responseCode: number;
}

// Normalized error structure
interface NormalizedError {
    code: string;
    message: string;
    details?: Record<string, any>;
}
```

### Validation and Sanitization Approaches

#### Entity-level Validation
```typescript
@EntityClass()
export class ProductApiIn extends Entity {
    @StringField({ minLength: 1, maxLength: 100 })
    name: string;
    
    @NumberField({ min: 0 })
    price: number;
    
    @BoolField()
    active: boolean;
    
    constructor(data: { name: string; price: number; active: boolean }) {
        super();
        this.name = data.name.trim();
        this.price = Math.round(data.price * 100) / 100; // Sanitize to 2 decimal places
        this.active = data.active;
    }
}
```

### Type-safe Data Mapping Strategies

#### Generic Mapping Utilities
```typescript
// Type-safe mapping function
function mapApiResponseToEntity<TApi, TEntity>(
    apiData: TApi,
    entityClass: new() => TEntity,
    mapper: (api: TApi) => Partial<TEntity>
): TEntity {
    const entity = hydrate(entityClass, mapper(apiData));
    return entity;
}

// Usage example
const productEntity = mapApiResponseToEntity(
    apiResponse.success(),
    ProductEntity,
    (api) => ({
        uid: api.uid,
        name: api.name,
        price: api.price,
        categoryUid: api.category?.uid
    })
);
```

## Service Layer Integration

### Repository Injection in Vue Components

#### Standard Injection Pattern
```typescript
import { Component, Vue } from "vue-facing-decorator";
import { AutoWired } from "@groupk/horizon2-core";
import { EstablishmentRepository } from "../../repositories/EstablishmentRepository";

@Component({
    components: {
        // Component registrations
    }
})
export default class MyComponent extends Vue {
    @AutoWired(EstablishmentRepository) accessor establishmentRepository!: EstablishmentRepository;
    @AutoWired(TransactionsRepository) accessor transactionsRepository!: TransactionsRepository;
    @AutoWired(AppState) accessor appState!: AppState;
    
    establishments: EstablishmentApiOut[] = [];
    loading = false;
    
    async mounted() {
        await this.loadEstablishments();
    }
    
    async loadEstablishments() {
        this.loading = true;
        try {
            const response = await this.establishmentRepository.callContract('list', {}, {});
            this.establishments = response.success();
        } catch (error) {
            console.error('Failed to load establishments:', error);
        } finally {
            this.loading = false;
        }
    }
}
```

### Composable Usage Patterns

#### Repository Composables
```typescript
// useEstablishments.ts - Repository composable
import { ref, computed } from 'vue';
import { AutoWired } from '@groupk/horizon2-core';
import { EstablishmentRepository } from '../repositories/EstablishmentRepository';

export function useEstablishments() {
    const repository = new EstablishmentRepository();
    const establishments = ref<EstablishmentApiOut[]>([]);
    const loading = ref(false);
    const error = ref<string | null>(null);
    
    const activeEstablishments = computed(() => 
        establishments.value.filter(e => e.active)
    );
    
    async function loadEstablishments() {
        loading.value = true;
        error.value = null;
        
        try {
            const response = await repository.callContract('list', {}, {});
            establishments.value = response.success();
        } catch (err) {
            error.value = err instanceof Error ? err.message : 'Unknown error';
        } finally {
            loading.value = false;
        }
    }
    
    return {
        establishments: readonly(establishments),
        activeEstablishments,
        loading: readonly(loading),
        error: readonly(error),
        loadEstablishments
    };
}
```

### State Management Integration

#### AppState Integration
```typescript
// Global application state
export class AppState {
    private _currentEstablishment: EstablishmentApiOut | null = null;
    private _currentUser: AccountApiOut | null = null;
    
    @AutoWired(EstablishmentRepository) accessor establishmentRepository!: EstablishmentRepository;
    
    get currentEstablishment(): EstablishmentApiOut | null {
        return this._currentEstablishment;
    }
    
    async setCurrentEstablishment(establishmentUid: string) {
        const response = await this.establishmentRepository.callContract('get', 
            { establishmentUid }, 
            {}
        );
        
        this._currentEstablishment = response.success();
        AppBus.emit('establishment-changed', this._currentEstablishment);
    }
    
    requireUrlEstablishmentUid(): string {
        const uid = this.getUrlEstablishmentUid();
        if (!uid) {
            throw new Error('Establishment UID is required');
        }
        return uid;
    }
}
```

### Reactive Data Patterns and Caching

#### Event-driven Updates
```typescript
// AppBus integration for reactive updates
export class LocalOrderRepository extends EntityIndexeddbRepository<LocalOrder> {
    @AutoWired(AppBus) private accessor appBus!: AppBus;
    
    async save(object: LocalOrder): Promise<void> {
        await super.save(object);
        // Emit event for reactive updates
        this.appBus.emit('orderSaved', object);
    }
    
    async delete(uid: string): Promise<void> {
        await super.delete(uid);
        this.appBus.emit('orderDeleted', uid);
    }
}

// Component listening to repository changes
export default class OrdersComponent extends Vue {
    @AutoWired(AppBus) accessor appBus!: AppBus;
    @AutoWired(LocalOrderRepository) accessor localOrderRepository!: LocalOrderRepository;
    
    orders: LocalOrder[] = [];
    
    mounted() {
        this.appBus.on('orderSaved', this.handleOrderSaved);
        this.appBus.on('orderDeleted', this.handleOrderDeleted);
    }
    
    beforeUnmount() {
        this.appBus.off('orderSaved', this.handleOrderSaved);
        this.appBus.off('orderDeleted', this.handleOrderDeleted);
    }
    
    handleOrderSaved(order: LocalOrder) {
        const index = this.orders.findIndex(o => o.uid === order.uid);
        if (index >= 0) {
            this.orders[index] = order;
        } else {
            this.orders.push(order);
        }
    }
    
    handleOrderDeleted(uid: string) {
        this.orders = this.orders.filter(o => o.uid !== uid);
    }
}
```

### Lifecycle Management and Cleanup Patterns

#### Repository Cleanup
```typescript
export default class ComponentWithRepositories extends Vue {
    private subscriptions: Array<() => void> = [];
    
    @AutoWired(WebSocketManager) accessor wsManager!: WebSocketManager;
    @AutoWired(TransactionsRepository) accessor transactionsRepository!: TransactionsRepository;
    
    mounted() {
        // Set up real-time subscriptions
        const unsubscribe = this.wsManager.subscribe('transactions', this.handleNewTransaction);
        this.subscriptions.push(unsubscribe);
    }
    
    beforeUnmount() {
        // Clean up all subscriptions
        this.subscriptions.forEach(unsub => unsub());
        this.subscriptions = [];
        
        // Clear any pending repository operations
        this.transactionsRepository.cancelPendingRequests?.();
    }
    
    handleNewTransaction(transaction: TransactionApiOut) {
        // Handle real-time updates
    }
}
```

## Error Handling & Resilience

### Standard Error Handling Patterns

#### Repository Error Handling
```typescript
export class RobustTransactionsRepository extends TransactionsRepository {
    async searchWithErrorHandling(
        establishmentUid: string,
        filters: TransactionSearchFilters
    ): Promise<{ transactions: TransactionApiOut[], error?: string }> {
        try {
            const response = await this.callContract('search', { establishmentUid }, filters);
            
            if (response.isError()) {
                const errorMessage = translateResponseError(response, {
                    'INVALID_FILTERS': 'Les filtres fournis sont invalides',
                    'ESTABLISHMENT_NOT_FOUND': 'Établissement introuvable',
                    'INSUFFICIENT_PERMISSIONS': 'Permissions insuffisantes'
                });
                
                return { 
                    transactions: [], 
                    error: errorMessage || 'Erreur lors de la recherche' 
                };
            }
            
            return { transactions: response.success() };
            
        } catch (networkError) {
            console.error('Network error in transaction search:', networkError);
            return { 
                transactions: [], 
                error: 'Erreur de connexion. Veuillez réessayer.' 
            };
        }
    }
}
```

### Network Failure Recovery Strategies

#### Offline Queue Pattern
```typescript
export class OfflineQueueRepository {
    private queue: Array<{ operation: string, data: any, timestamp: number }> = [];
    
    @AutoWired(NetworkStatusManager) accessor networkStatus!: NetworkStatusManager;
    
    async queueOperation(operation: string, data: any) {
        this.queue.push({
            operation,
            data,
            timestamp: Date.now()
        });
        
        // Try to process queue if online
        if (this.networkStatus.isOnline) {
            await this.processQueue();
        }
    }
    
    async processQueue() {
        while (this.queue.length > 0 && this.networkStatus.isOnline) {
            const item = this.queue.shift()!;
            
            try {
                await this.executeOperation(item.operation, item.data);
            } catch (error) {
                // Re-queue on failure
                this.queue.unshift(item);
                console.warn('Failed to process queued operation:', error);
                break;
            }
        }
    }
    
    private async executeOperation(operation: string, data: any) {
        switch (operation) {
            case 'CREATE_ORDER':
                return await this.orderRepository.callContract('create', data.pathParams, data.body);
            case 'UPDATE_TRANSACTION':
                return await this.transactionRepository.callContract('update', data.pathParams, data.body);
            default:
                throw new Error(`Unknown operation: ${operation}`);
        }
    }
}
```

### Validation Error Management

#### Form Validation Integration
```typescript
export class ValidationAwareRepository extends HttpAuthedRepository<any> {
    async createWithValidation<T>(
        pathParams: any,
        data: T,
        validationRules: ValidationRules<T>
    ): Promise<{ success?: any, errors?: ValidationErrors<T> }> {
        // Client-side validation
        const validationErrors = this.validateData(data, validationRules);
        if (Object.keys(validationErrors).length > 0) {
            return { errors: validationErrors };
        }
        
        // Server-side validation
        const response = await this.callContract('create', pathParams, data);
        
        if (response.isError()) {
            const error = response.error();
            
            // Handle validation errors from server
            if (error && typeof error === 'object' && 'validationErrors' in error) {
                return { errors: error.validationErrors };
            }
            
            throw new Error(translateResponseError(response, {}));
        }
        
        return { success: response.success() };
    }
    
    private validateData<T>(data: T, rules: ValidationRules<T>): ValidationErrors<T> {
        const errors: ValidationErrors<T> = {};
        
        for (const [field, rule] of Object.entries(rules)) {
            const value = data[field as keyof T];
            
            if (rule.required && (value === null || value === undefined || value === '')) {
                errors[field as keyof T] = 'Ce champ est requis';
            } else if (rule.minLength && typeof value === 'string' && value.length < rule.minLength) {
                errors[field as keyof T] = `Minimum ${rule.minLength} caractères`;
            }
            // Add more validation rules as needed
        }
        
        return errors;
    }
}
```

### Logging and Monitoring Integration

#### Structured Logging
```typescript
export class MonitoredRepository extends HttpAuthedRepository<any> {
    private logger = GetLogger('Repository');
    
    override async callContract<TMethod extends keyof ContractAggregate>(
        method: TMethod,
        pathParams: any,
        body: any
    ) {
        const startTime = performance.now();
        const operationId = this.generateOperationId();
        
        this.logger.info('Repository operation started', {
            operationId,
            method: method as string,
            pathParams,
            timestamp: new Date().toISOString()
        });
        
        try {
            const response = await super.callContract(method, pathParams, body);
            const duration = performance.now() - startTime;
            
            this.logger.info('Repository operation completed', {
                operationId,
                method: method as string,
                duration,
                success: response.isSuccess(),
                statusCode: response.responseCode
            });
            
            return response;
            
        } catch (error) {
            const duration = performance.now() - startTime;
            
            this.logger.error('Repository operation failed', {
                operationId,
                method: method as string,
                duration,
                error: error instanceof Error ? error.message : 'Unknown error',
                stack: error instanceof Error ? error.stack : undefined
            });
            
            throw error;
        }
    }
    
    private generateOperationId(): string {
        return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    }
}
```

## Testing Strategy

### Repository Mocking Patterns

The project primarily uses **integration testing** with real repositories rather than unit testing with mocks. However, for specific scenarios, here are mocking patterns:

#### Test Environment Repository
```typescript
// TestPaymentRepository for payment testing
export class TestPaymentRepository extends HttpAuthedRepository<typeof OnlinePaymentTestHttpContract> {
    constructor() {
        super(OnlinePaymentTestHttpContract);
    }
    
    async simulatePayment(
        amount: number,
        currency: string,
        shouldSucceed: boolean = true
    ): Promise<PaymentResult> {
        const response = await this.callContract('simulatePayment', {}, {
            amount,
            currency,
            forceFailure: !shouldSucceed
        });
        
        return response.success();
    }
}
```

#### Mock Repository for Unit Tests
```typescript
// Mock implementation for isolated testing
export class MockTransactionsRepository implements ITransactionsRepository {
    private transactions: TransactionApiOut[] = [];
    
    async callContract(method: string, pathParams: any, body: any): Promise<any> {
        switch (method) {
            case 'search':
                return {
                    success: () => this.transactions.filter(t => this.matchesFilters(t, body)),
                    isSuccess: () => true,
                    isError: () => false
                };
            case 'create':
                const newTransaction = { uid: this.generateUid(), ...body };
                this.transactions.push(newTransaction);
                return {
                    success: () => newTransaction,
                    isSuccess: () => true,
                    isError: () => false
                };
            default:
                throw new Error(`Mock not implemented for method: ${method}`);
        }
    }
    
    private matchesFilters(transaction: TransactionApiOut, filters: any): boolean {
        // Implement filter matching logic for testing
        return true;
    }
    
    private generateUid(): string {
        return `test-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    }
}
```

### Test Data Management

#### E2E Testing with Real Data
```typescript
// tests/ticketing-partner.spec.ts
import { test, expect } from '@playwright/test';
import { EventRepository } from '../src/shared/repositories/EventRepository';
import { MainConfig, Configuration } from '../src/shared/MainConfig';
import { hydrate, GetInstance } from '@groupk/horizon2-core';

test('Add ticket to an event', async ({ page }) => {
    // Set up real configuration for testing
    const setupApi = async () => {
        let mainConfig: MainConfig = GetInstance(MainConfig);
        
        const response = await fetch('http://*************:9857/config.json');
        mainConfig.configuration = hydrate(Configuration, await response.json());
        
        return mainConfig;
    };
    
    // Create test event using real repository
    const createTestEvent = async () => {
        const eventRepository = new EventRepository();
        
        return await eventRepository.callContract('create', 
            { establishmentUid: '0000652c-741f-8927-add0-c9dcd10dfb14' }, 
            new EventApiIn({
                name: 'Test Event for Automation',
                description: 'Automated test event',
                startingDate: new Date(Date.now() + 86400000), // Tomorrow
                endingDate: new Date(Date.now() + 172800000),  // Day after tomorrow
                location: 'Test Location'
            })
        );
    };
    
    await setupApi();
    const testEvent = await createTestEvent();
    
    // Continue with UI testing using the created event
    await page.goto(`/events/${testEvent.success().uid}`);
    // ... rest of the test
});
```

### Integration Testing Approaches

#### Repository Integration Tests
```typescript
// Integration test for repository behavior
describe('EstablishmentRepository Integration', () => {
    let repository: EstablishmentRepository;
    let testEstablishmentUid: string;
    
    beforeAll(async () => {
        // Set up test configuration
        const mainConfig = GetInstance(MainConfig);
        await mainConfig.init('/test-config.json');
        
        repository = new EstablishmentRepository();
    });
    
    beforeEach(async () => {
        // Create test establishment
        const response = await repository.callContract('create', {}, {
            name: 'Test Establishment',
            address: 'Test Address'
        });
        
        testEstablishmentUid = response.success().uid;
    });
    
    afterEach(async () => {
        // Clean up test data
        if (testEstablishmentUid) {
            await repository.callContract('delete', 
                { establishmentUid: testEstablishmentUid }, 
                {}
            );
        }
    });
    
    test('should create and retrieve establishment', async () => {
        const retrieved = await repository.callContract('get', 
            { establishmentUid: testEstablishmentUid }, 
            {}
        );
        
        expect(retrieved.isSuccess()).toBe(true);
        expect(retrieved.success().name).toBe('Test Establishment');
    });
    
    test('should update establishment name', async () => {
        const updated = await repository.callContract('update', 
            { establishmentUid: testEstablishmentUid }, 
            { name: 'Updated Name' }
        );
        
        expect(updated.isSuccess()).toBe(true);
        expect(updated.success().name).toBe('Updated Name');
    });
});
```

### Performance Testing Considerations

#### Load Testing Repository Operations
```typescript
// Performance testing utilities
export class RepositoryPerformanceTester {
    async measureRepositoryPerformance<T>(
        operation: () => Promise<T>,
        iterations: number = 100
    ): Promise<PerformanceMetrics> {
        const times: number[] = [];
        const errors: Error[] = [];
        
        for (let i = 0; i < iterations; i++) {
            const start = performance.now();
            
            try {
                await operation();
                times.push(performance.now() - start);
            } catch (error) {
                errors.push(error as Error);
            }
        }
        
        return {
            averageTime: times.reduce((a, b) => a + b, 0) / times.length,
            minTime: Math.min(...times),
            maxTime: Math.max(...times),
            successRate: (times.length / iterations) * 100,
            errorCount: errors.length,
            errors
        };
    }
}

// Usage example
const performanceTester = new RepositoryPerformanceTester();
const metrics = await performanceTester.measureRepositoryPerformance(
    () => transactionRepository.callContract('search', { establishmentUid }, {}),
    50
);

console.log(`Average response time: ${metrics.averageTime}ms`);
console.log(`Success rate: ${metrics.successRate}%`);
```

## Configuration Management

### Environment-specific Settings

#### Multi-environment Configuration
```typescript
// Development configuration
const developmentConfig: Configuration = {
    mastodonApiEndpoint: 'http://localhost:8080/api',
    remoteWsEndpoint: 'ws://localhost:8080/ws',
    casFrontUrl: 'http://localhost:9700',
    gmapsKey: 'development-key',
    gtagId: undefined,
    ticketingFrontUrl: 'http://localhost:9355',
    develop: true
};

// Production configuration  
const productionConfig: Configuration = {
    mastodonApiEndpoint: 'https://api.mastodon.com/api',
    remoteWsEndpoint: 'wss://api.mastodon.com/ws',
    casFrontUrl: 'https://auth.mastodon.com',
    gmapsKey: 'production-key',
    gtagId: 'GA-XXXXX-X',
    ticketingFrontUrl: 'https://tickets.mastodon.com',
    develop: false
};
```

### Base URL and Endpoint Configuration

#### Dynamic Endpoint Resolution
```typescript
export class EndpointBuilder {
    constructor(private config: Configuration) {}
    
    buildApiUrl(path: string): string {
        return `${this.config.mastodonApiEndpoint}${path}`;
    }
    
    buildWebSocketUrl(path: string): string {
        return `${this.config.remoteWsEndpoint}${path}`;
    }
    
    buildCasUrl(path: string): string {
        return `${this.config.casFrontUrl}${path}`;
    }
}

// Repository using dynamic endpoints
export class ConfigurableRepository extends HttpAuthedRepository<any> {
    private endpointBuilder: EndpointBuilder;
    
    constructor(contractAggregate: any) {
        super(contractAggregate);
        const config = GetInstance(MainConfig).configuration;
        this.endpointBuilder = new EndpointBuilder(config);
    }
    
    buildCustomEndpoint(path: string): string {
        return this.endpointBuilder.buildApiUrl(path);
    }
}
```

### Feature Flag Integration

#### Feature-driven Repository Behavior
```typescript
export class FeatureAwareRepository extends HttpAuthedRepository<any> {
    @AutoWired(FeatureFlags) accessor featureFlags!: FeatureFlags;
    
    async searchWithFeatures(filters: any): Promise<any> {
        // Use enhanced search if feature is enabled
        if (this.featureFlags.isEnabled('ENHANCED_SEARCH')) {
            return await this.callContract('enhancedSearch', {}, filters);
        }
        
        // Fallback to standard search
        return await this.callContract('search', {}, filters);
    }
    
    async createWithValidation(data: any): Promise<any> {
        // Skip validation in development mode
        if (this.config.develop && this.featureFlags.isEnabled('SKIP_VALIDATION')) {
            return await this.callContract('createDirect', {}, data);
        }
        
        return await this.callContract('create', {}, data);
    }
}
```

### Runtime Configuration Patterns

#### Hot Configuration Reload
```typescript
export class DynamicConfigRepository {
    private configCache = new Map<string, any>();
    private configWatchers = new Map<string, Array<(config: any) => void>>();
    
    async getConfig(key: string): Promise<any> {
        if (this.configCache.has(key)) {
            return this.configCache.get(key);
        }
        
        const config = await this.fetchConfig(key);
        this.configCache.set(key, config);
        return config;
    }
    
    watchConfig(key: string, callback: (config: any) => void): () => void {
        if (!this.configWatchers.has(key)) {
            this.configWatchers.set(key, []);
        }
        
        this.configWatchers.get(key)!.push(callback);
        
        // Return unsubscribe function
        return () => {
            const watchers = this.configWatchers.get(key) || [];
            const index = watchers.indexOf(callback);
            if (index >= 0) {
                watchers.splice(index, 1);
            }
        };
    }
    
    private async fetchConfig(key: string): Promise<any> {
        const response = await fetch(`/api/config/${key}`);
        return await response.json();
    }
    
    invalidateConfig(key: string): void {
        this.configCache.delete(key);
        const watchers = this.configWatchers.get(key) || [];
        watchers.forEach(callback => {
            this.getConfig(key).then(callback);
        });
    }
}
```

## Performance Optimization

### Caching Strategies and Implementation

#### Repository-level Caching
```typescript
export class CachedRepository extends HttpAuthedRepository<any> {
    private cache = new Map<string, { data: any, expiry: number }>();
    private readonly CACHE_TTL = 5 * 60 * 1000; // 5 minutes
    
    async getCached<T>(cacheKey: string, fetcher: () => Promise<T>): Promise<T> {
        const cached = this.cache.get(cacheKey);
        
        if (cached && cached.expiry > Date.now()) {
            return cached.data;
        }
        
        const data = await fetcher();
        this.cache.set(cacheKey, {
            data,
            expiry: Date.now() + this.CACHE_TTL
        });
        
        return data;
    }
    
    async getEstablishment(uid: string): Promise<EstablishmentApiOut> {
        return await this.getCached(
            `establishment:${uid}`,
            () => this.callContract('get', { establishmentUid: uid }, {}).then(r => r.success())
        );
    }
    
    invalidateCache(pattern?: string): void {
        if (pattern) {
            const regex = new RegExp(pattern);
            for (const key of this.cache.keys()) {
                if (regex.test(key)) {
                    this.cache.delete(key);
                }
            }
        } else {
            this.cache.clear();
        }
    }
}
```

### Request Deduplication Patterns

#### Preventing Duplicate Requests
```typescript
export class DeduplicatingRepository extends HttpAuthedRepository<any> {
    private pendingRequests = new Map<string, Promise<any>>();
    
    async callContractDeduped<TMethod extends keyof ContractAggregate>(
        method: TMethod,
        pathParams: any,
        body: any
    ): Promise<any> {
        const requestKey = this.generateRequestKey(method as string, pathParams, body);
        
        if (this.pendingRequests.has(requestKey)) {
            return await this.pendingRequests.get(requestKey)!;
        }
        
        const requestPromise = super.callContract(method, pathParams, body);
        this.pendingRequests.set(requestKey, requestPromise);
        
        try {
            const result = await requestPromise;
            return result;
        } finally {
            this.pendingRequests.delete(requestKey);
        }
    }
    
    private generateRequestKey(method: string, pathParams: any, body: any): string {
        return `${method}:${JSON.stringify(pathParams)}:${JSON.stringify(body)}`;
    }
}
```

### Lazy Loading and Pagination

#### Efficient Data Loading
```typescript
export class PaginatedRepository extends HttpAuthedRepository<any> {
    async loadPage<T>(
        method: string,
        pathParams: any,
        pageSize: number = 25,
        cursor?: string
    ): Promise<PaginatedResult<T>> {
        const filters = {
            elementsPerPage: pageSize,
            ...(cursor && { cursorAfter: cursor })
        };
        
        const response = await this.callContract(method, pathParams, filters);
        const items = response.success();
        
        return {
            items,
            nextCursor: items.length === pageSize ? items[items.length - 1].uid : undefined,
            hasMore: items.length === pageSize
        };
    }
    
    async *loadAllPages<T>(
        method: string,
        pathParams: any,
        pageSize: number = 25
    ): AsyncGenerator<T[], void, unknown> {
        let cursor: string | undefined;
        let hasMore = true;
        
        while (hasMore) {
            const page = await this.loadPage<T>(method, pathParams, pageSize, cursor);
            
            if (page.items.length > 0) {
                yield page.items;
            }
            
            cursor = page.nextCursor;
            hasMore = page.hasMore;
        }
    }
}

// Usage example
const repository = new PaginatedRepository();

// Load all transactions in chunks
for await (const transactions of repository.loadAllPages(
    'search', 
    { establishmentUid }, 
    50
)) {
    // Process each page of transactions
    await processTransactions(transactions);
}
```

### Memory Management Considerations

#### Repository Cleanup
```typescript
export class ManagedRepository extends HttpAuthedRepository<any> {
    private subscriptions: Array<() => void> = [];
    private cache = new Map<string, WeakRef<any>>();
    
    addSubscription(unsubscribe: () => void): void {
        this.subscriptions.push(unsubscribe);
    }
    
    setCachedEntity<T extends { uid: string }>(entity: T): void {
        this.cache.set(entity.uid, new WeakRef(entity));
    }
    
    getCachedEntity<T>(uid: string): T | undefined {
        const weakRef = this.cache.get(uid);
        if (weakRef) {
            const entity = weakRef.deref();
            if (entity) {
                return entity;
            } else {
                // Entity was garbage collected, remove weak reference
                this.cache.delete(uid);
            }
        }
        return undefined;
    }
    
    dispose(): void {
        // Clean up subscriptions
        this.subscriptions.forEach(unsub => unsub());
        this.subscriptions = [];
        
        // Clear cache
        this.cache.clear();
        
        // Cancel pending requests
        this.cancelPendingRequests?.();
    }
}
```

## Migration & Maintenance

### Version Compatibility Patterns

#### Backward Compatibility
```typescript
// Version-aware repository
export class VersionedRepository extends HttpAuthedRepository<any> {
    private readonly apiVersion: string;
    
    constructor(contractAggregate: any, apiVersion: string = 'v1') {
        super(contractAggregate);
        this.apiVersion = apiVersion;
    }
    
    override async callContract<TMethod extends keyof ContractAggregate>(
        method: TMethod,
        pathParams: any,
        body: any
    ) {
        // Add version header to all requests
        const options = {
            headers: {
                'API-Version': this.apiVersion,
                'Accept': `application/vnd.mastodon.${this.apiVersion}+json`
            }
        };
        
        return await super.callContract(method, pathParams, body);
    }
    
    async migrateToV2(): Promise<void> {
        // Migration logic for API version upgrade
        this.apiVersion = 'v2';
    }
}
```

### API Versioning Strategies

#### Multi-version Support
```typescript
// Version-specific repositories
export class EstablishmentRepositoryV1 extends HttpAuthedRepository<typeof EstablishmentContractV1> {
    constructor() {
        super(EstablishmentContractV1);
    }
}

export class EstablishmentRepositoryV2 extends HttpAuthedRepository<typeof EstablishmentContractV2> {
    constructor() {
        super(EstablishmentContractV2);
    }
    
    // V2-specific methods
    async getEstablishmentWithAnalytics(uid: string): Promise<EstablishmentWithAnalyticsApiOut> {
        const response = await this.callContract('getWithAnalytics', { establishmentUid: uid }, {});
        return response.success();
    }
}

// Version factory
export class RepositoryFactory {
    static createEstablishmentRepository(version: 'v1' | 'v2' = 'v2'): EstablishmentRepositoryV1 | EstablishmentRepositoryV2 {
        switch (version) {
            case 'v1':
                return new EstablishmentRepositoryV1();
            case 'v2':
                return new EstablishmentRepositoryV2();
            default:
                throw new Error(`Unsupported version: ${version}`);
        }
    }
}
```

### Deprecation and Upgrade Paths

#### Gradual Migration Pattern
```typescript
// Deprecated repository with migration path
export class LegacyOrderRepository extends HttpAuthedRepository<any> {
    constructor() {
        super(LegacyOrderContract);
        console.warn('LegacyOrderRepository is deprecated. Use OrderRepositoryV2 instead.');
    }
    
    async createOrder(data: LegacyOrderData): Promise<OrderApiOut> {
        // Convert legacy data format to new format
        const modernData = this.convertLegacyData(data);
        
        // Use new repository for actual operation
        const modernRepository = new OrderRepositoryV2();
        return await modernRepository.createOrder(modernData);
    }
    
    private convertLegacyData(legacy: LegacyOrderData): ModernOrderData {
        return {
            customerUid: legacy.customerId,
            items: legacy.orderItems.map(item => ({
                productUid: item.productId,
                quantity: item.qty,
                unitPrice: item.price
            })),
            metadata: {
                legacyOrderId: legacy.id,
                migrationTimestamp: new Date().toISOString()
            }
        };
    }
}
```

### Breaking Change Management

#### Change Detection and Handling
```typescript
// Breaking change handler
export class ResilientRepository extends HttpAuthedRepository<any> {
    async callContractWithFallback<TMethod extends keyof ContractAggregate>(
        method: TMethod,
        pathParams: any,
        body: any,
        fallbackMethod?: string
    ) {
        try {
            return await this.callContract(method, pathParams, body);
        } catch (error) {
            if (this.isBreakingChangeError(error) && fallbackMethod) {
                console.warn(`Method ${method as string} failed, trying fallback ${fallbackMethod}`);
                return await this.callContract(fallbackMethod as TMethod, pathParams, body);
            }
            throw error;
        }
    }
    
    private isBreakingChangeError(error: any): boolean {
        return error?.responseCode === 404 && 
               error?.message?.includes('method not found');
    }
}
```

## Examples & Recipes

### Basic Repository Implementation

```typescript
// Complete basic repository implementation
import { HttpAuthedRepository } from "./HttpAuthedRepository";
import { ProductContract } from "@groupk/mastodon-core";

export class ProductRepository extends HttpAuthedRepository<typeof ProductContract> {
    constructor() {
        super(ProductContract);
    }
    
    // Basic CRUD operations
    async createProduct(
        establishmentUid: string, 
        productData: ProductApiIn
    ): Promise<ProductApiOut> {
        const response = await this.callContract('create', 
            { establishmentUid }, 
            productData
        );
        
        if (response.isError()) {
            throw new Error(this.translateError(response));
        }
        
        return response.success();
    }
    
    async getProduct(
        establishmentUid: string, 
        productUid: string
    ): Promise<ProductApiOut> {
        const response = await this.callContract('get', 
            { establishmentUid, productUid }, 
            {}
        );
        
        return response.success();
    }
    
    async updateProduct(
        establishmentUid: string, 
        productUid: string, 
        updates: Partial<ProductApiIn>
    ): Promise<ProductApiOut> {
        const response = await this.callContract('update', 
            { establishmentUid, productUid }, 
            updates
        );
        
        return response.success();
    }
    
    async deleteProduct(
        establishmentUid: string, 
        productUid: string
    ): Promise<void> {
        await this.callContract('delete', 
            { establishmentUid, productUid }, 
            {}
        );
    }
    
    // Advanced query operations
    async searchProducts(
        establishmentUid: string,
        filters: ProductSearchFilters
    ): Promise<ProductApiOut[]> {
        const response = await this.callContract('search', 
            { establishmentUid }, 
            filters
        );
        
        return response.success();
    }
    
    private translateError(response: any): string {
        return translateResponseError(response, {
            'PRODUCT_NAME_EXISTS': 'Un produit avec ce nom existe déjà',
            'INVALID_CATEGORY': 'Catégorie invalide',
            'INSUFFICIENT_STOCK': 'Stock insuffisant'
        }) || 'Erreur inconnue';
    }
}
```

### Complex Query Examples

```typescript
// Advanced filtering and querying
export class AdvancedTransactionRepository extends TransactionsRepository {
    async getTransactionStatistics(
        establishmentUid: string,
        dateRange: { start: Date, end: Date },
        groupBy: 'day' | 'week' | 'month'
    ): Promise<TransactionStatistics[]> {
        const filters = {
            filter: {
                group: QueryFilterGroupClause.AND,
                filters: [
                    {
                        name: 'creationDatetime',
                        value: dateRange.start.toISOString(),
                        operator: QueryOperator.GREATER_OR_EQUAL
                    },
                    {
                        name: 'creationDatetime',
                        value: dateRange.end.toISOString(),
                        operator: QueryOperator.LESS_OR_EQUAL
                    },
                    {
                        name: 'status',
                        value: TransactionStatus.COMPLETED,
                        operator: QueryOperator.EQUAL
                    }
                ]
            },
            aggregations: {
                groupBy: groupBy,
                metrics: ['sum', 'count', 'avg']
            }
        };
        
        const response = await this.callContract('aggregate', 
            { establishmentUid }, 
            filters
        );
        
        return response.success();
    }
    
    async findSuspiciousTransactions(
        establishmentUid: string,
        threshold: number = 1000
    ): Promise<TransactionApiOut[]> {
        const filters = {
            filter: {
                group: QueryFilterGroupClause.OR,
                filters: [
                    {
                        name: 'amount',
                        value: threshold,
                        operator: QueryOperator.GREATER_OR_EQUAL
                    },
                    {
                        group: QueryFilterGroupClause.AND,
                        filters: [
                            {
                                name: 'creationDatetime',
                                value: new Date(Date.now() - 3600000).toISOString(), // Last hour
                                operator: QueryOperator.GREATER_OR_EQUAL
                            },
                            {
                                name: 'chipVisualId',
                                value: null,
                                operator: QueryOperator.EQUAL
                            }
                        ]
                    }
                ]
            },
            sorts: [
                { name: 'amount', direction: 'desc' },
                { name: 'creationDatetime', direction: 'desc' }
            ]
        };
        
        return (await this.callContract('search', { establishmentUid }, filters)).success();
    }
}
```

### Error Handling Scenarios

```typescript
// Comprehensive error handling examples
export class RobustOrderRepository extends OrderRepository {
    async createOrderWithRetry(
        establishmentUid: string,
        orderData: OrderApiIn,
        maxRetries: number = 3
    ): Promise<OrderApiOut> {
        let lastError: Error;
        
        for (let attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                const response = await this.callContract('create', 
                    { establishmentUid }, 
                    orderData
                );
                
                if (response.isSuccess()) {
                    return response.success();
                }
                
                // Handle specific errors
                const error = response.error();
                
                if (response.responseCode === HttpRouteResponseCode.conflict) {
                    // Conflict errors should not be retried
                    throw new Error(translateResponseError(response, {
                        'ORDER_ALREADY_EXISTS': 'Cette commande existe déjà',
                        'CUSTOMER_NOT_FOUND': 'Client introuvable'
                    }));
                }
                
                if (response.responseCode === HttpRouteResponseCode.badRequest) {
                    // Validation errors should not be retried
                    throw new Error(translateResponseError(response, {
                        'INVALID_ORDER_DATA': 'Données de commande invalides',
                        'PRODUCT_NOT_AVAILABLE': 'Produit indisponible'
                    }));
                }
                
                // For 5xx errors, prepare for retry
                lastError = new Error(`Server error (attempt ${attempt}/${maxRetries}): ${response.responseCode}`);
                
                if (attempt < maxRetries) {
                    // Exponential backoff
                    await this.delay(Math.pow(2, attempt) * 1000);
                }
                
            } catch (error) {
                lastError = error as Error;
                
                // Network errors can be retried
                if (error instanceof Error && error.message.includes('network')) {
                    if (attempt < maxRetries) {
                        await this.delay(Math.pow(2, attempt) * 1000);
                        continue;
                    }
                }
                
                // Other errors should not be retried
                throw error;
            }
        }
        
        throw lastError!;
    }
    
    private delay(ms: number): Promise<void> {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}
```

### Testing Examples

```typescript
// Complete testing example with real repositories
describe('ProductRepository Integration Tests', () => {
    let repository: ProductRepository;
    let testEstablishmentUid: string;
    let testProduct: ProductApiOut;
    
    beforeAll(async () => {
        // Initialize test environment
        const mainConfig = GetInstance(MainConfig);
        await mainConfig.init('/test-config.json');
        
        repository = new ProductRepository();
        testEstablishmentUid = 'test-establishment-uid';
    });
    
    beforeEach(async () => {
        // Create test product
        const productData = new ProductApiIn({
            name: 'Test Product',
            price: 9.99,
            categoryUid: 'test-category-uid',
            active: true
        });
        
        testProduct = await repository.createProduct(testEstablishmentUid, productData);
    });
    
    afterEach(async () => {
        // Clean up test data
        if (testProduct) {
            try {
                await repository.deleteProduct(testEstablishmentUid, testProduct.uid);
            } catch (error) {
                console.warn('Failed to clean up test product:', error);
            }
        }
    });
    
    test('should create product successfully', async () => {
        expect(testProduct).toBeDefined();
        expect(testProduct.name).toBe('Test Product');
        expect(testProduct.price).toBe(9.99);
    });
    
    test('should update product name', async () => {
        const updated = await repository.updateProduct(
            testEstablishmentUid,
            testProduct.uid,
            { name: 'Updated Product Name' }
        );
        
        expect(updated.name).toBe('Updated Product Name');
        expect(updated.price).toBe(9.99); // Unchanged
    });
    
    test('should search products by name', async () => {
        const searchResults = await repository.searchProducts(
            testEstablishmentUid,
            {
                filter: {
                    group: QueryFilterGroupClause.AND,
                    filters: [{
                        name: 'name',
                        value: 'Test Product',
                        operator: QueryOperator.LIKE
                    }]
                }
            }
        );
        
        expect(searchResults).toHaveLength(1);
        expect(searchResults[0].uid).toBe(testProduct.uid);
    });
    
    test('should handle validation errors gracefully', async () => {
        const invalidProduct = new ProductApiIn({
            name: '', // Invalid: empty name
            price: -1, // Invalid: negative price
            categoryUid: 'invalid-category',
            active: true
        });
        
        await expect(
            repository.createProduct(testEstablishmentUid, invalidProduct)
        ).rejects.toThrow();
    });
});
```

### Integration Patterns

```typescript
// Complete Vue component integration example
import { Component, Vue } from "vue-facing-decorator";
import { AutoWired } from "@groupk/horizon2-core";
import { ProductRepository } from "../../repositories/ProductRepository";
import { CategoryRepository } from "../../repositories/CategoryRepository";
import { AppState } from "../../AppState";

@Component({
    components: {
        'product-form': ProductFormComponent,
        'filter-table-layout': FilterTableLayoutComponent
    }
})
export default class ProductManagementPage extends Vue {
    @AutoWired(ProductRepository) accessor productRepository!: ProductRepository;
    @AutoWired(CategoryRepository) accessor categoryRepository!: CategoryRepository;
    @AutoWired(AppState) accessor appState!: AppState;
    
    products: ProductApiOut[] = [];
    categories: CategoryApiOut[] = [];
    selectedProduct: ProductApiOut | null = null;
    loading = false;
    showCreateModal = false;
    
    // Table configuration
    tableColumns: TableColumn[] = [
        { title: 'Nom', name: 'name', displayed: true, mobileHidden: false },
        { title: 'Prix', name: 'price', displayed: true, mobileHidden: false },
        { title: 'Catégorie', name: 'category', displayed: true, mobileHidden: true },
        { title: 'Statut', name: 'active', displayed: true, mobileHidden: true }
    ];
    
    filters = {};
    
    async mounted() {
        await this.loadInitialData();
    }
    
    private async loadInitialData() {
        this.loading = true;
        
        try {
            const establishmentUid = this.appState.requireUrlEstablishmentUid();
            
            // Load data in parallel
            const [productsResponse, categoriesResponse] = await Promise.all([
                this.productRepository.searchProducts(establishmentUid, {}),
                this.categoryRepository.callContract('list', { establishmentUid }, {})
            ]);
            
            this.products = productsResponse;
            this.categories = categoriesResponse.success();
            
        } catch (error) {
            console.error('Failed to load initial data:', error);
            this.$toast.error('Erreur lors du chargement des données');
        } finally {
            this.loading = false;
        }
    }
    
    async handleFiltersChanged(newFilters: any) {
        this.loading = true;
        
        try {
            const establishmentUid = this.appState.requireUrlEstablishmentUid();
            this.products = await this.productRepository.searchProducts(establishmentUid, newFilters);
            this.filters = newFilters;
        } catch (error) {
            console.error('Search failed:', error);
            this.$toast.error('Erreur lors de la recherche');
        } finally {
            this.loading = false;
        }
    }
    
    async createProduct(productData: ProductApiIn) {
        try {
            const establishmentUid = this.appState.requireUrlEstablishmentUid();
            const newProduct = await this.productRepository.createProduct(establishmentUid, productData);
            
            this.products.unshift(newProduct);
            this.showCreateModal = false;
            this.$toast.success('Produit créé avec succès');
            
        } catch (error) {
            console.error('Product creation failed:', error);
            this.$toast.error(error instanceof Error ? error.message : 'Erreur lors de la création');
        }
    }
    
    async updateProduct(productUid: string, updates: Partial<ProductApiIn>) {
        try {
            const establishmentUid = this.appState.requireUrlEstablishmentUid();
            const updatedProduct = await this.productRepository.updateProduct(
                establishmentUid, 
                productUid, 
                updates
            );
            
            const index = this.products.findIndex(p => p.uid === productUid);
            if (index >= 0) {
                this.products[index] = updatedProduct;
            }
            
            this.$toast.success('Produit mis à jour avec succès');
            
        } catch (error) {
            console.error('Product update failed:', error);
            this.$toast.error(error instanceof Error ? error.message : 'Erreur lors de la mise à jour');
        }
    }
    
    async deleteProduct(productUid: string) {
        if (!confirm('Êtes-vous sûr de vouloir supprimer ce produit ?')) {
            return;
        }
        
        try {
            const establishmentUid = this.appState.requireUrlEstablishmentUid();
            await this.productRepository.deleteProduct(establishmentUid, productUid);
            
            this.products = this.products.filter(p => p.uid !== productUid);
            this.selectedProduct = null;
            this.$toast.success('Produit supprimé avec succès');
            
        } catch (error) {
            console.error('Product deletion failed:', error);
            this.$toast.error(error instanceof Error ? error.message : 'Erreur lors de la suppression');
        }
    }
    
    selectProduct(product: ProductApiOut) {
        this.selectedProduct = this.selectedProduct?.uid === product.uid ? null : product;
    }
    
    getCategoryName(categoryUid: string): string {
        const category = this.categories.find(c => c.uid === categoryUid);
        return category?.name || 'Catégorie inconnue';
    }
}
```

This comprehensive documentation provides everything needed to understand, implement, and maintain repositories in the mastodon-fronts project. The patterns demonstrated are based on actual usage throughout the codebase and provide practical guidance for developers working with this architecture.

<function_calls>
<invoke name="TodoWrite">
<parameter name="todos">[{"id": "1", "content": "Discover all Repository classes and analyze architecture", "status": "completed", "priority": "high"}, {"id": "2", "content": "Analyze data access patterns and CRUD operations", "status": "completed", "priority": "high"}, {"id": "3", "content": "Document API integration patterns and HTTP client usage", "status": "completed", "priority": "high"}, {"id": "4", "content": "Extract TypeScript interfaces and type safety patterns", "status": "completed", "priority": "medium"}, {"id": "5", "content": "Analyze service integration and dependency injection", "status": "completed", "priority": "medium"}, {"id": "6", "content": "Document testing patterns and mock implementations", "status": "completed", "priority": "medium"}, {"id": "7", "content": "Analyze configuration and environment patterns", "status": "completed", "priority": "medium"}, {"id": "8", "content": "Generate comprehensive repository.md documentation", "status": "completed", "priority": "high"}]