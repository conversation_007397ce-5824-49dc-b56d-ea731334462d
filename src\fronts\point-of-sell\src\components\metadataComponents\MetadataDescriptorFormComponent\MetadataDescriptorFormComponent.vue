<script lang="ts" src="./MetadataDescriptorFormComponent.ts" />

<style lang="sass">
@import './MetadataDescriptorFormComponent'
</style>

<template>
    <div class="metadata-descriptor-form-component">
        <div class="modal">
            <div class="head">
                <span class="title"> {{ metadataDescriptor.descriptorTemplate.name }} </span>

                <div class="close" @click="close()" v-if="closable">
                    <i class="fa-regular fa-xmark"></i>
                </div>
            </div>

            <string-descriptor
                v-if="metadataDescriptor.descriptorTemplate instanceof MetadataDescriptorTemplateStringApiOut"
                :default-value="defaultValue"
                :metadata-descriptor-template="metadataDescriptor.descriptorTemplate"
                @validated="validated($event)"
            ></string-descriptor>
            <bool-descriptor
                v-if="metadataDescriptor.descriptorTemplate instanceof MetadataDescriptorTemplateBoolApiOut"
                :default-value="defaultValue"
                :metadata-descriptor-template="metadataDescriptor.descriptorTemplate"
                @validated="validated($event)"
            ></bool-descriptor>
            <string-list-descriptor
                v-if="metadataDescriptor.descriptorTemplate instanceof MetadataDescriptorTemplateStringListApiOut"
                :default-value="defaultValue"
                :metadata-descriptor-template="metadataDescriptor.descriptorTemplate"
                @validated="validated($event)"
            ></string-list-descriptor>
        </div>
    </div>
</template>