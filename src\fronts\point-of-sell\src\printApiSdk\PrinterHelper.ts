import PrinterLanguageConverter from "./Drivers/PrinterLanguageConverter";
import EvolisLanguageConverter from "./Drivers/EvolisLanguageConverter";
import ZplLanguageConverter from "./Drivers/ZplLanguageConverter";
import EscPosLanguageConverter from "./Drivers/EscPosLanguageConverter";
import {EnumUtils} from "@groupk/horizon2-core";

export enum PrinterLanguage {
	ZPL2 = "ZPL2",
	ESCPOS = "ESCPOS",
	EVOLIS = "EVOLIS",
}

export enum PrinterBrand {
	"BROTHER" = "BROTHER",
	"CITIZEN" = "CITIZEN",
	"EPSON" = "EPSON",
	"EVOLIS" = "EVOLIS",
	"TBD" = "TBD", //sunmi
	"SUNMI" = "SUNMI", //sunmi
	"SUMNI" = "SUMNI", //sunmi
	"RONGTA" = "RONG<PERSON>",
	"SNBC" = "SNBC",
}

type Size = {width: number | null; height: number | null};

export type PrinterDescriptor = {
	brand: string | null;
	model: string | null;
	acceptedLanguages: PrinterLanguage[];
	minSize: Size;
	maxSize: Size;
	density: {width: number; height: number};
};

const BrandsDefaultValues: {[brand in PrinterBrand]: {acceptedLanguages: PrinterLanguage[]; minSize: Size; maxSize: Size}} = {
	[PrinterBrand.BROTHER]: {acceptedLanguages: [PrinterLanguage.ESCPOS], minSize: {width: null, height: null}, maxSize: {width: null, height: null}},
	[PrinterBrand.EPSON]: {acceptedLanguages: [PrinterLanguage.ESCPOS], minSize: {width: null, height: null}, maxSize: {width: null, height: null}},
	[PrinterBrand.EVOLIS]: {acceptedLanguages: [PrinterLanguage.EVOLIS], minSize: {width: 648, height: 1016}, maxSize: {width: 648, height: 1016}},
	[PrinterBrand.TBD]: {acceptedLanguages: [PrinterLanguage.ESCPOS], minSize: {width: 400, height: null}, maxSize: {width: 400, height: null}},
	[PrinterBrand.SUNMI]: {acceptedLanguages: [PrinterLanguage.ESCPOS], minSize: {width: 400, height: null}, maxSize: {width: 400, height: -8}},
	[PrinterBrand.SUMNI]: {acceptedLanguages: [PrinterLanguage.ESCPOS], minSize: {width: 400, height: null}, maxSize: {width: 400, height: -8}},
	[PrinterBrand.SNBC]: {acceptedLanguages: [PrinterLanguage.ESCPOS], minSize: {width: 400, height: null}, maxSize: {width: 400, height: -8}},
	[PrinterBrand.CITIZEN]: {acceptedLanguages: [PrinterLanguage.ZPL2], minSize: {width: null, height: null}, maxSize: {width: null, height: null}},
	[PrinterBrand.RONGTA]: {acceptedLanguages: [PrinterLanguage.ESCPOS], minSize: {width: null, height: null}, maxSize: {width: null, height: null}},
};

const KnownPrinters: {[brand in PrinterBrand]?: {[product: string]: {acceptedLanguages: PrinterLanguage[]; minSize: Size; maxSize: Size}}} = {
	[PrinterBrand.EPSON]: {
		'tm-m30ii': {acceptedLanguages: [PrinterLanguage.ESCPOS], minSize: {width: null, height: null}, maxSize: {width: 540, height: -8}},
	}
}

let TBDMostCommonDensity = 8;

// paper size = 0.77 inches to 4.65 => 19.5mm to 118.0cm
// print size = 104.1mm
let CitizenLeftMargin = 2; //mm => due to motor
let CitizenMinWidthPerDensity = 18; //m
let CitizenMaxWidthPerDensity = 104; //m
let CitizenMostCommonDensity = 8;
BrandsDefaultValues[PrinterBrand.CITIZEN] = {acceptedLanguages: [PrinterLanguage.ZPL2], minSize: {width: null, height: null}, maxSize: {width: null, height: null}};

BrandsDefaultValues[PrinterBrand.RONGTA] = {acceptedLanguages: [PrinterLanguage.ESCPOS], minSize: {width: 400, height: null}, maxSize: {width: 400, height: -8}};
let RongtaMostCommonDensity = 8;

let SunmiMostCommonDensity = 8;
let EpsonMostCommonDensity = 8;

export interface PrinterConfig {
	loadedMediaWidth?: number;
	density?: number;
	loadedMediaType?: "continue" | "fanfold" | "blackmark" | "auto";
	openDrawer?: boolean;
	mediaTypeDetectionOffset?: number;
	autoPrint?: boolean;
}


export default class PrinterHelper {
	static getPrinterBrand(str: string) {
		if (str.startsWith("usb://")) {
			let splashSeparatorParts = str.substr("usb://".length).split("/");
			if (splashSeparatorParts.length !== 1) {
				return splashSeparatorParts[0];
			}
		} else if (str.split("&").length >= 2) {
			let strParts = str.split("&");
			return strParts[0];
		}
		return str;
	}

	static getBestDescriptorWithStr(str: string, printerRelayConfig?: PrinterConfig): PrinterDescriptor | null {
		let descriptor: PrinterDescriptor | null = null;

		if (str.startsWith("usb://")) {
			let splashSeparatorParts = str.substring("usb://".length).split("/");
			if (splashSeparatorParts.length !== 1) {
				let brandFromStr = splashSeparatorParts[0]!.trim().toLowerCase();
				let modelFromStr = splashSeparatorParts[splashSeparatorParts.length - 1]!.trim().toLowerCase();

				let possibleBrand: PrinterBrand | null = null;
				let foundModel: string | null = null;

				try{
					possibleBrand = EnumUtils.values(PrinterBrand).filter(b=>b.toLowerCase()===brandFromStr)[0] ?? null;
				}catch (e){}

				if (modelFromStr.length) {
					foundModel = modelFromStr;
				}

				if (foundModel && possibleBrand) {
					descriptor = this.getDescriptorForBrand(possibleBrand, {printerRelayConfig: printerRelayConfig});
					descriptor.model = possibleBrand;
				}
			}
		} else if (str.split("&").length >= 2) {
			let strParts = str.split("&");

			let possibleBrand: null | PrinterBrand = null;
			try{
				possibleBrand = EnumUtils.values(PrinterBrand).filter(b=>b.toLowerCase()===strParts[0])[0] ?? null;
			}catch (e){}

			if (possibleBrand) {
				descriptor = this.getDescriptorForBrand(possibleBrand, {printerRelayConfig: printerRelayConfig});
				for (let i = 1; i < strParts.length; ++i) {
					if (strParts[i]!.trim() !== "") {
						descriptor.model = strParts[i]!;
						break;
					}
				}
			}
		}

		return descriptor;
	}

	protected static doesBrandMatch(searchedBrand: string, existingBrand: string) {
		return searchedBrand.toLowerCase().indexOf(existingBrand.toLowerCase()) !== -1;
	}

	static getDescriptorForBrand(brand: PrinterBrand, options: {product?: string, printerRelayConfig?: PrinterConfig}): PrinterDescriptor {
		let maxSize = {...BrandsDefaultValues[brand].maxSize};
		let minSize = {...BrandsDefaultValues[brand].minSize};

		let density: {width: number; height: number} = {width: 1, height: 1};

		const knownPrinterBrand = KnownPrinters[brand];
		if(options.product && knownPrinterBrand && knownPrinterBrand[options.product.toLowerCase()]) {
			maxSize = knownPrinterBrand[options.product.toLowerCase()].maxSize
			minSize = knownPrinterBrand[options.product.toLowerCase()].minSize
		}

		if (brand === PrinterBrand.CITIZEN) {
			let printerDensity = options.printerRelayConfig?.density ?? CitizenMostCommonDensity;
			density.width = printerDensity;
			density.height = printerDensity;
			if (options.printerRelayConfig && options.printerRelayConfig?.loadedMediaWidth) {
				maxSize.width = (options.printerRelayConfig.loadedMediaWidth - CitizenLeftMargin) * printerDensity;
				minSize.width = maxSize.width;
			} else {
				maxSize.width = printerDensity * CitizenMaxWidthPerDensity;
				minSize.width = printerDensity * CitizenMinWidthPerDensity;
			}
		} else if (brand === PrinterBrand.TBD) {
			let printerDensity = options.printerRelayConfig?.density ?? TBDMostCommonDensity;
			density.width = printerDensity;
			density.height = printerDensity;
		} else if (brand === PrinterBrand.RONGTA) {
			let printerDensity = options.printerRelayConfig?.density ?? RongtaMostCommonDensity;
			density.width = printerDensity;
			density.height = printerDensity;
		} else if (brand === PrinterBrand.SUNMI || brand === PrinterBrand.SUMNI || brand === PrinterBrand.SNBC) {
			let printerDensity = options.printerRelayConfig?.density ?? SunmiMostCommonDensity;
			density.width = printerDensity;
			density.height = printerDensity;

			if (options.printerRelayConfig && options.printerRelayConfig?.loadedMediaWidth) {
				maxSize.width = options.printerRelayConfig.loadedMediaWidth * printerDensity;
				minSize.width = maxSize.width;
			}

		} else if (brand === PrinterBrand.EPSON || brand === PrinterBrand.BROTHER) {
			let printerDensity = options.printerRelayConfig?.density ?? EpsonMostCommonDensity;
			density.width = printerDensity;
			density.height = printerDensity;

			if (options.printerRelayConfig && options.printerRelayConfig?.loadedMediaWidth) {
				maxSize.width = options.printerRelayConfig.loadedMediaWidth * printerDensity;
				maxSize.height = -8;
				minSize.width = maxSize.width;
				minSize.height = -8;
			}
		}

		return {
			acceptedLanguages: BrandsDefaultValues[brand].acceptedLanguages,
			brand: brand,
			model: null,
			minSize: minSize,
			maxSize: maxSize,
			density: density,
		};
	}

	static getForPrinterLanguage(language: PrinterLanguage): PrinterLanguageConverter {
		if (language == PrinterLanguage.EVOLIS) {
			return new EvolisLanguageConverter();
		} else if (language == PrinterLanguage.ZPL2) {
			return new ZplLanguageConverter();
		} else if (language == PrinterLanguage.ESCPOS) {
			return new EscPosLanguageConverter();
		}

		throw new Error("unsupported_printer_language");
	}
}
