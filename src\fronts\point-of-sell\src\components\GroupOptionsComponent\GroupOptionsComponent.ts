import {Component, Prop, Vue, Watch} from "vue-facing-decorator";
import {
	MetadataApiOut_type,
	OrderExecutorModelPurchaseAddDescriptor,
	OrderExecutorModelPurchaseGroupItemAddDescriptor,
	ProductApiOut,
	ProductGroupApiOut,
	ProductGroupItemApiOut,
	ProductType,
	UuidScopeProductProductGroupItem
} from "@groupk/mastodon-core";
import {PosState} from "../../model/PosState";
import {VisualScopedUuid} from "@groupk/horizon2-core";
import ProductRequiredMetadataListComponent
	from "../metadataComponents/ProductRequiredMetadataListComponent/ProductRequiredMetadataListComponent.vue";
import {MetadataUtils} from "../../../../../shared/utils/MetadataUtils";
import {StackingContext} from "./GroupOptionWithCartComponent";

@Component({
	components: {
		'product-required-metadata-list': ProductRequiredMetadataListComponent,
	},
	emits: ['select-product', 'configured-group', 'cancel']
})
export default class GroupOptionsComponent extends Vue {
	@Prop() posState!: PosState;
	@Prop() product!: ProductApiOut;
	@Prop() stackingContext!: StackingContext[];
	@Prop({default: null}) item!: ProductGroupItemApiOut|null;
	@Prop() descriptor!: OrderExecutorModelPurchaseGroupItemAddDescriptor|OrderExecutorModelPurchaseAddDescriptor;
	@Prop({default: null}) firstNonMenuChild!: ProductApiOut|null;

	addingProductWithRequiredMetadata: {item: ProductGroupItemApiOut, product: ProductApiOut}|null = null;

	currentGroup: number = 0;

	@Watch('descriptor', { deep: true })
	descriptorWatch() {
		let index = 0;
		for(let group of this.product.lastRevision.groups) {
			if(!this.isGroupCorrect(group)) {
				console.log('incorrect', group);
				this.currentGroup = index;
			}
			index++;
		}
	}

	selectProduct(item: ProductGroupItemApiOut, product: ProductApiOut, requiredMetadataList: MetadataApiOut_type[]|null = null) {
		this.addingProductWithRequiredMetadata = null;

		let dining: {
			step: number,
		}|undefined = undefined;

		const firstNonMenuChild = this.getCurrentFirstNonMenuChild(product);
		console.log(this.getCurrentSelectionPath(product));
		console.log(firstNonMenuChild?.uid);
		console.log(product.uid);

		// If product is a menu, we only add first non menu child to step,
		// Exemple Menu double > Menu Burger > Burger Steak > Sauce Poivre
		// On ajoute Burger Steak à l'étape
		// If it is a customizable product, see comment in GroupOptionWithCartComponent
		if(firstNonMenuChild && firstNonMenuChild.uid === product.uid) {
			dining = {
				step: this.posState.currentOrder?.currentStep ?? 0,
			}
		}

		if(product.lastRevision.groups.length > 0) {
			this.$emit('select-product', {item: item, product: product, group: this.product.lastRevision.groups[this.currentGroup], dining});
		} else {
			if(!this.descriptor.groups) throw new Error('missing_group');

			const productRequiredMetadata = MetadataUtils.getRequiredMetadata(product, this.posState.metadataDescriptors);
			if(productRequiredMetadata.length > 0 && !requiredMetadataList) {
				this.addingProductWithRequiredMetadata = {item: item, product: product};
			} else {
				// const sameItem = this.descriptor.groups[this.currentGroup].items.find((existingItem) => existingItem.itemUid === item.uid);
				// if(sameItem) {
				// 	sameItem.quantity++;
				// } else {

				this.descriptor.groups[this.currentGroup].items.push({
					itemUid: item.uid,
					productUid: product.uid,
					dining: dining,
					metadataList: requiredMetadataList ?? undefined,
					quantity: 1
				});
				// }
			}
		}
	}

	cancel() {
		this.$emit('cancel');
	}

	nextStep() {
		if(this.currentGroup + 1 === this.product.lastRevision.groups.length) {
			this.$emit('configured-group', this.descriptor);
		} else {
	 		this.currentGroup++;
		}
	}

	isGroupCorrect(group: ProductGroupApiOut) {
		if(!this.descriptor.groups) throw new Error('missing_group');
		const descriptorGroup = this.descriptor.groups.find((descriptorGroup) => descriptorGroup.groupUid === group.uid);
		if(!descriptorGroup) throw new Error('missing_descriptor_group');

		// Check group min/max
		const totalItemsQuantity = descriptorGroup.items.reduce((quantity, item) => quantity + item.quantity, 0);
		if(totalItemsQuantity < group.minQuantity) return false;
		if(group.maxQuantity && totalItemsQuantity > group.maxQuantity) return false;

		// Check items min/max
		for(const descriptorItem of descriptorGroup.items) {
			const item = this.findItemWithUid(descriptorItem.itemUid);
			if(descriptorItem.quantity < item.minQuantity) return false;
			if(item.maxQuantity && descriptorItem.quantity > item.maxQuantity) return false;
		}

		return true;
	}

	isItemSelectable(item: ProductGroupItemApiOut) {
		if(!this.descriptor.groups) throw new Error('missing_group');
		const descriptorGroup = this.descriptor.groups[this.currentGroup];

		let quantity = 0;
		for(const descriptorItem of descriptorGroup.items) {
			if(descriptorItem.itemUid === item.uid) quantity += descriptorItem.quantity;
		}

		return !item.maxQuantity || quantity < item.maxQuantity;
	}

	isGroupFull() {
		const group = this.product.lastRevision.groups[this.currentGroup];
		if(!this.descriptor.groups) throw new Error('missing_group');
		const descriptorGroup = this.descriptor.groups[this.currentGroup];

		let quantity = 0;
		for(const descriptorItem of descriptorGroup.items) {
			quantity += descriptorItem.quantity;
		}

		return group.maxQuantity && quantity >= group.maxQuantity;
	}

	getGroupQuantity() {
		const group = this.product.lastRevision.groups[this.currentGroup];
		if(!this.descriptor.groups) throw new Error('missing_group');
		const descriptorGroup = this.descriptor.groups[this.currentGroup];

		let quantity = 0;
		for(const descriptorItem of descriptorGroup.items) {
			quantity += descriptorItem.quantity;
		}
		return quantity;
	}

	getItemQuantity(item: ProductGroupItemApiOut) {
		let quantity: number = 0;
		if(!this.descriptor.groups) throw new Error('missing_group');
		const descriptorGroup = this.descriptor.groups[this.currentGroup];
		for(let descriptorItem of descriptorGroup.items) {
			if(descriptorItem.itemUid === item.uid) quantity += descriptorItem.quantity;
		}
		return quantity;
	}

	getItemPrice(item: ProductGroupItemApiOut) {
		if(!item.prices) {
			return item.product.lastRevision.prices[0].withTaxes;
		} else {
			return item.prices[0].withTaxes;
		}
	}

	findItemWithUid(itemUid: VisualScopedUuid<UuidScopeProductProductGroupItem>): ProductGroupItemApiOut {
		for(const group of this.product.lastRevision.groups) {
			for(const item of group.items) {
				if(item.uid === itemUid) return item;
			}
		}
		throw new Error('missing_item');
	}

	/**
	 * Get the current selection path from the stacking context
	 * This represents the actual path the user has taken through the menu hierarchy
	 */
	getCurrentSelectionPath(selectedProduct: ProductApiOut): ProductApiOut[] {
		return [...this.stackingContext.map(context => context.product), selectedProduct];
	}

	/**
	 * Find the first non-menu child in the given selection path
	 * This ensures we get the correct "main product" based on the user's actual navigation
	 */
	findFirstNonMenuChildInPath(path: ProductApiOut[]): ProductApiOut | null {
		for (const product of path) {
			if (product.lastRevision.type !== ProductType.FOOD_MENU) {
				return product;
			}
		}
		return null; // All products in path are menus
	}

	/**
	 * Get the first non-menu child for the current stacking context
	 * This is used to determine which product should be associated with dining steps
	 */
	getCurrentFirstNonMenuChild(selectedProduct: ProductApiOut): ProductApiOut | null {
		const currentPath = this.getCurrentSelectionPath(selectedProduct);
		return this.findFirstNonMenuChildInPath(currentPath);
	}

}