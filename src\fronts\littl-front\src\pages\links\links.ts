import {Component, Ref, Vue} from 'vue-facing-decorator';
import {
	ContentHeaderComponent,
	LayoutContentWithRightPanelComponent,
	ModalCustomFilterComponent,
	ModalOrDrawerComponent,
	TableColumnsOrganizationComponent,
	ToggleComponent,
	DropdownButtonComponent,
	ContentHeaderParameters,
	FilterParameters, InputDateTimeComponent, DropdownComponent, DropdownValue
} from '@groupk/vue3-interface-sdk';
import {
	AutoWired, QueryFilter, QueryFilterGroup,
	ScopedUuid, TypedQuerySearch, UuidUtils, VisualScopedUuid
} from '@groupk/horizon2-core';
import {LinkRepository} from '../../repositories/LinkRepository';
import {
	LinkApiIn,
	LinkApiOut,
	LinkTarget,
	LittlLinkTargetContract,
	LittlLinkTargetContractSearchConfig
} from '@groupk/mastodon-core';
import {
	TableColumn
} from '@groupk/vue3-interface-sdk';
import {OptionBuilder} from 'vue-facing-decorator/dist/optionBuilder';
import {Router} from '@groupk/horizon2-front';
import {uuidScopeEstablishment, UuidScopeEstablishment} from '@groupk/mastodon-core';
import LinkComponent from '../../components/LinkComponent/LinkComponent.vue';
import {QueryOperator} from '@groupk/horizon2-core';
import {
	FilterTableLayoutComponent,ModalComponent
} from "@groupk/vue3-interface-sdk";
import {AppBus} from "../../config/AppBus";
import {SavedFilter, DropdownButtonAction} from "@groupk/vue3-interface-sdk";
import {SavedFiltersRepository} from "../../repositories/SavedFiltersRepository";
import {Qrcode, QRErrorCorrectionLevel} from "../../libs/QrCode/qrcode";
import LinkTagsFormComponent from "../../components/LinkTagsFormComponent/LinkTagsFormComponent.vue";
import {CleaveDirective} from "../../../../../shared/directives/CleaveDirective";
import {
	GroupedTargetsManager,
	TargetGroup,
	TargetGroupEdition
} from "../../../../../shared/utils/GroupedTargetsManager";
import SidebarStateListener from "../../../../../shared/utils/SidebarStateListener";
import DateUtils from "../../../../../shared/utils/DateUtils";
import {DateFilter} from "../../../../../shared/filters/DateFilter";
import {MainConfig} from "../../../../../shared/MainConfig";
import {translateResponseError} from "../../../../../shared/RepositoryExtensions";
import {TableColumnsRepository} from "../../../../../shared/repositories/TableColumnsRepository";

@Component({
	directives: {
		cleave: CleaveDirective
	},
	components: {
		'layout': LayoutContentWithRightPanelComponent,
		'content-header': ContentHeaderComponent,
		'modal-custom-filter': ModalCustomFilterComponent,
		'table-columns-organization': TableColumnsOrganizationComponent,
		'modal-or-drawer': ModalOrDrawerComponent,
		'toggle': ToggleComponent,
		'dropdown-button': DropdownButtonComponent,
		'link-component': LinkComponent,
		'filter-table-layout': FilterTableLayoutComponent,
		'input-date-time': InputDateTimeComponent,
		'link-tags-form': LinkTagsFormComponent,
		'dropdown': DropdownComponent,
		'modal': ModalComponent
	}
})
export default class IndexView extends Vue {
	establishmentUid!: VisualScopedUuid<UuidScopeEstablishment>;

	links: LinkApiOut[] = [];
	existingTags: string[] = [];
	filteredTag: string|null = null;

	selectedLink: LinkApiOut | null = null;
	groupedTargetsManager: GroupedTargetsManager | null = null;

	filters: TypedQuerySearch<typeof LittlLinkTargetContractSearchConfig> = {};

	tableKey = 'littl-links';
	tableColumns: TableColumn[] = [{
		title: 'ID', name: 'id', displayed: true, mobileHidden: false
	}, {
		title: 'URL Cible', name: 'targetUri', displayed: true, mobileHidden: true
	}, {
		title: 'Plateformes', name: 'restrictions', displayed: true, mobileHidden: true
	}, {
		title: 'Clics', name: 'clics', displayed: true, mobileHidden: false
	}, {
		title: 'Labels', name: 'labels', displayed: true, mobileHidden: false
	}, {
		title: 'Date de création', name: 'creationDate', displayed: true, mobileHidden: true
	}];

	headerParameters: ContentHeaderParameters = {
		header: 'Mes Littl',
		subtitle: 'Créez vos liens courts, choisissez l\'url cible pour chaque plateforme et analysez leur impact',
		actions: [{
			type: 'SIMPLE_ACTION',
			name: 'Nouveau lien',
			icon: 'fa-regular fa-circle-plus',
			callback: () => this.startCreateLink()
		}],
		searchPlaceholder: 'Rechercher un lien'
	};

	filterParameters: { [filterName: string]: FilterParameters } = {
		'id': {
			translation: 'ID',
			type: 'UNKNOWN',
			validation: (value: unknown) => {
				return typeof value === 'string' && value.trim() !== '';
			}
		},
		'creation_datetime': {
			translation: 'Date de création',
			type: 'DATETIME',
			validation: (value: unknown) => {
				if (value === null || value === '') return false;
				return typeof value === 'string' && !isNaN(Date.parse(value));
			}
		},
	}

	@Ref() filtersModal!: ModalCustomFilterComponent;
	allowedFilters = LittlLinkTargetContractSearchConfig;
	appliedFilters: TypedQuerySearch<typeof LittlLinkTargetContractSearchConfig> = {};

	creatingLink: boolean = false;
	editingLinkTargets: TargetGroupEdition | null = null;

	loading: boolean = false;
	linkTargetsFormError: string | null = null;
	loadingLinkTargets: boolean = false;
	deletingGroupId: string|null = null;
	deletingGroup: boolean = false;
	deletingLink: boolean = false;
	resetingLinkClicks: boolean = false;
	copiedLink: string|null = null;
	showQrCode: boolean = false;
	showAddLabel: boolean = false;

	savedFilters: SavedFilter[] = [];

	/** Loading **/
	loadingDeleteLink: boolean = false;
	loadingResetClicks: boolean = false;

	@AutoWired(LinkRepository) accessor linkRepository!: LinkRepository;
	@AutoWired(TableColumnsRepository) accessor tableColumnsRepository!: TableColumnsRepository;
	@AutoWired(SavedFiltersRepository) accessor savedFiltersRepository!: SavedFiltersRepository;
	@AutoWired(SidebarStateListener) accessor sidebarStateListener!: SidebarStateListener;
	@AutoWired(MainConfig) accessor mainConfig!: MainConfig;
	@AutoWired(Router) accessor router!: Router;
	@AutoWired(AppBus) accessor appBus!: AppBus;

	constructor(optionBuilder: OptionBuilder, vueInstance: any) {
		super(optionBuilder, vueInstance);
		let regexMatch = this.router.lastRouteRegexMatches;

		if (regexMatch && regexMatch[1]) {
			this.establishmentUid = UuidUtils.scopedToVisual(regexMatch[1] as ScopedUuid<UuidScopeEstablishment>, uuidScopeEstablishment);
		}

		this.savedFilters = this.savedFiltersRepository.getFilters(this.tableKey) ?? [];

		this.sidebarStateListener.setHiddenSidebar(false)
	}

	unmounted() {
		window.removeEventListener('keydown', this.customSearchShortcut);
	}

	async mounted() {
		window.addEventListener('keydown', this.customSearchShortcut);

		let savedPreferences = this.tableColumnsRepository.getColumnPreferences(this.tableKey, this.tableColumns);
		if(savedPreferences) this.tableColumns = savedPreferences;

		await this.loadLinks();
	}

	customSearchShortcut(e: KeyboardEvent) {
		if (e.shiftKey && e.code === 'KeyN') {
			e.preventDefault();
			if (!this.deletingLink) {
				this.creatingLink = true;
			}
		} else if (this.selectedLink && e.ctrlKey && e.code === "KeyC") {
			e.preventDefault();
			this.copyLinkId(this.selectedLink);
		}
	}

	async loadLinks(filters?: TypedQuerySearch<typeof LittlLinkTargetContractSearchConfig>) {
		this.loading = true;
		if(filters){
			this.appliedFilters = JSON.parse(JSON.stringify(filters));
		}
		const response = await this.linkRepository.callContract('list', {establishmentUid: this.establishmentUid}, filters??{});
		if (response.isSuccess()) {
			this.links = response.success();
			for(let link of this.links) {
				if(link.tags) {
					for(let tag of link.tags) {
						if(!this.existingTags.includes(tag)) this.existingTags.push(tag);
					}
				}
			}
		} else {

		}
		this.loading = false;
	}

	resetFilters() {
		this.filters = {};
		this.appliedFilters = {};
		this.loadLinks();
	}

	getFiltersCount(filter: QueryFilter | QueryFilterGroup | undefined) {
		if(!filter) return 0;
		if('group' in filter) {
			let filters = 0;
			for(let subFilter of filter.filters) filters += this.getFiltersCount(subFilter);
			return filters;
		} else {
			return 1;
		}
	}

	updateLinks(link: LinkApiOut) {
		let index = this.links.findIndex((linkSearch) => link.id === linkSearch.id);
		if (index !== -1) this.links.splice(index, 1, link);
		else this.links.push(link);

		this.selectedLink = null;
		this.selectLink(link);
	}

	saveColumnPreferences(columns: TableColumn[]) {
		this.tableColumnsRepository.saveColumnsPreferences(this.tableKey, columns);
	}

	selectLink(link: LinkApiOut) {
		if (this.selectedLink && this.selectedLink.id === link.id) {
			this.selectedLink = null;
			return;
		}
		this.groupedTargetsManager = new GroupedTargetsManager(link);
		this.selectedLink = link;
	}

	startCreateLink() {
		this.creatingLink = true;
	}

	editTargetGroup(linkId: string, group: TargetGroup) {
		this.editingLinkTargets = {
			id: group.id,
			allowDateRestrictions: !!(group.startDatetime || group.endDatetime),
			startDate: group.startDatetime ? group.startDatetime.split('T')[0] : '',
			startTime: group.startDatetime ? DateUtils.formatDateHour(group.startDatetime, ':') : '',
			endDate: group.endDatetime ? group.endDatetime.split('T')[0] : '',
			endTime: group.endDatetime ? DateUtils.formatDateHour(group.endDatetime, ':') : '',
			all: group.all ? group.all.targetUri : '',
			windows: group.windows ? group.windows.targetUri : '',
			android: group.android ? group.android.targetUri : '',
			ios: group.ios ? group.ios.targetUri : ''
		};
	}

	createTargetGroup() {
		this.editingLinkTargets = {
			id: null,
			allowDateRestrictions: false,
			startDate: '',
			startTime: '',
			endDate: '',
			endTime: '',
			all: '',
			windows: '',
			android: '',
			ios: ''
		};
	}

	getPlatforms(link: LinkApiOut) {
		return link.targets.map((target) => this.getTargetType(target)).filter((value, index, array) => array.indexOf(value) === index);
	}

	getTargetUrlWithType(link: LinkApiOut) {
		if (link.targets.length === 0) {
			return {
				uri: '-',
				type: null
			};
		}
		if (link.targets.length > 1) {
			return {
				uri: 'Ce lien contient plusieurs url cible',
				type: null
			};
		}
		return {
			uri: link.targets[0].targetUri,
			type: this.getTargetType(link.targets[0])
		};
	}

	getTypeIcon(type: 'all' | 'android' | 'ios' | 'windows') {
		if (type === 'android') return 'fa-fw fa-brands fa-android';
		if (type === 'ios') return 'fa-fw fa-brands fa-apple';
		if (type === 'windows') return 'fa-fw fa-brands fa-windows';
		return 'fa-fw fa-regular fa-earth-americas';
	}

	getTargetType(target: LinkTarget): 'all' | 'android' | 'ios' | 'windows' {
		if (target.os === 'IOS') return 'ios';
		if (target.os === 'ANDROID') return 'android';
		if (target.os === 'WINDOWS') return 'windows';
		return 'all';
	}

	getTargetGroupName(group: TargetGroup) {
		let title = '';
		if (group.startDatetime && group.endDatetime) title = `Du ${DateFilter(group.startDatetime)} au ${DateFilter(group.endDatetime)}`;
		else if (group.startDatetime) title = `A partir du ${DateFilter(group.startDatetime)}`;
		else if (group.endDatetime) title = `Jusqu'au ${DateFilter(group.endDatetime)}`;
		else title = 'Toujours actif';
		return title;
	}

	isDefaultGroup(group: TargetGroup) {
		return !group.all && !group.windows && !group.android && !group.ios;
	}

	setLoading() {
		this.loading = true;
		setTimeout(() => {
			this.loading = false;
		}, 1000);
	}

	validateTargetsGroupForm(edition: TargetGroupEdition) {
		// if (edition.allowDateRestrictions) {
		// 	if (edition.startDatetime.length > 0 && edition.startDatetime.length !== 10) throw new Error('Merci de renseigner une date de début valide');
		// 	if (edition.endDatetime.length > 0 && edition.endDatetime.length !== 10) throw new Error('Merci de renseigner une date de fin valide');
		// }
	}

	async editTargetsGroup() {
		if (this.selectedLink === null) return;
		if (this.groupedTargetsManager === null) return;
		if (this.editingLinkTargets === null) return;

		this.loadingLinkTargets = true;
		this.linkTargetsFormError = null;

		try {
			this.validateTargetsGroupForm(this.editingLinkTargets);

			this.groupedTargetsManager.saveTargetGroup(this.groupedTargetsManager.convertTargetGroupEditionToTargetGroup(this.editingLinkTargets));

			let linkApiIn = new LinkApiIn();
			linkApiIn.targets = this.groupedTargetsManager.getGroupsAsTargets();

			let response = await this.linkRepository.callContract(
				'update',
				{establishmentUid: this.establishmentUid, linkId: this.groupedTargetsManager.link.id},
				linkApiIn
			);

			if (response.isSuccess()) {
				this.selectedLink.targets = linkApiIn.targets;
				this.editingLinkTargets = null;
			} else {
				this.linkTargetsFormError = translateResponseError<typeof LittlLinkTargetContract, 'update'>(response, {
					invalid_target: 'Le lien ne semble plus exister',
					id_already_used: 'Ce Littl est déjà utilisé',
					max_link_limit_reach: 'Vous avez atteint votre limite de liens',
				})
			}
		} catch (err) {
			this.linkTargetsFormError = (err as any).message;
		}

		this.loadingLinkTargets = false;
	}

	async deleteGroup(groupId: string | null) {
		if (groupId === null) return;
		if (this.selectedLink === null) return;
		if (this.groupedTargetsManager === null) return;
		this.groupedTargetsManager.deleteGroup(groupId);

		this.deletingGroup = true;

		try {
			let linkApiIn = new LinkApiIn();
			linkApiIn.targets = this.groupedTargetsManager.getGroupsAsTargets();

			let response = await this.linkRepository.callContract(
				'update',
				{establishmentUid: this.establishmentUid, linkId: this.groupedTargetsManager.link.id},
				linkApiIn
			);
			if (response.isSuccess()) {
				this.selectedLink.targets = linkApiIn.targets;
				this.editingLinkTargets = null;
			} else {
				this.linkTargetsFormError = translateResponseError<typeof LittlLinkTargetContract, 'update'>(response, {
					invalid_target: 'Le lien ne semble plus exister',
					id_already_used: 'Ce Littl est déjà utilisé',
					max_link_limit_reach: 'Vous avez atteint votre limite de liens',
				})
			}
		} catch (err) {
		}

		this.deletingGroup = false;
		this.deletingGroupId = null;
	}

	async deleteLink(linkId: string) {
		this.loadingDeleteLink = true;

		const response = await this.linkRepository.callContract(
			'remove',
			{establishmentUid: this.establishmentUid, linkId: linkId},
			undefined
		);

		if (response.isSuccess()) {
			this.appBus.emit('linkDeleted', null);
			this.selectedLink = null;
			await this.loadLinks();
		} else {
		}

		this.deletingLink = false;
		this.loadingDeleteLink = false;
	}

	async resetClicks(linkId: string) {
		this.loadingResetClicks = true;

		const response = await this.linkRepository.callContract(
			'resetClicks',
			{establishmentUid: this.establishmentUid, linkId: linkId},
			undefined
		);

		if(response.isSuccess()) {
			const updatedLink = response.success();
			const index = this.links.findIndex((link) => link.id === updatedLink.id);
			if(index !== -1) this.links.splice(index, 1, updatedLink);
		}

		this.resetingLinkClicks = false;
		this.loadingResetClicks = false;
	}

	async searchLinks(filters: TypedQuerySearch<typeof LittlLinkTargetContractSearchConfig>) {
		this.loading = true;
		this.filters = {...filters};

		this.links = (await this.linkRepository.callContract('list', {establishmentUid: this.establishmentUid}, filters)).success();
		this.appliedFilters = filters;
		this.loading = false;
	}

	async search(search: string) {
		const response = await this.linkRepository.callContract(
			'list',
			{establishmentUid: this.establishmentUid},
			{filter: {name: 'id', operator: QueryOperator.CONTAINS, value: search}}
		);

		if (response.isSuccess()) {
			this.links = response.success();
			this.selectedLink = null;
		} else {
			console.log(response.error());
		}
	}

	get filteredByTags() {
		return this.links.filter((link) => !this.filteredTag || link.tags && link.tags.includes(this.filteredTag));
	}

	dropdownClicked(action: DropdownButtonAction) {
		if(action.id === 'delete') {
			this.deletingLink = true;
		} else {
			this.showQrCode = true;
		}
	}

	generateQrCode(text: any, typeNumber: any = 0, errorCorrectionLevel: QRErrorCorrectionLevel = QRErrorCorrectionLevel.L, mode: any = "Byte", mb: any = "UTF-8"): string {
		let qr = new Qrcode(typeNumber || 4, errorCorrectionLevel);
		Qrcode.stringToBytes = function (params: any) {
			if (Array.isArray(params))
				//do nothing as it looks like it's what we are expecting
				return params;
			else {
				return Qrcode.stringToBytesFuncs[mb](params);
			}
		};
		qr.addData(text, mode);
		qr.make();
		return qr.createDataURL(20, 20);
	}

	downloadQr() {
		if(!this.selectedLink) return;
		const a = document.createElement("a");
		a.href = this.generateQrCode(this.selectedLink.fullUrl, 0, 0)
		a.download = this.selectedLink.id + "-qrcode.gif";
		a.click();
	}

	copyLinkId(link: LinkApiOut) {
		navigator.clipboard.writeText(link.fullUrl);
		this.copiedLink = link.id;
		setTimeout(() => {
			this.copiedLink = null;
		}, 1000)
	}

	saveFilter(name: string) {
		if (this.filters.filter) {
			this.savedFilters.push({
				name: name,
				filter: this.filters.filter
			});
			this.savedFiltersRepository.saveFilters(this.tableKey, this.savedFilters);
		}
	}

	selectFilter(savedFilter: SavedFilter) {
		this.filters.filter = savedFilter.filter as any;
		this.searchLinks(this.filters);
	}

	deleteFilter(index: number) {
		this.savedFilters.splice(index, 1);
		this.savedFiltersRepository.saveFilters(this.tableKey, this.savedFilters);
	}

	get labelsDropdownValues(): DropdownValue[] {
		return ([{
			name: 'Aucun',
			value: null
		}] as DropdownValue[]).concat(this.existingTags.map((tag) => {
			return {
				name: tag,
				value: tag
			} as DropdownValue
		}))
	}
}
