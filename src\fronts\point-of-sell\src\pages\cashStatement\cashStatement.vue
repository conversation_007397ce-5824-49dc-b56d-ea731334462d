<script lang="ts" src="./cashStatement.ts">
</script>

<style scoped lang="sass">
@import './cashStatement.scss'
</style>

<template>
    <div id="cash-statement-page">
        <div class="left">
            <template v-if="loading">
                <div class="loading">
                    <div class="loader"></div>
                    Chargement...
                </div>
            </template>
            <div class="form-error" v-else-if="dataError">
                <i class="fa-regular fa-circle-exclamation"></i>
                <div class="details">
                    <span class="title">
                        Impossible de charger la caisse
                    </span>
                    {{ dataError }}
                </div>
            </div>
            <template v-else-if="!currentCashStatement">
                <h2> Ouverture du ticket Z.</h2>
                <span> Un ticket Z est un rapport rendant compte du chiffre d'affaires effectué sur la journée. </span>

                <button class="big button" :class="{loading: openingCashStatement, disabled: openingCashStatement}" @click="openCashStatementAndGoToPos()">
                    Ouvrir le ticket Z
                </button>
            </template>
            <template v-else-if="!currentCashStatementSession && getOpenedSessions(currentCashStatement).length > 0">
                <h2> Un ticket Z est <br/>déjà ouvert sur d'autre caisses. </h2>
                <span> Voulez vous ajouter cette caisse au ticket Z ? </span>

                <button class="big button" :class="{loading: redirecting || openingCashStatement, disabled: redirecting || openingCashStatement}" @click="openCashStatementAndGoToPos()">
                    Ouvrir la caisse
                </button>
                <button class="tertiary big button" @click="reload()">
                    Recharger la page
                </button>
            </template>
            <template v-else-if="!currentCashStatementSession">
                <h2> Toutes les caisses ont bien été fermées. </h2>
                <span> Voulez vous définitivement clore le ticket Z ou ouvrir une nouvelle session ? </span>

<!--                <button class="big button" :class="{disabled: redirecting}" @click="closeCashStatement()">-->
<!--                    Fermer le ticket Z global-->
<!--                </button>-->
                <button class="big white button" :class="{disabled: redirecting}" @click="openCashStatementAndGoToPos()">
                    Ouvrir la caisse
                </button>
            </template>
            <template v-else>
                <h2> Un ticket Z est en cours. </h2>
                <span> Un ticket Z est ouvert, vous pouvez dès à présent commencer à vendre. </span>

                <button class="big green button" :class="{loading: redirecting, disabled: redirecting}" @click="goToPos()">
                    Aller sur la caisse
                </button>
            </template>

            <div class="form-error" v-if="error">
                <i class="fa-regular fa-circle-exclamation"></i>
                <div class="details">
                    <span class="title">
                        Impossible d'ouvrir le ticket Z
                    </span>
                    {{ error }}
                </div>
            </div>

            <div class="displayed-receipt" v-if="displayedReceipt" @click="displayedReceipt = null">
                <img :src="displayedReceipt">
            </div>

            <div class="bottom" v-if="device">
                <button class="grey button" :class="{loading: printingCashStatement, disabled: printingCashStatement}" v-if="lastClosedSession" @click="printCashStatement()">
                    <i class="fa-regular fa-print"></i>
                    Ré-imprimer le dernier ticket Z
                </button>

                {{ device.brand }} - {{ device.hardwareId ?? device.softwareId }}
            </div>

            <print-settings-modal v-if="showPrintSettings" @close="showPrintSettings = false"></print-settings-modal>
        </div>
        <div class="right">
            <template v-if="loading || dataError">

            </template>
            <template v-else-if="posProfile.enableWorkClock">
<!--                <h3> Pointeuse </h3>-->
<!--                <div class="establishment-accounts">-->
<!--                    <div class="establishment-account" v-for="establishmentAccountUid of posProfile.overridableAccountsUids" @click="clockInOut(establishmentAccountUid)">-->
<!--                        <div class="left">-->
<!--                        <span class="name">-->
<!--                            {{ requireEstablishmentAccountWithUid(establishmentAccountUid).firstname }}-->
<!--                            {{ requireEstablishmentAccountWithUid(establishmentAccountUid).lastname }}-->
<!--                        </span>-->
<!--                            <span class="green" v-if="isClockedIn(establishmentAccountUid)"> Travail en cours... </span>-->
<!--                            <span v-else> Travail terminé </span>-->
<!--                        </div>-->

<!--                        <i class="fa-regular fa-arrow-right-from-arc" v-if="isClockedIn(establishmentAccountUid)"></i>-->
<!--                        <i class="fa-regular fa-arrow-right-to-arc" v-else></i>-->
<!--                    </div>-->
<!--                </div>-->
            </template>

        </div>

        <toast-manager></toast-manager>
    </div>
</template>