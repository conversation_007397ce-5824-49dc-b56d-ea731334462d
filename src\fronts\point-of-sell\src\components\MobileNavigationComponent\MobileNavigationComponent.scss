.mobile-navigation-component {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: var(--pos-color);
    color: var(--pos-current-text-color);
    padding: 10px 10px var(--safe-area-bottom) 10px;

    .item {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 5px;
        padding: 10px;
        width: 100%;
        border-radius: 8px;
        box-sizing: border-box;
        cursor: pointer;

        i {
            font-size: 28px;
        }

        span {
            font-family: Montserrat, sans-serif;
            font-size: 12px;
            font-weight: 600;
            text-align: center;
        }

        &.red {
            &:hover {
                background: var(--error-color);
                color: white;
            }
        }

        &.active {
            background: var(--pos-current-selected-color);
            color: var(--pos-current-selected-text-color);
        }
    }
}