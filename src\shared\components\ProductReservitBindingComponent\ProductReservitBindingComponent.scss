.product-reservit-binding-component {


    .product-cell {
        display: flex;
        flex-direction: column;
        gap: 10px;

        .product-name {
            display: flex;
            align-items: center;
            gap: 10px;
            word-break: break-word;

            .image {
                flex-shrink: 0;
                height: 32px;
                width: 32px;
                border-radius: 4px;
                overflow: hidden;
            }
        }
    }

    .left {
        display: flex;
        align-items: center;
        flex-grow: 2;
    }

    .shortcut {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        gap: 5px;
        margin-top: 5px;

        .title {
            font-size: 14px;
        }

        .key {
            padding: 5px;
            background: var(--secondary-hover-color);
            font-size: 12px;
            border-radius: 4px;
            border-bottom: 2px solid #c2c2c2;

            &.down {
                border-bottom: 1px solid #c2c2c2;
            }
        }
    }

    textarea {
        height: 250px;
        resize: none;
    }

    .pages {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        gap: 5px;
        flex-shrink: 0;

        span {
            font-size: 14px;
            margin-right: 10px;
        }

        .button:hover {
            background: var(--secondary-hover-color);
        }

        .button.no-pointer-event {
            pointer-events: none;
        }
    }

    .top {
        display: flex;
        justify-content: space-between;

        .toggle-group {
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 15px;
        }
    }
}