#statistics-component {
    display: flex;
    flex-direction: column;
    gap: 20px;
    height: 100%;
    width: 100%;
    user-select: none;
    padding: 25px 20px;
    box-sizing: border-box;
    overflow: auto;

    .button-group {
        display: flex;

        button {
            all: unset;
            display: flex;
            align-items: center;
            gap: 10px;
            background: white;
            padding: 12px;

            &.active {
                background: var(--primary-color);
                color: var(--primary-text-color);
            }

            &:first-child {
                border-radius: 8px 0 0 8px;
            }

            &:last-child {
                border-radius: 0 8px 8px 0;
            }
        }
    }

    .key-numbers {
        display: grid;
        grid-template-columns: 1fr 1fr 1fr;
        gap: 20px;

        @media (max-width: 900px) {
            grid-template-columns: 1fr 1fr;
        }

        .key-number {
            display: flex;
            flex-direction: column;
            gap: 10px;
            padding: 25px;
            border-radius: 8px;
            background: white;

            .type {
                font-size: 14px;
            }

            .value {
                font-size: 24px;
                font-weight: 700;
            }

            .indicator {
                display: flex;
                align-items: center;
                gap: 4px;
                color: #079455;
                font-weight: 600;
                font-size: 14px;

                span {
                    color: #545454;
                    font-size: 12px;
                    font-weight: 500;
                }
            }
        }
    }

    table {
        background: white;
        border: none;

        .empty-row {
            text-align: center;
        }

        tr {
            td {
                padding: 15px;
            }
        }

        * {
            font-size: 16px;
        }
    }

    .remaining-stock {
        display: flex;
        align-items: center;
        gap: 10px;

        .indicator {
            height: 10px;
            width: 10px;
            border-radius: 50%;

            &.green {
                background: green;
            }

            &.orange {
                background: orange;
            }

            &.red {
                background: var(--error-color);
            }
        }
    }

    .stock-params {
        display: flex;
        justify-content: flex-end;
        gap: 10px;

        width: 100%;

        input {
            width: 30vw;
        }
    }
}