import "./assets/css/global.scss";
import "./assets/css/global-vueInterface.scss";
import "./assets/css/vueInterface/layout.scss";
import "./assets/css/vueInterface/buttons.scss";
import "./assets/css/vueInterface/labels.scss";
import "./assets/css/vueInterface/TableComponent.scss";
import SidebarView from "./pages/sidebar/sidebar.vue";
import {createApp} from "vue";
import "@groupk/font-awesome-sdk/src/V6/css/pro-v4-font-face.min.css";
import "@groupk/font-awesome-sdk/src/V6/css/pro-v4-shims.min.css";
import "@groupk/font-awesome-sdk/src/V6/css/pro.min.css";
import "@groupk/vue3-interface-sdk/dist/style.css";
import TopBarView from "./pages/topbar/topbar.vue";
import {
	Router,
	RouterRoute,
	RouterStateConfigVersionModifier,
	VueRouteFactory,
	VueRouteOptions
} from "@groupk/horizon2-front";
import {GetInstance, setExecutionContext, SetInstance} from "@groupk/horizon2-core";
import {AnimatedRoute, AnimationBetweenRouteChange} from "../../../shared/routing/AnimatedRouter";
import {GlobalFilters} from "../../../shared/filters/GlobalFilters";
import {VueAuthedRouteFactory} from "../../../shared/routing/AuthedVueJsRoute";
import {Configuration, MainConfig} from "../../../shared/MainConfig";
import {AuthStateModel} from "../../../shared/AuthStateModel";
import {AppState} from "../../../shared/AppState";
import {VueAppRouteFactory} from "../../../shared/routing/AppVueJsRoute";
import assetsPlugin from "../../../shared/routing/assetsPlugin";


let mainConfig: MainConfig = GetInstance(MainConfig);

mainConfig.init().then(async function(configFromFile: Configuration) {
	if(configFromFile.develop) {
		setExecutionContext({
			debug: true
		});
	}

	const appState = GetInstance(AppState);
	appState.platformId = 'Admin';

	const authStateModel = new AuthStateModel(configFromFile.mastodonApiEndpoint, false);
	try {
		await authStateModel.getState(true);
	} catch(err){}
	SetInstance(AuthStateModel, authStateModel);

	startApp(configFromFile);
});

function startApp(config: Configuration) {
	let router = new Router<AnimatedRoute | RouterRoute>({
		prefix: "/",
		registerGlobalInterceptor: true,
		container: "mainRouterContainer",
		oldContainer: "oldRouterContainer",
		disableRouteTriggerIfIdentical: true,
	});
	if (window.innerWidth < 900) {
		new AnimationBetweenRouteChange(router);
	}

	router.addHook(new RouterStateConfigVersionModifier(13));

	let uuidRegex = /([0-9a-f]{8}\-[0-9a-f]{4}\-[0-9a-f]{4}\-[0-9a-f]{4}\-[0-9a-f]{12})/;
	const vueRouteOptions: Partial<VueRouteOptions> = {
		filters: GlobalFilters,
		hookAppCreated(app) {
			app.use(assetsPlugin);
		},
	};

	router.addRoute({regex: new RegExp(/establishment\//.source + uuidRegex.source + /\/auth/.source), loader: () => import('./pages/auth/auth.vue').then((vue) => VueRouteFactory(vue.default, vueRouteOptions))});
	router.addRoute({regex: new RegExp(/establishment\//.source + uuidRegex.source + /\/payment-methods/.source), loader: () => import('./pages/paymentMethods/paymentMethods.vue').then((vue) => VueAuthedRouteFactory(vue.default, vueRouteOptions))});
	router.addRoute({regex: new RegExp(/establishment\//.source + uuidRegex.source + /\/temporary/.source), loader: () => import('./pages/temporary/temporary.vue').then((vue) => VueAuthedRouteFactory(vue.default, vueRouteOptions))});
	router.addRoute({regex: new RegExp(/establishment\//.source + uuidRegex.source + /\/establishments/.source), loader: () => import('./pages/establishments/establishments.vue').then((vue) => VueAuthedRouteFactory(vue.default, vueRouteOptions))});
	router.addRoute({regex: new RegExp(/establishment\//.source + uuidRegex.source + /\/package-tracking/.source), loader: () => import('./pages/packageTracking/packageTracking.vue').then((vue) => VueAuthedRouteFactory(vue.default, vueRouteOptions))});
	router.addRoute({regex: new RegExp(/establishment\//.source + uuidRegex.source + /\/role-generator/.source), loader: () => import('./pages/roleGenerator/roleGenerator.vue').then((vue) => VueAuthedRouteFactory(vue.default, vueRouteOptions))});
	router.addRoute({regex: new RegExp(/establishment\//.source + uuidRegex.source + /\/config/.source), loader: () => import('./pages/eCommerceConfig/eCommerceConfig.vue').then((vue) => VueAuthedRouteFactory(vue.default, vueRouteOptions))});
	router.addRoute({regex: new RegExp(/establishment\//.source + uuidRegex.source + /\/metadata/.source), loader: () => import('./pages/metadata/metadata.vue').then((vue) => VueAuthedRouteFactory(vue.default, vueRouteOptions))});
	router.addRoute({regex: new RegExp(/establishment\//.source + uuidRegex.source + /\/self-order/.source), loader: () => import('./pages/selfOrder/selfOrder.vue').then((vue) => VueAppRouteFactory(vue.default, vueRouteOptions))});
	router.addRoute({regex: new RegExp(/establishment\//.source + uuidRegex.source + /\/package-scan/.source), loader: () => import('./pages/packageScan/packageScan.vue').then((vue) => VueAuthedRouteFactory(vue.default, vueRouteOptions))});
	router.addRoute({regex: new RegExp(/.*/.source), loader: () => import('./pages/casRedirect/casRedirect.vue').then((vue) => VueRouteFactory(vue.default, vueRouteOptions))});

	SetInstance(Router, router);

	router.updateCurrentPageFromCurrentLocation().then(async () => {
		const sidebar = createApp(SidebarView);
		sidebar.mount("#sidebar");

		const topBar = createApp(TopBarView);
		topBar.mount("#top-bar");
	});
}
