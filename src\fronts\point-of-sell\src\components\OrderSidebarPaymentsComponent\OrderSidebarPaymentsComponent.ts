import {Component, Prop, Vue} from "vue-facing-decorator";
import {PosState} from "../../model/PosState";
import {LocalOrder} from "../../model/LocalOrder";
import {AutoWired, VisualScopedUuid} from "@groupk/horizon2-core";
import {LocalOrderRepository} from "../../repositories/LocalOrderRepository";
import {AppBus} from "../../config/AppBus";
import {OrderPaymentApiOut, OrderPaymentStatus} from "@groupk/mastodon-core";
import {PaymentMethodApiOut, UuidScopePayment_method} from "@groupk/mastodon-core";
import {ErrorHandler} from "../../class/ErrorHandler";
import {DropdownButtonComponent} from "@groupk/vue3-interface-sdk";
import {OrderReceiptRender} from "../../class/OrderReceiptRender";
import {PrinterRepository} from "../../repositories/PrinterRepository";
import KeypadComponent from "../KeypadComponent/KeypadComponent.vue";
import {KeypadKey} from "../KeypadComponent/KeypadComponent";

@Component({
	components: {
		'dropdown-button': DropdownButtonComponent,
		'keypad': KeypadComponent,
	},
	emits: ['close']
})
export default class OrderSidebarPaymentsComponent extends Vue {
	@Prop() posState!: PosState;
	@Prop() localOrder!: LocalOrder;

	partsAmount: number = 1;

	showKeypad: OrderPaymentApiOut|null = null;

	@AutoWired(LocalOrderRepository) accessor localOrderRepository!: LocalOrderRepository;
	@AutoWired(PrinterRepository) accessor printerRepository!: PrinterRepository;
	@AutoWired(AppBus) accessor appBus!: AppBus;
	@AutoWired(ErrorHandler) accessor errorHandler!: ErrorHandler;

	OrderPaymentStatus = OrderPaymentStatus;

	isCanceled(payment: OrderPaymentApiOut) {
		return payment.status
	}

	printTicket(payment: OrderPaymentApiOut) {
		const orderToPrint = this.posState.currentOrder;
		if(!orderToPrint) throw new Error('no_current_order');

		const orderReceiptRender = new OrderReceiptRender();
		const parts = orderReceiptRender.renderPaymentNote(orderToPrint.order, payment, orderReceiptRender.getReceiptConfig(), this.partsAmount);

		this.printerRepository.printToSelectedPrinter(parts).catch((err) => {
			console.log(err);
		});

		this.showKeypad = null;
		this.partsAmount = 1;
	}

	cancelPayment(payment: OrderPaymentApiOut) {
		const refundPayment = this.posState.orderExecutorModel.refundPayment(this.localOrder.order, payment, payment.method);
		refundPayment.status = OrderPaymentStatus.SUCCESS;
		this.localOrderRepository.saveAndResync(this.localOrder);
	}

	haveBeenOverpaid(payment: OrderPaymentApiOut) {
		let total = 0;
		for(let orderPayment of this.localOrder.order.payments) {
			if(orderPayment.parents.includes(payment.uid)) total += orderPayment.amount;
		}
		return -total !== payment.amount ? total : 0;
	}

	haveBeenRefund(payment: OrderPaymentApiOut) {
		let total = 0;
		for(let orderPayment of this.localOrder.order.payments) {
			if(orderPayment.parents.includes(payment.uid)) total += orderPayment.amount;
		}
		return -total === payment.amount;
	}

	isRefundPayment(payment: OrderPaymentApiOut) {
		return payment.parents.length > 0;
	}

	getMethodWithUid(paymentMethodUid: VisualScopedUuid<UuidScopePayment_method>): PaymentMethodApiOut {
		const method = this.posState.paymentMethods.find((method) => method.uid === paymentMethodUid);
		if(!method) throw new Error('missing_payment_method - err45');
		return method;
	}

	manuallySetStatus(payment: OrderPaymentApiOut, status: OrderPaymentStatus) {
		if(status === 'SUCCESS' && payment.amount > this.posState.orderExecutorModel.getOrderTotals(this.localOrder.order).leftToPay) {
			this.errorHandler.logToToastManager({
				title: 'Impossible de valider le paiement',
				description: 'Le paiement est supérieur au reste à payer de la commande'
			});
		} else {
			this.posState.orderExecutorModel.setPaymentStatus(this.localOrder.order, payment, status);
			this.localOrderRepository.saveAndResync(this.localOrder);
		}
	}

	updatePartsAmount(key: KeypadKey) {
		if (key < 10) {
			if (this.partsAmount === 0) {
				this.partsAmount = key;
			} else {
				this.partsAmount = parseInt(this.partsAmount + '' + key);
			}
		} else {
			if (key === KeypadKey.BACKSPACE) {
				if ((this.partsAmount + "").length === 1) {
					this.partsAmount = 0;
				} else {
					this.partsAmount = parseInt((this.partsAmount + "").slice(0, -1));
				}
			} else if (key === KeypadKey.TRASH) {
				this.partsAmount = 0;
			}
		}
	}

	close() {
		this.$emit('close');
	}
}
