<script lang="ts" src="./TransfersSettingsComponent.ts" />

<style lang="sass" scoped>
@import './TransfersSettingsComponent.scss'
</style>

<template>
    <div class="transfers-settings-component">
        <div class="loading-container" v-if="loading">
            <div class="loader"></div>
        </div>
        <div v-else>
            <div class="table-scroll-wrapper">
                <div class="form-error" v-if="isOffline">
                    <i class="fa-solid fa-triangle-exclamation"></i>
                    <div class="details">
                        <span class="title"> Pas de connexion internet </span>
                        <span class="description"> La liste des demandes de transferts n'a pas pu être chargée</span>
                    </div>
                </div>

                <table class="data-table">
                    <thead>
                    <tr>

                    </tr>
                    </thead>
                    <tbody>
                    <tr v-if="transfers.length === 0">
                        <td class="empty-row" colspan="100%"> Aucun transfert </td>
                    </tr>
                    <tr v-for="transfer of transfers">
                        <td>
                            <div class="transfer-name">
                                <span class="title" v-if="transfer.originIotDevice === iotDeviceAppScopedToVisual(posState.iotDevice.uid)"> Demande envoyée </span>
                                <span class="title" v-else> Demande reçue </span>
                                <span class="subtitle">
                                     {{ $filters.Date(transfer.creationDatetime) }} à
                                    {{ $filters.Hour(transfer.creationDatetime) }}
                                </span>
                            </div>
                        </td>

                        <td style="width: 0;">
                            <div class="buttons">
                                <button
                                    v-if="transfer.creatorIotDevice === iotDeviceAppScopedToVisual(posState.iotDevice.uid)"
                                    class="green button"
                                    @click="acceptTransfer(transfer)"
                                >
                                    Accepter
                                </button>

                                <button
                                    class="grey button"
                                    :class="{loading: cancelingTransfer === transfer.uid, disabled: cancelingTransfer === transfer.uid}"
                                    @click="cancelTransfer(transfer)"
                                >
                                    Annuler
                                </button>
                            </div>
                        </td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</template>