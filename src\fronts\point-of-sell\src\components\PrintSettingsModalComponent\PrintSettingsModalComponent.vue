<script lang="ts" src="./PrintSettingsModalComponent.ts" />

<style lang="sass" scoped>
@import './PrintSettingsModalComponent.scss'
</style>

<template>
    <div class="print-settings-modal-component">
        <div class="modal-dimmer" @click="close()">
            <div class="modal" @click.stop>
                <print-settings></print-settings>

                <div class="buttons">
                    <button class="big button" @click="close()"> Sauvegarder </button>
                </div>
            </div>
        </div>
    </div>
</template>