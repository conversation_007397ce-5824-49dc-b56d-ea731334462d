#self-order-page {
    height: 100%;

    .selection {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        gap: 2vw;
        height: 100%;
        padding: 2vw;
        box-sizing: border-box;
        background: #01b1da;

        .number {
            font-size: 28vw;
            font-weight: 400;
            text-align: center;
            color: white;
        }

        .description {
            font-size: 4vw;
            text-align: center;
            color: white;
        }
    }

    .selected-product {
        display: flex;
        flex-direction: column;
        align-items: stretch;
        justify-content: center;
        gap: 2vw;
        height: 100%;
        box-sizing: border-box;
        background: #01b1da;

        .top {
            flex-grow: 2;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            gap: 2vw;
            padding: 2vw;

            .small-title {
                font-size: 3vw;
                color: white;
                text-align: center;
            }

            .title {
                font-size: 8vw;
                font-weight: 600;
                color: white;
                text-align: center;
            }

            .price {
                font-size: 8vw;
                color: white;
                text-align: center;
            }
        }
    }

    .error {
        display: flex;
        flex-direction: column;
        align-items: stretch;
        justify-content: center;
        gap: 2vw;
        height: 100%;
        box-sizing: border-box;
        background: var(--error-color);

        .top {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            gap: 2vw;
            padding: 2vw;
            flex-grow: 2;

            .title {
                font-size: 8vw;
                font-weight: 600;
                color: white;
                text-align: center;
            }

            .subtitle {
                font-size: 4vw;
                color: white;
                text-align: center;
            }

        }


        .white.button {
            gap: 2vw;
            font-size: 3vw;
            padding: 2vw 4vw;
            border: none;
            margin-top: 2vw;
        }
    }

    .buttons {
        display: grid;
        grid-template-columns: 1fr 1fr;
        padding: 1vw 2vw;

        .white.button {
            gap: 2vw;
            font-size: 3vw;
            padding: 3vw 4vw;
            border: none;

            &.double {
                grid-column: 1/3;
            }
        }
    }

    .green-button-icon {
        background: #07aa07;
        display: flex;
        justify-content: center;
        padding: 2vw 2vw;
        width: 8vw;
        box-sizing: border-box;
        color: white;
        border-radius: 1vw;
    }

    .yellow-button-icon {
        display: flex;
        justify-content: center;
        background: #fddf0c;
        padding: 2vw 2vw;
        width: 8vw;
        box-sizing: border-box;
        color: white;
        border-radius: 1vw;
    }
}