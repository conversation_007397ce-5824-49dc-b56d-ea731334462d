ARG FRONT_NAME="default"

# ================================================
FROM nginx:1.27-alpine as baseimage

COPY  --chmod=777 --chown=nginx ./docker/docker-entrypoint.sh /usr/local/bin/
COPY  --chmod=777 --chown=nginx ./docker/docker-entrypoint-inject_config.sed /usr/local/bin/

ADD ./docker/nginx_default.conf /etc/nginx/conf.d/default.conf.base

ENV DOLLAR $
ENTRYPOINT ["docker-entrypoint.sh"]
CMD ["nginx", "-g", "daemon off;"]

# ================================================
FROM node:22-alpine as compiledsources
ARG FRONT_NAME
WORKDIR /var/www/html

COPY --chown=nginx ./package.json .
COPY --chown=nginx ./package-lock.json .
COPY .npmrc .
RUN npm ci
COPY --chown=nginx ./src src/.
COPY --chown=nginx ./tsconfig.json .
COPY --chown=nginx ./tsconfig.node.json .
COPY --chown=nginx ./vite.config.shared.ts .
RUN npm run buildprod-base
RUN npm run buildprod-$FRONT_NAME

# ================================================
FROM baseimage as final
ARG FRONT_NAME
WORKDIR /var/www/html

COPY --from=compiledsources --chown=nginx /var/www/html/src/fronts/$FRONT_NAME/dist/ /var/www/html
RUN mkdir -p /var/www/html/ && echo "$(date +%s)" >> "/var/www/version"


