.string-descriptor-component {
    display: flex;
    flex-direction: column;
    gap: 10px;

    .button {
        all: unset;
        display: flex;
        align-items: center;
        justify-content: center;
        height: 56px;
        width: 100%;
        border-radius: 8px;
        background: var(--primary-color);
        color: var(--primary-text-color);
        font-family: Montserrat, sans-serif;
        font-size: 15px;
        font-weight: 700;
        cursor: pointer;

        &:hover {
            background: var(--primary-button-hover-color);
            color: var(--primary-text-color);
        }

        &.disabled {
            opacity: 0.5;
            pointer-events: none;
        }
    }
}