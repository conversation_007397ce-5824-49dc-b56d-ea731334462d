import CanvasRendererSDK from "./CanvasRendererSDK";
import {SimplifiedPrintingPart_Align, SimplifiedPrintingPart_Table} from "./SimplifiedPrintingPartData";
import {SimplifiedRenderPart_Common} from "./SimplifiedRenderPart_Common";

export class SimplifiedPart_TableRenderer extends SimplifiedRenderPart_Common{

	private recreateCellsWithMargins(cells : string[], widths: number[], aligns : SimplifiedPrintingPart_Align[], margin : number){
		const newCells : string[] = [];
		const newWidths : number[] = [];
		const newAligns : SimplifiedPrintingPart_Align[] = [];

		const sumAllMargins = 100-margin*(cells.length-1);

		for(let i = 0; i < cells.length; ++i){
			newCells.push(cells[i]!);
			newWidths.push(Math.floor(widths[i]! * sumAllMargins / 100));
			newAligns.push(aligns[i]!);

			if(i !== cells.length-1){
				newCells.push('');
				newWidths.push(margin);
				newAligns.push('left');
			}
		}

		return {
			cells: newCells,
			widths: newWidths,
			aligns: newAligns,
		}
	}

	render(renderer: CanvasRendererSDK, y : number, baseLineHeight: number, textPart : SimplifiedPrintingPart_Table) : number{
		const sumCellWidth = textPart.widthPerCell.reduce((a, b)=>a+b);
		if(sumCellWidth !== 100) throw new Error('part_table_with_not_100');
		for(const cells of textPart.rows){
			if(textPart.textAlignPerCellRows.length !== cells.length) throw new Error('different_length');
		}

		if(textPart.header) {
			if(textPart.textAlignPerCellHeader === undefined) throw new Error('missing_cell_header');
			if(textPart.textAlignPerCellHeader.length !== textPart.header.length) throw new Error('different_length');

			const headerWithMargins = this.recreateCellsWithMargins(textPart.header, textPart.widthPerCell, textPart.textAlignPerCellHeader, textPart.marginBetweenCells);
			y += this.renderTextWithColumns(
				headerWithMargins.cells,
				headerWithMargins.widths,
				y,
				renderer,
				baseLineHeight,
				headerWithMargins.aligns
			);
		}

		y += baseLineHeight * 0.5;

		for (let tableRow of textPart.rows) {
			const rowWithMargins = this.recreateCellsWithMargins(tableRow, textPart.widthPerCell, textPart.textAlignPerCellRows, textPart.marginBetweenCells);
			y += this.renderTextWithColumns(
				rowWithMargins.cells,
				rowWithMargins.widths,
				y,
				renderer,
				baseLineHeight,
				rowWithMargins.aligns
			);
		}

		y += baseLineHeight * 0.5;

		return y;
	}

	protected renderTextWithColumns(
		textColumns: string[],
		sizes: number[],
		y: number,
		renderer: CanvasRendererSDK,
		baseLineHeight: number,
		horizontalAligns: SimplifiedPrintingPart_Align[] = [],
		fontWeights: number[] = []
	): number {
		let rendererConfig = renderer.getConfig();

		let widthWithMargins = rendererConfig.targetSize.width - (this.config.margins.left + this.config.margins.right) * baseLineHeight;
		let xOffsetPos = this.config.margins.left * baseLineHeight;

		if (textColumns.length !== sizes.length) throw new Error("unable to render text tab: columns length doesnt match size length");

		let totalSizes = 0;
		for (let size of sizes) totalSizes += size;
		if (totalSizes > 100) throw new Error("Columns represents more than 100% width (" + totalSizes + ")");

		let offsetColumn = xOffsetPos;
		for (let iColumn = 0; iColumn < textColumns.length; ++iColumn) {
			let columnText = textColumns[iColumn]!;
			let columnSize = sizes[iColumn]!;
			let horizontalAlign = horizontalAligns.length ? horizontalAligns[iColumn] : "left";
			let fontWeight = fontWeights.length ? (fontWeights.length === 1 ? fontWeights[0] : fontWeights[iColumn]) : 400;
			let widthInPixels = Math.floor((columnSize / 100) * widthWithMargins);

			if (columnText.length > 0) {
				renderer.renderText(
					columnText,
					{
						x: offsetColumn,
						y: y,
					},
					{
						width: widthInPixels,
						height: baseLineHeight,
					},
					{
						horizontalAlign: horizontalAlign!,
						fontWeight: "" + fontWeight,
					}
				);
			}

			offsetColumn += widthInPixels;
		}

		return baseLineHeight;
	}

	estimateHeight(baseLineHeight: number, tablePart : SimplifiedPrintingPart_Table): number{
		let height = 0;

		if(tablePart.header){
			height += baseLineHeight;
		}
		height += baseLineHeight*0.5;

		height += tablePart.rows.length * baseLineHeight;
		height += baseLineHeight*0.5;
		return height;
	}

}
