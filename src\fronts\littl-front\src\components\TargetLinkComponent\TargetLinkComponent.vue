<script lang="ts" src='./TargetLinkComponent.ts' />

<style lang="sass" scoped>
@use './TargetLinkComponent.scss' as *
</style>

<template>
	<div class="form">
		<div class="input-group">
			<label for="title">
				<i class="fa-fw fa-regular fa-earth-americas"></i>
                Redirection par défaut
                <span style="float: right"> {{target.targetUri.length}}/{{ linkTargetDefinition.getFieldString('targetUri').maxLength }} </span>
			</label>
			<div class="ui input">
				<input v-model="target.targetUri" type="text" placeholder="Lien" @keydown.enter='enter()'/>
			</div>
		</div>
	</div>
</template>
