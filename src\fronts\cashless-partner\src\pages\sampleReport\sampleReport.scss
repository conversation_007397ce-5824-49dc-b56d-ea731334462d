#sidebar, #top-bar {
    display: none !important;
}

body {
    overflow: visible;
}

#sample-report-page {
    display: flex;
    flex-direction: column;
    gap: 50px;

    .logos {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 40px;

        .logo {
            height: 160px;
        }
    }

    .full-grid {
        grid-column: 1/3;
    }

    >.title {
        display: flex;
        justify-content: center;

        h1 {
            margin: 0;
            font-size: 22px;
        }
    }

    .grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        grid-gap: 40px;
        padding: 0 40px;

        .title {
            font-weight: bold;
            text-align: left;
        }

        .total {
            display: flex;
            justify-content: flex-end;
            font-weight: bold;
            text-decoration: underline;
        }
    }

    .refunds-table {
        display: grid;
        grid-template-columns: 1fr 1fr;
        grid-gap: 20px;
        padding: 0 40px;
        align-items: flex-start;

        h2 {
            font-size: 20px;
        }

        .table-header {
            text-align: center;
        }

        td.chip{
            font-size:10px;
        }
    }
}