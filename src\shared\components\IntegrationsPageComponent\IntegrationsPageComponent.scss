.integrations-page-component {
    .integrations {
        display: grid;
        grid-template-columns: 1fr 1fr 1fr;
        align-items: start;
        grid-gap: 15px;
        padding: 40px;

        .integration {
            display: flex;
            flex-direction: column;
            height: 100%;
            border-radius: 12px;
            border: 1px solid var(--border-color);

            .top {
                display: flex;
                flex-direction: column;
                gap: 6px;
                padding: 15px;
                flex-grow: 2;

                .head {
                    display: flex;
                    justify-content: space-between;
                    margin-bottom: 10px;

                    .logo {
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        flex-shrink: 0;
                        height: 42px;
                        width: 42px;
                        background-size: contain;
                        background-position: center;
                        border-radius: 8px;
                        border: 3px solid white;
                        box-shadow: 0 0 0 1px var(--border-color);

                        i {
                            font-size: 24px;
                        }
                    }

                    .to-website {
                        color: #393939;
                        text-decoration: none;

                        &:hover {
                            text-decoration: underline;
                        }

                        i {
                            font-size: 12px;
                        }
                    }
                }


                .name {
                    font-size: 18px;
                    font-weight: 500;
                }

                .description {
                    font-size: 15px;
                }
            }

            .bottom {
                display: flex;
                justify-content: flex-end;
                align-items: center;
                gap: 10px;
                padding: 8px 15px 8px 15px;
                border-top: 1px solid var(--border-color);
            }
        }
    }
}