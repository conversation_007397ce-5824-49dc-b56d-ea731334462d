<script lang="ts" src="./CustomerFormComponent.ts">
</script>

<style lang="sass">
@use './CustomerFormComponent.scss' as *
</style>

<template>
    <div class="customer-form-component">
        <form-modal-or-drawer
            :state="opened"
            :title="editingCustomer ? 'Modifier le client' : 'Ajouter un client'"
            :subtitle="editingCustomer ? 'Modifier les informations du client' : 'Création d\'un nouveau client manuellement'"
            @close="close()"
        >
            <template v-slot:content>
                <div class="input-group">
                    <label> Prénom </label>

                    <div class="fluid input">
                        <input v-model="customer.firstname" class="fluid" placeholder="Prénom" />
                    </div>
                </div>

                <div class="input-group">
                    <label> Nom </label>

                    <div class="fluid input">
                        <input v-model="customer.lastname" class="fluid" placeholder="Nom" />
                    </div>
                </div>

                <div class="input-group">
                    <label> Email </label>

                    <div class="fluid input">
                        <input v-model="customer.email" class="fluid" placeholder="Email" />
                    </div>
                </div>

                <div class="input-group">
                    <label> Description </label>

                    <div class="fluid input">
                        <textarea v-model="customer.description" class="fluid" placeholder="Description"></textarea>
                    </div>
                </div>

                <div class="form-error" v-if="error">
                    <i class="fa-solid fa-exclamation-circle"></i>
                    <div class="details">
                        <span class="title"> Erreur </span>
                        <span class="description">{{ error }}</span>
                    </div>
                </div>
            </template>

            <template v-slot:buttons>
                <button type="button" class="white button" @click="close()"> Annuler </button>
                <button type="button" class="button" @click="create()">
                    <i v-if="!editingCustomer" class="fa-regular fa-user-plus"></i>
                    <i v-else class="fa-regular fa-pen-line fa-flip-horizontal"></i>
                    {{ editingCustomer ? 'Valider' : 'Créer le client' }}
                </button>
            </template>
        </form-modal-or-drawer>
    </div>
</template>