import {EventEmitter, VisualScopedUuid} from "@groupk/horizon2-core";
import {LocalOrder, LocalOrderTransfer} from "../model/LocalOrder";
import {Toast} from "../components/ToastManagerComponent/ToastManagerComponent";
import {UuidScopeEstablishmentAccount} from "@groupk/mastodon-core";
import {LocalWorkClock} from "../model/LocalWorkClock";

export class AppBus extends EventEmitter<{
	orderSaved: LocalOrder,
	orderTransferSaved: LocalOrderTransfer,
	workClockSaved: LocalWorkClock,
	changeEstablishmentAccount: VisualScopedUuid<UuidScopeEstablishmentAccount>|null,
	displayToast: Toast,
	bluetoothData: string
}>{

}
