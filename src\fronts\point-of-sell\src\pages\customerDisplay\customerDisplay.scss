#customer-display-page {
    display: flex;
    flex-direction: column;
    height: 100%;

    .waiting {
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;

        img {
            height: 50%;
        }
    }

    .current-order {
        height: 100%;
        display: grid;
        grid-template-columns: 2fr 1fr;
    }

    .left, .right {
        padding: 40px 30px;
        height: 100%;
        box-sizing: border-box;
    }

    .left {
        display: flex;
        flex-direction: column;
        gap: 2vw;
        background: white;
        overflow: auto;

        .purchase {
            display: flex;
            align-items: center;
            padding: 2vw 3vw;
            border-radius: 1.6vw;
            background: #F2F4F7;
            gap: 2vw;

            .quantity {
                flex-shrink: 0;
                display: flex;
                width: 5vw;
                height: 5vw;
                justify-content: center;
                align-items: center;
                background: var(--primary-color);
                color: var(--primary-text-color);
                border-radius: 50%;
                font-family: Montserrat, sans-serif;
                font-size: 3vw;
                font-weight: 700;
                line-height: 20px;
            }

            .data {
                flex-grow: 2;
                display: flex;
                flex-direction: column;
                gap: 4px;
                padding: 5px 10px;

                .name {
                    font-family: Montserrat, sans-serif;
                    font-size: 4vw;
                    font-weight: 700;
                }

                .total {
                    font-family: Montserrat, sans-serif;
                    font-size: 3vw;
                }
            }

            &.discounted {
                .quantity {
                    background: #FFF066;
                    color: black;
                }

                .price {
                    text-decoration: line-through;
                }
            }
        }
    }

    .right {
        display: flex;
        flex-direction: column;
        justify-content: flex-end;
        background: #F2F4F7;

        .title {
            font-size: 3vw;
            font-weight: 600;
        }

        .amount {
            font-size: 6vw;
            font-weight: bold;
        }
    }

    .banner {
        height: 3.5vw;
        grid-column: 1/3;
        padding: 2vw 4vw;
        font-size: 2.5vw;
        background: black;
        color: white;
        text-align: end;
    }
}