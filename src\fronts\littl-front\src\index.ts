import {Router, RouterRoute, VueRouteFactory, VueRouteOptions} from "@groupk/horizon2-front";
import "@groupk/vue3-interface-sdk/dist/style.css";
import "./assets/css/global.scss";
import "./assets/css/labels.scss";
import "@groupk/font-awesome-sdk/src/V6/css/pro-v4-font-face.min.css";
import "@groupk/font-awesome-sdk/src/V6/css/pro-v4-shims.min.css";
import "@groupk/font-awesome-sdk/src/V6/css/pro.min.css";
import {createApp} from "vue";
import SidebarView from "./pages/sidebar/sidebar.vue";
import TopBarView from "./pages/topbar/topbar.vue";
import {GetInstance, setExecutionContext, SetInstance} from "@groupk/horizon2-core";
import {VueAuthedRouteFactory} from "../../../shared/routing/AuthedVueJsRoute";
import {GlobalFilters} from "../../../shared/filters/GlobalFilters";
import {AuthStateModel} from "../../../shared/AuthStateModel";
import {Configuration, MainConfig} from "../../../shared/MainConfig";
import {AppState} from "../../../shared/AppState";
import {EstablishmentUrlBuilder} from "../../../shared/utils/EstablishmentUrlBuilder";
import {CashlessEstablishmentActivationApiIn, LittlAppId} from "@groupk/mastodon-core";
import {CashlessEstablishmentRepository} from "../../../shared/repositories/CashlessEstablishmentRepository";
import {LittlEstablishmentRepository} from "./repositories/LittlEstablishmentRepository";
import assetsPlugin from "../../../shared/routing/assetsPlugin";

let mainConfig: MainConfig = GetInstance(MainConfig);

mainConfig.init().then(async function(configFromFile: Configuration) {
    if(configFromFile.develop) {
        setExecutionContext({
            debug: true
        });
    }

    const appState = GetInstance(AppState);
    appState.platformId = 'Littl';

    const authStateModel = new AuthStateModel(configFromFile.mastodonApiEndpoint, false);
    try {
        await authStateModel.getState(true);
    } catch(err){}
    SetInstance(AuthStateModel, authStateModel);

    startApp(configFromFile);
});

async function startApp(config: Configuration) {
    let router = new Router<RouterRoute>({
        prefix: '/',
        registerGlobalInterceptor: true,
        container: 'mainRouterContainer',
        oldContainer: 'oldRouterContainer',
        disableRouteTriggerIfIdentical: true
    });

    router.hookOn('jsError', (err) => {
        console.error(err);
        return err;
    });
    router.hookOn('routeNotFound', (err) => {
        console.error('route not found', err);
        return err;
    });

    let uuidRegex = /([0-9a-f]{8}\-[0-9a-f]{4}\-[0-9a-f]{4}\-[0-9a-f]{4}\-[0-9a-f]{12})/;
    const vueRouteOptions: Partial<VueRouteOptions> = {
        filters: GlobalFilters,
        hookAppCreated(app) {
            app.use(assetsPlugin);
        },
    };

    router.addRoute({regex: new RegExp(/establishment\//.source + uuidRegex.source + /\/links$/.source), loader: () => import('./pages/links/links.vue').then((vue) => VueAuthedRouteFactory(vue.default, vueRouteOptions))});
    router.addRoute({regex: new RegExp(/establishment\//.source + uuidRegex.source + /\/auth/.source), loader: () => import('./pages/auth/auth.vue').then((vue) => VueRouteFactory(vue.default, vueRouteOptions))});
    router.addRoute({regex: new RegExp(/establishment\//.source + uuidRegex.source + /\/upgrade/.source), loader: () => import('./pages/upgrade/upgrade.vue').then((vue) => VueAuthedRouteFactory(vue.default, vueRouteOptions))});
    router.addRoute({regex: new RegExp(/establishment\//.source + uuidRegex.source + /.*/.source), loader: () => import('./pages/links/links.vue').then((vue) => VueAuthedRouteFactory(vue.default, vueRouteOptions))});
    router.addRoute({regex: new RegExp(/.*/.source), loader: () => import('./pages/casRedirect/casRedirect.vue').then((vue) => VueRouteFactory(vue.default, vueRouteOptions))});

    SetInstance(Router, router);

    const isEnabled = await EstablishmentUrlBuilder.isPlatformEnabled(LittlAppId);
    if(isEnabled.establishmentUid && !isEnabled.enabled) {
        const littlEstablishmentRepository = GetInstance(LittlEstablishmentRepository);
        await littlEstablishmentRepository.callContract('enable', {establishmentUid: isEnabled.establishmentUid}, new CashlessEstablishmentActivationApiIn());
    }

    router.updateCurrentPageFromCurrentLocation().then(async () => {
        if(!window.location.href.includes('auth')) {

            const sidebar = createApp(SidebarView);
            sidebar.mount("#sidebar");

            const topBar = createApp(TopBarView);
            topBar.mount("#top-bar");
        }
    });
}