import {Component, Prop, Vue} from "vue-facing-decorator";
import {DropdownComponent, DropdownValue, FormModalOrDrawerComponent} from "@groupk/vue3-interface-sdk";
import {AutoWired} from "@groupk/horizon2-core";
import {PaymentMethodsRepository} from "../../../../../shared/repositories/PaymentMethodsRepository";
import {PaymentMethodApiOut, PaymentMethodCreateApiIn, PaymentMethodUpdateApiIn} from "@groupk/mastodon-core";
import {PaymentMethodType} from "@groupk/mastodon-core";
import {AppState} from "../../../../../shared/AppState";

@Component({
	components: {
		'form-modal-or-drawer': FormModalOrDrawerComponent,
		'dropdown': DropdownComponent,
	},
	emits: ['updated', 'close']
})
export default class PaymentMethodFormComponent extends Vue {
	@Prop() editingPaymentMethod!: PaymentMethodApiOut;

	name: string = '';
	type: PaymentMethodType|null = null;

	opened: boolean = false;
	loading: boolean = false;
	error: string|null = null;

	@AutoWired(PaymentMethodsRepository) accessor paymentMethodsRepository!: PaymentMethodsRepository;
	@AutoWired(AppState) accessor appState!: AppState;

	mounted() {
		if(this.editingPaymentMethod) {
			this.name = this.editingPaymentMethod.name;
			this.type = this.editingPaymentMethod.type;
		}
		setTimeout(() => this.opened = true, 0);
	}

	close() {
		this.opened = false;
		setTimeout(() => this.$emit('close'), 300);
	}

	getTypeDropdownValues(): DropdownValue[] {
		return [{
			name: 'Liquide',
			value: PaymentMethodType.CASH
		}, {
			name: 'Carte bancaire',
			value: PaymentMethodType.CREDITCARD
		}, {
			name: 'Web',
			value: PaymentMethodType.WEB
		}, {
			name: 'Carte cadeau',
			value: PaymentMethodType.GIFTCARD
		}, {
			name: 'Chèque vancances',
			value: PaymentMethodType.HOLIDAY_VOUCHER
		}, {
			name: 'Ticket restaurant',
			value: PaymentMethodType.LUNCH_VOUCHER
		}, {
			name: 'Autre',
			value: PaymentMethodType.OTHER
		}];
	}

	async create() {
		if(!this.editingPaymentMethod)  {
			if(!this.type) {
				this.error = 'Le type est requis';
				return
			}
			this.loading = true;

			const apiIn = new PaymentMethodCreateApiIn({
				name: this.name,
				type: this.type,
			});

			const result = await this.paymentMethodsRepository.callContract('create', {establishmentUid: this.appState.requireUrlEstablishmentUid()}, apiIn);
			if(result.isSuccess()) {
				this.$emit('updated', result.success());
				this.$emit('close');
			} else {
				this.error = 'Erreur inconnue';
			}
		} else {
			this.loading = true;

			const apiIn = new PaymentMethodUpdateApiIn({
				name: this.name,
			});

			const result = await this.paymentMethodsRepository.callContract('update', {establishmentUid: this.appState.requireUrlEstablishmentUid(), methodUid: this.editingPaymentMethod.uid}, apiIn);
			if(result.isSuccess()) {
				this.$emit('updated', result.success());
				this.$emit('close');
			} else {
				this.error = 'Erreur inconnue';
			}
		}
		this.loading = false;
	}
}