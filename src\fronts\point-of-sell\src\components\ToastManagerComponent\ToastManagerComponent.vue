<script lang="ts" src="./ToastManagerComponent.ts">
</script>

<style lang="sass" scoped>
@import './ToastManagerComponent.scss'
</style>

<template>
    <div class="toast-manager-component" :class="{'display-all': displayAll}">
        <div class="toast" @click="toggleDisplayAll()" :class="`${data.toast.color} position-${state.length - index > 3 ? 'big' : (state.length - index)}`" v-for="(data, index) of state">

           <div class="left">
               <div v-if="data.toast.closable" class="close" @click.stop="removeToast(index)">
                   <i class="fa-solid fa-xmark"></i>
               </div>
               <div class="content">
                   <span class="title"> {{ data.toast.title }} </span>
                   <span class="description"> {{ data.toast.description }} </span>
               </div>
           </div>

            <button
                v-if="data.toast.action && Array.isArray(data.toast.action)"
                class="white button" @click.stop="clickedAction(action, index, data.toast)"
                v-for="action of data.toast.action"
            >
                <i v-if="action.icon" :class="action.icon"></i>
                {{ action.name }}
            </button>
            <button
                v-else-if="data.toast.action"
                class="white button" @click.stop="clickedAction(data.toast.action, index, data.toast)"
            >
                <i v-if="data.toast.action.icon" :class="data.toast.action.icon"></i>
                {{ data.toast.action.name }}
            </button>
        </div>
    </div>
</template>