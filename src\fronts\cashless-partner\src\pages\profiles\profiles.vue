<script lang="ts" src="./profiles.ts">
</script>

<style scoped lang="sass">
@use './profiles.scss' as *
</style>

<template>
    <div id="profiles-page" class="page">
        <forbidden-message v-if="forbidden"></forbidden-message>
        <layout v-else :drawerOpened="selectedProfile">
            <template v-slot:content>
                <content-header
                    :parameters="headerParameters"
                    @search="profilesSearch = $event"
                ></content-header>

                <div class="page-content">

                    <div class="top-bar">
                        <div class="button-group">
                            <button class="button" :class="{active: tableDisplay}" @click="tableDisplay = !tableDisplay">
                                <i class="fa-regular fa-grid-2"></i>
                            </button>
                            <button class="button" :class="{active: !tableDisplay}" @click="tableDisplay = !tableDisplay">
                                <i class="fa-regular fa-list-ul"></i>
                            </button>
                        </div>
                    </div>

                    <div class="loading-container" v-if="loading">
                        <div class="loader"></div>
                    </div>

                    <template v-else>
                        <div class="profiles" v-if="tableDisplay">
                            <div class="profile" v-for="profile of getFilteredProfiles()">
                                <div class="top">
                                    <span class="title"> {{ profile.name }} </span>
                                    <div class="employees">
                                    </div>
                                </div>
                                <div class="datas">
                                    <div class="data">
                                        <span class="amount"> {{ getProfileDevices(profile).length }} </span>
                                        <span class="title"> Appareils </span>
                                    </div>
                                    <div class="separator"></div>
                                    <div class="data">
                                        <span class="amount"> {{ getCategoriesInProfile(profile).length }} </span>
                                        <span class="title"> Catégories </span>
                                    </div>
                                </div>

                                <div class="buttons">
                                    <a :href="getEditUrl(profile)" class="fluid grey button">
                                        <i class="fa-regular fa-cog"></i>
                                        Configurer
                                    </a>
<!--                                    <button class="tertiary icon button">-->
<!--                                        <i class="fa-regular fa-ellipsis-vertical"></i>-->
<!--                                    </button>-->
                                </div>
                            </div>

                            <div class="profile new" @click="showCreationModal = true">
                                <i class="fa-regular fa-circle-plus"></i>
                                <span class="add"> Ajouter un point de vente </span>
                            </div>
                        </div>

                        <div v-else>
                            <div class="table-scroll-wrapper">
                                <table class="data-table">
                                    <thead>
                                    <tr>
                                        <td> Nom </td>
                                        <td class="no-break"> Nombre d'appareils </td>
                                        <td class="no-break"> Nombre de catégories</td>
                                        <td> </td>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <tr v-if="getFilteredProfiles().length === 0">
                                        <td class="empty-row" colspan="100%"> Aucune commande </td>
                                    </tr>
                                    <tr v-for="profile of getFilteredProfiles()">
                                        <td class="name"> {{ profile.name }} </td>
                                        <td> {{ getProfileDevices(profile).length }} </td>
                                        <td> {{ getCategoriesInProfile(profile).length }} </td>
                                        <td>
                                            <a :href="getEditUrl(profile)" class="fluid grey button">
                                                <i class="fa-regular fa-cog"></i>
                                                Configurer
                                            </a>
                                        </td>
                                    </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </template>
                </div>
            </template>

            <template v-slot:right>

            </template>
        </layout>

        <profile-form
            v-if="showCreationModal"
            :state="showCreationModal"
            :establishment-uid="establishmentUid"
            :editing-profile="editingProfile"
            @created="createdProfile($event)"
            @updated="updatedProfile($event)"
            @close="showCreationModal = false; editingProfile = null"
        ></profile-form>

        <import-profile-modal
            v-if="showProfilesImportModal"
            @imported-profiles="importedProfiles($event)"
            @close="showProfilesImportModal = false"
        ></import-profile-modal>

        <cashless-establishment-form
            v-if="showSettingsModal"
            :editing-cashless-establishment="cashlessEstablishment"
            @updated="cashlessEstablishment = $event"
            @close="showSettingsModal = false"
        ></cashless-establishment-form>
    </div>
</template>