#package-scan-page {
    height: 100%;
    background: #f0f2f3;
    overflow: auto;

    .loading-container {
        margin: 0;
        height: 100%;
    }

    .container {
        display: flex;
        flex-direction: column;
        gap: 20px;
        padding: 20px;
        max-width: 700px;
        margin: auto;

        h3 {
            font-size: 18px;
            font-weight: 600;
            margin: 10px 0 0 0;
        }

        .actions {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            grid-gap: 20px;

            .action-group {
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                gap: 4px;

                .action {
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;
                    background: white;
                    border-radius: 20px;
                    cursor: pointer;
                    width: 80%;
                    aspect-ratio: 1/1;

                    i {
                        font-size: 22px;
                    }
                }

                .text {
                    font-size: 14px;
                }
            }
        }

        .packages {
            display: flex;
            flex-direction: column;
            gap: 10px;

            .package {
                display: flex;
                align-items: center;
                justify-content: space-between;
                background: white;
                padding: 30px;
                border-radius: 20px;

                &.done {
                    background: rgba(0, 128, 0, 0.6);
                    color: white;
                }

                .name {
                    font-size: 18px;
                    font-weight: 500;
                }

                i {
                    font-size: 18px;
                }
            }
        }
    }
}