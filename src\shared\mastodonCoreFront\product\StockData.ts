import {StockSimpleApiIn, StockSimpleApiOut, StockType} from "@groupk/mastodon-core";
import {UuidScopeProduct_stockSimple} from "@groupk/mastodon-core";
import {VisualScopedUuid} from "@groupk/horizon2-core";

export class StockData {
    uid: VisualScopedUuid<UuidScopeProduct_stockSimple>|null = null;
    currentQuantity: number = 0;
    minQuantity: number|null = null;
    displayThreshold: number|null = null;


    constructor(stockApiOut: StockSimpleApiOut|null = null) {
        if(stockApiOut) {
            this.currentQuantity = stockApiOut.currentQuantity;
            this.displayThreshold = stockApiOut.displayThreshold;
            this.minQuantity = stockApiOut.minQuantity;
            this.uid = stockApiOut.uid;
        }
    }

    toApi() {
        return new StockSimpleApiIn({
            type: StockType.SIMPLE,
            initialQuantity: this.currentQuantity,
            displayThreshold: this.displayThreshold,
            minQuantity: this.minQuantity
        });
    }
}