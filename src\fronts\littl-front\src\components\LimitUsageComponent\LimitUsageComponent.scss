.limit-usage-component {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 20px;
    padding: 25px 20px;
    border: 1px solid var(--border-color);
    background: linear-gradient(197deg, rgba(4, 114, 77, 0.18) 5.97%, rgba(217, 217, 217, 0.00) 45.77%);
    border-radius: 12px;
    margin: 0 -10px;

    .header {
        display: flex;
        flex-direction: column;
        gap: 5px;
        text-align: center;

        .title {
            font-size: 16px;
            font-weight: 700;
        }

        .subtitle {
            font-size: 12px;
        }
    }

    .quota {
        display: flex;
        flex-direction: column;
        align-items: stretch;
        justify-content: flex-start;
        width: 100%;
        gap: 5px;
        box-sizing: border-box;

        span {
            font-size: 13px;
        }

        .bar {
            height: 7px;
            border-radius: 8px;
            background: rgba(4, 114, 77, 0.12);
            width: 100%;

            .progress {
                height: 100%;
                border-radius: 8px;
                background: #04724D;

                &.links {
                    width: 0%;
                }

                &.qr {
                    width: 66%;
                }

                transition: width .5s cubic-bezier(0.22, 1, 0.36, 1);

                &.red{
                    background: var(--error-color);
                }
            }
        }
    }

    button {
        margin-top: 10px;
    }
}