<script lang="ts" src="./CashKeeperActionsComponent.ts">
</script>

<style lang="sass" scoped>
@import './CashKeeperActionsComponent.scss'
</style>

<template>
    <div class="empty-cash-keeper-component">
        <modal :closable="true" :state="true" @close="close()">
            <div class="modal">
                <template v-if="!filling">
                    <h3> Sélectionnez un CashKeeper </h3>

                    <div class="cash-keepers">
                        <button
                            class="button"
                            :class="{active: selectedCashKeeper?.settings === settings}"
                            v-for="settings of cashKeeperSettings"
                            @click="selectCashKeeper(settings)"
                        >
                            {{ settings.name }}
                            <span class="small"> {{ settings.address }} </span>
                        </button>
                    </div>

                    <div class="loading-container" v-if="connecting || (selectedCashKeeper && !selectedCashKeeper.denominations)">
                        <div class="loader"></div>
                    </div>

                    <template v-else-if="selectedCashKeeper !== null">

                        <h4> Contenu </h4>

                        <div class="cash-content">
                            <div class="amounts">
                                <div class="value"> x{{ getDenominationLevel(2) }} </div>
                                <div class="value"> x{{ getDenominationLevel(5) }} </div>
                                <div class="value"> x{{ getDenominationLevel(10) }} </div>
                                <div class="value"> x{{ getDenominationLevel(20) }} </div>
                                <div class="value"> x{{ getDenominationLevel(50) }} </div>
                                <div class="value"> x{{ getDenominationLevel(100) }} </div>
                            </div>

                            <div class="denominations">
                                <div class="value"> 0.2€</div>
                                <div class="value"> 0.5€</div>
                                <div class="value"> 0.10€</div>
                                <div class="value"> 0.20€</div>
                                <div class="value"> 0.50€</div>
                                <div class="value"> 1.00€</div>
                            </div>
                        </div>

                        <div class="cash-content">
                            <div class="amounts">
                                <div class="value"> x{{ getDenominationLevel(200) }} </div>
                                <div class="value"> x{{ getDenominationLevel(500) }} </div>
                                <div class="value"> x{{ getDenominationLevel(1000) }} </div>
                                <div class="value"> x{{ getDenominationLevel(2000) }} </div>
                                <div class="value"> x{{ getDenominationLevel(5000) }} </div>
                                <div class="value"> x{{ getDenominationLevel(10000) }} </div>
                            </div>

                            <div class="denominations">
                                <div class="value"> 2.00€</div>
                                <div class="value"> 5.00€</div>
                                <div class="value"> 10.00€</div>
                                <div class="value"> 20.00€</div>
                                <div class="value"> 50.00€</div>
                                <div class="value"> 100.00€</div>
                            </div>
                        </div>

                        <div class="denominations-total"> Total {{ $filters.Money(getDenominationsTotal()) }}</div>

                        <h4> Actions </h4>
                        <div class="actions">
                            <button class="button" @click="emptyCashKeeper()" :class="{loading: emptying, disabled: emptying||filling||getDenominationsTotal() === 0}">
                                <i class="fa-regular fa-empty-set"></i>
                                Vider le CashKeeper
                            </button>
                            <button class="button" :class="{loading: filling, disabled: emptying||filling}" @click="fillCashKeeper()">
                                <i class="fa-regular fa-coins"></i>
                                Remplir le CashKeeper
                            </button>
                        </div>
                    </template>
                </template>

                <template v-else-if="selectedCashKeeper !== null && filling">
                    <h3> Remplissage de {{ selectedCashKeeper.settings.name }} </h3>

                    <div class="total"> {{ $filters.Money(fillingAmountIn) }} </div>

                    <div class="buttons">
                        <button class="white button" @click="cancelFill()"> Annuler et rendre l'argent </button>
                        <button class="button" @click="validateFill()"> Confirmer le remplissage </button>
                    </div>

                </template>

                <div class="form-error" v-if="error">
                    <i class="fa-regular fa-circle-exclamation"></i>
                    <div class="details">
                        <span class="title">
                            Une erreur est survenue
                        </span>
                        {{ error }}
                    </div>
                </div>
            </div>
        </modal>
    </div>
</template>