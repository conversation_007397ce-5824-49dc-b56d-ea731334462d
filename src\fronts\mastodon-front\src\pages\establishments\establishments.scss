.dropdown.small.white.button {
    padding: 0;

    &.opened:hover {
        background: white;
    }
}

.table-scroll-wrapper {
    overflow: auto;
}

.status {
    display: flex;
    justify-content: center;

    .square {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 22px;
        width: 22px;
        border-radius: 4px;
        box-sizing: border-box;

        &.green {
            background: #1099FD;
            color: white !important;
        }

        i {
            font-size: 14px;
        }
    }

}

.selected-establishment {
    display: flex;
    flex-direction: column;
    gap: 20px;
    padding: 40px;

    .close {
       margin: 0;
    }

    @media (max-width: 900px) {
        padding: 20px;
    }

    .content {
        display: flex;
        flex-direction: column;
        gap: 20px;

        .iot-devices {
            display: flex;
            flex-direction: column;
            gap: 10px;
            padding: 15px;
            border-radius: 8px;
            background: var(--secondary-hover-color);

            .top {
                display: flex;
                align-items: center;
                gap: 5px;

                .title {
                    font-weight: 600;
                }
            }
        }


    }
}