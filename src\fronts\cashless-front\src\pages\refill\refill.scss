#refill-page {
    display: flex;
    flex-direction: column;
    height: 100%;
    background: white;
    padding: 20px;
    box-sizing: border-box;
    max-width: 500px;
    margin: auto;

    .fixed-payment-modal {
        position: fixed;
        inset: 0;
        z-index: 500;
        background: white;
        padding: 20px;
    }

    .top {
        flex-grow: 2;
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        gap: 20px;

        span {
            font-size: 16px;
            line-height: 24px;
        }
    }

    .amount-container {
        display: flex;
        gap: 5px;
        font-size: 80px;
        font-weight: bold;
        margin-bottom: 20px;
        margin-left: 20px;

        &.small {
            font-size: 64px;
        }

        &.tiny {
            font-size: 50px;
        }

        .currency {
            opacity: 0.5;
        }
    }

    .prefilled-amounts {
        display: grid;
        grid-template-columns: 1fr 1fr 1fr;
        grid-gap: 10px;
        font-size: 16px;
        text-align: center;

        .amount {
            text-align: center;
            justify-content: center;
            font-weight: bold;

            &:hover {
                background: #EAF8FC;
            }
        }
    }

    .bottom {
        display: flex;
        flex-direction: column;
        gap: 20px;
    }

    .mobile-keyboard {
        display: grid;
        grid-template-columns: 1fr 1fr 1fr;
        text-align: center;
        grid-gap: 10px;
        font-size: 18px;
        font-weight: bold;
        box-sizing: border-box;

        div {
            padding: 15px 0;
            box-sizing: border-box;
            border-radius: 6px;
            cursor: pointer;

            &:hover {
                background: #F2F2F2;
            }
        }
    }

    &.small-device {
        .mobile-keyboard div {
            padding: 12px 0;
        }
    }

    .refill-button {
        display: flex;
        align-items: center;
        justify-content: center;
        background: #2AB9D9;
        height: 50px;
        color: white;
        font-size: 16px;
        font-weight: bold;
        border-radius: 100px;
        cursor: pointer;

        &:hover {
            filter: brightness(0.96);
        }

        &.disabled {
            background: #F2F2F2;
            color: rgba(0, 0, 0, 0.47);
            font-weight: normal;
            pointer-events: none;
        }
    }

    .error {
        color: var(--error-color);
        text-align: center;
        font-weight: 500;
    }

}