<script lang="ts" src="./MobileOrderListComponent.ts">
</script>

<style lang="sass" scoped>
@import './MobileOrderListComponent.scss'
</style>

<template>
    <div class="mobile-order-list-component">
        <div class="buttons">
            <div class="button-group">
                <button :class="{selected: sort === 'DATE'}" @click="sort = 'DATE'; sortAndChunkOrders()">
                    <i class="fa-regular fa-arrow-down-9-1"></i>
                    Date
                </button>
                <button :class="{selected: sort === 'NOT_PAYED'}" @click="sort = 'NOT_PAYED'; sortAndChunkOrders()">
                    <i class="fa-regular fa-arrow-down-wide-short"></i>
                    Impayé
                </button>
                <button :class="{selected: sort === 'ALL'}" @click="sort = 'ALL'; sortAndChunkOrders()">
                    <i class="fa-regular fa-eye"></i>
                    Tout voir
                </button>
            </div>
        </div>

        <div class="form-error" v-if="!hasValidToken">
            <i class="fa-solid fa-exclamation-circle"></i>
            <div class="details">
                <span class="title"> Compte déconnecté </span>
                <span class="description"> Votre compte a été déconnecté et les commandes ne peuvent plus être synchronisées. </span>
            </div>
            <button class="red button" @click="goToCas()"> Se reconnecter </button>
        </div>

        <div class="empty" v-if="chunkedOrders.length === 0">
            Aucune commande
        </div>

        <div class="pages" v-if="pagesNumber() > 1">
            <span>
                {{ page * quantityPerPage - quantityPerPage + 1 }}-{{ Math.min(page * quantityPerPage, totalOrders()) }}
                sur {{ totalOrders() }}
            </span>
            <div class="small white button" :class="{disabled: page === 1}" @click="page--">
                <i class="fa-regular fa-arrow-left" ></i>
            </div>
            <div class="small white button" :class="{disabled: page === Math.ceil(totalOrders() / quantityPerPage)}" @click="page++">
                <i class="fa-regular fa-arrow-right"></i>
            </div>
        </div>

        <div class="orders">
            <mobile-order-card
                :pos-state="posState"
                :local-order="localOrder"
                :local-order-transfer="orderTransfers.find((transfer) => transfer.order.uid === localOrder.uid && !transfer.canceled)"
                v-for="localOrder of chunkedOrders[page - 1]"
                @click="selectOrder(localOrder.uid)"
                @unlock-transfer="unlockTransfer = $event"
            ></mobile-order-card>
        </div>

        <div class="pages" v-if="pagesNumber() > 1">
            <span>
                {{ page * quantityPerPage - quantityPerPage + 1 }}-{{ Math.min(page * quantityPerPage, totalOrders()) }}
                sur {{ totalOrders() }}
            </span>
            <div class="small white button" :class="{disabled: page === 1}" @click="page--">
                <i class="fa-regular fa-arrow-left" ></i>
            </div>
            <div class="small white button" :class="{disabled: page === Math.ceil(totalOrders() / quantityPerPage)}" @click="page++">
                <i class="fa-regular fa-arrow-right"></i>
            </div>
        </div>

        <mobile-bottom-cart
            v-if="getPurchasesQuantity > 0"
            :pos-state="posState"
            :opened="animateMobileCart"
            @click="toggleMobileCart()"
        ></mobile-bottom-cart>
    </div>
    <div class="right" :class="{opened: showMobileCart && posState.currentOrder}">
        <order-sidebar :pos-state="posState" :pos-profile="posProfile" @close="closeMobileCart()"></order-sidebar>
    </div>

    <unlock-transfer-modal
        v-if="unlockTransfer"
        :local-order-transfer="unlockTransfer"
        @unlocked="unlockedTransfer($event)"
        @close="unlockTransfer = null"
    ></unlock-transfer-modal>

</template>