<script lang="ts" src="./ImportProductModalComponent.ts">
</script>

<style lang="sass" scoped>
@use './ImportProductModalComponent.scss' as *
</style>

<template>
    <modal-or-drawer :state="opened" @close="close()">
        <table-import
            v-if="opened"
            :expected-columns="csvImportProductsColumns"
            :entity-type="csvImportEntityType"
            :entity-builder="(a: any, b: any)=>productImportExportHelper.entityBuilder(a, b,products??[])"
            :entity-saver="(a: any)=>productImportExportHelper.entitySaver(a)"
            @close="close()"
        ></table-import>
    </modal-or-drawer>
</template>