.mobile-order-card-component {
    position: relative;
    display: flex;
    flex-direction: column;
    border-radius: 8px;
    background: white;
    border: 1px solid transparent;

    &.selected {
        border: 1px solid var(--pos-color);
    }

    .top {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 20px;
        border-bottom: 1px solid #D8D8D8;

        .date {
            font-weight: bold;
            font-size: 15px;

            .ago {
                font-weight: normal;
                font-size: 14px;
            }
        }
    }

    .bottom {
        display: flex;
        gap: 5px;
        padding: 12px 20px;

        .data {
            flex-shrink: 0;
            display: flex;
            flex-direction: column;
            align-items: stretch;
            gap: 4px;
            padding: 6px 10px;

            .value {
                font-size: 15px;
                font-weight: 600;
            }

            .title {
                font-size: 13px;
            }
        }

        .actions {
            display: flex;
            justify-content: flex-end;
            gap: 5px;
            flex-grow: 2;

            .quick-edit {
                display: flex;
                align-items: center;
                justify-content: flex-end;
                padding: 10px;

                i {
                    font-size: 18px;
                }
            }
        }
    }

    .reservit-logo {
        height: 48px;
    }

    .transferring-hover {
        position: absolute;
        inset: 0;
        display: flex;
        justify-content: center;
        align-items: center;
        background: rgba(255, 255, 255, 0.9);
        font-size: 18px;
        font-weight: 600;
        color: black;
        border-bottom: 1px solid var(--border-color);

        .dropdown-button-component {
            position: absolute;
            right: 15px;
        }
    }
}