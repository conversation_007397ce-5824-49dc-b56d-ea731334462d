import {PosState} from "../model/PosState";
import {AutoWired, UuidUtils} from "@groupk/horizon2-core";
import {LocalOrderTransferRepository} from "../repositories/LocalOrderTransferRepository";
import {OrderRepository} from "../../../../shared/repositories/OrderRepository";
import {
    OrderTransferApiIn, OrderTransferApiOut,
    OrderTransferItemApiOut, ProductOrderJsonRpcContract, uuidScopeIot_deviceApp,
    PurchaseItemApiOut
} from "@groupk/mastodon-core";
import {LocalOrder, LocalOrderTransfer} from "../model/LocalOrder";
import {LocalOrderRepository} from "../repositories/LocalOrderRepository";
import {AppBus} from "../config/AppBus";
import {WebSocketManager} from "./WebSocketManager";
import {AuthStateModel} from "../../../../shared/AuthStateModel";
import {MainConfig} from "../../../../shared/MainConfig";
import {ErrorHandler} from "./ErrorHandler";
import {randomUUID} from "@groupk/horizon2-front";

export class OrderTransferWorker {
    private posState: PosState;

     @AutoWired(OrderRepository) accessor orderRepository!: OrderRepository;
     @AutoWired(LocalOrderRepository) accessor localOrderRepository!: LocalOrderRepository;
     @AutoWired(LocalOrderTransferRepository) accessor localOrderTransferRepository!: LocalOrderTransferRepository;
     @AutoWired(AppBus) accessor appBus!: AppBus;
     @AutoWired(MainConfig) accessor mainConfig!: MainConfig;
     @AutoWired(AuthStateModel) accessor authStateModel!: AuthStateModel;
     @AutoWired(ErrorHandler) accessor errorHandler!: ErrorHandler;

     private async routeRequestedTransfer(orderTransfer: OrderTransferApiOut){
         const correspondingLocalOrder = await this.localOrderRepository.findOne(orderTransfer.orderUid);

         if(!correspondingLocalOrder || correspondingLocalOrder.transferred || correspondingLocalOrder.error) {
             // Cancel transfer request as i dont own the order anymore
            this.cancelTransfer(orderTransfer);
         } else if(!correspondingLocalOrder.synced) {
             // TODO WARNING => sync might not be required ! (as the order is sent with commit)
             const fetchOrderWhileNotSynced = async () => {
                 const correspondingLocalOrder = await this.localOrderRepository.findOne(orderTransfer.orderUid);
                 if(correspondingLocalOrder && correspondingLocalOrder.synced) {
                     await this.acceptTransfer(orderTransfer, correspondingLocalOrder);
                 } else {
                     setTimeout(async () => {
                         await fetchOrderWhileNotSynced();
                     }, 1000)
                 }
             }

             await fetchOrderWhileNotSynced();
         } else if(this.posState.currentOrder && this.posState.currentOrder.uid === correspondingLocalOrder.uid) {
             this.appBus.emit('displayToast', {
                 title: `Une caisse demande à récupérer la commande en cours`,
                 description: `Acceptez-vous d'envoyer la commande à la caisse ? La commande sera fermée.`,
                 color: 'orange',
                 closable: false,
                 duration: 120000,
                 action: [{
                     name: 'Refuser',
                     icon: 'fa-regular fa-xmark',
                     callback: () => this.cancelTransfer(orderTransfer)
                 }, {
                     name: 'Envoyer',
                     icon: 'fa-regular fa-paper-plane',
                     callback: () => this.acceptTransfer(orderTransfer, correspondingLocalOrder)
                 }]
             });
         } else {
             await this.acceptTransfer(orderTransfer, correspondingLocalOrder);
         }
     }

     private async markOrderAsSent(orderTransfer: OrderTransferApiOut) {
         try {
             const pendingTransfers = await this.localOrderTransferRepository.findAllNotDone();
             const localTransfer = pendingTransfers.find((transfer) => transfer.transferUid === orderTransfer.uid);
             if(localTransfer) {
                 await this.updateLocalTransferFromServerTransfer(localTransfer, orderTransfer);
             }
         } catch(err) {
             this.errorHandler.logEverywhere({
                 title: 'Could not save local transfer',
                 description: 'Updated local transfer indexed db save failed'
             });
         }
     }

     private async updateLocalTransferFromServerTransfer(localOrderTransfer: LocalOrderTransfer, serverOrderTransfer: OrderTransferApiOut) {
         localOrderTransfer.transferUid = serverOrderTransfer.uid;
         localOrderTransfer.confirmedFromTargetDatetime = serverOrderTransfer.confirmedFromTargetDatetime;
         localOrderTransfer.fetchedFromTargetDatetime = serverOrderTransfer.fetchedFromTargetDatetime;
         localOrderTransfer.canceled = serverOrderTransfer.cancelDatetime !== null;

         await this.localOrderTransferRepository.save(localOrderTransfer);

         if(serverOrderTransfer.fetchedFromTargetDatetime) {
             const localOrder = await this.localOrderRepository.findOne(localOrderTransfer.order.uid);
             if(localOrder) {
                 localOrder.transferred = true;
                 await this.localOrderRepository.save(localOrder);
             }
         }
     }

    constructor(posState: PosState) {
        this.posState = posState;

        const webSocketManager = new WebSocketManager(posState);

        webSocketManager.on('reconnect', async () => {
            const contract = webSocketManager.connection.getForContract(ProductOrderJsonRpcContract);

            await contract.onNotify('product_orderTransfer_updated', async (orderTransfer) => {
                if(orderTransfer.cancelDatetime) return;

                if(orderTransfer.originIotDevice === UuidUtils.scopedToVisual(this.posState.iotDevice.uid, uuidScopeIot_deviceApp) && orderTransfer.creatorIotDevice === UuidUtils.scopedToVisual(this.posState.iotDevice.uid, uuidScopeIot_deviceApp)) {
                    // we are sending the order (without asking)
                    if(orderTransfer.fetchedFromTargetDatetime){
                        this.markOrderAsSent(orderTransfer);
                    }
                } else if(orderTransfer.originIotDevice !== UuidUtils.scopedToVisual(this.posState.iotDevice.uid, uuidScopeIot_deviceApp) && orderTransfer.creatorIotDevice === UuidUtils.scopedToVisual(this.posState.iotDevice.uid, uuidScopeIot_deviceApp)) {
                    // we are sending the order WITH asking

                    // identify the "first" time we receive the request / not completed
                    // => we have to commit the order
                    if(orderTransfer.receivedFromCreatorDatetime === null) {
                        this.routeRequestedTransfer(orderTransfer);
                    } else if(orderTransfer.fetchedFromTargetDatetime) {
                        this.markOrderAsSent(orderTransfer);
                    }
                } else if(orderTransfer.targetIotDevice === UuidUtils.scopedToVisual(this.posState.iotDevice.uid, uuidScopeIot_deviceApp)) {
                    // we are receiving the order
                    if(
                        orderTransfer.receivedFromCreatorDatetime !== null &&
                        orderTransfer.fetchedFromTargetDatetime === null
                    ) await this.synchroniseReceivedTransfers();
                }

            });

            this.synchronize().catch();
        });
    }

    async synchronize() {
         try {
             await this.synchroniseCreatedTransfers();
         } catch(err) {}

        try {
            await this.synchroniseReceivedTransfers();
        } catch(err) {}
    }

    async synchroniseCreatedTransfers() {
        const pendingTransfers = await this.localOrderTransferRepository.findAllNotDone();
        for(const transfer of pendingTransfers) {
            // fetchedFromTargetDatetime could have been s
            if(transfer.fetchedFromTargetDatetime !== null) continue;

            let updatedTransfer: OrderTransferItemApiOut|null = null;

            if(transfer.transferUid) {
                try {
                    updatedTransfer = (await this.orderRepository.callContract('getTransfer', {
                        establishmentUid: this.posState.establishmentUid,
                        transferUid: transfer.transferUid
                    }, undefined)).success();
                } catch(err) {
                    this.errorHandler.logEverywhere({
                        title: 'Get transfer failed',
                        description: 'Could not retrieve transfer information from server. The transfer may be unavailable or there might be connection issues.'
                    });
                }
            } else {
                try {
                    updatedTransfer = (await this.orderRepository.callContract('createTransfer', {establishmentUid: this.posState.establishmentUid}, new OrderTransferApiIn({
                        order: transfer.order,
                        targetIotDevice: transfer.targetIotDevice,
                        creatorIotDevice: UuidUtils.scopedToVisual(this.posState.iotDevice.uid, uuidScopeIot_deviceApp),
                        unlockCodeOk: transfer.unlockCodeOk,
                        unlockCodeKo: transfer.unlockCodeKo
                    }))).success();
                } catch(err) {
                    this.errorHandler.logEverywhere({
                        title: 'Create transfer failed',
                        description: 'Could not create order transfer on server. The order might be unavailable or there might be connection issues.'
                    });
                }
            }

            if(updatedTransfer) {
                await this.updateLocalTransferFromServerTransfer(transfer, updatedTransfer.item);
            }
        }
    }

    private sortItemStatusUpdatesRecursively(item: PurchaseItemApiOut) {
        // Sort statusUpdates for current item
        item.statusUpdates.sort((a, b) => new Date(a.creationDatetime).getTime() - new Date(b.creationDatetime).getTime());

        // Recursively sort statusUpdates for nested items in groups
        for (const group of item.groups || []) {
            for (const purchase of group.purchases || []) {
                for (const nestedItem of purchase.items || []) {
                    this.sortItemStatusUpdatesRecursively(nestedItem);
                }
            }
        }
    }

    async synchroniseReceivedTransfers() {
        const data = (await this.orderRepository.callContract('fetchAsTargetPendingTransfers', {
            establishmentUid: this.posState.establishmentUid,
            iotDeviceUid: this.posState.iotDevice.uid
        }, undefined)).success();

        if(data.list.length === 0) return;

        for(const receivedOrder of data.orderList) {

            // Sort statusUpdates recursively
            for (const purchase of receivedOrder.purchases) {
                for (const item of purchase.items) {
                    this.sortItemStatusUpdatesRecursively(item);
                }
            }

            // Find corresponding transfer for received order
            const transferForOrder = data.list.find(transfer => transfer.orderUid === receivedOrder.uid);

            const localOrder = new LocalOrder({
                uid: receivedOrder.uid,
                order: receivedOrder
            });

            await this.localOrderRepository.save(localOrder);

            if(transferForOrder && transferForOrder.originIotDevice === UuidUtils.scopedToVisual(this.posState.iotDevice.uid, uuidScopeIot_deviceApp)) {
                this.posState.currentOrder = localOrder;
            }
        }

        for(const transfer of data.list) {
            (await this.orderRepository.callContract('confirmTransferFetching', {establishmentUid: this.posState.establishmentUid, transferUid: transfer.uid}, undefined)).success();
        }

        this.appBus.emit('displayToast', {
            title: `Vous avez reçu ${data.orderList.length} commande${data.orderList.length > 1 ? 's' : ''}`,
            description: `Elle${data.orderList.length > 1 ? 's' : ''} ${data.orderList.length > 1 ? 'sont' : 'est'} disponible${data.orderList.length > 1 ? 's' : ''} dans l'onglet commandes`,
            color: 'grey',
            closable: true,
            duration: 3000,
        });
    }

    async cancelTransfer(orderTransfer: OrderTransferApiOut) {
        (await this.orderRepository.callContract('cancelTransfer', {
            establishmentUid: this.posState.establishmentUid,
            transferUid: orderTransfer.uid
        }, undefined)).success();

        const transfers = await this.localOrderTransferRepository.findAllNotDone();
        for(const transfer of transfers) {
            if(transfer.transferUid === orderTransfer.uid) {
                transfer.canceled = true;
                await this.localOrderTransferRepository.save(transfer);
                break;
            }
        }
    }

    async acceptTransfer(orderTransfer: OrderTransferApiOut, correspondingLocalOrder: LocalOrder) {
        if(this.posState.currentOrder && this.posState.currentOrder.uid === correspondingLocalOrder.uid) {
            this.posState.currentOrder = null;
        }

        const codes = OrderTransferApiIn.generateUnlockCodes();

        const pendingTransfer = new LocalOrderTransfer({
            uid: randomUUID(),
            transferUid: orderTransfer.uid,
            order: correspondingLocalOrder.order,
            creatorIotDevice: orderTransfer.creatorIotDevice,
            originIotDevice: orderTransfer.originIotDevice,
            targetIotDevice: orderTransfer.targetIotDevice,
            unlockCodeOk: codes.ok,
            unlockCodeKo: codes.ko,
            canceled: false
        })

        await this.localOrderTransferRepository.save(pendingTransfer);
        const response = await this.orderRepository.callContract('commitTransfer', {establishmentUid: this.posState.establishmentUid, transferUid: orderTransfer.uid}, new OrderTransferApiIn({
            order: correspondingLocalOrder.order,
            targetIotDevice: orderTransfer.targetIotDevice,
            creatorIotDevice: orderTransfer.creatorIotDevice,
            unlockCodeOk: pendingTransfer.unlockCodeOk,
            unlockCodeKo: pendingTransfer.unlockCodeKo
        }));

        if(response.isSuccess()) {
            const data = response.success();
            pendingTransfer.transferUid = data.item.uid;
            await this.localOrderTransferRepository.save(pendingTransfer);
        } else if(response.isError()) {
            pendingTransfer.canceled = true;
            await this.localOrderTransferRepository.save(pendingTransfer);
        }
    }
}