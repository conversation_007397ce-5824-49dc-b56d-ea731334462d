export type LocalSettings = {
    autoCloseOrderAfterPayment: boolean,
    autoSendToKitchenAfterPayment?: boolean,
}

export class LocalSettingsRepository {
    lsKey = 'pos-settings';

    get(): LocalSettings {
        const rawLocalSettings = localStorage.getItem(this.lsKey);
        if(rawLocalSettings) {
            return JSON.parse(rawLocalSettings);
        } else {
            return {
                autoCloseOrderAfterPayment: true
            }
        }
    }

    save(localSettings: LocalSettings) {
        localStorage.setItem(this.lsKey, JSON.stringify(localSettings));
    }
}