<script lang="ts" src="./CustomersListComponent.ts">
</script>

<style lang="sass">
@import './CustomersListComponent.scss'
</style>

<template>
    <right-modal
        :stacked="creatingCustomer"
        :bottom-button-text="selectedCustomer ? `Lier à ${selectedCustomer.lastname} ${selectedCustomer.firstname}` : 'Fermer'"
        @bottom-button-clicked="close()"
    >
        <div class="customers-list">
            <div class="top">
                <div class="input-group">
                    <input v-model="searchValue" placeholder="Rechercher..." type="text" @input="search()" />
                    <div class="loader" v-if="loading"></div>
                </div>
                <button class="icon button" @click="showCreateModal()">
                    <i class="fa-regular fa-user-plus"></i>
                </button>
            </div>

            <div class="customers">
                <div class="empty-customers" v-if="Object.keys(groupedCustomers).length === 0">
                    Aucun client
                </div>
                <template v-for="(customers, letter) of groupedCustomers">
                    <div class="letter-separator"> {{ letter }} </div>
                    <div class="letter-group">
                        <div
                            class="customer"
                            v-for="customer in customers"
                            @click="selectedCustomer = customer"
                        >
                            <div class="profile-picture" :style="`background-image: url(data:image/svg+xml;base64,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)`"></div>
                            <div class="infos">
                                <span class="name"> {{ customer.lastname }} {{ customer.firstname }} </span>
                                <span class="email"> {{ customer.email }} </span>
                            </div>
                            <i v-if="selectedCustomer && selectedCustomer.uid === customer.uid" class="fa-solid fa-circle-dot"></i>
                            <i v-else class="fa-regular fa-circle"></i>
                        </div>
                    </div>
                </template>
            </div>
        </div>
    </right-modal>

    <right-modal
        v-if="creatingCustomer"
        :bottom-button-loading="creating"
        @bottom-button-clicked="createCustomer()"
        bottom-button-text="Créer le client"
    >
        <div class="create-customer form">
            <div class="close" @click="creatingCustomer = null">
                <i class="fa-regular fa-xmark"></i>
            </div>
            <div class="head">
                <span class="title"> Créer un compte </span>
                <span class="description"> Créer un compte client pour y lier des commandes </span>
            </div>

            <div class="profile-picture-container">
                <div class="profile-picture" :style="`background-image: url(data:image/svg+xml;base64,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)`"></div>
                <span> Prendre une photo </span>
            </div>

            <div class="input-group">
                <label> Nom </label>
                <input placeholder="Nom" v-model="creatingCustomer.lastname" />
            </div>

            <div class="input-group">
                <label> Prénom </label>
                <input placeholder="Prénom" v-model="creatingCustomer.firstname" />
            </div>

            <div class="input-group">
                <label> Email </label>
                <input placeholder="Email" v-model="creatingCustomer.email" />
            </div>
        </div>
    </right-modal>
</template>