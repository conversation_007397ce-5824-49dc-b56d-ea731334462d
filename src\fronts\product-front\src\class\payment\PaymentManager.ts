import {
	allowedPaymentProtocolsPerPaymentMethod,
	PaymentMethodApiOut,
	PaymentProtocol,
	UuidScopePayment_method
} from "@groupk/mastodon-core";
import {AutoWired, UuidUtils, VisualScopedUuid} from "@groupk/horizon2-core";
import {PhysicalPaymentProtocol} from "./processors/PhysicalPaymentProtocol";
import {
	OrderApiOut,
	OrderExecutorModel,
	OrderPaymentApiOut,
	OrderPaymentStatus,
	PaymentMethodSettings_type, PosProfileApiOut
} from "@groupk/mastodon-core";
import {CaisseApPaymentProcessor} from "./processors/CaisseApPaymentProcessor";
import {OrderRepository} from "../../../../../shared/repositories/OrderRepository";
import {AppState} from "../../../../../shared/AppState";

export type PendingPayment = {payment: OrderPaymentApiOut, processors: PhysicalPaymentProtocol[]};

export class PaymentManager {
	private currentOrder: OrderApiOut;
	private orderExecutorModel: OrderExecutorModel;
	private posProfile: PosProfileApiOut;

	@AutoWired(OrderRepository) accessor orderRepository!: OrderRepository;
	@AutoWired(AppState) accessor appState!: AppState;

	constructor(currentOrder: OrderApiOut, orderExecutorModel: OrderExecutorModel, posProfile: PosProfileApiOut) {
		this.currentOrder = currentOrder;
		this.orderExecutorModel = orderExecutorModel;
		this.posProfile = posProfile;
	}

	willPaymentBeAutomatic(paymentMethod: PaymentMethodApiOut) {
		const availableProcessors = this.handlePayment(paymentMethod);
		return availableProcessors.length === 0 && allowedPaymentProtocolsPerPaymentMethod[paymentMethod.type].includes(PaymentProtocol.AUTOMATIC);
	}

	async addPaymentToCurrentOrder(amount: number, paymentMethod: PaymentMethodApiOut): Promise<PendingPayment|null> {
		if(!this.currentOrder) return null;

		const payment = this.orderExecutorModel.createPaymentForOrder(this.currentOrder, amount, paymentMethod.uid);

		const availableProcessors = this.handlePayment(paymentMethod);
		if(availableProcessors.length === 0 && allowedPaymentProtocolsPerPaymentMethod[paymentMethod.type].includes(PaymentProtocol.AUTOMATIC)) {
			await this.updatePaymentState(this.currentOrder, payment, true);
		} else if(availableProcessors.length === 0) {
			throw new Error('not_configured_payment_method');
		} else {
			await this.orderRepository.callContract('create_update_offline', {establishmentUid: this.appState.requireUrlEstablishmentUid()}, this.currentOrder);
		}

		return {payment: payment, processors: this.handlePayment(paymentMethod)};
	}

	async updatePaymentState(order: OrderApiOut, payment: OrderPaymentApiOut, paid: boolean) {
		if(paid) {
			this.orderExecutorModel.setPaymentStatus(order, payment, OrderPaymentStatus.SUCCESS);
		} else {
			this.orderExecutorModel.setPaymentStatus(order, payment, OrderPaymentStatus.ERROR);
		}
		await this.orderRepository.callContract('create_update_offline', {establishmentUid: this.appState.requireUrlEstablishmentUid()}, this.currentOrder);
	}

	private handlePayment(paymentMethod: PaymentMethodApiOut): PhysicalPaymentProtocol[] {
		const availableProcessors: PhysicalPaymentProtocol[] = [];

		const paymentMethodSettings = this.getSettingsForPaymentMethod(paymentMethod.uid).map((data) => data.settings);
		for(let settings of paymentMethodSettings) {
			if(settings.protocol === PaymentProtocol.PHYSICAL_CASHKEEPER) {
				throw new Error('CASHKEEPER_NOT_IMPLEMENTED')
			} else if(settings.protocol === PaymentProtocol.PHYSICAL_CAISEAP) {
				availableProcessors.push(new CaisseApPaymentProcessor(settings));
			}
		}

		return availableProcessors;
	}

	getSettingsForPaymentMethod(paymentMethodUid : VisualScopedUuid<UuidScopePayment_method>): {settings: PaymentMethodSettings_type, index: number}[] {
		const settings: {settings: PaymentMethodSettings_type, index: number}[] = [];
		let index: number = 0;
		for(let config of this.posProfile.paymentMethodConfigs ?? []) {
			if(config.paymentMethodUid === UuidUtils.visualToScoped(paymentMethodUid)) settings.push({settings: config.settings, index: index});
		}
		return settings;
	}
}