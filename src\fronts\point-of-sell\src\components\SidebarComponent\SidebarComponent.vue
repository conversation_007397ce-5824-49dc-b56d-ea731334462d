<script lang="ts" src="./SidebarComponent.ts">
</script>

<style lang="sass" scoped>
@import './SidebarComponent.scss'
</style>

<template>
    <div class="sidebar-component" :class="{employees: posState.pointOfSale.establishmentAccountsUid.length > 1}">
        <div class="navigation">
            <div class="item" :class="{active: posState.currentPage === 'sell'}" @click="posState.currentPage = 'sell'">
                <i class="far fa-badge-dollar"></i>
                <span> Caisse </span>
            </div>
            <div class="item" :class="{active: posState.currentPage === 'orders'}" @click="posState.currentPage = 'orders'">
                <i class="far fa-th-list"></i>
                <span> Commandes </span>
            </div>
            <div class="item" :class="{active: posState.currentPage === 'tables'}" @click="posState.currentPage = 'tables'" v-if="posState.isDiningEnabled()">
                <i class="far fa-hashtag"></i>
                <span> Tables </span>
            </div>
            <div class="item" :class="{active: posState.currentPage === 'statistics'}" @click="posState.currentPage = 'statistics'">
                <i class="fa-regular fa-chart-simple"></i>
                <span> Statistiques </span>
            </div>
            <div class="item" :class="{active: posState.currentPage === 'settings'}" @click="posState.currentPage = 'settings'">
                <i class="fa-regular fa-cog"></i>
                <span> Paramètres </span>
            </div>
            <div class="item" @click="posState.showQuickActions = true">
                <i class="fa-regular fa-ellipsis"></i>
                <span> Actions </span>
            </div>
        </div>
        <div class="employees" v-if="posState.pointOfSale.establishmentAccountsUid.length > 1">
            <div
                class="employee"
                :class="{active: posState.currentEstablishmentAccountUid === establishmentAccountUid}" v-for="establishmentAccountUid in posState.pointOfSale.establishmentAccountsUid"
                @click="changeAccount(establishmentAccountUid)"
            >
                {{ posState.getEstablishmentAccountWithUid(establishmentAccountUid).firstname  }}
            </div>
        </div>
        <div class="disconnect" v-if="posState.pointOfSale.establishmentAccountsUid.length > 1" @click="changeAccount(null)">
            <i class="fa-regular fa-arrow-right-from-bracket"></i>
            Quitter
        </div>
    </div>
</template>