<script lang="ts" src="./products.ts">
</script>

<style scoped lang="sass">
@use './products.scss' as *
</style>

<template>
    <div id="products-page" class="page">
        <forbidden-message v-if="forbidden"></forbidden-message>
        <filter-table-layout
            v-else
            :header-parameters="headerParameters"
            :allowed-filters="allowedFilters"
            :table-columns="tableColumns"
            :filters="filters"
            :drawer-opened="selectedProduct !== null"
            @sorted="sorted($event)"
            @changed-column-preferences="saveColumnPreferences($event)"
        >
            <template v-slot:table-data>
                <tr class="table-dimmer" :class="{relative: products.length === 0}" v-if="loading">
                    <td colspan="100%">
                        <div class="dimmer">
                            <div class="loader-container">
                                <div class="loader"></div>
                            </div>
                        </div>
                    </td>
                </tr>
                <tr class="table-no-data" v-else-if="products.length === 0">
                    <td colspan="100%">Aucune donnée</td>
                </tr>
                <tr v-for="product in products" :class="{selected: selectedProduct && product.id === selectedProduct.id}">
                    <td :class="{'mobile-hidden': column.mobileHidden, grow: column.name === 'name'}" v-for="column in tableColumns.filter((column) => column.displayed)" @click="toggleSelectedProduct(product)">
                        <template v-if="column.name === 'id'"> {{ product.id }} </template>
                        <template v-if="column.name === 'icon'">
                            <img class="product-icon" v-if="product.img" :src="product.img" />
                        </template>
                        <template v-if="column.name === 'name'"> {{ product.name }} </template>
                        <template v-if="column.name === 'price'">
                            <div class="price-column">
                                <div class="hover-able">
                                    <i v-if="product.prices[0] > 0" class="fa-solid fa-circle-exclamation warning"></i>
                                    <div class="popup warning">
                                        Ce produit donne de l'argent
                                    </div>
                                </div>
                                {{ $filters.Money(product.prices[0]) }}
                            </div>
                        </template>
                        <template v-if="column.name === 'creationDatetime'"> {{ $filters.Date(product.creationDatetime) }} </template>
                    </td>
                </tr>
            </template>
            <template v-slot:right>
                <div v-if="!selectedProduct" class="empty-right-panel">
                    <img src="../../assets/img/select-hint.svg" />
                    Cliquer sur un produit pour <br/> le sélectionner
                </div>
                <div v-else class="selected-product">
                    <div class="close" @click="toggleSelectedProduct(selectedProduct)">
                        <i class="fa-regular fa-xmark"></i>
                        <span> Fermer </span>
                    </div>

                    <div class="header">
                        <div class="left">
                            <h2> {{ selectedProduct.name }} </h2>
                        </div>
                        <button class="black button" :class="{disabled: disabledActions.includes('edit')}" @click="editingProduct = selectedProduct; showCreationModal = true;">
                            <i class="fa-regular fa-pen-line"></i>
                            Modifier
                        </button>
                    </div>



                    <div class="product-details">
                        <div class="properties-table">
                            <div class="row">
                                <span class="title"> ID </span>
                                <span class="value"> {{ selectedProduct.id }} </span>
                            </div>
                            <div class="row">
                                <span class="title"> Icône </span>
                                <span class="value">
                                    <img v-if="selectedProduct.img" :src="selectedProduct.img" />
                                </span>
                            </div>
                            <div class="row">
                                <span class="title"> Nom </span>
                                <span class="value"> {{ selectedProduct.name }} </span>
                            </div>
                            <div class="row">
                                <span class="title"> Prix </span>
                                <span class="value"> {{ $filters.Money(selectedProduct.prices[0]) }} </span>
                            </div>
                            <div class="row">
                                <span class="title"> Date de création </span>
                                <span class="value"> {{ $filters.Date( selectedProduct.creationDatetime) }} </span>
                            </div>
                        </div>
                    </div>
                </div>
            </template>
        </filter-table-layout>

        <product-import-modal
            v-if="showImportModal"
            @close="showImportModal = false"
        ></product-import-modal>

        <product-form
            v-if="showCreationModal"
            :establishment-uid="establishmentUid"
            :editing-product="editingProduct"
            :next-product-id="getNextProductId()"
            @saved="savedProduct($event)"
            @close="editingProduct = null; showCreationModal = false;"
        ></product-form>
    </div>
</template>