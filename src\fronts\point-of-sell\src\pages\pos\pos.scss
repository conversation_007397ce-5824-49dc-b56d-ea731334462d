// FontAwesome icons preload for point of sale
// This CSS preloads FontAwesome icons used in the POS to improve performance
// by forcing the browser to load the font glyphs on page load
.pos-fa-preload {
    position: absolute;
    left: -9999px;
    opacity: 0;
    pointer-events: none;

    // Preload commonly used FontAwesome icons in POS
    &::before {
        font-family: "Font Awesome 6 Pro", "Font Awesome 6 Free";
        content: "\f071\f10c\f00d\f1d8\f021\f024\f0e0\f007\f1d9\f061\f00c\f110\f293\f192\f111\f1ce\f51f\f06a\f013\f6ac\f0d6\f08e\f107\f106\f058\f234\f060\f044\f067\f2ed\f1f8\f1c0\f017\f0f3\f292\f0e7\f05a\f0c9\f142\f065\f06e\f0c0\f0c1\f0c2\f0c3\f0c4\f0c5\f0c6\f0c7\f0c8\f0ca\f0cb\f0cc\f0cd\f0ce\f0d0\f0d1\f0d2\f0d3\f0d4\f0d5\f0d7\f0d8\f0d9\f0da\f0db\f0dc\f0dd\f0de\f0df\f0e1\f0e2\f0e3\f0e4\f0e5\f0e6\f0e8\f0e9\f0ea\f0eb\f0ec\f0ed\f0ee\f0ef\f0f0\f0f1\f0f2\f0f4\f0f5\f0f6\f0f7\f0f8\f0f9\f0fa\f0fb\f0fc\f0fd\f0fe";
    }
}

#pos {
    display: flex;
    height: 100%;

    .loading {
        display: flex;
        flex-direction: column;
        gap: 10px;
        align-items: center;
        justify-content: center;
        width: 100%;
        font-weight: 600;
        font-size: 13px;
    }

    .sidebar {
        flex-shrink: 0;
        height: 100%;
    }

    .content {
        flex-grow: 2;
    }
}

#mobile-pos {
    display: flex;
    flex-direction: column;
    height: 100%;

    .loading {
        display: flex;
        flex-direction: column;
        gap: 10px;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 100%;
        font-weight: 600;
        font-size: 13px;
    }

    .mobile-content {
        padding-top: var(--safe-area-top);
        overflow: auto;
        flex-grow: 2;
    }
}

.security-code-dimmer {
    position: fixed;
    inset: 0;
    background: rgba(0, 0, 0, 0.6);
    z-index: 1002;
}

.security-code {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: white;
    width: 300px;
    z-index: 1003;
    border-radius: 8px;

    .code {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 5px;
        padding: 20px;
        font-size: 32px;
        font-weight: bold;
    }
}

.displayed-receipt {
    position: fixed;
    inset: 0;
    z-index: 5002;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: flex-start;
    justify-content: center;
    padding: 40px;
    overflow: auto;

    img {
        width: min(100%, 400px);
    }
}