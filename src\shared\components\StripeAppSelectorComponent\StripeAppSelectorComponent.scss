.stripe-app-configuration-form-component {
    .available-apps {
        display: grid;
        grid-template-columns: 1fr 1fr;
        grid-gap: 15px;

        .app {
            display: flex;
            align-items: center;
            gap: 15px;
            background: var(--secondary-hover-color);
            padding: 15px;
            border-radius: 12px;
            cursor: pointer;
            border: 2px solid var(--secondary-hover-color);

            &.selected {
                border: 2px solid #635bff;
            }

            .logo {
                flex-shrink: 0;
                height: 36px;
                width: 36px;
                background-size: contain;
                background-position: center;
                border-radius: 6px;
                border: 2px solid white;
                box-shadow: 0 0 0 1px var(--border-color);
            }

            .data {
                flex-grow: 2;
                display: flex;
                flex-direction: column;
                gap: 4px;

                .subtitle {
                    font-size: 13px;
                }
            }

            i.fa-circle, i.fa-circle-dot {
                flex-shrink: 0;
                margin-right: 5px;
            }

            i.fa-circle-dot {
                color: #635bff;
            }

            i.fa-plus {
                flex-shrink: 0;
                margin-left: 5px;
            }
        }
    }

    .connecting-dimmer {
        position: fixed;
        z-index: 9995;
        inset: 0;
        background: rgba(0, 0, 0, 0.5);
        display: flex;
        align-items: center;

        .loader::after {
            color: white;
        }
    }
}