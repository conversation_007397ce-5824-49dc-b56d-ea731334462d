<script lang="ts" src="./IconBankComponent.ts">
</script>

<style lang="sass">
@use './IconBankComponent.scss' as *
</style>

<template>
    <div class="icon-bank-component">
        <div class="navigation">
            <div class="item" :class="{selected: selectedCategory === 'all'}" @click="selectedCategory = 'all'">
                <i class="fa-regular fa-asterisk"></i>
                <span class="name"> Tout </span>
            </div>

            <div class="item" :class="{selected: selectedCategory === category.name}" v-for="category in categories" @click="selectedCategory = category.name">
                <i :class="category.icon"></i>
                <span class="name"> {{ category.name }} </span>
            </div>
        </div>
        <div class="content">
            <div class="top">
                <div class="icon input-group">
                    <i class="fa-regular fa-search"></i>
                    <input v-model="iconSearch" placeholder="Rechercher" />
                </div>
                <button class="tertiary button">
                    <i class="fa-regular fa-sparkles"></i>
                </button>
            </div>

            <div class="icons">
                <img
                    v-for="icon in selectedCategoryIcons"
                    class="icon" :src="icon.url"
                    @click="selectIcon(icon.url)"
                />
            </div>
        </div>
    </div>
</template>