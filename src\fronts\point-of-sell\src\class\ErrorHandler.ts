import {AutoWired, dehydrate} from "@groupk/horizon2-core";
import {AppBus} from "../config/AppBus";
import * as Sentry from "@sentry/browser";
import {LocalOrder} from "../model/LocalOrder";
import {PrinterDescriptor} from "../printApiSdk/PrinterHelper";

export type PosError = {
	error?: Error,
	order?: LocalOrder,
	printDescriptor?: PrinterDescriptor,
	title: string;
	description: string;
}

export class ErrorHandler {
	sentryAvailable: boolean = false;

	@AutoWired(AppBus) accessor appBus!: AppBus;

	constructor(sentryConfig: {dsn: string}|null = null) {
		if(sentryConfig) {
			Sentry.init({
				dsn: sentryConfig.dsn,
				release: 'point-of-sell',
				tracesSampleRate: 1.0,
				attachStacktrace: true,
				normalizeDepth: 20,
				transport: Sentry.makeBrowserOfflineTransport(Sentry.makeFetchTransport),
				beforeBreadcrumb: (breadcrumb, hint) => {
					if (breadcrumb.category === 'ui.click') {
						if(!hint) return breadcrumb;
						const { target } = hint.event;
						breadcrumb.message = this.getDomPath(target).join(' ');
					}
					return breadcrumb;
				}
			});
			this.sentryAvailable = true;
		}
	}

	logEverywhere(error: PosError) {
		this.logToToastManager(error);
		this.logToSentry(error);
	}

	logToToastManager(error: PosError) {
		this.appBus.emit('displayToast', {
			title: error.title ?? 'Une erreur est survenue',
			description: error.description ?? (error.error ? error.error.message : ''),
			color: 'red',
			closable: true,
			duration: 8000,
		});
	}

	logToSentry(error: PosError) {
		if(!this.sentryAvailable) return;

		Sentry.withScope(function (scope) {
			scope.setLevel("fatal");
			if(error.order) Sentry.setContext('order', dehydrate(error.order));
			if(error.error) Sentry.setContext('error', error);
			Sentry.captureException(new Error(error.title + (error.description ? (' - ' + error.description) : '')));
		});
	}

	getDomPath(initial: Element) {
		let el : Element|null = initial;
		let stack: string[] = [];
		while (el != null) {
			let sibCount = 0;
			let sibIndex = 0;
			if(el.parentNode) {
				for (let i = 0; i < el.parentNode.childNodes.length; i++) {
					const sib: ChildNode = el.parentNode.childNodes[i];
					if (sib.nodeName == el.nodeName) {
						if (sib === el) {
							sibIndex = sibCount;
						}
						sibCount++;
					}
				}
			}

			let nodeInfos = '';
			if(el.hasAttribute('id') && el.id !== '') {
				nodeInfos = el.nodeName.toLowerCase() + '#' + el.id;
			} else if (sibCount > 1) {
				nodeInfos = el.nodeName.toLowerCase() + ':eq(' + sibIndex + ')';
			} else {
				nodeInfos = el.nodeName.toLowerCase();
			}

			if(el.className) nodeInfos += '.' + el.className;

			if(el instanceof HTMLElement && el.dataset.selector) {
				nodeInfos += ' [selector=' + el.dataset.selector + ']';
			}

			stack.unshift(nodeInfos);

			if(el.parentNode instanceof Element || el.parentNode instanceof HTMLElement) // el
				el = el.parentNode;
			else
				el = null;
		}

		return stack.slice(1); // removes the html element
	}
}