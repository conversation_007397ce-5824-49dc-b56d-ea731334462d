#index-page {
    height: 100%;

    .main-content {
        display: flex;
        flex-direction: column;
        gap: 40px;
        padding: 40px var(--desktop-padding);

        @media (max-width: 900px) {
            padding: 40px var(--mobile-padding);
        }
    }

    .steps {
        display: grid;
        grid-template-columns: 1fr 1fr 1fr;
        grid-gap: 40px;

        @media (max-width: 1650px) {
            grid-template-columns: 1fr 1fr;
        }

        @media (max-width: 1400px) {
            grid-template-columns: 1fr 1fr 1fr;
        }

        @media (max-width: 1200px) {
            grid-template-columns: 1fr 1fr;
        }

        @media (max-width: 900px) {
            grid-template-columns: 1fr;
        }

        .step {
            position: relative;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 20px;
            padding: 35px;
            border: 1px solid var(--border-color);
            border-radius: 8px;

            &.step-1 {
                background: linear-gradient(201deg, rgba(88, 73, 255, 0.18) 12.63%, rgba(217, 217, 217, 0.00) 65.67%);
            }

            &.step-2 {
                background: linear-gradient(177deg, rgba(73, 255, 211, 0.18) 3.08%, rgba(217, 217, 217, 0.00) 70.71%);
            }

            &.step-3 {
                background: linear-gradient(197deg, rgba(175, 255, 73, 0.18) 11.97%, rgba(217, 217, 217, 0.00) 66.77%);
            }

            &.disabled {
                opacity: 0.4;
                user-select: none;
            }

            .check {
                position: absolute;
                top: 15px;
                right: 15px;
                display: flex;
                align-items: center;
                justify-content: center;
                background: #60C964;
                height: 32px;
                width: 32px;
                border-radius: 50%;

                i {
                    font-size: 14px;
                    color: white;
                }
            }

            .step-number {
                display: flex;
                justify-content: center;
                align-items: center;
                height: 55px;
                width: 55px;
                font-size: 24px;
                border-radius: 8px;
                border: 1px solid #AFAFAF;
                background: white;
                font-weight: 600;
            }

            .name {
                font-size: 16px;
                font-weight: bold;
                text-align: center;
            }

            .description {
                font-size: 16px;
                text-align: center;
                flex-grow: 2;
            }

            .button {
                text-align: center;
                line-height: 20px;
            }
        }
    }

    .asks {
        display: flex;
        flex-direction: column;
        gap: 20px;
        align-items: flex-start;

        h2 {
            font-size: 24px;
        }

        .icon.input-group {
            position: relative;
            width: 300px;
            max-width: 100%;

            i {
                position: absolute;
                left: 15px;
                top: 50%;
                transform: translateY(-50%);
            }

            input {
                padding-left: 40px;
            }
        }

        .ask {
            display: flex;
            align-items: flex-start;
            gap: 20px;
            width: 100%;
            cursor: pointer;

            .icon {
                display: flex;
                align-items: center;
                justify-content: center;
                height: 42px;
                width: 42px;
                border-radius: 8px;
                border: 1px solid var(--border-color);
                flex-shrink: 0;

                i {
                    font-size: 20px;
                }
            }

           .content {
               flex-grow: 2;
               display: flex;
               flex-direction: column;
               gap: 5px;

               .title {
                   font-size: 16px;
                   font-weight: 500;
                   user-select: none;
                   margin-top: 12px;
               }

               .response {
                   font-size: 15px;
               }
           }

           > i {
                transition: transform .5s cubic-bezier(0.22, 1, 0.36, 1);
                &.flipped {
                    transform: rotate(180deg);
                }
            }
        }
    }
}