#profile-page {
    .layout-center-content{
        position: relative;
    }
    
    .right-panel {
        display: flex;
        flex-direction: column;
        margin-bottom: 40px;

        .top {
            display: flex;
            flex-direction: column;
            gap: 20px;
            position: sticky;
            z-index: 200;
            top: 0;
            background: white;
            padding: 20px 40px;

            @media (max-width: 900px) {
                padding: 20px;
            }

            &.scrolled {
                box-shadow: 0 3px 12px rgba(0, 0, 0, 0.1);
            }

            .search {
                display: flex;
                gap: 10px;

                i {
                    font-size: 20px;
                }

                input {
                    font-size: 14px;
                    font-weight: 400;
                }
            }

            .buttons {
                margin-top: 0;
            }
        }

        .drop-all-container {
            display: flex;
            gap: 10px;
            padding: 20px 40px;

            @media (max-width: 900px) {
                padding: 20px;
            }
        }

        .add-color-container {
            display: flex;
            justify-content: flex-end;
            padding: 20px 40px;

            @media (max-width: 900px) {
                padding: 20px;
            }
        }

        .limited {
            padding: 0 40px 10px 40px;
            font-size: 14px;
            font-style: italic;
        }

        .available-products {
            display: flex;
            flex-direction: column;
            padding: 0 40px;

            @media (max-width: 900px) {
                padding: 20px;
            }

            .no-product {
                font-size: 14px;
            }

            .product {
                display: flex;
                align-items: center;
                gap: 10px;
                border-radius: 8px;
                background: white;
                padding: 10px;
                margin: 0 -10px;
                cursor: pointer;
                user-select: none;

                @media (max-width: 900px) {
                    border: 1px solid var(--border-color);
                    margin-bottom: 10px;

                    &.in {
                        background: #EEF2FF;
                        border: 1px solid #C7D2FE;
                    }
                }

                @media (min-width: 900px) {
                    &:hover {
                        background: var(--secondary-hover-color);

                        i {
                            display: initial;
                        }
                    }
                }

                @media (min-width: 900px) {
                    &.selected {
                        opacity: 0.5;
                        user-select: none;
                    }
                }

                @media (max-width: 900px) {
                    border: 1px solid var(--border-color);
                    margin-bottom: 10px;

                    &.selected {
                        background: #EEF2FF;
                        border: 1px solid #C7D2FE;
                    }
                }

                .product-image {
                    height: 64px;
                    width: 64px;
                    border-radius: 4px;
                    background-position: center;
                    background-size: contain;
                    background-repeat: no-repeat;
                }

                .right {
                    display: flex;
                    flex-direction: column;
                    gap: 2px;
                    flex-grow: 2;

                    .name {
                        font-size: 14px;
                        font-weight: 600;
                    }

                    .description {
                        font-size: 12px;
                        font-weight: 400;
                    }
                }

                i {
                    font-size: 22px;
                    display: none;
                    margin-right: 10px;
                }
            }
        }

        .available-colors {
            display: flex;
            flex-direction: column;
            padding: 0 40px;

            .no-color {
                font-size: 14px;
            }

            .color {
                display: flex;
                align-items: center;
                gap: 10px;
                border-radius: 8px;
                background: white;
                padding: 10px;
                margin: 0 -10px;
                cursor: pointer;

                &.selected{
                    background-color: var(--secondary-hover-color);
                }

                &.white {
                    .color-preview {
                        border: 1px solid var(--border-color);
                    }
                }

                .color-preview {
                    height: 64px;
                    width: 64px;
                    border-radius: 4px;
                }

                .right {
                    display: flex;
                    flex-direction: column;
                    gap: 2px;
                    flex-shrink: 100;

                    .name {
                        font-size: 14px;
                        font-weight: 600;
                    }

                    .description {
                        font-size: 12px;
                        font-weight: 400;
                    }
                }
            }
        }
    }

    .bottom-save-container {
        position: sticky;
        bottom: 0;
        left: 0;
        right: 0;
        display: flex;
        align-items: center;
        justify-content: flex-end;
        padding: 20px 40px;
        background: white;
        border-top: 1px solid var(--secondary-hover-color);
    }
}