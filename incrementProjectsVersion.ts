import * as fs from 'fs';
import * as path from 'path';

function findProjectJsonFiles(dir: string): string[] {
    const files: string[] = [];
    const entries = fs.readdirSync(dir);

    for (const entry of entries) {
        const fullPath = path.join(dir, entry);
        const stat = fs.statSync(fullPath);

        if (stat.isDirectory()) {
            files.push(...findProjectJsonFiles(fullPath));
        } else if (entry === 'project.json') {
            files.push(fullPath);
        }
    }

    return files;
}

function incrementVersion(version: string): string {
    const parts = version.split('.');
    if (parts.length >= 3) {
        // Semantic versioning: increment patch version (last number)
        const patchVersion = parseInt(parts[parts.length - 1], 10);
        if (!isNaN(patchVersion)) {
            parts[parts.length - 1] = (patchVersion + 1).toString();
            return parts.join('.');
        }
    } else if (parts.length === 1) {
        // Simple version number: increment by 1
        const versionNumber = parseInt(parts[0], 10);
        if (!isNaN(versionNumber)) {
            return (versionNumber + 1).toString();
        }
    }
    
    // Fallback: couldn't parse version, return original
    console.warn(`Warning: Could not parse version "${version}", skipping increment`);
    return version;
}

function incrementProjectVersions() {
    const frontsDir = path.join(process.cwd(), 'src', 'fronts');
    const projectFiles = findProjectJsonFiles(frontsDir);

    console.log('Incrementing project versions...\n');

    for (const file of projectFiles) {
        try {
            const content = fs.readFileSync(file, 'utf-8');
            const projectData = JSON.parse(content);
            const subfolderName = path.basename(path.dirname(file));
            
            if (!projectData.version) {
                console.log(`${subfolderName} - No version found, skipping`);
                continue;
            }

            const oldVersion = projectData.version;
            const newVersion = incrementVersion(oldVersion);
            
            if (oldVersion === newVersion) {
                console.log(`${subfolderName} - Version unchanged: ${oldVersion}`);
                continue;
            }

            projectData.version = newVersion;
            
            // Write back to file with proper formatting
            fs.writeFileSync(file, JSON.stringify(projectData, null, 2) + '\n', 'utf-8');
            
            console.log(`${subfolderName} - ${oldVersion} → ${newVersion}`);
        } catch (error) {
            const subfolderName = path.basename(path.dirname(file));
            console.error(`Error processing ${subfolderName}: ${error}`);
        }
    }

    console.log('\nVersion increment completed!');
}

incrementProjectVersions();
