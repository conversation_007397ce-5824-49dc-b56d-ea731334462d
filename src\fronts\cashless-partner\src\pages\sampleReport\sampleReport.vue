<script lang="ts" src="./sampleReport.ts">
</script>

<style lang="sass">
@use './sampleReport.scss' as *
</style>

<template>
    <div id="sample-report-page">
        <div v-if="loading"></div>
        <template v-else>
            <div class="logos">
                <img src="/img/weecop.png" class="logo">
                <img v-if="getEstablishmentImageUrl()" :src="getEstablishmentImageUrl()" class="logo">
            </div>

            <div class="title">
              <h1> Rapport activité monétique du {{$filters.Date(afterDate)}}
                <template v-if="beforeDate">au {{$filters.Date(beforeDate)}}</template>
              </h1>
            </div>

            <div class="grid">
                <div class="full-grid">
                    <span class="title"> Moyens de paiement </span>
                    <hr/>
                </div>

                <div class="refills">
                    <span class="title"> Rechargements </span>
                    <ul>
                        <li v-for="method in visiblePaymentMethods"> {{ method.name }} : {{ $filters.Money(getRefillsForPaymentMethod(method.uid)) }} </li>
                    </ul>

                    <div class="total">
                        Total encaissé: {{ $filters.Money(getRefillsForPaymentMethod(null)) }}
                    </div>
                </div>

                <div class="refunds">
                    <span class="title"> Remboursements </span>
                    <ul>
                        <li v-for="method in visiblePaymentMethods"> {{ method.name }} : {{ $filters.Money(getRefundsForPaymentMethod(method.uid)) }} </li>
                    </ul>

                    <div class="total">
                        Total remboursé: {{ $filters.Money(getRefundsForPaymentMethod(null)) }}
                    </div>
                </div>

                <div class="full-grid">
                    <span class="title"> Points de vente </span>
                    <hr/>
                </div>

                <div class="payments">

                    <span class="title"> Paiement </span>

                    <ul>
                        <li v-for="profile in profiles"> {{ profile.name }} : {{ $filters.Money(getPaymentsForProfile(profile.uid)) }} </li>
                    </ul>

                    <div class="total">
                        Total payé: {{ $filters.Money(getPaymentsForProfile(null)) }}
                    </div>
                </div>
            </div>

            <div class="refunds-table">
                <h2> Liste des remboursements </h2>
                <div></div>

                <table class="data-table" v-for="paymentMethod in visiblePaymentMethods">
                    <thead>
                    <tr>
                        <td class="table-header" colspan="100%"> {{ paymentMethod.name }} </td>
                    </tr>
                    <tr>
                        <td> € </td>
                        <td> Support </td>
                        <td> Utilisateur </td>
                    </tr>
                    </thead>
                    <tbody>
                    <tr v-if="getRefundTransactionsForPaymentMethod(paymentMethod.uid).length === 0">
                        <td colspan="100%" style="text-align: center"> Aucun remboursement en {{ paymentMethod.name }} </td>
                    </tr>
                    <tr v-for="transaction in getRefundTransactionsForPaymentMethod(paymentMethod.uid)">
                        <td> {{ $filters.Money(transaction.signedAmount) }}</td>
                        <td class="chip">{{ transaction.chipVisualId }}</td>
                        <td> {{ customerChipsPerTransaction[transaction.uid] ? requireCustomerWithUid(requireTransactionCustomerUid(transaction.uid)) : 'Aucun' }} </td>
                    </tr>
                    </tbody>
                </table>
            </div>

            <div class="refunds-table">
                <h2> Liste des rechargements </h2>
                <div></div>

                <table class="data-table" v-for="paymentMethod in visiblePaymentMethods">
                    <thead>
                    <tr>
                        <td class="table-header" colspan="100%"> {{ paymentMethod.name }} </td>
                    </tr>
                    <tr>
                        <td> € </td>
                        <td> Support </td>
                        <td> Utilisateur </td>
                    </tr>
                    </thead>
                    <tbody>
                    <tr v-if="getRefillsTransactionsForPaymentMethod(paymentMethod.uid).length === 0">
                        <td colspan="100%" style="text-align: center"> Aucun rechargement en {{ paymentMethod.name }} </td>
                    </tr>
                    <tr v-for="transaction in getRefillsTransactionsForPaymentMethod(paymentMethod.uid)">
                        <td> {{ $filters.Money(transaction.signedAmount) }}</td>
                        <td class="chip">{{ transaction.chipVisualId}}</td>
                        <td> {{ customerChipsPerTransaction[transaction.uid] ? requireCustomerWithUid(requireTransactionCustomerUid(transaction.uid)) : 'Aucun' }} </td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </template>
    </div>
</template>