.sidebar-component {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    padding: 40px 10px 10px 10px;
    height: 100%;
    width: 120px;
    background: var(--pos-color);
    color: var(--pos-current-text-color);
    user-select: none;
    box-sizing: border-box;
    gap: 10px;

    &.employees {
        width: 157px;
    }

    .navigation {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 15px;

        .item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 5px;
            padding: 10px;
            width: 100%;
            border-radius: 8px;
            box-sizing: border-box;
            cursor: pointer;

            i {
                font-size: 28px;
            }

            span {
                font-family: Montserrat, sans-serif;
                font-size: 12px;
                font-weight: 600;
                text-align: center;
            }

            &.red {
                &:hover {
                    background: var(--error-color);
                    color: white;
                }
            }

            &.active {
                background: var(--pos-current-selected-color);
                color: var(--pos-current-selected-text-color);
            }
        }
    }

    .employees {
        display: flex;
        flex-direction: column;
        border-radius: 8px;
        padding: 10px;
        width: 100%;
        box-sizing: border-box;
        gap: 5px;
        overflow: auto;
        margin-top: 20px;
        background: var(--pos-current-selected-color);

        .employee {
            padding: 10px 12px;
            font-size: 15px;
            border-radius: 8px;
            color: var(--pos-current-text-color);
            word-break: break-word;
            font-weight: 600;

            &.active {
                background: var(--pos-color);
                color: var(--pos-current-selected-text-color);
                font-weight: 700;

            }
        }

        &::-webkit-scrollbar {
            width: 0;
            height: 0;
            background-color: transparent !important;
        }

        &::-webkit-scrollbar-track {
            background-color: transparent !important;
        }
    }

    .disconnect {
        display: flex;
        align-items: center;
        border-radius: 8px;
        padding: 10px 22px;
        width: 100%;
        box-sizing: border-box;
        gap: 5px;
        overflow: auto;
        background: var(--pos-current-selected-color);
        font-weight: 600;
        font-size: 15px;
        flex-shrink: 0;
    }
}