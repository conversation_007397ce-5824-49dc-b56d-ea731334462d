<script lang="ts" src="./login.ts">
</script>

<style scoped lang="sass">
@use './login.scss' as *
</style>

<template>
    <div id="login-page">
        <div class="left">
            <div class="loading-container" v-if="loading">
                <div class="loader"></div>
            </div>
            <template v-else>
                <form class="form step step1" :class="{hidden: step !== 'LOGIN'}" @submit.prevent="login()">

                    <div class="title-group">
                        <img v-if="currentPlatform" class="logo" :src="currentPlatform.url + 'img/logo-long.svg'" >

                        <h3 v-if="!establishmentJoining"> Connexion </h3>
                        <h3 v-else> Invitation à {{ establishmentJoining.establishment.name }} </h3>

                        <span v-if="establishmentJoining"> Connectez vous pour rejoindre {{ establishmentJoining.establishment.name }} </span>
                        <span v-else-if="currentPlatform"> Connectez vous pour accéder à {{ currentPlatform.name }} </span>
                    </div>

                    <template v-if="connectedAccount">
                        <div class="connected-account" @click="continueWithAccount()">
                            <div class="user">
                                <div class="profile-picture"></div>

                                <div class="info">
                                    <span class="continue-as"> Continuer en tant que </span>
                                    <span class="name"> {{ connectedAccount.firstname }} {{ connectedAccount.lastname }} </span>
                                </div>
                            </div>

                            <i class="fa-regular fa-arrow-right"></i>
                        </div>

                        <span class="or-separator"> - ou - </span>
                    </template>
                    <template v-else-if="latestUsedEmail && credentials.email !== latestUsedEmail">
                        <div class="connected-account email" @click="credentials.email = latestUsedEmail">
                            <div class="user">
                                <div class="profile-picture"></div>

                                <div class="info">
                                    <span class="continue-as"> Continuer en tant que </span>
                                    <span class="name email"> {{ latestUsedEmail }} </span>
                                </div>
                            </div>

                            <i class="fa-regular fa-arrow-right"></i>
                        </div>

                        <span class="or-separator"> - ou - </span>
                    </template>

                    <div class="input-group">
                        <label for="title"> Email </label>
                        <div class="ui input">
                            <input aria-label="Email de connexion" autocomplete="email" spellcheck="false" v-model="credentials.email" type="email" placeholder="Email de votre compte" />
                        </div>
                    </div>

                    <div class="fluid input-group">
                        <label for="title"> Mot de passe </label>
                        <div class="ui input">
                            <input autocomplete="password" spellcheck="false" class="password-input" v-model="credentials.password" type="password" placeholder="Mot de passe associé au compte" />
                        </div>
                    </div>

                    <a :href="getPasswordResetUrl()" class="forgot-password"> Mot de passe oublié ? </a>

                    <div class="form-error" v-if="error">
                        <i class="fa-regular fa-circle-exclamation"></i>
                        <div class="details">
                            {{ error }}
                        </div>
                    </div>

                    <div class="buttons">
                        <button type="button" class="white button" @click="nativeScanCredentialsQr()" v-if="isNative">
                            <i class="fa-regular fa-qrcode"></i>
                        </button>
                        <button class="ui black button" :class="{ loading: connecting || redirecting, disabled: connecting || redirecting }">
                            <i class="fa-regular fa-arrow-right-to-bracket" style="margin-top: 3px"></i>
                            Connexion
                        </button>
                    </div>

                    <div class="signup-link">
                        <span class="title"> Pas encore de compte ? </span>
                        <span @click="goToSignup()" class="link"> S'inscrire en deux clics </span>
                    </div>
                </form>

                <div class="select-establishment step step2" :class="{show: step === 'SELECT_ESTABLISHMENT'}">
                    <div class="title-group">
                        <h3>
                            Re-bonjour
                            <span class="welcome-firstname" v-if="connectedAccount"> {{connectedAccount.firstname}} </span>,
                        </h3>
                        <span> Sur quel établissement souhaitez vous continuer ? </span>
                    </div>

                    <div class="loading-container" v-if="loadingEstablishments">
                        <div class="loader"></div>
                    </div>

                    <div class="form-error" v-if="establishmentsError">
                        <i class="fa-regular fa-circle-exclamation"></i>
                        <div class="details">
                        <span class="title">
                            Impossible de charger les établissements
                        </span>
                            {{ establishmentsError }}
                        </div>
                    </div>

                    <div class="establishments">
                        <div class="icon input-group" v-if="establishments.length > 4">
                            <i class="fa-regular fa-search"></i>
                            <input v-model="establishmentSearch" placeholder="Rechercher" />
                        </div>

                        <div class="establishment" v-for="establishment in filteredEstablishments" @click="selectEstablishment(establishment)">
                            <div class="info">
                                <span class="name"> {{ establishment.name }} </span>
                                <span class="platforms-text"> Continuer avec cet établissement </span>
<!--                                <span class="platforms-text"> Configuré pour Invoice, Littl </span>-->
                            </div>
<!--                            <div class="right">-->
<!--                                <div class="platforms-icon">-->
<!--                                    <div class="platform-icon" :style="`background-image: url(http://192.168.50.80:9555/img/logo.svg)`"></div>-->
<!--                                    <div class="platform-icon" :style="`background-image: url(http://192.168.50.80:9400/img/logo.svg)`"></div>-->
<!--                                </div>-->
<!--                                <i v-if="selectingEstablishment && selectingEstablishment.uid === establishment.uid" class="fa-regular fa-paymentReturn fa-spin"></i>-->
<!--                                <i v-else class="fa-regular fa-arrow-right"></i>-->
<!--                            </div>-->
                            <i v-if="selectingEstablishment && selectingEstablishment.uid === establishment.uid" class="fa-regular fa-loader fa-spin"></i>
                            <i v-else class="fa-regular fa-arrow-right"></i>
                        </div>

                        <button class="new-establishment" @click="showNewEstablishmentModal()">
                            Créer un nouvel établissement
                        </button>
                    </div>
                </div>
            </template>
        </div>
        <div class="littl-preview" v-if="currentPlatform && currentPlatform.id === 'Littl'">
            <div class="littl-container">
                <div class="head">
                    <span class="title"> Contrôlez vos QR codes à distance </span>
                    <span class="subtitle"> Modifiez le lien de votre QR code à tout moment, même une fois imprimé </span>
                </div>
                <video autoplay muted loop>
                    <source src="/littl.mp4" type="video/webm" />
                </video>
            </div>
        </div>
        <div v-else class="right" :style="currentPlatform ? `background-image: url(${currentPlatform.url + 'img/login.png'})` : ''"></div>

        <form-modal-or-drawer
            :state="showEstablishmentCreationModal"
            title="Créer un nouvel établissement"
            subtitle="Vous êtes sur le point de créer un établissement pour lequel vous serez le gestionnaire"
            @close="showEstablishmentCreationModal = null"
        >
            <template v-slot:content>
                <div class="input-group" v-if="showEstablishmentCreationModal">
                    <label> Nom de l'établissement </label>
                    <input v-model="showEstablishmentCreationModal.name" placeholder="Nom de l'établissement" type="text" />
                </div>

                <div class="form-error" v-if="creatingEstablishmentError">
                    <i class="fa-solid fa-exclamation-circle"></i>
                    <div class="details">
                        <span class="title"> Erreur </span>
                        <span class="description">{{ creatingEstablishmentError }}</span>
                    </div>
                </div>
            </template>
            <template v-slot:buttons>
                <div class="white button" @click="showEstablishmentCreationModal = null"> Fermer </div>
                <div class="button" :class="{disabled: creatingEstablishment, loading: creatingEstablishment}" @click="createNewEstablishment()"> Créer </div>
            </template>
        </form-modal-or-drawer>

        <form-modal-or-drawer
            :state="joiningError"
            title="Invitation invalide"
            :subtitle="joiningError"
            @close="joiningError = null"
        >
            <template v-slot:content></template>
            <template v-slot:buttons>
                <div class="button" @click="joiningError = null"> Fermer </div>
            </template>
        </form-modal-or-drawer>
    </div>
</template>