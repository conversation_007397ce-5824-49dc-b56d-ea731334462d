import {Component, Vue} from "vue-facing-decorator";
import {
	AutoWired,
	QueryOperator,
	ScopedUuid, SetInstance,
	TypedQuerySearch,
	UuidUtils,
	VisualScopedUuid
} from "@groupk/horizon2-core";
import {CashStatementRepository} from "../../../../../shared/repositories/CashStatementRepository";
import {OptionBuilder} from "vue-facing-decorator/dist/optionBuilder";
import {
	EstablishmentAccountApiOut, EstablishmentDeviceCreationNewApiIn, OrderExecutorModel,
	uuidScopeEstablishment,
	UuidScopeEstablishment,
	UuidScopeEstablishmentAccount, uuidScopeIot_deviceApp, UuidScopeIot_deviceApp
} from "@groupk/mastodon-core";
import {randomUUID, Router} from "@groupk/horizon2-front";
import {
	CashStatementApiOut,
	CashStatementSessionApiOut,
	CashStatementSessionOpenApiIn, OrderApiOut, ProductHttpOrderContractSearchConfig, WorkClockApiIn, WorkClockApiOut,
} from "@groupk/mastodon-core";
import {LocalCashStatementRepository} from "../../repositories/LocalCashStatementRepository";
import {IotUtils} from "../../class/IotUtils";
import {EstablishmentDeviceRepository} from "../../../../../shared/repositories/EstablishmentDeviceRepository";
import {EstablishmentAccountRepository} from "../../../../../shared/repositories/EstablishmentAccountRepository";
import {PosProfileRepository} from "../../../../../shared/repositories/PosProfileRepository";
import {WorkClockRepository} from "../../../../../shared/repositories/WorkClockRepository";
import {
	EstablishmentDeviceApiOut,
} from "@groupk/mastodon-core";
import {PosProfile} from "../../model/PosProfile";
import {OrderReceiptRender} from "../../class/OrderReceiptRender";
import {OrderRepository} from "../../../../../shared/repositories/OrderRepository";
import {PrinterRepository} from "../../repositories/PrinterRepository";
import {EstablishmentUrlBuilder} from "../../class/EstablishmentUrlBuilder";
import {AuthStateModel} from "../../../../../shared/AuthStateModel";
import {BillingRegionRepository} from "../../../../../shared/repositories/BillingRegionRepository";
import {PurchaseStatusRepository} from "../../../../../shared/repositories/PurchaseStatusRepository";
import {PaymentMethodRepository} from "../../../../../shared/repositories/PaymentMethodRepository";
import {ProductRepository} from "../../../../../shared/repositories/ProductRepository";
import PrintSettingsModalComponent from "../../components/PrintSettingsModalComponent/PrintSettingsModalComponent.vue";
import ToastManagerComponent, {Toast} from "../../components/ToastManagerComponent/ToastManagerComponent.vue";
import {LS_DEVICE_APP_UID_KEY} from "../iotOnboarding/iotOnboarding";

@Component({
	components: {
		'print-settings-modal': PrintSettingsModalComponent,
		'toast-manager': ToastManagerComponent,
	}
})
export default class CashStatementView extends Vue {
	establishmentUid!: VisualScopedUuid<UuidScopeEstablishment>;
	establishmentAccounts: EstablishmentAccountApiOut[] = [];
	lastWorkClocks: WorkClockApiOut[] = [];
	posProfile!: PosProfile;
	currentCashStatement: CashStatementApiOut|null = null;
	currentCashStatementSession: CashStatementSessionApiOut|null = null;
	lastClosedSession: CashStatementSessionApiOut|null = null;

	device!: EstablishmentDeviceCreationNewApiIn;

	loading: boolean = false;
	error: string|null = null;
	displayedReceipt: string|null = null;
	dataError: string|null = null;
	openingCashStatement: boolean = false;
	closingCashStatement: boolean = false;
	printingCashStatement: boolean = false;
	showPrintSettings: boolean = false;
	redirecting: boolean = false;

	noPrinterToast: Toast = {
		title: 'Aucune imprimante sélectionnée',
		description: 'Veuillez sélectionner une imprimante dans les paramètres afin d’imprimer',
		color: 'red',
		closable: true,
		duration: 4000,
		action: [{
			name: 'Afficher',
			icon: 'fa-regular fa-receipt',
			callback: () => {}
		}, {
			name: 'Paramètres',
			icon: 'fa-regular fa-cog',
			callback: this.showPrintSettingsModal
		}]
	}

	@AutoWired(CashStatementRepository) accessor cashStatementRepository!: CashStatementRepository;
	@AutoWired(EstablishmentDeviceRepository) accessor establishmentDeviceRepository!: EstablishmentDeviceRepository;
	@AutoWired(BillingRegionRepository) accessor billingRegionRepository!: BillingRegionRepository;
	@AutoWired(PurchaseStatusRepository) accessor purchaseStatusRepository!: PurchaseStatusRepository;
	@AutoWired(PaymentMethodRepository) accessor paymentMethodRepository!: PaymentMethodRepository;
	@AutoWired(ProductRepository) accessor productRepository!: ProductRepository;
	@AutoWired(LocalCashStatementRepository) accessor localCashStatementRepository!: LocalCashStatementRepository;
	@AutoWired(EstablishmentAccountRepository) accessor establishmentAccountRepository!: EstablishmentAccountRepository;
	@AutoWired(OrderRepository) accessor orderRepository!: OrderRepository;
	@AutoWired(PrinterRepository) accessor printerRepository!: PrinterRepository;
	@AutoWired(PosProfileRepository) accessor posProfileRepository!: PosProfileRepository;
	@AutoWired(WorkClockRepository) accessor workClockRepository!: WorkClockRepository;
	@AutoWired(Router) accessor router!: Router;
	@AutoWired(AuthStateModel) accessor authCenter!: AuthStateModel;

	constructor(optionBuilder: OptionBuilder, vueInstance: any) {
		super(optionBuilder, vueInstance);

		let regexMatch = this.router.lastRouteRegexMatches;

		if (regexMatch && regexMatch[1]) {
			this.establishmentUid = UuidUtils.scopedToVisual<UuidScopeEstablishment>(regexMatch[1] as ScopedUuid<UuidScopeEstablishment>, uuidScopeEstablishment);
		}

		if(!this.authCenter.getStateSync()) window.location.href = '/auth';

		this.loading = true;
	}

	async beforeMount() {
		try {
			this.establishmentAccounts = (await this.establishmentAccountRepository.callContract('list', {establishmentUid: this.establishmentUid}, undefined)).success();
			this.lastWorkClocks = (await this.workClockRepository.callContract('getLastForAll', {establishmentUid: this.establishmentUid}, undefined)).success();
			const profiles = (await this.posProfileRepository.callContract('list', {establishmentUid: this.establishmentUid}, undefined)).success();
			this.posProfile = new PosProfile(profiles);
			this.currentCashStatement = (await this.cashStatementRepository.callContract('currentCashStatement', {establishmentUid: this.establishmentUid }, undefined)).success();
			if(this.currentCashStatement) {
				const lastSession = this.localCashStatementRepository.getSession();
				if(lastSession && this.localCashStatementRepository.isSessionActive(lastSession, this.currentCashStatement)) {
					this.currentCashStatementSession = lastSession;
				}
			}

			const currentDeviceAppUid = this.getIotDeviceAppUid();
			if(this.currentCashStatementSession === null){
				// TODO will grow when data grow warning
				const cashStatements = (await this.cashStatementRepository.callContract('list', {establishmentUid: this.establishmentUid}, undefined)).success();

				for(let cashStatement of cashStatements.sort((c1, c2) => new Date(c1.creationDatetime).getTime() - new Date(c2.creationDatetime).getTime())) {
					for(let session of cashStatement.sessions) {
						if(session.iotDeviceUid === UuidUtils.scopedToVisual(currentDeviceAppUid, uuidScopeIot_deviceApp)) {
							this.lastClosedSession = session;
							break;
						}
						if(this.lastClosedSession) break;
					}
				}
			}
			this.device = await IotUtils.getCurrentDeviceSerialAndBrand();

			if(
				this.currentCashStatement &&
				this.currentCashStatement.sessions.filter((session) => session.iotDeviceUid !== UuidUtils.scopedToVisual(currentDeviceAppUid, uuidScopeIot_deviceApp)).length > 0
			) {
				this.startAutoReload();
			}
		} catch(err) {
			this.dataError = 'Une erreur est survenue.'
		}

		this.loading = false;
	}

	mounted() {
		const preLoaderDiv = document.getElementById('pre-loader');
		if(preLoaderDiv) preLoaderDiv.remove();
	}

	getOpenedSessions(cashStatement: CashStatementApiOut) {
		return cashStatement.sessions.filter((session) => session.closingDatetime === null);
	}

	startAutoReload() {
		setTimeout(async () => {
			await this.reload();

			if(this.currentCashStatement && this.currentCashStatement.closingDatetime === null) this.startAutoReload();
		}, 30000);
	}

	getIotDeviceAppUid(): ScopedUuid<UuidScopeIot_deviceApp> {
		const iotDeviceAppUid = localStorage.getItem(LS_DEVICE_APP_UID_KEY);
		if(iotDeviceAppUid === null) {
			window.location.href = EstablishmentUrlBuilder.buildUrl('/iot-onboarding');

			// const response = await this.establishmentDeviceRepository.callContract('onboard', {establishmentUid: this.establishmentUid}, await IotUtils.getCurrentDeviceSerialAndBrand());
			// if(response.isSuccess()) {
			// 	const createdDevice = response.success();
			// 	localStorage.setItem('auto-connect-secret', createdDevice.autoConnectSecret)
			// 	iotDevice = createdDevice.device;
			// } else if(response.isError()) {
			// 	const error = response.error();
			// 	if(!error || !('error' in error)) {
			// 		this.error = 'Erreur inconnue';
			// 	} else if(error.error === 'device_manual_limit_reached') {
			// 		this.error = 'Vous avez atteint le quota d\'appareils enregistrés';
			// 	} else if(error.error === 'device_already_in_use') {
			// 		this.error = 'Cet appareil est déjà enregistré';
			// 	} else if(error.error === 'invalid_data') {
			// 		this.error = 'Erreur inconnue; Données invalides';
			// 	}
			// } else {
			// 	this.error = 'Erreur inconnue';
			// }
		}

		return iotDeviceAppUid as ScopedUuid<UuidScopeIot_deviceApp>;
	}

	async openCashStatement() {
		this.currentCashStatement = (await this.cashStatementRepository.callContract('currentCashStatement', {establishmentUid: this.establishmentUid }, undefined)).success();
		if(!this.currentCashStatement) {
			const response = await this.cashStatementRepository.callContract('openCashStatement', {establishmentUid: this.establishmentUid }, undefined);
			if(response.isSuccess()) {
				this.currentCashStatement = response.success();
			} else {
				const error = response.error();
				if(error && 'error' in error && error.error === 'invalid_data') {
					this.error = error.error_details;
				} else if(error && 'error' in error && error.error === 'cash_statement_already_opened') {
					this.currentCashStatement = (await this.cashStatementRepository.callContract('currentCashStatement', {establishmentUid: this.establishmentUid }, undefined)).success();
				} else {
					this.error = 'Une erreur inconnue est survenue';
				}
				throw new Error();
			}
		}
	}

	async openSession(iotDeviceAppUid: ScopedUuid<UuidScopeIot_deviceApp>) {
		if(!this.currentCashStatement) throw new Error('cannot_open_session_before_cash_statement');

		const apiIn = new CashStatementSessionOpenApiIn({iotDeviceUid: UuidUtils.scopedToVisual(iotDeviceAppUid, uuidScopeIot_deviceApp), startingCash: undefined });

		const response = await this.cashStatementRepository.callContract('openSession', {establishmentUid: this.establishmentUid, cashStatementUid: this.currentCashStatement.uid}, apiIn);
		if(response.isSuccess()) {
			this.localCashStatementRepository.saveSession(response.success());
		} else {
			const error = response.error();
			if(error && 'error' in error && error.error === 'invalid_data') {
				this.error = error.error_details;
			} else if(error && 'error' in error && error.error === 'cash_statement_session_already_opened') {
				this.error = 'Ce compte a déjà ouvert une caisse';
			} else {
				this.error = 'Une erreur inconnue est survenue';
			}
			throw new Error();
		}
	}

	async openCashStatementAndGoToPos() {
		this.openingCashStatement = true;
		this.error = null;

		try {
			const iotDeviceAppUid = this.getIotDeviceAppUid();
			if(!this.currentCashStatement) await this.openCashStatement();
			if(!this.currentCashStatementSession) await this.openSession(iotDeviceAppUid);

			this.goToPos();
		} catch(err) {
			this.openingCashStatement = false;
		}
	}

	goToPos() {
		this.redirecting = true;
		window.location.href = '/establishment/' + UuidUtils.visualToScoped(this.establishmentUid) + '/pos';
	}

	// async closeCashStatement() {
	// 	if(!this.currentCashStatement) return;
	// 	this.closingCashStatement = true;
	// 	this.error = null;
	//
	// 	const result = await this.cashStatementRepository.callContract('closeCashStatement', {establishmentUid: this.establishmentUid, cashStatementUid: this.currentCashStatement.uid}, undefined);
	// 	if(result.isSuccess()) {
	// 		window.location.reload();
	// 	} else if(result.isError()) {
	// 		const error = result.error();
	// 		this.error = error?.error ?? 'Erreur inconnue';
	// 	} else {
	// 		this.error = 'Erreur inconnue';
	// 	}
	//
	// 	this.closingCashStatement = false;
	// }

	// isClockedIn(establishmentAccountUid: VisualScopedUuid<UuidScopeEstablishmentAccount>): boolean {
	// 	const accountLastWorkClock = this.lastWorkClocks.find((workClock) => workClock.establishmentAccountUid === establishmentAccountUid) ?? null;
	// 	if(accountLastWorkClock && accountLastWorkClock.clockIn) return true;
	// 	return false;
	// }

	// async clockInOut(establishmentAccountUid: VisualScopedUuid<UuidScopeEstablishmentAccount>){
	// 	const isClockedIn = this.isClockedIn(establishmentAccountUid);
	// 	const iotDevice = await IotUtils.getCurrentIotDevice(this.establishmentUid);
	//
	// 	if(!iotDevice) throw new Error('iot_device_is_not_registered');
	//
	// 	const apiIn = new WorkClockApiIn({
	// 		clockIn: !isClockedIn,
	// 		establishmentAccountUid: establishmentAccountUid,
	// 		iotDeviceUid: iotDevice.uid
	// 	});
	// 	const workClock = (await this.workClockRepository.callContract('createOrUpdate', {establishmentUid: this.establishmentUid}, apiIn)).success();
	// 	this.updateLastWorkClocks(workClock);
	// }
	//
	// updateLastWorkClocks(newWorkClock: WorkClockApiOut) {
	// 	const existingIndex = this.lastWorkClocks.findIndex((workClock) => newWorkClock.establishmentAccountUid === workClock.establishmentAccountUid);
	// 	if(existingIndex !== -1) {
	// 		this.lastWorkClocks.splice(existingIndex, 1, newWorkClock);
	// 	} else {
	// 		this.lastWorkClocks.push(newWorkClock);
	// 	}
	// }

	requireEstablishmentAccountWithUid(accountUid: VisualScopedUuid<UuidScopeEstablishmentAccount>) {
		const account = this.establishmentAccounts.find((account) => account.uid === accountUid);
		if(!account) throw new Error('no_account_matching');
		return account;
	}

	async reload() {
		this.currentCashStatement = (await this.cashStatementRepository.callContract('currentCashStatement', {establishmentUid: this.establishmentUid }, undefined)).success();
	}

	showPrintSettingsModal() {
		this.showPrintSettings = true;
	}

	async printCashStatement() {
		if(!this.lastClosedSession) return;

		this.printingCashStatement = true;

		const billingRegions = (await this.billingRegionRepository.callContract('list', {establishmentUid: this.establishmentUid}, {})).success();
		const statuses = (await this.purchaseStatusRepository.callContract('list', {establishmentUid: this.establishmentUid}, {})).success();
		const paymentMethods = (await this.paymentMethodRepository.callContract('list', {establishmentUid: this.establishmentUid}, {})).success();
		const products = (await this.productRepository.callContract('list', {establishmentUid: this.establishmentUid}, undefined)).success();

		const orderExecutorModel = new OrderExecutorModel<undefined>({
			randomUUID: randomUUID,
			purchaseStatuses: statuses,
			billingRegions: billingRegions,
			paymentMethods: paymentMethods
		});

		orderExecutorModel.registerProducts(products);

		SetInstance(OrderExecutorModel, orderExecutorModel);

		let allOrders: OrderApiOut[] = [];
		let orders: OrderApiOut[] = [];
		do {
			let params: TypedQuerySearch<typeof ProductHttpOrderContractSearchConfig> = {
				filter: {
					name: 'cashStatementSessionUid',
					value: this.lastClosedSession.uid,
					operator: QueryOperator.EQUAL
				}
			};

			if(orders[49]) {
				params.cursorAfter = orders[49].uid
			}

			orders = (await this.orderRepository.callContract('search', {establishmentUid: this.establishmentUid}, params)).success();
			allOrders = allOrders.concat(orders);
		} while(orders.length === 50);

		const orderReceiptRender = new OrderReceiptRender();
		const parts = orderReceiptRender.renderCashStatement(orders, this.lastClosedSession);

		if(Array.isArray(this.noPrinterToast.action)) this.noPrinterToast.action[0].callback = async () => {
			this.displayedReceipt = await this.printerRepository.printToBase64(parts);
		}

		this.printerRepository.printToSelectedPrinter(parts, this.noPrinterToast).catch(async () => {});

		this.printingCashStatement = false;
	}
}