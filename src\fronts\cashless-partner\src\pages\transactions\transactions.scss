#transactions-page {
    .new-data {
        display: flex;
        align-items: center;

        .cursor-pointer {
            cursor: pointer;
            user-select: none;
        }
    }

    .selected-transaction {
        display: flex;
        flex-direction: column;
        padding: 40px;

        @media screen and (max-width: 900px) {
            padding: 20px;
        }

        .transaction-details {
            display: flex;
            flex-direction: column;
            gap: 20px;
            margin-top: 20px;

            .labels {
                display: flex;
                gap: 5px;
            }
        }

        h3 {
            margin: 0;
            font-size: 16px;
        }

        .products {
            display: flex;
            flex-direction: column;
            gap: 10px;

            .product {
                display: flex;
                align-items: center;
                gap: 10px;

                img {
                    width: 24px;
                }

                .name {
                    font-weight: 500;
                }
            }
        }

        .empty {
           text-align: center;
            font-style: italic;
        }
    }
}