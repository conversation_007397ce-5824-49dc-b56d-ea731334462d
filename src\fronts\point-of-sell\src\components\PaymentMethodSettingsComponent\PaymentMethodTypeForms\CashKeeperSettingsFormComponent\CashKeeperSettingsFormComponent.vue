<script lang="ts" src="./CashKeeperSettingsFormComponent.ts">
</script>

<style lang="sass" scoped>
@import './CashKeeperSettingsFormComponent.scss'
</style>

<template>
    <div class="cash-drawer-settings-form-component">
        <div class="form">
            <div class="input-group">
                <label for="title"> Nom de l'appareil </label>
                <div class="ui input">
                    <input v-model="settings.name" type="text" placeholder="Nom de l'appareil" />
                </div>
            </div>

            <div class="input-group">
                <label for="title"> IP du CashKeeper </label>
                <div class="ui input">
                    <input v-model="settings.address" type="text" placeholder="IP du CashKeeper" />
                </div>
            </div>

            <div class="two-inputs">
                <div class="input-group">
                    <label for="title"> Port MASTER </label>
                    <div class="ui input">
                        <input v-model.number="settings.masterPort" type="text" placeholder="Port MASTER" />
                    </div>
                </div>

                <div class="input-group">
                    <label for="title"> Port OFFICE </label>
                    <div class="ui input">
                        <input v-model.number="settings.officePort" type="text" placeholder="Port OFFICE" />
                    </div>
                </div>
            </div>

            <div class="input-group">
                <label for="title"> Security Seed </label>
                <div class="ui input">
                    <input v-model.number="settings.securitySeed" type="text" placeholder="Security seed" />
                </div>
            </div>

            <div class="buttons">
                <button class="tertiary button" :class="{loading: connectionTest === 'LOADING', disabled: connectionTest === 'LOADING'}" @click="testConnection()">
                    <i class="fa-regular fa-signal-stream"></i>
                    Tester la connection
                </button>
            </div>

            <div class="form-error" v-if="connectionTest === 'ERROR'">
                <i class="fa-solid fa-exclamation-circle"></i>
                <div class="details">
                    <span class="title"> Connexion impossible </span>
                    <span class="description"> Il semble que les informations fournies ne permettent pas de se connecter au CashKeeper </span>
                </div>
            </div>

            <div class="form-success" v-if="connectionTest === 'SUCCESS'">
                <i class="fa-solid fa-circle-check"></i>
                <div class="details">
                    <span class="title"> Connexion réussie </span>
                    <span class="description"> Les informations ont bien permis de se connecter au CashKeeper </span>
                </div>
            </div>

            <div class="buttons">
                <button class="ui white button" @click="close()">
                    Annuler
                </button>

                <button v-if="editing" class="ui red button" @click="deleteSettings()">
                    Supprimer
                </button>

                <button class="ui black button" @click="save()">
                    <i class="fa-regular fa-check" style="margin-top: 3px"></i>
                    Appliquer les paramètres
                </button>
            </div>
        </div>
    </div>
</template>