.device-selection-component {
    .ticket-data {
        display: flex;
        gap: 15px;
        align-items: center;
    }

    .iot-data {
        flex-grow: 2;
        display: flex;
        gap: 15px;
        align-items: center;

        .iot-icon {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 32px;
            width: 32px;
            background: var(--secondary-hover-color);
            border-radius: 4px;
            flex-shrink: 0;
        }

        .infos {
            display: flex;
            flex-direction: column;
            gap: 2px;

            .name {
                font-weight: 600;
            }
        }
    }


    .checkbox {
        display: flex;
        align-items: center;
        justify-content: center;
        border: 1px solid black;
        border-radius: 3px;
        height: 18px;
        width: 18px;
        cursor: pointer;
        box-sizing: border-box;
        flex-shrink: 0;

        i {
            font-size: 11px;
            display: none;
        }

        &.selected {
            border: none;
            background: #1099FD;
            color: white !important;

            i {
                display: initial;
            }
        }

        &.disabled {
            opacity: 0.5;
            pointer-events: none;
        }

        &.half-selected {
            color: black;

            i {
                display: initial;
            }
        }
    }
}