import {Component, Vue} from "vue-facing-decorator";
import {FilterTableLayoutComponent, ShortcutsManager, ForbiddenMessageComponent} from "@groupk/vue3-interface-sdk";
import {CashlessPermissions, SimpleProductApiOut, ApplicationPermission, EstablishmentAccountPermissionModel, CashlessHttpSimpleProductContract} from "@groupk/mastodon-core";
import {ContentHeaderParameters, TableColumn} from "@groupk/vue3-interface-sdk";
import {
	AutoWired,
	ScopedUuid,
	TypedQuerySearch,
	UuidUtils,
	VisualScopedUuid
} from "@groupk/horizon2-core";
import {TableColumnsRepository} from "../../../../../shared/repositories/TableColumnsRepository";
import {OptionBuilder} from "vue-facing-decorator/dist/optionBuilder";
import {ProductsRepository} from "../../../../../shared/repositories/ProductsRepository";
import {ComponentUtils} from "../../../../../shared/utils/ComponentUtils";
import {PermissionBuilder, uuidScopeEstablishment, UuidScopeEstablishment} from "@groupk/mastodon-core";
import {Router} from "@groupk/horizon2-front";
import ProductFormComponent from "../../components/ProductFormComponent/ProductFormComponent.vue";
import SidebarStateListener from "../../../../../shared/utils/SidebarStateListener";
import ImportProductModalComponent from "../../components/ImportProductModalComponent/ImportProductModalComponent.vue";
import {CashlessProductImportExportHelper} from "../../../../../shared/utils/CashlessProductImportExportHelper";
import {AppState} from "../../../../../shared/AppState";
import {ProductFormComponentHasRequiredPermissions} from "../../components/ProductFormComponent/ProductFormComponent";
import {ImportProductModalComponentHasRequiredPermissions} from "../../components/ImportProductModalComponent/ImportProductModalComponent";

@Component({
	components: {
		'filter-table-layout': FilterTableLayoutComponent,
		'product-form': ProductFormComponent,
		'product-import-modal': ImportProductModalComponent,
		'forbidden-message': ForbiddenMessageComponent,
	}
})
export default class ProductsView extends Vue {
	establishmentUid!: VisualScopedUuid<UuidScopeEstablishment>;
	productImportExportHelper!: CashlessProductImportExportHelper;
	products: SimpleProductApiOut[] = [];
	selectedProduct: SimpleProductApiOut|null = null;
	loading: boolean = false;
	forbidden: boolean = false;

	headerParameters: ContentHeaderParameters = {
		header: 'Produits',
		subtitle: 'Créez les produits que vous allez mettre en vente',
		actions: [{
			id: 'new',
			type:"SIMPLE_ACTION",
			name: 'Nouveau produit',
			icon: 'fa-regular fa-circle-plus',
			callback: this.createProduct
		}, {
			id: 'import',
			type:"SIMPLE_ACTION",
			name: 'Importer',
			icon: 'fa-regular fa-arrow-up-to-line',
			callback: this.showImport
		}, {
			id: 'export',
			type:"SIMPLE_ACTION",
			name: 'Exporter',
			icon: 'fa-regular fa-arrow-down-to-line',
			callback: this.exportProducts
		}],
		hideSearch: true,
		searchPlaceholder: 'Rechercher une campagne'
	}

	allowedFilters = { filters: [], sorts: [] };
	filters: TypedQuerySearch<{ filters: [], sorts: [] }> = {};

	tableKey = 'cashless-products';
	tableColumns: TableColumn[] = [{
		title: 'Icône', name: 'icon', displayed: true, mobileHidden: false
	}, {
		title: 'ID', name: 'id', displayed: false, mobileHidden: true
	}, {
		title: 'Titre', name: 'name', displayed: true, mobileHidden: false
	}, {
		title: 'Prix', name: 'price', displayed: true, mobileHidden: false
	}, {
		title: 'Date de création', name: 'creationDatetime', displayed: true, mobileHidden: true
	}];

	editingProduct: SimpleProductApiOut|null = null;
	showCreationModal: boolean = false;
	showImportModal: boolean = false;

	disabledActions: string[] = [];

	@AutoWired(ProductsRepository) accessor productsRepository!: ProductsRepository
	@AutoWired(TableColumnsRepository) accessor tableColumnsRepository!: TableColumnsRepository
	@AutoWired(SidebarStateListener) accessor sidebarStateListener!: SidebarStateListener
	@AutoWired(ShortcutsManager) accessor shortcutsManager!: ShortcutsManager;
	@AutoWired(AppState) accessor appState!: AppState;
	@AutoWired(Router) accessor router!: Router

	constructor(optionBuilder: OptionBuilder, vueInstance: any) {
		super(optionBuilder, vueInstance);

		this.sidebarStateListener.setHiddenSidebar(false);
		this.sidebarStateListener.setMinimizedSidebar(false);

		let regexMatch = this.router.lastRouteRegexMatches;

		if (regexMatch && regexMatch[1]) {
			this.establishmentUid = UuidUtils.uuidToVisual<UuidScopeEstablishment>(uuidScopeEstablishment,regexMatch[1] as ScopedUuid<UuidScopeEstablishment>);
		}
		this.productImportExportHelper = new CashlessProductImportExportHelper(this.establishmentUid);

		let savedPreferences = this.tableColumnsRepository.getColumnPreferences(this.tableKey, this.tableColumns);
		if(savedPreferences) this.tableColumns = savedPreferences;

		// Check permissions for form components and disable actions accordingly
		if (!ComponentUtils.hasPermissions(ProductFormComponentHasRequiredPermissions)) {
			ComponentUtils.disableActions(this.headerParameters, ['new']);
			this.disabledActions.push('edit');
		}

		if (!ComponentUtils.hasPermissions(ImportProductModalComponentHasRequiredPermissions)) {
			ComponentUtils.disableActions(this.headerParameters, ['import']);
		}
	}

	getNextProductId(){
		return this.products.reduce((acc, product) => {
			return product.id > acc ? product.id : acc;
		}, -1) + 1;
	}

	async mounted() {
		// Check for essential page functionality
		if(!ComponentUtils.hasPermissions((ownedPermissions: ApplicationPermission[]) => {
			return EstablishmentAccountPermissionModel.hasPermissionsForContracts(ownedPermissions, [
				CashlessHttpSimpleProductContract.list,
			])
		})) {
			this.forbidden = true;
			this.loading = false;
			return;
		}

		this.products = (await this.productsRepository.callContract('list', {establishmentUid: this.establishmentUid}, {})).success();
		this.setupShortcutListeners();
		this.loading = false;
	}


	beforeUnmount() {
		this.shortcutsManager.off('ArrowUp', this.handleKeypress);
		this.shortcutsManager.off('ArrowDown', this.handleKeypress);
	}

	setupShortcutListeners() {
		this.shortcutsManager.on('ArrowUp', this.handleKeypress);
		this.shortcutsManager.on('ArrowDown', this.handleKeypress);
	}

	handleKeypress(e: KeyboardEvent) {
		if (e.code === 'ArrowUp' || e.code === 'ArrowDown') {
			if (!this.selectedProduct) {
				this.selectedProduct = this.products[0] ?? null;
			} else {
				const currentIndex = this.products.findIndex((product) => product.id === this.selectedProduct?.id);
				if (e.code === 'ArrowUp') {
					if (currentIndex > 0) {
						this.selectedProduct = this.products[currentIndex - 1];
					}
				} else if (e.code === 'ArrowDown') {
					if (currentIndex < this.products.length - 1) {
						this.selectedProduct = this.products[currentIndex + 1];
					}
				}
			}
		} else if (e.code === 'Escape') {
			this.selectedProduct = null;
		}
	}

	saveColumnPreferences(columns: TableColumn[]) {
		this.tableColumnsRepository.saveColumnsPreferences(this.tableKey, columns);
	}

	createProduct() {
		this.showCreationModal = true;
	}

	showImport() {
		this.showImportModal = true;
	}

	savedProduct(savedProduct: SimpleProductApiOut) {
		const index = this.products.findIndex((product) => product.id === savedProduct.id);
		if(index !== -1) this.products.splice(index, 1, savedProduct);
		else this.products.push(savedProduct);

		if(this.selectedProduct && this.selectedProduct.id === savedProduct.id) this.selectedProduct = savedProduct;

		this.editingProduct = null;
		this.showCreationModal = false;
	}

	toggleSelectedProduct(product: SimpleProductApiOut) {
		const selection = window.getSelection();
		if (selection && !selection.isCollapsed) {
			return;
		}
		if(this.selectedProduct && this.selectedProduct.id === product.id) this.selectedProduct = null;
		else this.selectedProduct = product;
	}

	sorted(data: { name: string, direction: 'asc'|'desc' }) {
		const order = data.direction === 'asc' ? 1 : -1;
		this.products.sort((p1, p2) => {
			if(data.name === 'price') return (p1.prices[0] ?? 0) > (p2.prices[0] ?? 0) ? order : order * -1;
			else return 0;
		});
	}

	exportProducts(){
		this.productImportExportHelper.exportProducts();
	}
}