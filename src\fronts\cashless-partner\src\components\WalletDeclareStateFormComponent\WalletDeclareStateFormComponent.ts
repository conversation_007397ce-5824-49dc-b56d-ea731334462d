import {Component, Prop, Vue} from "vue-facing-decorator";
import {DropdownComponent, FormModalOrDrawerComponent} from "@groupk/vue3-interface-sdk";
import {AutoWired, VisualScopedUuid} from "@groupk/horizon2-core";
import {CustomerRepository} from "../../../../../shared/repositories/CustomerRepository";
import {
	CurrencyApiOut,
	ProfileApiOut,
	UuidScopeCashless_profile,
	WalletInactiveReason,
	ApplicationPermission,
	EstablishmentAccountPermissionModel,
	CashlessHttpWalletContract, CashlessHttpCurrencyContract, CashlessHttpProfileContract
} from "@groupk/mastodon-core";
import {CurrencyRepository} from "../../../../../shared/repositories/CurrencyRepository";
import {ProfilesRepository} from "../../../../../shared/repositories/ProfilesRepository";
import {TransactionsRepository} from "../../../../../shared/repositories/TransactionsRepository";
import {WalletsRepository} from "../../../../../shared/repositories/WalletsRepository";
import {WalletSetStateApiIn} from "@groupk/mastodon-core";
import {PaymentMethodsRepository} from "../../../../../shared/repositories/PaymentMethodsRepository";
import {UuidScopeCashless_wallet} from "@groupk/mastodon-core";
import {CustomerData} from "../../../../../shared/mastodonCoreFront/CustomerData";
import {AppState} from "../../../../../shared/AppState";

export function WalletDeclareStateFormComponentHasRequiredPermissions(ownedPermissions: ApplicationPermission[]): boolean {
	return EstablishmentAccountPermissionModel.hasPermissionsForContracts(ownedPermissions, [
		CashlessHttpWalletContract.setState,
		CashlessHttpCurrencyContract.list,
		CashlessHttpProfileContract.list,
	]);
}

@Component({
	components: {
		'form-modal-or-drawer': FormModalOrDrawerComponent,
		'dropdown': DropdownComponent
	},
	emits: ['close']
})
export default class WalletDeclareStateFormComponent extends Vue {
	@Prop() reason!: WalletInactiveReason;
	@Prop({required: true}) walletUid!: VisualScopedUuid<UuidScopeCashless_wallet>;

	currencies: CurrencyApiOut[] = [];
	profiles: ProfileApiOut[] = [];

	selectedProfile: VisualScopedUuid<UuidScopeCashless_profile>|null = null;
	customer: CustomerData = new CustomerData();

	WalletInactiveReason = WalletInactiveReason;

	opened: boolean = false;
	loading: boolean = true;
	saving: boolean = false;
	error: { title: string, description: string }|null = null;

	@AutoWired(CustomerRepository) accessor customerRepository!: CustomerRepository;
	@AutoWired(CurrencyRepository) accessor currencyRepository!: CurrencyRepository;
	@AutoWired(PaymentMethodsRepository) accessor paymentMethodsRepository!: PaymentMethodsRepository;
	@AutoWired(WalletsRepository) accessor walletsRepository!: WalletsRepository;
	@AutoWired(ProfilesRepository) accessor profilesRepository!: ProfilesRepository;
	@AutoWired(TransactionsRepository) accessor transactionsRepository!: TransactionsRepository;
	@AutoWired(AppState) accessor appState!: AppState;

	async mounted() {
		setTimeout(() => this.opened = true, 0);

		this.currencies = (await this.currencyRepository.callContract('list', {establishmentUid: this.appState.requireUrlEstablishmentUid()}, undefined)).success();
		this.profiles = (await this.profilesRepository.callContract('list', {establishmentUid: this.appState.requireUrlEstablishmentUid()}, undefined)).success();

		this.loading = false;
	}

	get profilesDropdownValues() {
		return this.profiles.map((profile) => {
			return {
				name: profile.name,
				value: profile.uid
			}
		})
	}

	close() {
		this.opened = false;
		setTimeout(() => this.$emit('close'), 300);
	}

	async validate() {
		this.error = null;
		if(!this.selectedProfile) {
			this.error = {
				title: 'Veuillez sélectionnez un point de vente',
				description: 'Le point de vente impactera les statistiques de remboursements, puisqu\'une transaction va vider la puce de son solde'
			};
			return;
		}

		this.saving = true;

		const apiIn = new WalletSetStateApiIn({
			inactiveReason: this.reason,
			profileUid: this.selectedProfile,
			disassociateCustomerChips: true,
		});

		const response = await this.walletsRepository.callContract(
			'setState',
			{establishmentUid: this.appState.requireUrlEstablishmentUid(), walletUid: this.walletUid},
			apiIn
		);

		if (response.isSuccess()) {
			this.close();
		} else {
			const error = response.error();
			if (error && 'error' in error) {
				if(error.error === 'tx_creation_weird_state') {
					this.error = {
						title: 'Une erreur est survenue',
						description: 'Le statut actuel du support ne permet pas cette action'
					}
				} else if(error.error === 'wallet_already_inactive') {
					this.error = {
						title: 'Une erreur est survenue',
						description: 'Ce support est déjà désactivé'
					}
				}

			} else {
				this.error = {
					title: 'Une erreur est survenue',
					description: 'Raison inconnue'
				}
			}
		}
		this.saving = false;
	}
}