#kiosk-founding-page {
    .top-content {
        display: flex;
        flex-direction: column;
        gap: 20px;
        padding: 40px;

        @media (max-width: 900px) {
            grid-template-columns: 1fr;

            padding: 40px var(--mobile-padding);
        }
    }

    .cards {
        display: grid;
        grid-template-columns: 1fr 1fr;
        grid-gap: 30px;

        @media screen and (max-width: 900px) {
            grid-template-columns: 1fr;
        }

        .card {
            display: flex;
            flex-direction: column;
            gap: 20px;
            justify-content: space-between;
            padding: 40px 30px;
            background: var(--secondary-hover-color);
            position: relative;
            box-sizing: border-box;
            border-radius: 8px;

            .header {
                display: flex;
                flex-direction: column;
                gap: 6px;

                .title {
                    font-size: 18px;
                    font-weight: 600;
                }

                .description {
                    font-size: 16px;
                    line-height: 22px;

                    .small {
                        display: inline-block;
                        font-size: 13px;
                        margin-top: 5px;
                    }
                }
            }

            .animation-container  {
                display: flex;
                justify-content: center;
                width: 100%;
                height: 160px;
            }

            .kiosk-image {
                height: 180px;
            }

            .stacked-transactions {
                position: relative;
                display: flex;
                flex-direction: column;
                gap: 5px;
                width: 100%;
                transform: translateY(-62px);
                margin-top: 80px;

                .automatic-transaction {
                    position: absolute;
                    left: 50%;
                    word-break: keep-all;
                    white-space: nowrap;
                    transform: translateY(0) translateX(-50%) scale(1);
                    transition: transform ease-out .2s, opacity ease-out .2s;

                    display: none;
                    &.step-1 {
                        transform: translateY(62px) translateX(-50%);
                        z-index: 12;
                        display: flex;
                    }

                    &.step-2 {
                        z-index: 11;
                        display: flex;
                    }

                    &.step-3 {
                        transform: translateY(-15px) scale(0.92) translateX(-50%);
                        z-index: 10;
                        filter: brightness(0.99);
                        display: flex;
                    }

                    &.step-4 {
                        transform: translateY(-28px) scale(0.82) translateX(-50%);
                        z-index: 9;
                        filter: brightness(0.98);
                        display: flex;
                    }
                }
            }

            .automatic-transaction {
                display: flex;
                align-items: center;
                gap: 10px;
                padding: 18px;
                border-radius: 8px;
                background: white;
                border: 1px solid var(--border-color);
                transition: ease-out .2s cubic-bezier(0.22, 1, 0.36, 1);
                width: 300px;
                justify-content: space-between;
                animation: .3s ease-out 0s 1 slideIn;

                .left {
                    display: flex;
                    align-items: center;
                    gap: 10px;
                }

                .hour {
                    font-size: 13px
                }

                .name {
                    font-size: 15px;
                    font-weight: 600;
                }

                .amount {
                    color: #16B926;
                    font-weight: 600;
                    margin-left: 20px;
                }

                @media (max-width: 900px) {
                    width: 240px;

                    .name {
                        font-size: 14px
                    }

                    .amount {
                        font-size: 14px;
                    }
                }
            }
        }
    }

    .banner {
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 20px;
        padding: 20px;
        border-radius: 12px;
        background: var(--primary-hover-color);

        @media screen and (max-width: 900px) {
            flex-direction: column;
            align-items: stretch;
        }

        .left {
            display: flex;
            flex-direction: column;
            gap: 4px;

            .title {
                font-weight: 600;
            }

            .description {
                font-size: 14px;
            }
        }
    }

    .selected-kiosk-funding {
        display: flex;
        flex-direction: column;
        gap: 20px;
        padding: 40px;

        @media (max-width: 900px) {
            padding: 20px;
        }
    }

    .value {
        display: inline-flex;
        align-items: center;
        gap: 5px;

        .tertiary.button {
            padding: 10px;
        }
    }
}

@-webkit-keyframes slideIn {
    from {
        transform: translateY(120px) translateX(-50%);
        opacity: 0;
    }
    to {
        transform: translateY(62px) translateX(-50%);
        opacity: 1;
    }
}