import {Component, Prop, Vue} from "vue-facing-decorator";
import {FormModalOrDrawerComponent, DropdownComponent} from "@groupk/vue3-interface-sdk";
import type {DropdownValue} from "@groupk/vue3-interface-sdk";
import {
	AutoWired, Uuid,
} from "@groupk/horizon2-core";
import {
	SimpleProductCategoryApiOut,
	ApplicationPermission,
	EstablishmentAccountPermissionModel,
	CashlessHttpSimpleProductCategoryContract
} from "@groupk/mastodon-core";
import {CategoriesRepository} from "../../../../../shared/repositories/CategoriesRepository";
import {CategoryData} from "../../../../../shared/mastodonCoreFront/cashless/CategoryData";
import {AppState} from "../../../../../shared/AppState";

export function CategoryFormComponentHasRequiredPermissions(ownedPermissions: ApplicationPermission[]): boolean {
	return EstablishmentAccountPermissionModel.hasPermissionsForContracts(ownedPermissions, [
		CashlessHttpSimpleProductCategoryContract.create,
		CashlessHttpSimpleProductCategoryContract.update,
	]);
}

@Component({
	components: {
		'form-modal-or-drawer': FormModalOrDrawerComponent,
		'dropdown': DropdownComponent
	},
	emits: ['created', 'updated']
})
export default class CategoryFormComponent extends Vue {
	@Prop({default: null}) editingCategory!: SimpleProductCategoryApiOut | null;
	@Prop({default: []}) parentCategories!: SimpleProductCategoryApiOut[];
	@Prop({default: null}) parentCategory!: Uuid | null;
	@Prop({default: true}) displayParentCategory!: boolean;

	category: CategoryData = new CategoryData();

	saving: boolean = false;
	deleting: boolean = false;
	error: string | null = null;

	@AutoWired(CategoriesRepository) accessor categoriesRepository!: CategoriesRepository;
	@AutoWired(AppState) accessor appState!: AppState;

	beforeMount() {
		this.category = new CategoryData(this.editingCategory);
		if(this.parentCategory) this.category.parent = this.parentCategory;
	}

	getParentDropdownValues(): DropdownValue[] {
		return [{name: 'Aucune', value: null} as DropdownValue].concat(this.parentCategories.map((category) => {
			return {
				name: category.name,
				value: category.uid
			} as DropdownValue
		}));
	}

	async deleteCategory() {
		if (!this.editingCategory) return;
		this.deleting = true;

		const response = await this.categoriesRepository.callContract('delete', {establishmentUid: this.appState.requireUrlEstablishmentUid(), categoryUid: this.editingCategory.uid}, undefined);
		if (response.isSuccess()) {
			this.$emit('deleted', this.editingCategory);
		} else {
			const error = response.error();
			if (error && 'error' in error) {
				if (error.error === 'cannot_delete_parent') {
					this.error = 'Impossible de supprimer une catégorie parent';
				} else {
					this.error = error.error;
				}
			} else {
				this.error = 'Une erreur inconnue est survenue'
			}
		}

		this.deleting = false;
	}

	async save() {
		this.saving = true;
		this.error = null;

		if (!this.appState.advancedInterfaces && this.category.parent === null) {
			this.error = 'Veuillez sélectionner une catégorie parent';
			this.saving = false;
			return;
		}

		try {
			const apiIn = this.category.toApiIn();

			if (this.editingCategory === null) {
				const response = await this.categoriesRepository.callContract('create', {establishmentUid: this.appState.requireUrlEstablishmentUid()}, apiIn);
				if (response.isSuccess()) {
					const category = response.success();

					this.$emit('created', category);
				} else {
					const error = response.error();
					if (error && 'error' in error) {
						if (error.error === 'invalid_data') this.error = error.error_details;
						if (error.error === 'unknown_product') this.error = 'Produit inconnu';
						if (error.error === 'unknown_product') this.error = 'Parent inconnu';
					} else {
						this.error = 'Erreur inconnue';
					}
				}
			} else {
				const response = await this.categoriesRepository.callContract('update', {establishmentUid: this.appState.requireUrlEstablishmentUid(), categoryUid: this.editingCategory.uid}, apiIn);
				if (response.isSuccess()) {
					const category = response.success();
					this.$emit('updated', category);
				} else {
					const error = response.error();
					if (error && 'error' in error) {
						if (error.error === 'invalid_data') this.error = error.error_details;
						if (error.error === 'unknown_product') this.error = 'Produit inconnu';
						if (error.error === 'unknown_product') this.error = 'Parent inconnu';
					} else {
						this.error = 'Erreur inconnue';
					}
				}
			}
		} catch (err) {
			// form errors
			console.log(err);
		}

		this.saving = false;
	}
}