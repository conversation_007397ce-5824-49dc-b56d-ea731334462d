.web-qr-code-reader-component {
    .browser-camera-dimmer {
        display: none;

        &.opened {
            position: fixed;
            inset: 0;
            background: rgba(0, 0, 0, 0.4);
            z-index: 5;
            display: initial;
        }
    }

    .browser-camera-drawer {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        z-index: 6;
        height: 90%;
        border-radius: 16px 16px 0 0;
        background: black;
        transform: translateY(100%);
        transition: transform .5s cubic-bezier(0.22, 1, 0.36, 1);
        overflow: hidden;

        &.opened {
            transform: none;
        }

        video {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
    }

    .devices {
        display: flex;
        flex-direction: column;
        gap: 10px;

        .video-device {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 15px 20px;
            border: 1px solid black;
            border-radius: 8px;

            i {
                font-size: 15px;
            }
        }
    }

    .no-device {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 20px;
        text-align: center;
        font-style: italic;
        margin-top: 20px;
    }

    .loader-container {
        display: flex;
        justify-content: center;
    }
}