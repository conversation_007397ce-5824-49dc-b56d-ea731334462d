<script lang="ts" src="./RightModalComponent.ts">
</script>

<style lang="sass">
@import './RightModalComponent.scss'
</style>

<template>
    <div class="right-modal-component" :class="{opened: opened, stacked: stacked, 'performance-mode': posProfile.performanceMode}">
        <slot></slot>

        <div class="fixed-bottom">
            <div class="form-error" v-if="error">
                <i class="fa-regular fa-circle-exclamation"></i>
                <div class="details">
                    <span class="title">
                        Une erreur est survenue
                    </span>
                    {{ error }}
                </div>
            </div>

            <button class="bottom button" :class="{loading: bottomButtonLoading, disabled: bottomButtonLoading}" @click="bottomButtonClicked()">
                {{ bottomButtonText }}
            </button>
        </div>
    </div>
</template>