<script lang="ts" src="./GroupOptionsComponent.ts">
</script>

<style lang="sass" scoped>
@import './GroupOptionsComponent.scss'
</style>

<template>
    <div class="group-option-component">
        <div class="header">
            <span class="title"> {{ product.lastRevision.name }} </span>
            <span class="subtitle"> Configurez le produit </span>
        </div>

        <div class="steps">
            <div class="step" :class="{active: index === currentGroup}" v-for="(group, index) of product.lastRevision.groups">
                <span class="number"> {{ index + 1 }} </span>
                <span class="name"> {{ group.name }} </span>
            </div>
        </div>

        <div class="restrictions" v-if="product.lastRevision.groups[currentGroup].maxQuantity">
            {{ getGroupQuantity() }} / {{ product.lastRevision.groups[currentGroup].maxQuantity }}
            option{{ (product.lastRevision.groups[currentGroup].maxQuantity??0) > 1 ? 's' : '' }}
            maximum
        </div>

        <div class="options">
            <div
                v-for="item in product.lastRevision.groups[currentGroup].items"
                class="option"
                :class="{disabled: isGroupFull() || !isItemSelectable(item)}"
                @click="selectProduct(item, posState.getProductWithUid(item.product.uid))"
            >
                <div class="quantity"> {{ getItemQuantity(item) }} </div>
                <div class="top"> {{ posState.getProductWithUid(item.product.uid).lastRevision.name }} </div>
                <div class="bottom"> {{ getItemPrice(item) > 0 ? ('+' + $filters.Money(getItemPrice(item))) : 'Inclus' }} </div>
            </div>
        </div>

        <div class="buttons spaced">
            <button class="white button" @click="cancel()">
                <i class="fa-regular fa-close"></i>
                <span> Annuler </span>
            </button>

            <div class="buttons">
                <button class="white button" @click="currentGroup--" v-if="currentGroup > 0">
                    <i class="fa-regular fa-arrow-left"></i>
                    Préc.
                </button>
                <button class="button" :class="{disabled: !isGroupCorrect(product.lastRevision.groups[currentGroup])}" @click="nextStep()">
                    <template v-if="currentGroup + 1 < product.lastRevision.groups.length">
                        Suiv.
                        <i class="fa-regular fa-arrow-right"></i>
                    </template>
                    <template v-else>
                        <i class="fa-regular fa-check"></i>
                        Valider
                    </template>
                </button>
            </div>
        </div>

        <product-required-metadata-list
            v-if="addingProductWithRequiredMetadata"
            :product="addingProductWithRequiredMetadata.product"
            :pos-state="posState"
            @done="selectProduct(addingProductWithRequiredMetadata.item, addingProductWithRequiredMetadata.product, $event)"
            @cancel="addingProductWithRequiredMetadata = null"
        ></product-required-metadata-list>
    </div>
</template>