<script lang="ts" src="./PermissionGroupFormComponent.ts">
</script>

<style lang="sass">
@import './PermissionGroupFormComponent.scss'
</style>

<template>
    <div class="permission-group-form-component">
        <form-modal-or-drawer
            :state="opened"
            title="Modifier les permissions"
            subtitle="-"
            @close="close()"
        >
            <template v-slot:content>
                <div class="permissions" v-if="currentFrontPermissions">
                    <div class="permission" :class="{toggled: isToggledFront(permission)}" @click="toggleFront(permission)" v-for="permission of availableFrontPermissions">
                        <i v-if="!isToggledFront(permission)" class="fa-regular fa-circle"></i>
                        <i v-else class="fa-regular fa-circle-dot"></i>

                        <span> {{ permission }} </span>
                    </div>
                </div>

                <div class="permissions">
                    <div class="permission" :class="{toggled: isToggled(permission)}" @click="toggle(permission)" v-for="permission of allPermissions">
                        <i v-if="!isToggled(permission)" class="fa-regular fa-circle"></i>
                        <i v-else class="fa-regular fa-circle-dot"></i>

                        <span> {{ permission.applicationId }} {{ permission.id }} </span>
                    </div>
                </div>
            </template>

            <template v-slot:buttons>
                <button type="button" class="white button" @click="close()"> Annuler </button>
                <button type="button" class="button" @click="save()"> Valider </button>
            </template>
        </form-modal-or-drawer>
    </div>
</template>