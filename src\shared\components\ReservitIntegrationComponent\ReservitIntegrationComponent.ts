import {Component, Vue} from "vue-facing-decorator";
import {FormModalOrDrawerComponent} from "@groupk/vue3-interface-sdk";
import {ReservitAppRepository} from "../../repositories/ReservitAppRepository";
import {AutoWired} from "@groupk/horizon2-core";
import {AppReservitApiIn, AppReservitPlatform, AppType, ReservitAppHttpContractAggregate} from "@groupk/mastodon-core";
import {AppState} from "../../AppState";
import {translateResponseError} from "../../RepositoryExtensions";

@Component({
    components: {
        'form-modal-or-drawer': FormModalOrDrawerComponent,
    }
})
export default class ReservitIntegrationComponent extends Vue {
    opened: boolean = false;

    reservitApp: AppReservitApiIn = new AppReservitApiIn({
        type: AppType.RESERVIT,
        name: 'Reservit',
        token: '',
        platform: AppReservitPlatform.DEMO
    });

    error: string|null = null;

    AppReservitPlatform = AppReservitPlatform;

    @AutoWired(ReservitAppRepository) private accessor reservitAppRepository!: ReservitAppRepository;
    @AutoWired(AppState) private accessor appState!: AppState;

    mounted() {
        setTimeout(() => this.opened = true, 0);
    }

    async save() {
        const response = await this.reservitAppRepository.callContract('createApp', {establishmentUid: this.appState.requireUrlEstablishmentUid()}, this.reservitApp);
        if(response.isSuccess()) {
            this.$emit('created', response.success());
            this.close();
        } else {
            this.error = translateResponseError<typeof ReservitAppHttpContractAggregate, 'createApp'>(response, {});
        }
    }

    close() {
        this.opened = false;
        setTimeout(() => this.$emit('close'), 300);
    }
}