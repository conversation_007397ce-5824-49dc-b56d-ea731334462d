<script lang="ts" src="./GroupOptionWithCartComponent.ts">
</script>

<style lang="sass" scoped>
@import './GroupOptionWithCartComponent.scss'
</style>

<template>
    <div class="vertical-container">
        <div class="group-option-with-cart-component" :class="{'performance-mode': posProfile.performanceMode, 'display-cart': displayCart}">
            <div class="stacked-groups">
                <group-option
                    v-for="(data, index) in stackingContext"
                    :key="data.product.uid"
                    :class="`position-${stackingContext.length - index > 2 ? 'small' : (stackingContext.length - index)}`"
                    :pos-state="posState"
                    :root-product="product"
                    :product="data.product"
                    :item="data.item ?? null"
                    :descriptor="data.descriptor"
                    :stacking-context="stackingContext"
                    @select-product="addToContext($event)"
                    @cancel="cancelGroup()"
                    @configured-group="configuredGroup($event)"
                ></group-option>
            </div>
            <div class="cart" v-if="!initiate">
                <div class="empty" v-if="getCurrentGroups().reduce((total, group) => total + group.items.length, 0) === 0">
                    Les produits choisis apparaîtrons ici
                </div>
                <template v-else>
                    <div class="groups">
                        <div class="group" v-for="group of getCurrentGroups()">
                            <div class="group-name" v-if="group.items.length > 0"> {{ getGroupWithUid(group.groupUid).name }} </div>
                            <div class="purchase" v-for="item of group.items">
                                <div v-if="item.quantity > 1"  class="quantity"> {{ item.quantity }} </div>

                                <div class="left">
                                    <span class="title"> {{ posState.getProductWithUid(item.productUid).lastRevision.name }} </span>

                                    <div class="sub-items" v-if="getItemRecursiveItems(item).length > 1">
                                        <template v-for="(subItem, index) of getItemRecursiveItems(item)">
                                        <span v-if="index !== 0">
                                            x{{ subItem.quantity }}
                                            {{ subItem.name }}
                                        </span>
                                        </template>
                                    </div>

                                    <div class="edit" v-if="getItemRecursiveItems(item).length > 1" @click="editItem(item, group.groupUid)">
                                        <i class="fa-regular fa-pen-line"></i>
                                        Éditer
                                    </div>
                                </div>

                                <div class="cancel" @click="cancelItem(item, group)">
                                    <i class="fa-regular fa-xmark"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </template>
                <div class="recap">
                    <span class="title"> Total </span>
                    <span class="amount"> {{ $filters.Money(getTotalPrice()) }} </span>
                </div>
            </div>

            <div class="show-cart" @click="displayCart = !displayCart">
                <span v-if="!displayCart"> Afficher le panier </span>
                <span v-else> Masquer le panier </span>
            </div>
        </div>

        <div class="step-selector" v-if="posState.currentOrder && product.lastRevision.type === 'FM'">
            <span> Choisir une étape </span>
            <div
                v-for="step of posState.currentOrder.getSteps()"
                class="step"
                :class="{selected: step === posState.currentOrder.currentStep}"
                @click="posState.currentOrder.currentStep = step"
            >
                {{ step === 0 ? 'Direct' : ('Suite ' + step) }}
            </div>
            <div class="step" @click="posState.currentOrder.currentStep++">
                <i class="fa-regular fa-plus"></i>
            </div>
        </div>

<!--        <metadata-descriptor-form-->
<!--            :metadata-descriptor="posState.metadataDescriptors[0]"-->
<!--        ></metadata-descriptor-form>-->
    </div>

</template>