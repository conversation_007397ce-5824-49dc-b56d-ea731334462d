import {PosState} from "../model/PosState";
import {AutoWired, EventEmitter} from "@groupk/horizon2-core";
import {LocalOrderTransferRepository} from "../repositories/LocalOrderTransferRepository";
import {OrderRepository} from "../../../../shared/repositories/OrderRepository";
import {
    IotDeviceJsonRpcContract,
    MastodonJsonRpcNetworkContract,
} from "@groupk/mastodon-core";
import {LocalOrderRepository} from "../repositories/LocalOrderRepository";
import {AppBus} from "../config/AppBus";
import {JsonRpcWsClientContractor} from "@groupk/horizon2-front";
import {IotDeviceConnectWs, WsAuthTokenApiIn} from "@groupk/mastodon-core";
import {MainConfig} from "../../../../shared/MainConfig";
import {AuthStateModel} from "../../../../shared/AuthStateModel";
import {EstablishmentDeviceAppSetStateApiIn} from "@groupk/mastodon-core";

export class WebSocketManager extends EventEmitter<{
    reconnect: void;
}> {
    public readonly connection: JsonRpcWsClientContractor;
    private nextReconnectTimeout: number = 0;
    private posState: PosState;

    @AutoWired(OrderRepository) accessor orderRepository!: OrderRepository;
    @AutoWired(LocalOrderRepository) accessor localOrderRepository!: LocalOrderRepository;
    @AutoWired(LocalOrderTransferRepository) accessor localOrderTransferRepository!: LocalOrderTransferRepository;
    @AutoWired(AppBus) accessor appBus!: AppBus;
    @AutoWired(MainConfig) accessor mainConfig!: MainConfig;
    @AutoWired(AuthStateModel) accessor authStateModel!: AuthStateModel;

    constructor(posState: PosState) {
        super();
        console.log('WEBSOCKETMANAGER');
        this.posState = posState;
        this.connection = new JsonRpcWsClientContractor(this.mainConfig.configuration.remoteWsEndpoint);

        this.connection.connect().catch();

        this.connection.on('close', async () => {
            console.log('DISCONNECTED FROM REMOTE');
            this.scheduleReconnect();
        });
        this.connection.on('connected', async () => {
            console.log('RECONNECT');

            const token = this.authStateModel.getStateSync();
            console.log(token);
            if (token === null) {
                this.connection.close();
                return;
            }

            const mastodonContract = this.connection.getForContract(MastodonJsonRpcNetworkContract);

            const authResult = await mastodonContract.request('mastodon_auth',
                new WsAuthTokenApiIn(token.token, this.posState.establishmentUid)
            );

            if (authResult.isError()) {
                console.log('authResult.isError()');
                console.log(authResult.error());
                // AppState.getInstance().setAccessToken(null);
                this.connection.close();
                return;
            }

            const iotContract = this.connection.getForContract(IotDeviceJsonRpcContract);
            await iotContract.request('iot_connect', new IotDeviceConnectWs({
                deviceUid: this.posState.iotDevice.uid,
                state: new EstablishmentDeviceAppSetStateApiIn({})
            }))
            console.log('emit reconnect');
            this.emit('reconnect', undefined);
        });
    }

    start() {
        this.scheduleReconnect(0);
    }

    scheduleReconnect(delay: number = 5 * 1000) {
        if (this.nextReconnectTimeout === 0 && this.authStateModel.getStateSync() !== null && !this.isConnected) {
            this.nextReconnectTimeout = window.setTimeout(async () => {
                this.nextReconnectTimeout = 0;
                this.connection.connect().catch(() => {
                    this.scheduleReconnect();
                });
            }, delay);
        }
    }

    get isConnected() {
        return this.connection.isConnected;
    }
}