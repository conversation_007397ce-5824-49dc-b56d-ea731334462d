import {Component, Vue} from "vue-facing-decorator";
import {FormModalOrDrawerComponent} from "@groupk/vue3-interface-sdk";
import {AutoWired} from "@groupk/horizon2-core";
import {EstablishmentDeviceRepository} from "../../../../../shared/repositories/EstablishmentDeviceRepository";
import {
    ApplicationPermission,
    EstablishmentAccountPermissionModel,
    IotEstablishmentDeviceV2HttpContract,
    QuickLinkApiOut
} from "@groupk/mastodon-core";
import {AppBus} from "../../config/AppBus";
import ToastManagerComponent from "../ToastManagerComponent/ToastManagerComponent.vue";
import {AppState} from "../../../../../shared/AppState";
import {translateResponseError} from "../../../../../shared/RepositoryExtensions";
import {DeviceImportExportHelper} from "../../../../../shared/utils/DeviceImportExportHelper";

export function DeviceOnboardingScanComponentHasRequiredPermissions(ownedPermissions: ApplicationPermission[]): boolean {
    return EstablishmentAccountPermissionModel.hasPermissionsForContracts(ownedPermissions, [
        IotEstablishmentDeviceV2HttpContract.searchQuickLinks,
        IotEstablishmentDeviceV2HttpContract.importQuickLink,
    ]);
}

@Component({
    components: {
        'form-modal-or-drawer': FormModalOrDrawerComponent,
        'toast-manager': ToastManagerComponent
    }
})
export default class DeviceOnboardingScanComponent extends Vue {
    quickLinks: QuickLinkApiOut[] = [];

    updatingQuickLinks: boolean = false;
    opened: boolean = false;

    @AutoWired(EstablishmentDeviceRepository) accessor establishmentDeviceRepository!: EstablishmentDeviceRepository;
    @AutoWired(AppState) accessor appState!: AppState;
    @AutoWired(AppBus) accessor appBus!: AppBus;

    async mounted() {
        setTimeout(() => this.opened = true, 0);

        await this.updateQuickLinkList();
    }

    async updateQuickLinkList() {
        this.updatingQuickLinks = true;

        const response = (await this.establishmentDeviceRepository.callContract('searchQuickLinks', {establishmentUid: this.appState.requireUrlEstablishmentUid()}, undefined));

        await new Promise(resolve => setTimeout(resolve, 500));

        if(response.isSuccess()) {
            this.quickLinks = response.success().list;
        } else {
            this.appBus.emit('emit-toast', {
                title: 'La liste n\'a pas pu être rafraîchie',
                description: translateResponseError<typeof IotEstablishmentDeviceV2HttpContract, 'searchQuickLinks'>(response, {}) ?? '',
                duration: 3000,
                type: 'ERROR',
                closable: true
            });
        }

        this.updatingQuickLinks = false;
    }

    async importQuickLink(quickLinkToImport: QuickLinkApiOut) {
        const response = await this.establishmentDeviceRepository.callContract(
            'importQuickLink',
            {establishmentUid: this.appState.requireUrlEstablishmentUid(), quickLinkUid: quickLinkToImport.uid},
            undefined
        );

        if(response.isSuccess()) {
            const quickLink = response.success().item;

            await DeviceImportExportHelper.createEstablishmentAccountForDevice(quickLink);

            const index = this.quickLinks.findIndex((quickLink) => quickLink.uid === quickLinkToImport.uid);
            if(index !== -1) this.quickLinks.splice(index, 1);

            this.$emit('imported', quickLink);

            this.appBus.emit('emit-toast', {
                title: 'Appareil ajouté',
                description: 'L\'appareil a bien été ajouté à votre établissement',
                duration: 3000,
                type: 'SUCCESS',
                closable: true
            });
        } else {
            this.appBus.emit('emit-toast', {
                title: 'L\'appareil n\'a pas pu être importé',
                description: translateResponseError<typeof IotEstablishmentDeviceV2HttpContract, 'importQuickLink'>(response, {}) ?? '',
                duration: 3000,
                type: 'ERROR',
                closable: true
            });
        }
    }

    close() {
        this.opened = false;
        setTimeout(() => this.$emit('close'), 300);
    }
}