<script lang="ts" src="./MobileNavigationComponent.ts">
</script>

<style lang="sass">
@import './MobileNavigationComponent.scss'
</style>

<template>
    <div class="mobile-navigation-component">
        <div class="item" :class="{active: posState.currentPage === 'sell'}" @click="posState.currentPage = 'sell'; posState.showQuickActions = false;">
            <i class="far fa-badge-dollar"></i>
            <span> Caisse </span>
        </div>
        <div class="item" :class="{active: posState.currentPage === 'orders'}" @click="posState.currentPage = 'orders'; posState.showQuickActions = false;">
            <i class="far fa-th-list"></i>
            <span> Commandes </span>
        </div>
        <div class="item" :class="{active: posState.currentPage === 'tables'}" @click="posState.currentPage = 'tables'; posState.showQuickActions = false;" v-if="posState.isDiningEnabled()">
            <i class="fa-regular fa-hashtag"></i>
            <span> Tables </span>
        </div>
        <div class="item" :class="{active: posState.currentPage === 'statistics'}" @click="posState.currentPage = 'statistics'; posState.showQuickActions = false;" v-else>
            <i class="fa-regular fa-chart-simple"></i>
            <span> Statistiques </span>
        </div>
        <div class="item" :class="{active: posState.currentPage === 'settings'}" @click="posState.currentPage = 'settings'; posState.showQuickActions = false;">
            <i class="fa-regular fa-cog"></i>
            <span> Paramètres </span>
        </div>
    </div>
</template>