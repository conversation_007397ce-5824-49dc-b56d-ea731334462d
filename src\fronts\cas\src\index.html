<!DOCTYPE html>
<html lang="fr">
<head>
	<title> Mastodon - Connexion </title>
	<link rel="icon" type="image/svg+xml" href="/img/icon.png">
	<meta charset="UTF-8"/>
	<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no"/>
	<link href="//fonts.googleapis.com/css?family=Montserrat:thin,extra-light,light,100,200,300,400,500,600,700,800" rel="stylesheet" type="text/css">
</head>
<body>
<div id="oldRouterContainer"></div>
<div id="mainRouterContainer"></div>

<script>
    if(typeof String.prototype.replaceAll === 'undefined')
        String.prototype.replaceAll = function(str1, str2, ignore){
            return this.replace(new RegExp(str1.replace(/([\/\,\!\\\^\$\{\}\[\]\(\)\.\*\+\?\|\<\>\-\&])/g,"\\$&"),(ignore?"gi":"g")),(typeof(str2)=="string")?str2.replace(/\$/g,"$$$$"):str2);
        };
</script>

<script type="module" src="./index.ts"></script>

<script id="config" type="application/json">
	"__INJECTED_CONFIG__"
</script>

</body>
</html>
