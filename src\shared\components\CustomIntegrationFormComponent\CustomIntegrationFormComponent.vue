<script lang="ts" src="./CustomIntegrationFormComponent.ts" />

<style lang="sass" scoped>
@import './CustomIntegrationFormComponent.scss'
</style>

<template>
    <div class="custom-integration-form-component">
        <form-modal-or-drawer
            :state="opened"
            :title="'Intégration manuelle'"
            :subtitle="'Configurez les webhooks manuellement'"
            @close="close()"
        >
            <template v-slot:content>
                <div class="input-group" :class="{disabled: editingApp !== null}">
                    <label> Nom de l'intégration </label>
                    <input v-model="appName" placeholder="Nom de l'intégration" />
                </div>

                <div class="sub-form" v-for="eventDestination of eventDestinations">
                    <div class="input-group">
                        <label> Événement </label>
                        <dropdown
                            :values="getAppEventDropdownValues()"
                            :default-selected="eventDestination.event"
                            @update="eventDestination.event = $event"
                        ></dropdown>
                    </div>

                    <div class="two-inputs">
                        <div class="input-group">
                            <label> Méthode </label>
                            <dropdown
                                :values="getWebhookMethodDropdownValues()"
                                :default-selected="eventDestination.config.method"
                                @update="eventDestination.config.method = $event"
                            ></dropdown>
                        </div>
                    </div>

                    <div class="input-group">
                        <label> Url </label>
                        <input placeholder="url" v-model="eventDestination.config.url" />
                    </div>
                </div>

                <button class="grey button" @click="addEventDestination()">
                    <i class="fa-regular fa-plus"></i>
                    Ajouter un url
                </button>
            </template>

            <template v-slot:buttons>
                <button type="button" class="white button" @click="close()"> Fermer </button>
                <button type="button" class="button" @click="save()"> Sauvegarder </button>
            </template>
        </form-modal-or-drawer>
    </div>
</template>