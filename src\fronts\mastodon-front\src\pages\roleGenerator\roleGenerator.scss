#role-generator-page {
    height: 100%;
    padding: 40px;
    box-sizing: border-box;
    overflow: scroll;

    .container {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        gap: 20px;
        margin-top: 40px;

        .contracts {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            grid-gap: 20px;
            width: 100%;

            .contract {
                display: flex;
                flex-direction: column;
                gap: 20px;
                padding: 20px;
                border: 1px solid var(--border-color);
                width: 100%;
                box-sizing: border-box;

                .permissions-groups {
                    display: flex;
                    flex-direction: column;
                    gap: 20px;
                    padding: 20px;
                    background: var(--secondary-hover-color);

                    .head {
                        display: flex;
                        justify-content: space-between;
                        align-items: center;

                        i {
                            padding: 10px;
                            cursor: pointer;
                            border-radius: 10px;

                            &:hover {
                                background: white;
                            }
                        }
                    }

                    .permissions-group {
                        display: flex;
                        flex-direction: column;
                        gap: 20px;

                        padding: 20px;
                        background: #D9D9D9;

                        input {
                            background: white;
                        }
                    }
                }
            }
        }
    }
}
