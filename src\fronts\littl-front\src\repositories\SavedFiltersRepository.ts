import {
	SavedFilter
} from "@groupk/vue3-interface-sdk";

export type PageIndexedSavedFilters = {
	[key: string]: SavedFilter[]
}

export class SavedFiltersRepository {
	private lsKey = 'saved-filters';

	saveFilters(key: string, toSave: SavedFilter[]) {
		const rawFilters: string|null = localStorage.getItem(this.lsKey);
		let filters: PageIndexedSavedFilters = {};
		if(rawFilters !== null) {
			filters = JSON.parse(rawFilters);
		}

		filters[key] = toSave;

		localStorage.setItem(this.lsKey, JSON.stringify(filters));
	}

	getFilters(key: string): SavedFilter[]|null {
		const rawFilters: string|null = localStorage.getItem(this.lsKey);
		let filters: PageIndexedSavedFilters = {};
		if(rawFilters !== null) {
			filters = JSON.parse(rawFilters);
		}
		return filters[key] ?? [];
	}
}