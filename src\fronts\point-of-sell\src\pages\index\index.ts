import {Component, Vue} from "vue-facing-decorator";
import {OptionBuilder} from "vue-facing-decorator/dist/optionBuilder";
import {GetInstance} from "@groupk/horizon2-core";
import {MainConfig} from "../../../../../shared/MainConfig";

@Component({})
export default class IndexView extends Vue {
	constructor(optionBuilder: OptionBuilder, vueInstance: any) {
		super(optionBuilder, vueInstance);

		const config = GetInstance(MainConfig);
		window.location.href = config.configuration.casFrontUrl + '?platform=PointOfSell2';
	}
}