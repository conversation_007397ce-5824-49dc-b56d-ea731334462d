<script lang="ts" src="./PackageValidationComponent.ts"/>

<style lang="sass" scoped>
@import './PackageValidationComponent.scss'
</style>

<template>
    <div class="package-validation-component">
        <div class="container">
            <div class="actions">
                <div class="action-group" @click="close()">
                    <div class="action">
                        <i class="fa-regular fa-arrow-left"></i>
                    </div>
                    <span class="text"> retour </span>
                </div>
            </div>

            <div class="done" v-if="notScannedObjects.length === 0">
                Le colis est complet

                <button class="button" :class="{loading: saving, disabled: saving}" @click="validatePackage()">
                    Valider le colis
                </button>
            </div>

            <template v-else>
                <h3> Objets non vérifiés ({{ notScannedObjects.length }})</h3>

                <div class="objects">
                    <div class="object" v-for="objectData of notScannedObjects" @click="showActionsForObject = objectData">
                        {{ getObjectTranslation(objectData.fields.code) }}

                        <div class="images">
                            <i class="fa-regular fa-image" v-if="objectData.fields.image"></i>
                            <i class="fa-regular fa-image" v-if="objectData.fields.returnImage"></i>
                        </div>
                    </div>
                </div>
            </template>

            <h3> Objets vérifiés ({{ scannedObjects.length }})</h3>

            <div class="objects">
                <div class="object" v-for="objectData of scannedObjects" @click="showActionsForObject = objectData">
                    {{ getObjectTranslation(objectData.fields.code) }}

                    <div class="images">
                        <i class="fa-regular fa-image" v-if="objectData.fields.image"></i>
                        <i class="fa-regular fa-image" v-if="objectData.fields.returnImage"></i>
                    </div>
                </div>
            </div>
        </div>

        <div class="scan-state success" v-if="scanState === 'SUCCESS'"></div>
        <div class="scan-state error" v-else-if="scanState === 'ERROR'"></div>

        <div class="scanning-modal" v-if="scanning">
            <div class="container">
                <div class="loading-container">
                    <div class="loader"></div>
                </div>
            </div>
        </div>

        <item-actions
            v-if="showActionsForObject"
            :actions="getObjectActions(showActionsForObject)"
            @action-clicked="actionClicked($event)"
            @close="showActionsForObject = null"
        ></item-actions>

        <camera
            v-if="showCamera && showCamera.opened"
            @photo-captured="photoCaptured($event)"
            @close="showCamera.opened = false"
        ></camera>

        <img class="object-image" v-if="showObjectImage" :src="showObjectImage.fields.image" @click="showObjectImage = null" />
        <img class="object-image" v-if="showObjectReturnImage" :src="showObjectReturnImage.fields.returnImage" @click="showObjectReturnImage = null" />
    </div>
</template>