<script lang="ts" src="./ProductChartWithComparisonComponent.ts"/>

<style lang="sass" scoped>
@import './ProductChartWithComparisonComponent.scss'
</style>

<template>
    <div class="product-chart-with-comparison-component">
        <div class="chart-area">
            <line-chart
                v-if="selectedProduct"
                :key="selectedProduct.uid + '-' + compareWithProducts.join('-')"
                class="ca-chart"
                :data="getSelectedProductChartData()"
                :area-at-index="cashStatementsDataIndex"
            ></line-chart>
        </div>

        <div class="compare-with">
            <select-multiple-dropdown
                placeholder="Comparer avec"
                :searchable="true"
                :values="productComparisonDropdownValues"
                :default-selected="[]"
                @update="compareWithProducts = $event"
            ></select-multiple-dropdown>
        </div>
    </div>
</template>