#add-chip-page {
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 30px;
    background: white;
    max-width: 500px;
    margin: auto;
    padding: 30px 20px 50px 20px;
    position: relative;

    .form {
        display: flex;
        flex-direction: column;
        gap: 30px;

        .input {
            display: flex;
            flex-direction: column;
            gap: 10px;

            label {
                font-size: 18px;
            }

            .description {
                color: #575757;
            }

            .info {
                color: #2c7ffc;
                cursor: pointer;
            }

            input {
                all: unset;
                font-size: 18px;
                padding: 10px 15px;

                border: 1px solid black;
                border-radius: 8px;
                width: 100%;
                box-sizing: border-box;
            }
        }
    }

    .add-button {
        display: flex;
        align-items: center;
        justify-content: center;
        background: #2AB9D9;
        height: 50px;
        color: white;
        font-size: 16px;
        font-weight: bold;
        border-radius: 100px;
        cursor: pointer;

        &:hover {
            filter: brightness(0.96);
        }

        &.disabled {
            background: #F2F2F2;
            color: rgba(0, 0, 0, 0.47);
            font-weight: normal;
            pointer-events: none;
        }
    }

    .code-hint-dimmer {
        position: fixed;
        inset: 0;
        z-index: 500;
        background: rgba(0, 0, 0, 0.6);
    }
    .close-code-hint {
        display: flex;
        align-items: center;
        gap: 10px;
        position: fixed;
        top: 60px;
        left: 50%;
        transform: translateX(-50%);
        z-index: 501;
        color: white;
        font-size: 18px;
        cursor: pointer;

        i {
            margin-top: 2px;
        }
    }
    .code-hint {
        position: fixed;
        top: 100px;
        left: 50%;
        z-index: 501;
        width: 70vw;
        max-width: 500px;
        transform: translateX(-50%);
    }

}