<script lang="ts" src="./AppIntegrationListComponent.ts" />

<style lang="sass" scoped>
@import './AppIntegrationListComponent.scss'
</style>

<template>
    <div class="app-integration-list-component">
        <form-modal-or-drawer
            :state="opened"
            :title="'Vos apps'"
            :subtitle="'Modifiez ou ajouter une app'"
            @close="close()"
        >
            <template v-slot:content>
                <div class="available-apps">
                    <div
                        class="app"
                        v-for="app of apps"
                        @click="selectExisting(app)"
                    >
                        <div class="logo" :style="`background-image: url('${$assets[getImageForAppType(app.type)]}');`"></div>

                        <div class="data">
                            <span class="name"> {{ app.name }} </span>
                            <span class="subtitle"> Modifier </span>
                        </div>

                        <i class="fa-regular fa-cog"></i>
                    </div>

                    <div class="app" @click="createNew()">
                        <i class="fa-regular fa-plus"></i>

                        <div class="data">
                            <span class="name"> Nouveau </span>
                            <span class="subtitle"> Ajouter une nouvelle une app </span>
                        </div>
                    </div>
                </div>
            </template>

            <template v-slot:buttons>
                <button type="button" class="white button" @click="close()"> Fermer </button>
<!--                <button type="button" class="button" @click="save()"> Sauvegarder </button>-->
            </template>
        </form-modal-or-drawer>
    </div>
</template>