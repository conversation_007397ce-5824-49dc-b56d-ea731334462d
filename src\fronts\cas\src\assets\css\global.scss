
:root {
  --primary-hover-color: #1e1e1e !important;
  --primary-button-hover-color: #1e1e1e !important;
  --primary-hover-text-color: black !important;
  --primary-color: black !important;
  --primary-text-color: white !important;
}

html, body {
  height: 100%;
  margin: 0;
  padding: 0;
  overflow: hidden;
}

*:not(i, text) {
  font-family: 'Montserrat', sans-serif !important;
}

.page {
  overflow: hidden;
  height: 100%;
  width: 100%;
}

#mainRouterContainer {
  width: 100%;
  height: 100%;
}

// Computer
@media (min-width: 900px) {
  body, #mainRouterContainer {
    display: flex;
  }
}

// Mobile & Tablet
@media (max-width: 900px) {
  #mainRouterContainer .page {
    padding-top: 60px;
  }
}

.layout-main-content-right-panel {
  .close {
    display: none;
    margin-bottom: 20px;
    cursor: pointer;
    user-select: none;
  }

  @media screen and (max-width: 1400px) {
    .close {
      display: flex;
      gap: 10px;
      align-items: center;
    }
  }
}

.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 20px;
  width: 100%;
}

.label {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 5px;
  padding: 5px 8px;
  font-size: 12px;
  font-weight: 600;
  color: var(--primary-color);
  border-radius: 4px;
  border: 1px solid var(--primary-color);
  background: var(--primary-hover-color);

  i {
    font-size: 12px;
    line-height: 1px;
  }

  &.white {
    background: white;
    border-color: #E2E2E2;
    color: black;
  }

  &.red {
    background: rgba(225, 39, 39, 0.10);
    border-color: #E12727;
    color: #E12727;
  }

  &.orange {
    background: rgba(244, 144, 37, 0.1);
    border-color: #F49025;
    color: #F49025;
  }

  &.green {
    background: rgba(67, 191, 87, 0.1);
    border-color: #43BF57;
    color: #43BF57;
  }

  &.clickable {
    cursor: pointer;
  }
}