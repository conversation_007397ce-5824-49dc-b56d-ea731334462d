import {Component, Vue} from "vue-facing-decorator";
import {
	ContentHeaderComponent,
	ContentHeaderParameters, ForbiddenMessageComponent,
	FormModalOrDrawerComponent,
	LayoutContentWithRightPanelComponent
} from "@groupk/vue3-interface-sdk";
import {EstablishmentUrlBuilder} from "../../../../../shared/utils/EstablishmentUrlBuilder";
import {AutoWired, ScopedUuid, SearchUtils, UuidUtils, VisualScopedUuid} from "@groupk/horizon2-core";
import {Router} from "@groupk/horizon2-front";
import {
	ApplicationPermission,
	CashlessDeviceStateApiOut,
	CashlessHttpCurrencyContract,
	CashlessHttpDeviceContract,
	CashlessHttpEstablishmentAccountProfileContract,
	CashlessHttpKioskFundingContract,
	CashlessHttpProfileContract,
	CashlessHttpSimpleProductCategoryContract,
	CashlessHttpSimpleProductContract,
	EstablishmentAccountPermissionModel,
	EstablishmentAccountProfileApi,
	IotHttpEstablishmentDeviceContract,
	PaymentHttpMethodContract,
	ProfileApiOut,
	SimpleProductApiOut,
	SimpleProductCategoryApiOut,
	uuidScopeCashless_profile,
	UuidScopeCashless_profile
} from "@groupk/mastodon-core";
import {ProfilesRepository} from "../../../../../shared/repositories/ProfilesRepository";
import SidebarStateListener from "../../../../../shared/utils/SidebarStateListener";
import ProfileCategoriesComponent
	from "../../components/ProfileComponents/ProfileCategoriesComponent/ProfileCategoriesComponent.vue";
import {CategoriesRepository} from "../../../../../shared/repositories/CategoriesRepository";
import {ProductsRepository} from "../../../../../shared/repositories/ProductsRepository";
import {AppBus} from "../../config/AppBus";
import ProfileSettingsComponent
	from "../../components/ProfileComponents/ProfileSettingsComponent/ProfileSettingsComponent.vue";
import {PaymentMethodApiOut} from "@groupk/mastodon-core";
import {PaymentMethodsRepository} from "../../../../../shared/repositories/PaymentMethodsRepository";
import ToastManagerComponent from "../../components/ToastManagerComponent/ToastManagerComponent.vue";
import ProfileDevicesComponents
	from "../../components/ProfileComponents/ProfileDevicesComponents/ProfileDevicesComponents.vue";
import {EstablishmentDeviceApiOut} from "@groupk/mastodon-core";
import {EstablishmentDevicesRepository} from "../../../../../shared/repositories/EstablishmentDevicesRepository";
import {EstablishmentAccountProfileRepository} from "../../../../../shared/repositories/EstablishmentAccountProfileRepository";
import {CashlessDeviceRepository} from "../../../../../shared/repositories/CashlessDeviceRepository";
import {ProfileData} from "../../../../../shared/mastodonCoreFront/cashless/ProfileData";
import {AppState} from "../../../../../shared/AppState";
import {ComponentUtils} from "../../../../../shared/utils/ComponentUtils";

@Component({
	components: {
		'layout': LayoutContentWithRightPanelComponent,
		'form-modal-or-drawer': FormModalOrDrawerComponent,
		'content-header': ContentHeaderComponent,
		'profile-categories': ProfileCategoriesComponent,
		'profile-settings': ProfileSettingsComponent,
		'profile-devices': ProfileDevicesComponents,
		'toast-manager': ToastManagerComponent,
		'forbidden-message': ForbiddenMessageComponent,
	}
})
export default class profile extends Vue {
	headerParameters: ContentHeaderParameters = {
		header: '-',
		subtitle: 'Paramétrer le point de vente',
		breadcrumbs: [{
			name: 'Points de vente physiques',
			url: EstablishmentUrlBuilder.buildUrl('/profiles')
		}, {
			name: 'Piscine',
			url: ''
		}],
		actions: [],
		tabs: [{
			id: 'CATEGORIES',
			name: 'Catégories'
		}, {
			id: 'DEVICES',
			name: 'Appareils'
		}, {
			id: 'SETTINGS',
			name: 'Paramètres'
		}],
		hideSearch: true,
		searchPlaceholder: '',
	}

	profile!: ProfileData;
	categories: SimpleProductCategoryApiOut[] = [];
	products: SimpleProductApiOut[] = [];
	paymentMethods: PaymentMethodApiOut[] = [];
	productSearch: string = '';
	devices: EstablishmentDeviceApiOut[] = [];
	accountProfiles: EstablishmentAccountProfileApi[] = [];
	cashlessDevices: CashlessDeviceStateApiOut[] = [];

	openSideDrawer: boolean = false;
	selectedCategory: SimpleProductCategoryApiOut|null = null;
	currentPage: 'CATEGORIES'|'DEVICES'|'SETTINGS' = 'CATEGORIES';
	loading: boolean = true;
	saving: boolean = false;
	forbidden: boolean = false;

	@AutoWired(EstablishmentDevicesRepository) accessor establishmentDevicesRepository!: EstablishmentDevicesRepository;
	@AutoWired(EstablishmentAccountProfileRepository) accessor establishmentAccountProfileRepository!: EstablishmentAccountProfileRepository;
	@AutoWired(ProfilesRepository) accessor profilesRepository!: ProfilesRepository;
	@AutoWired(CategoriesRepository) accessor categoriesRepository!: CategoriesRepository;
	@AutoWired(ProductsRepository) accessor productsRepository!: ProductsRepository;
	@AutoWired(PaymentMethodsRepository) accessor paymentMethodsRepository!: PaymentMethodsRepository;
	@AutoWired(CashlessDeviceRepository) accessor cashlessDeviceRepository!: CashlessDeviceRepository;
	@AutoWired(Router) accessor router!: Router;
	@AutoWired(AppState) accessor appState!: AppState;
	@AutoWired(SidebarStateListener) accessor sidebarStateListener!: SidebarStateListener;
	@AutoWired(AppBus) accessor appBus!: AppBus;

	beforeMount() {
		this.sidebarStateListener.setHiddenSidebar(false);
		if(window.innerWidth < 1600) this.sidebarStateListener.setMinimizedSidebar(true);
		else this.sidebarStateListener.setMinimizedSidebar(false);
		this.appBus.on('selectedCategory', this.selectedCategoryChanged);
	}

	async mounted() {
		let regexMatch = this.router.lastRouteRegexMatches;

		if(!ComponentUtils.hasPermissions((ownedPermissions: ApplicationPermission[]) => {
			return EstablishmentAccountPermissionModel.hasPermissionsForContracts(ownedPermissions, [
				CashlessHttpSimpleProductCategoryContract.list,
				CashlessHttpSimpleProductContract.list,
				PaymentHttpMethodContract.list,
				IotHttpEstablishmentDeviceContract.list,
				CashlessHttpEstablishmentAccountProfileContract.list,
				CashlessHttpDeviceContract.list,
				CashlessHttpProfileContract.update,
			])
		})) {
			this.forbidden = true;
			this.loading = false;
			return;
		}

		if (regexMatch && regexMatch[2]) {
			const profileUid = UuidUtils.scopedToVisual(regexMatch[2] as ScopedUuid<UuidScopeCashless_profile>, uuidScopeCashless_profile);

			const profiles = (await this.profilesRepository.callContract('list', {establishmentUid: this.appState.requireUrlEstablishmentUid()}, undefined)).success()
			this.profile = new ProfileData(this.requireProfileWithUid(profileUid, profiles));

			this.headerParameters.header = this.profile.name;
			this.headerParameters.breadcrumbs![1].name = this.profile.name;
			this.router.updatePageWithDescriptor({
				pageTitle: `Point de vente ${this.profile.name} - Cashless`
			});

			this.categories = (await this.categoriesRepository.callContract('list', {establishmentUid: this.appState.requireUrlEstablishmentUid()}, undefined)).success();
			this.products = (await this.productsRepository.callContract('list', {establishmentUid: this.appState.requireUrlEstablishmentUid()}, undefined)).success();
			this.paymentMethods = (await this.paymentMethodsRepository.callContract('list', {establishmentUid: this.appState.requireUrlEstablishmentUid()}, undefined)).success();
			this.devices = (await this.establishmentDevicesRepository.callContract('list', {establishmentUid: this.appState.requireUrlEstablishmentUid()}, undefined)).success();
			this.accountProfiles = (await this.establishmentAccountProfileRepository.callContract('list', {establishmentUid: this.appState.requireUrlEstablishmentUid()}, undefined)).success();
			this.cashlessDevices = (await this.cashlessDeviceRepository.callContract('list', {establishmentUid: this.appState.requireUrlEstablishmentUid()}, undefined)).success();

			this.loading = false;
		}
	}

	unmounted() {
		this.appBus.off('selectedCategory', this.selectedCategoryChanged);
	}

	async updateProfile() {
		if(!this.profile.uid) return;

		this.saving = true;

		try {
			await this.profilesRepository.callContract(
				'update',
				{establishmentUid: this.appState.requireUrlEstablishmentUid(), deviceProfileUid: this.profile.uid},
				this.profile.toApiIn()
			);

			this.headerParameters.header = this.profile.name;
			this.headerParameters.breadcrumbs![1].name = this.profile.name;
			this.router.updatePageWithDescriptor({
				pageTitle: `Point de vente ${this.profile.name} - Cashless`
			});

			this.appBus.emit('emit-toast', {
				title: 'Sauvegarde réussie',
				description: 'Le point de vente a bien été mis à jour',
				duration: 3000,
				type: 'SUCCESS',
				closable: true
			});
		} catch(err) {
			console.log(err);
			this.appBus.emit('emit-toast', {
				title: 'La sauvegarde a échoué',
				description: 'Le point de vente n\'a pas pû être sauvegardé correctement',
				duration: 3000,
				type: 'ERROR',
				closable: true
			});
		}

		this.saving = false;
	}

	selectedCategoryChanged(category: SimpleProductCategoryApiOut) {
		this.selectedCategory = category;
	}

	getFilteredProducts(limited: boolean = false) {
		const results = SearchUtils.searchInTab(this.products, (product) => {
			return [product.name];
		}, this.productSearch);
		return limited ? results.slice(0, 100) : results;
	}

	addAllFiltered() {
		const filteredProducts = this.getFilteredProducts();
		this.appBus.emit('point-of-sale-add-filtered-products-clicked', filteredProducts);
	}

	clickedProduct(product: SimpleProductApiOut) {
		this.appBus.emit('point-of-sale-product-clicked', {product: product, isMobile: this.openSideDrawer});
	}

	requireProfileWithUid(profileUid: VisualScopedUuid<UuidScopeCashless_profile>, profiles: ProfileApiOut[]) {
		const profile = profiles.find((profile) => profile.uid === profileUid);
		if(!profile) throw new Error('missing_profile');
		return profile;
	}
}