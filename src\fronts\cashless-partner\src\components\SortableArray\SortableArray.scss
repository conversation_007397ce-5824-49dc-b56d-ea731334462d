:root {
    --sortable-array-mirror-width: 1000px;
}
.sortable-container {
    .draggable-mirror {
        box-sizing: border-box;
        width: var(--sortable-array-mirror-width) !important;
    }
}
.deleting {
    .draggable-mirror {
        color: white !important;
        background: #EB5757 !important;
        * {
            background: #EB5757 !important;
        }
    }
}

tbody {
    .draggable-mirror {
        display: flex;
        align-items: center;
        border: 1px solid var(--border-color);
        border-radius: 6px;
        background: white;
    }
}