.label {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 5px;
    padding: 5px 8px;
    font-size: 12px;
    font-weight: 600;
    color: #2788E1;
    border-radius: 4px;
    background: rgba(39, 136, 225, 0.2);
    min-width: 30px;
    box-sizing: border-box;

    i {
        font-size: 12px;
    }

    &.white {
        background: white;
        border-color: black;
        color: black;

    }

    &.red {
        background: rgba(225, 39, 39, 0.2);
        border-color: #E12727;
        color: #E12727;
    }

    &.green {
        background: rgba(13, 184, 27, 0.2);
        color: #0db71b;
    }

    &.grey {
        background: rgba(148, 148, 148, 0.2);
        color: #726f6f;
    }


    &.orange {
        background: rgba(225, 138, 39, 0.2);
        color: #f37901;
    }

    &.clickable {
        cursor: pointer;
    }
}