import {Component, Vue} from "vue-facing-decorator";
import {
	FilterTableLayoutComponent,
} from "@groupk/vue3-interface-sdk";
import {ContentHeaderParameters, TableColumn} from "@groupk/vue3-interface-sdk";
import {
	AutoWired, SearchUtils,
} from "@groupk/horizon2-core";
import {TableColumnsRepository} from "../../../../../shared/repositories/TableColumnsRepository";
import {Router} from "@groupk/horizon2-front";
import {MetadataDescriptorRepository} from "../../../../../shared/repositories/MetadataDescriptorRepository";
import {MetadataDescriptorApiOut} from "@groupk/mastodon-core";
import SidebarStateListener from "../../../../../shared/utils/SidebarStateListener";
import {AppState} from "../../../../../shared/AppState";

@Component({
	components: {
		'filter-table-layout': FilterTableLayoutComponent
	}
})
export default class Metadata extends Vue {
	metadataDescriptors: MetadataDescriptorApiOut[] = [];
	selectedMetadata: MetadataDescriptorApiOut|null = null;

	headerParameters: ContentHeaderParameters = {
		header: 'Métadonnées',
		subtitle: 'Liste des descripteurs de métadonnées',
		actions: [],
		hideSearch: false,
		searchPlaceholder: 'Rechercher un descripteur'
	}

	allowedFilters = {};

	tableKey = 'mastodon-metadata';
	tableColumns: TableColumn[] = [{
		title: 'Identifiant', name: 'uid', displayed: false, mobileHidden: true
	}, {
		title: 'Nom', name: 'name', displayed: true, mobileHidden: false
	}, {
		title: 'Type', name: 'type', displayed: true, mobileHidden: false
	}, {
		title: 'Requis', name: 'required', displayed: true, mobileHidden: true
	}, {
		title: 'Description', name: 'description', displayed: true, mobileHidden: true
	}];

	searchString: string = '';
	sort: {
		name: string,
		direction: 'asc'|'desc'
	}|null = null;
	availableSorts: string[] = ['name', 'type', 'required'];

	filteredMetadataDescriptors: MetadataDescriptorApiOut[] = [];
	loading: boolean = false;

	@AutoWired(MetadataDescriptorRepository) accessor metadataDescriptorRepository!: MetadataDescriptorRepository
	@AutoWired(TableColumnsRepository) accessor tableColumnsRepository!: TableColumnsRepository
	@AutoWired(SidebarStateListener) accessor sidebarStateListener!: SidebarStateListener
	@AutoWired(AppState) accessor appState!: AppState
	@AutoWired(Router) accessor router!: Router

	beforeMount() {
		this.loading = true;

		this.sidebarStateListener.setHiddenSidebar(false);
		this.sidebarStateListener.setMinimizedSidebar(false);

		let savedPreferences = this.tableColumnsRepository.getColumnPreferences(this.tableKey, this.tableColumns);
		if(savedPreferences) this.tableColumns = savedPreferences;
	}

	async mounted() {
		const establishmentUid = this.appState.requireUrlEstablishmentUid();
		this.metadataDescriptors = (await this.metadataDescriptorRepository.callContract('listDescriptors', {establishmentUid}, undefined)).success();

		this.filteredMetadataDescriptors = this.metadataDescriptors;
		this.loading = false;
	}

	toggleSelectedMetadata(metadata: MetadataDescriptorApiOut) {
		if(this.selectedMetadata && this.selectedMetadata.uid === metadata.uid) {
			this.selectedMetadata = null;
		} else {
			this.selectedMetadata = metadata;
		}
	}

	saveColumnPreferences(columns: TableColumn[]) {
		this.tableColumnsRepository.saveColumnsPreferences(this.tableKey, columns);
	}

	search(value: string) {
		this.searchString = value;
		this.filter();
	}

	filter() {
		this.filteredMetadataDescriptors = SearchUtils.searchInTab(this.metadataDescriptors, (metadata) => {
			return [metadata.descriptorTemplate.name, metadata.description || '', metadata.descriptorTemplate.type];
		}, this.searchString);

		if(this.sort) this.filteredMetadataDescriptors = this.filteredMetadataDescriptors.sort((m1, m2) => {
			if(!this.sort) return 0;
			const directionMultiplier = this.sort.direction === 'asc' ? 1 : -1;

			if(this.sort.name === 'required') {
				return ((m1.descriptorTemplate.required ? 1 : 0) - (m2.descriptorTemplate.required ? 1 : 0)) * directionMultiplier;
			}

			const getValue = (metadata: MetadataDescriptorApiOut) => {
				switch(this.sort!.name) {
					case 'name': return metadata.descriptorTemplate.name || '';
					case 'type': return metadata.descriptorTemplate.type || '';
					default: return '';
				}
			};

			return getValue(m1).localeCompare(getValue(m2)) * directionMultiplier;
		});
	}

	sorted(sort: {
		name: string,
		direction: 'asc'|'desc'
	}) {
		this.sort = sort;
		this.filter();
	}
}