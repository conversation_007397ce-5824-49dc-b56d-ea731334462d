.bluetooth-device-component {
    display: flex;
    flex-direction: column;
    gap: 10px;
    align-items: center;

    .no-device {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 15px 25px;
        text-align: center;

        .hint {
            font-size: 12px;
        }
    }

    .separator {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 20px;
        font-size: 15px;
        font-weight: 600;
        background: #F2F4F7;
        width: 100%;
        margin: 0 -20px;

        .tertiary.button {
            padding: 0;
            font-weight: normal;
        }
    }

    .bluetooth-device {
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 10px;
        padding: 15px 25px;
        border-radius: 8px;
        background: #F2F4F7;

        &.connected {
            color: white;
            background: #06F;
        }
    }
}