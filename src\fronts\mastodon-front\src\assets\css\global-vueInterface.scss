@import './variables';

html, body {
  height: 100%;
  margin: 0;
  padding: 0;
  overflow: hidden;
}

.ui.primary.button {
  background: #004FFF !important;
}

*:not(i, text) {
  font-family: 'Montserrat', sans-serif !important;
}

#page {
  overflow: hidden;
  height: 100%;
  width: 100%;
}

#mainRouterContainer {
  width: 100%;
  height: 100%;
}

// Computer
@media (min-width: 900px) {
  body, #mainRouterContainer {
    display: flex;
  }
}

// Mobile & Tablet
@media (max-width: 900px) {
  #page, .parameters-content {
    padding-top: 60px;
  }
}

.main-scrolling-content {
  overflow-y: auto;
  height: 100%;
  //padding-bottom: 60px;
  overflow-x: hidden;
  width: 100%;
}

.container-page {
  display: flex;
  height: 100%;
}

input, textarea {
  font-weight: 400;
  font-size: 15px;
  font-style: normal;
  padding: 10px 15px;
  border: 1px solid #D8D8D8;
  border-radius: 8px;
  background: none;
  box-sizing: border-box;
  resize: none;

  &:focus {
    outline: none;
    border: 1px solid;
  }

  &.fluid {
    width: 100%;
  }
}

/** Drawer **/
.drawer-container, .custom-modal {
  color: black;

  .drawer-buttons {
    display: flex;

    .button {
      flex-grow: 1;

      &.cancel {
        background-color: #fff;
        color: #7e828c;
        border: 1px solid #eaebef;
      }

      &.confirm {
        &.red {
          background-color: #fef2f2;
          color: #cc7b7d;
        }
      }
    }
  }
}


.loader {
  position: relative;
  width: 32px;
  height: 32px;
  border-radius: 500rem;
  border: 0.2em solid rgba(0, 0, 0, 0.15);
}

.loader::after {
  position: absolute;
  content: '';
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 32px;
  height: 32px;
  border-radius: 500rem;
  -webkit-animation: loader 0.6s infinite linear;
  animation: loader 0.6s infinite linear;
  border: 0.2em solid currentColor;
  color: #000;
  -webkit-box-shadow: 0 0 0 1px transparent;
  box-shadow: 0 0 0 1px transparent;
  border-left-color: transparent;
  border-right-color: transparent;
  border-bottom-color: transparent;
}

@-webkit-keyframes loader {
  100% {
    -webkit-transform: translate(-50%, -50%) rotate(360deg);
    transform: translate(-50%, -50%) rotate(360deg);
  }
}
@keyframes loader {
  100% {
    -webkit-transform: translate(-50%, -50%) rotate(360deg);
    transform: translate(-50%, -50%) rotate(360deg);
  }
}

@-webkit-keyframes custom-loader {
  100% {
    -webkit-transform: translate(-50%, -50%) rotate(360deg);
    transform: translate(-50%, -50%) rotate(360deg);
  }
}

@keyframes custom-loader {
  100% {
    -webkit-transform: translate(-50%, -50%) rotate(360deg);
    transform: translate(-50%, -50%) rotate(360deg);
  }
}

.center-content {
  @media screen and (max-width: 900px) {
    transition: transform 0.5s cubic-bezier(0.22, 1, 0.36, 1), filter 0.5s cubic-bezier(0.22, 1, 0.36, 1);
    &.opened {
      filter: brightness(0.6);
      transform: translateX(-25%);
    }
  }

  flex-grow: 2;
  display: flex;
  flex-direction: column;
  overflow: auto;
  background: white;

  .floating-action {
    position: fixed;
    bottom: 25px;
    right: 25px;
    height: 65px;
    width: 65px;
    display: none;
    align-items: center;
    justify-content: center;
    background: white;
    border-radius: 50%;
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.16);
    font-size: 20px;
  }

  @media screen and (max-width: 1400px) {
    .floating-action {
      display: flex;
    }
  }
}

.main-content-right-panel {
  width: 450px;
  max-width: 450px;
  overflow-x: hidden;
  background: white;
  overflow-y: auto;
  height: 100%;
  flex-shrink: 0;
  flex-grow: 0;
  border-left: #eeeeee 1px solid;
}

.main-content-right-panel .mobile-return {
  display: none;
  cursor: pointer;
}

.empty-right-panel {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
  justify-content: center;
  height: 100%;
  color: #636363;
  font-size: 14px;
  text-align: center;

  img {
    width: 150px;
  }
}

@media screen and (min-width: 900px) and (max-width: 1400px) {
  .main-content-right-panel {
    transition: transform 0.5s cubic-bezier(0.22, 1, 0.36, 1);
    transform: translateX(100%);
    position: absolute;
    top: 0;
    bottom: 0;
    right: 0;
    display: initial;
    width: 450px;
    flex-grow: 0;
    z-index: 1000;
  }

  .main-content-right-panel.opened {
    transform: none;
  }

  .right-panel-dimmer.opened {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(6px);
    z-index: 999;
  }

  .main-content-right-panel .mobile-return {
    display: initial;
  }
}

@media screen and (max-width: 900px) {
  .main-content-right-panel {
    display: initial;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 4;
    background: white;
    height: 100%;
    width: 100%;
    max-width: 100%;
    transition: transform 0.5s cubic-bezier(0.22, 1, 0.36, 1);
    transform: translateX(100%);
    overflow-y: auto;
  }

  .main-content-right-panel.opened {
    transform: none;
  }

  .main-content-right-panel .mobile-return {
    display: initial;
  }
}
