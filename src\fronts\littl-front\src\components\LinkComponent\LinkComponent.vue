<script lang="ts" src='./LinkComponent.ts' />

<style lang="sass" scoped>
@use './LinkComponent.scss' as *
</style>

<template>
	<div class="form" v-if="link" :id='id'>
		<div class="title-group">
			<h3> Créer un nouveau lien </h3>
            <span> <i>ID du lien</i> correspond au nouveau lien que vous allez partager, et qui redirigera vers le lien du champ <i>Redirection</i> </span>
		</div>

		<div class="input-group">
			<label for="title">
                Lien court
                <span style="float: right"> {{link.id.length}}/{{ linkDefinition.getFieldString('id').maxLength }} </span>
            </label>
            <div class="prefixed-input">
                <label for="short-link" class="prefix"> https://littl.fr/ </label>
                <input id="short-link" name="title" v-model="link.id" type="text" placeholder="" @keydown.enter="create()"/>
            </div>
		</div>

		<targets :target=link.targets[0]
			@enter='create()'
		></targets>

        <div class="form-error" v-if="error">
            <i class="fa-solid fa-exclamation-circle"></i>
            <div class="details">
                <span class="title"> Erreur </span>
                <span class="description">{{ error }}</span>
            </div>
        </div>

		<div class="buttons">
			<button class="ui white button" @click="cancel()">
				<i class="fa-regular fa-xmark" style="margin-top: 3px"></i>
				Annuler
			</button>
			<div @click='create()' class="button" :class="{loading: loading, disabled: loading}">
				<i class="fa-regular fa-check" style="margin-top: 3px"></i>
				Sauvegarder
			</div>
		</div>
	</div>
</template>
