<script lang="ts" src="./PaymentMethodSettingsComponent.ts">
</script>

<style lang="sass" scoped>
@import './PaymentMethodSettingsComponent.scss'
</style>

<template>
    <div class="payment-method-settings-component">
        <div class="payment-method" v-for="paymentMethod in posState.paymentMethods">
            <div class="separator">
                <div class="infos">
                    <span class="name"> {{ paymentMethod.name }} </span>
                    <span class="type"> {{ paymentMethod.type }} </span>
                </div>
            </div>


            <div class="configured-protocols">
                <div class="empty" v-if="getSettingsForPaymentMethod(paymentMethod.uid).length === 0">
                    Aucun appareil relié
                </div>
                <template v-else>
                    <div
                        class="linked-device"
                        v-for="data of getSettingsForPaymentMethod(paymentMethod.uid)"
                        @click="editDevice(paymentMethod, data.settings, data.index)"
                    >
                        <span> {{ data.settings.name }} </span>
                        <i class="fa-regular fa-cog"></i>
                    </div>
                </template>
            </div>

            <div class="action" v-if="getSettingsForPaymentMethod(paymentMethod.uid).length === 0">
                <div class="white button" @click="addSettings(paymentMethod)">
                    <i class="fa-regular fa-plus"></i>
                    Lier un appareil
                </div>
            </div>
        </div>

        <form-modal
            v-if="editing"
            :state="true"
            :closable="true"
            @close="editing = null"
            :title="'Relier un appareil'"
            :subtitle="'Appeler automatiquement un appareil lors du paiement'"
        >
            <template v-slot:content>
                <div class="input-group">
                    <dropdown
                        :default-selected="editing && editing.settings ? editing.settings.protocol : null"
                        :values="getAuthorizedProtocolsDropdownValues()"
                        @update="updateProtocol($event)"
                    ></dropdown>
                </div>

                <caisse-app-settings-form
                    v-if="editing.settings && editing.settings.protocol === PaymentProtocol.PHYSICAL_CAISEAP"
                    :settings="editing.settings"
                    :editing="editing.editIndex !== null"
                    @save="saveSettings($event)"
                    @close="close()"
                    @delete="deleteSettings()"
                ></caisse-app-settings-form>

                <cash-keeper-settings
                    v-else-if="editing.settings && editing.settings.protocol === PaymentProtocol.PHYSICAL_CASHKEEPER"
                    :settings="editing.settings"
                    :editing="editing.editIndex !== null"
                    @save="saveSettings($event)"
                    @close="close()"
                    @delete="deleteSettings()"
                ></cash-keeper-settings>
            </template>

            <template v-slot:buttons></template>
        </form-modal>
    </div>
</template>