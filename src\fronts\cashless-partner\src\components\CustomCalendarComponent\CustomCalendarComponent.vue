<script lang="ts" src="./CustomCalendarComponent.ts" />

<style lang="sass" scoped>
@use './CustomCalendarComponent.scss' as *
</style>

<template>
	<div class="custom-calendar-component" :class="{small: small}">
		<div class="month-navigation" v-if="getViewType() === 'NAVIGATION'">
			<div class="left" @click="previousMonth()" data-cy="custom-calendar-previous">
                <i class="fa-regular fa-chevron-left"></i>
			</div>
			<span class="month" data-cy="custom-calendar-selected-month"> {{ monthNames[selectedMonth].toUpperCase() }} {{ selectedYear }} </span>
			<div class="right" @click="nextMonth()" data-cy="custom-calendar-next">
                <i class="fa-regular fa-chevron-right"></i>
			</div>
		</div>

		<template v-for="data of getDisplayedMonth()">
			<div class="scroll-month" v-if="getViewType() === 'SCROLL'">{{ monthNames[data.month].toUpperCase() }} {{ data.year }}</div>

			<table class="fluid" cellspacing="0">
				<thead>
					<tr>
						<th>L</th>
						<th>M</th>
						<th>M</th>
						<th>J</th>
						<th>V</th>
						<th>S</th>
						<th>D</th>
					</tr>
				</thead>
				<tbody>
					<tr v-for="week of getChunkedMonthDays(data.month, data.year)">
						<td
							v-for="day of week"
							:class="getTdClass(data, day)"
							:data-cy="'custom-calendar-td-' + day.getUTCFullYear() + '-' + day.getUTCMonth() + '-' + day.getUTCDate()">
							<div class="day" @click="selectDay(day)" :data-cy="'custom-calendar-' + day.getUTCFullYear() + '-' + day.getUTCMonth() + '-' + day.getUTCDate()">
								<div
									class="number"
									:class="getDayClass(data, day)"
									:data-cy="'custom-calendar-number-' + day.getUTCFullYear() + '-' + day.getUTCMonth() + '-' + day.getUTCDate()">
									<div class="profiles" v-if="getPicturesForDay(day).pictures.length > 0">
										<div v-for="picture of getPicturesForDay(day).pictures" class="profile-thumbnail" :style="'background-image: url(' + picture + ')'"></div>
										<div class="text profile-thumbnail" v-if="getPicturesForDay(day).hidden">+{{ getPicturesForDay(day).hidden }}</div>
									</div>
									<span data-cy="calendar-day"> {{ day.getUTCDate() < 10 ? '0' + day.getUTCDate() : day.getUTCDate() }} </span>
								</div>
							</div>
						</td>
					</tr>
				</tbody>
			</table>
		</template>

        <div class="form" style="margin-top: 10px">
            <div class="input-group">
                <label> Début > Fin </label>
                <date-time-input
                    :date="rangeSelection[0]?.start?.toISOString()??undefined"
                    :time="startTime"
                    @updated-date="updateStartDate($event)"
                    @updated-time="startTime = $event; updatedTime()"
                ></date-time-input>
            </div>
            <div class="input-group">
                <date-time-input
                    :date="rangeSelection[0]?.end?.toISOString()??undefined"
                    :time="endTime"
                    @updated-date="updateEndDate($event)"
                    @updated-time="endTime = $event; updatedTime()"
                ></date-time-input>
            </div>
        </div>

		<div class="load-more-button" v-if="getViewType() === 'SCROLL'" @click="loadMoreMonths()">Voir plus</div>
	</div>
</template>
