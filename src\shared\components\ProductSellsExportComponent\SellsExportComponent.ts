import {Component, Prop, Vue} from "vue-facing-decorator";
import {
	DropdownComponent, DropdownValue,
	FormModalOrDrawerComponent, ToggleComponent
} from "@groupk/vue3-interface-sdk";
import {
	ApplicationPermission,
	CashStatementApiOut,
	CashStatementSessionApiOut,
	EstablishmentAccountPermissionModel, ProductHttpCashStatementContract,
	ProductHttpOrdersStatsContract,
} from "@groupk/mastodon-core";
import {AutoWired, EnumUtils, VisualScopedUuid} from "@groupk/horizon2-core";
import {UuidScopeEstablishment} from "@groupk/mastodon-core";
import {ExportFiletype, OrderStatsExportSettingsApiIn} from "@groupk/mastodon-core";
import {CleaveDirective} from "../../directives/CleaveDirective";
import {CashStatementRepository} from "../../repositories/CashStatementRepository";
import {MainConfig} from "../../MainConfig";
import {OrderStatisticsRepository} from "../../repositories/OrderStatisticsRepository";
import {AuthStateModel} from "../../AuthStateModel";
import DateUtils from "../../utils/DateUtils";
import {translateResponseError} from "../../RepositoryExtensions";

export function SellsExportComponentHasRequiredPermissions(ownedPermissions: ApplicationPermission[]) {
	return EstablishmentAccountPermissionModel.hasPermissionsForContracts(ownedPermissions, [
		ProductHttpCashStatementContract.list,
		ProductHttpCashStatementContract.listSessions,
	]);
}

@Component({
	directives: {
		cleave: CleaveDirective,
	},
	components: {
		'toggle': ToggleComponent,
		'form-modal-or-drawer': FormModalOrDrawerComponent,
		'dropdown': DropdownComponent,
	},
	emits: ['close']
})
export default class SellsExportComponent extends Vue {
	@Prop() establishmentUid!: VisualScopedUuid<UuidScopeEstablishment>;

	exportDates: {start: string, end: string} = {start: '', end: ''};
	cashStatementSessions: CashStatementSessionApiOut[] = [];
	cashStatements: CashStatementApiOut[] = [];
	selectedFormat: ExportFiletype = ExportFiletype.CSV;

	bySession: boolean = true;
	downloading: boolean = false;
	error: string|null = null;

	cleaveDate = {
		date: true,
		delimiter: '/',
		datePattern: ['d', 'm', 'Y']
	}


	@AutoWired(CashStatementRepository) accessor cashStatementRepository!: CashStatementRepository;
	@AutoWired(MainConfig) accessor mainConfig!: MainConfig;
	@AutoWired(OrderStatisticsRepository) accessor orderStatisticsRepository!: OrderStatisticsRepository;
	@AutoWired(AuthStateModel) accessor authStateModel!: AuthStateModel;

	async mounted() {
		this.cashStatements = (await this.cashStatementRepository.callContract('list', {establishmentUid: this.establishmentUid}, undefined)).success();
		this.cashStatementSessions = (await this.cashStatementRepository.callContract('listSessions', {establishmentUid: this.establishmentUid}, undefined)).success().list;
	}


	getFormatDropdownValues(): DropdownValue[] {
		return EnumUtils.values(ExportFiletype).map((value) => {
			return {
				name: value,
				value: value
			}
		})
	}

	setExportDates(period: 'today' | 'last-month' | 'this-month') {
		const now = new Date();

		const formatDate = (date: Date): string => {
			const day = String(date.getDate()).padStart(2, '0');
			const month = String(date.getMonth() + 1).padStart(2, '0'); // Mois 0-indexé
			const year = date.getFullYear();
			return `${day}/${month}/${year}`;
		};

		if (period === 'today') {
			this.exportDates.start = formatDate(now);
			this.exportDates.end = formatDate(now);
		}

		if (period === 'last-month') {
			const firstDay = new Date(now.getFullYear(), now.getMonth() - 1, 1);
			const lastDay = new Date(now.getFullYear(), now.getMonth(), 0);
			this.exportDates.start = formatDate(firstDay);
			this.exportDates.end = formatDate(lastDay);
		}

		if (period === 'this-month') {
			const firstDay = new Date(now.getFullYear(), now.getMonth(), 1);
			this.exportDates.start = formatDate(firstDay);
			this.exportDates.end = formatDate(now); // jusqu’à aujourd’hui
		}
	}

	async exportStats() {
		this.error = null;
		this.downloading = true;

		try {
			if(this.exportDates.start.length !== 10) return;
			const startDate = new Date(DateUtils.convertFrToIso(this.exportDates.start));

			let endDate: Date|null = null;
			if(this.exportDates.end.length === 10) {
				endDate = new Date(DateUtils.convertFrToIso(this.exportDates.end));
				endDate.setHours(23);
				endDate.setMinutes(59);
				endDate.setSeconds(59);
			} else if(this.exportDates.end.length > 0) return;

			const matchingCashStatementSessions = this.cashStatementSessions.filter((cashStatement) => {
				const creationDate = new Date(cashStatement.creationDatetime);
				return creationDate.getTime() >= startDate.getTime() && (!endDate || (creationDate.getTime() <= endDate.getTime()));
			});

			const matchingCashStatements = this.cashStatements.filter((cashStatement) => {
				const creationDate = new Date(cashStatement.creationDatetime);
				return creationDate.getTime() >= startDate.getTime() && (!endDate || (creationDate.getTime() <= endDate.getTime()));
			});

			const apiIn = new OrderStatsExportSettingsApiIn({
				filetype: this.selectedFormat,
				timezone: 'Europe/Paris',
				locale: 'fr-fr',
				cashStatementSessionUidList: this.bySession ? matchingCashStatementSessions.map((cashStatementSession) => cashStatementSession.uid) : [],
				cashStatementsUid: this.bySession ? [] : matchingCashStatements.map((cashStatement) => cashStatement.uid)
			});

			const response = await this.orderStatisticsRepository.callContract('export', {
				establishmentUid: this.establishmentUid
			}, apiIn);

			if(!response.isSuccess()) {
				this.error = translateResponseError<typeof ProductHttpOrdersStatsContract, 'export'>(response, {
					too_many_cash_statements: 'La plage sélectionnée est trop grande'
				});
			} else {
				const data = response.success();

				const blob = new Blob([data]);
				const link= document.createElement('a');
				link.href = window.URL.createObjectURL(blob);
				link.download = "export." + this.selectedFormat.toLowerCase();
				link.click();
				window.URL.revokeObjectURL(link.href);
				link.remove();

				this.$emit('close');
			}
		} catch(err) {
			this.downloading = false;
		}

		this.downloading = false;
	}

	close() {
		this.$emit('close');
	}
}