import {Component, Vue} from "vue-facing-decorator";
import {OptionBuilder} from "vue-facing-decorator/dist/optionBuilder";
import {AutoWired} from "@groupk/horizon2-core";
import {CasRedirectUtils} from "../../../../../shared/utils/CasRedirectUtils";
import {AuthStateModel} from "../../../../../shared/AuthStateModel";

@Component({})
export default class CasRedirectView extends Vue {
	@AutoWired(AuthStateModel) accessor authCenter!: AuthStateModel;

	constructor(optionBuilder: OptionBuilder, vueInstance: any) {
		super(optionBuilder, vueInstance);

		window.location.href = 'https://weecop.fr/fr/logiciels/cashless';
	}
}