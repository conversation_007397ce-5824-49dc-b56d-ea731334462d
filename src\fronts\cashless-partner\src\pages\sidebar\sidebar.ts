import {Component, Vue} from "vue-facing-decorator";
import SidebarStateListener from "../../../../../shared/utils/SidebarStateListener";
import {
	SidebarComponent,
	SidebarHeader,
	SidebarMenu,
	ApplicationsSwitcherComponent,
	ModalOrDrawerComponent
} from "@groupk/vue3-interface-sdk";
import {randomUUID, Router} from "@groupk/horizon2-front";
import {AutoWired, ScopedUuid, UuidUtils, VisualScopedUuid} from "@groupk/horizon2-core";
import {EstablishmentUrlBuilder} from "../../../../../shared/utils/EstablishmentUrlBuilder";
import {OptionBuilder} from "vue-facing-decorator/dist/optionBuilder";
import {PlatformRepository} from "../../../../../shared/repositories/PlatformRepository";
import {
	PermissionBuilder,
	PlatformDescriptorApiOut,
	uuidScopeEstablishment,
	UuidScopeEstablishment
} from "@groupk/mastodon-core";
import {EstablishmentRepository} from "../../../../../shared/repositories/EstablishmentRepository";
import {SidebarNavigation} from "@groupk/vue3-interface-sdk";
import {IotPermissions} from "@groupk/mastodon-core";
import {CashlessPermissions} from "@groupk/mastodon-core";
import {CustomerPermissions} from "@groupk/mastodon-core";
import EstablishmentSwitchComponent
	from "../../../../../shared/components/EstablishmentSwitchComponent/EstablishmentSwitchComponent.vue";
import {AppState} from "../../../../../shared/AppState";

@Component({
	components: {
		sidebar: SidebarComponent,
		'application-switcher': ApplicationsSwitcherComponent,
		'modal-or-drawer': ModalOrDrawerComponent,
		'establishment-switch': EstablishmentSwitchComponent
	},
})
export default class SidebarView extends Vue {
	opened: boolean = false;
	minimized: boolean = false;
	hidden: boolean = true;
	showContactInfos: boolean = false;
	establishmentUid!: VisualScopedUuid<UuidScopeEstablishment>;

	displayMobileMenu!: boolean;
	header: SidebarHeader | undefined = {
		title: "Cashless",
		icon: "fa-square-c",
		subtitle: "Votre plateforme",
	};

	menus: SidebarMenu[] = [
		{
			separator: true,
			navigations: [
				{icon: "fa-house-blank", title: "Accueil", url: EstablishmentUrlBuilder.buildUrl('/'), name: EstablishmentUrlBuilder.buildUrl('/')},
			],
		}
	];

	bottom: SidebarMenu[] = [
		{
			separator: false,
			navigations: [],
		},
	];

	platformDescriptor!: PlatformDescriptorApiOut|null;
	selectedNavigation: string | undefined = undefined;

	/** UUID **/
	uid: string = randomUUID();

	@AutoWired(SidebarStateListener) accessor sidebarStateListener!: SidebarStateListener;
	@AutoWired(PlatformRepository) accessor platformRepository!: PlatformRepository;
	@AutoWired(EstablishmentRepository) accessor establishmentRepository!: EstablishmentRepository;
	@AutoWired(AppState) accessor appState!: AppState;
	@AutoWired(Router) accessor router!: Router;

	constructor(optionBuilder: OptionBuilder, vueInstance: any) {
		super(optionBuilder, vueInstance);

		this.opened = this.sidebarStateListener.openedSidebar;
		this.hidden = this.sidebarStateListener.hiddenSidebar;
		this.minimized = this.sidebarStateListener.minimizedSidebar;

		this.sidebarStateListener.listen(this.stateChanged);
	}

	stateChanged(event: {type: string; value: any}){
		if (event.type === "openedSidebar") {
			this.opened = event.value;
		} else if (event.type === "minimizedSidebar") {
			this.minimized = event.value;
		} else if (event.type === "hiddenSidebar") {
			this.hidden = event.value;
		}
	}

	async mounted() {
		this.selectedNavigation = window.location.pathname;

		this.router.hookOnAsync('newPageLoaded', (data) => {
			if (data.newRoute.location !== null) {
				this.selectedNavigation = window.location.pathname;
			}
			return Promise.resolve(data);
		});

		let regexMatch = this.router.lastRouteRegexMatches;

		if (regexMatch && regexMatch[1]) {
			this.establishmentUid = UuidUtils.scopedToVisual<UuidScopeEstablishment>(regexMatch[1] as ScopedUuid<UuidScopeEstablishment>, uuidScopeEstablishment);
			try {
				const establishment = (await this.establishmentRepository.callContract('get', {establishmentUid: this.establishmentUid}, undefined)).success()
				if(this.header) this.header.subtitle = establishment.name;
			} catch(er) {}
		}

		const configurationNavigations: SidebarNavigation[] = [];
		if(this.appState.hasPermission(PermissionBuilder(CashlessPermissions, 'SIMPLE-PRODUCT_LIST'))) {
			configurationNavigations.push({icon: "fa-tag", title: "Produits", url: EstablishmentUrlBuilder.buildUrl('/products'), name: EstablishmentUrlBuilder.buildUrl('/products')});
			// configurationNavigations.push({icon: "fa-grid-2", title: "Catégories", url: EstablishmentUrlBuilder.buildUrl('/categories'), name: EstablishmentUrlBuilder.buildUrl('/categories')},);
		}
		if(this.appState.hasPermission(PermissionBuilder(CashlessPermissions, 'PROFILES_LIST'))) {
			configurationNavigations.push({icon: "fa-store", title: "Points de ventes", url: EstablishmentUrlBuilder.buildUrl('/profiles'), name: EstablishmentUrlBuilder.buildUrl('/profiles')});
		}
		if(this.appState.hasPermission(PermissionBuilder(IotPermissions, 'ESTABLISHMENT-DEVICE_LIST'))) {
			configurationNavigations.push({icon: "fa-mobile", title: "Flotte", url: EstablishmentUrlBuilder.buildUrl('/devices'), name: EstablishmentUrlBuilder.buildUrl('/devices')},);
		}
		if(configurationNavigations.length > 0) {
			this.menus.push({
				separator: true,
				title: "Configuration",
				navigations:configurationNavigations,
			});
		}

		const analysisNavigations: SidebarNavigation[] = [];
		if(this.appState.hasPermission(PermissionBuilder(CashlessPermissions, 'WALLET_LIST'))) {
			analysisNavigations.push({icon: "fa-badge-dollar", title: "Bracelets / cartes", url: EstablishmentUrlBuilder.buildUrl('/wallets'), name: EstablishmentUrlBuilder.buildUrl('/wallets')});
		}
		if(this.appState.hasPermission(PermissionBuilder(CashlessPermissions, 'TRANSACTIONS_LIST'))) {
			analysisNavigations.push({icon: "fa-arrow-right-arrow-left", title: "Transactions", url: EstablishmentUrlBuilder.buildUrl('/transactions'), name: EstablishmentUrlBuilder.buildUrl('/transactions')});
		}
		analysisNavigations.push({icon: "fa-message-bot", title: "Bornes", url: EstablishmentUrlBuilder.buildUrl('/kiosk-fundings'), name: EstablishmentUrlBuilder.buildUrl('/kiosk-fundings'), premium: false},);

		if(this.appState.hasPermission(PermissionBuilder(CashlessPermissions, 'TRANSACTIONS_LIST'))) {
			analysisNavigations.push({icon: "fa-chart-simple", title: "Statistiques", url: EstablishmentUrlBuilder.buildUrl('/transactions-statistics'), name: EstablishmentUrlBuilder.buildUrl('/transactions-statistics')});
		}

		if(this.appState.hasPermission(PermissionBuilder(CustomerPermissions, 'CUSTOMER_LIST'))) {
			analysisNavigations.push({icon: "fa-circle-user", title: "Utilisateurs", url: EstablishmentUrlBuilder.buildUrl('/customers'), name: EstablishmentUrlBuilder.buildUrl('/customers')});
		}

		if(analysisNavigations.length > 0) {
			this.menus.push({
				separator: true,
				title: "Analyse",
				navigations: analysisNavigations,
			});
		}

		const bottomNavigations: SidebarNavigation[] = [];
		bottomNavigations.push({icon: "fa-cog", title: "Paramètres", url: EstablishmentUrlBuilder.buildUrl('/settings'), name: EstablishmentUrlBuilder.buildUrl('/settings')});
		bottomNavigations.push({icon: "fa-sign-out-alt", title: "Déconnexion", url: '/cas-redirect?disconnect=true', name: "disconnect"});

		this.bottom[0].navigations = bottomNavigations;
	}

	async loadPlatform() {
		if(!this.platformDescriptor) this.platformDescriptor = (await this.platformRepository.callContract('get', undefined, undefined)).success();
	}

	navigationClicked(_value: string) {}
}
