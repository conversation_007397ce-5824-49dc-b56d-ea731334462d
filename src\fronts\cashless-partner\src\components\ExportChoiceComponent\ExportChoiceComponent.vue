<script lang="ts" src="./ExportChoiceComponent.ts">
</script>

<style lang="sass">
@use './ExportChoiceComponent.scss' as *
</style>

<template>
    <div class="export-choice-component">
        <form-modal-or-drawer
            :title="'Exporter des données'"
            :subtitle="'Choisissez les données que vous souhaitez exporter'"
            :state="opened"
            @close="close()"
        >
            <template v-slot:content>
                <div class="table-scroll-wrapper">
                    <table class="data-table">
                        <thead>
                        <tr>
                            <td> Choisir un type d'export </td>
                        </tr>
                        </thead>
                        <tbody>
                        <tr v-for="exportChoice in exportChoices" @click="selectedChoice = exportChoice.id">
                            <td class="export-choice">
                                <div class="radio" :class="{selected: selectedChoice === exportChoice.id}">
                                    <div class="center"></div>
                                </div>
                                <div class="export-data">
                                    <span class="title"> {{ exportChoice.title }} </span>
                                    <span class="description">
                                        {{ exportChoice.description }}
                                    </span>
                                </div>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>

                <div class="input-group">
                    <dropdown
                        :values="[{
                            image: '/img/excel.png',
                            name: 'Excel',
                            value: 'xlsx'
                        }, {
                            image: '/img/orb.png',
                            name: 'OpenOffice',
                            value: 'ods'
                        }, {
                            name: 'CSV',
                            value: 'csv'
                        }]"
                        :default-selected="selectedFileType"
                        @update="selectedFileType = $event"
                    ></dropdown>
                </div>

                <div class="toggle-input" v-if="askExportZeroLines">
                    <div class="infos">
                        <span class="title"> Exporter également les lignes à zéro</span>
                    </div>

                    <toggle :default-toggled-value="exportZeroLines" @toggled="exportZeroLines = $event"></toggle>
                </div>
            </template>

            <template v-slot:buttons>
                <button type="button" class="white button" @click="close()"> Annuler </button>
                <button type="button" class="button" :class="{disabled: selectedChoice === null}" @click="validate()">
                    <i class="fa-regular fa-arrow-down-to-line"></i>
                    Exporter
                </button>
            </template>
        </form-modal-or-drawer>
    </div>
</template>