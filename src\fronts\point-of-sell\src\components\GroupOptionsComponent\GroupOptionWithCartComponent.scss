.vertical-container {
    display: flex;
    flex-direction: column;
    gap: 10px;
    align-items: center;
    height: 100%;
}

.group-option-with-cart-component {
    display: flex;
    align-items: stretch;
    gap: 10px;
    height: 80vh;

    &.performance-mode {
        .group-option-component {
            animation: none !important;
            transition: none !important;
        }
    }

    .group-option-component {
        position: absolute;
        background: white;
        padding: 40px;
        border-radius: 8px;
        width: 55vw;
        animation: .3s cubic-bezier(0.22, 1, 0.36, 1) 0s 1 slideIn;
        height: 100%;
        box-sizing: border-box;
        overflow: auto;

        transition: transform cubic-bezier(0.22, 1, 0.36, 1) .3s, background cubic-bezier(0.22, 1, 0.36, 1) .2s;

        &.position-1 {
            position: relative;
            z-index: 50;
            box-shadow: 0px -2px 29.1px 0px rgba(0, 0, 0, 0.25);
        }

        &.position-2 {
            z-index: 49;
            transform: scale(0.95) translateY(-6%);
        }

        &.position-small {
            z-index: 49;
            transform: scale(0.90) translateY(-12%);
        }
    }

    .cart {
        display: flex;
        flex-direction: column;
        gap: 10px;
        background: #F2F4F7;
        padding: 20px;
        border-radius: 8px;
        width: 300px;
        box-shadow: 0px -2px 29.1px 0px rgba(0, 0, 0, 0.25);
        box-sizing: border-box;
        height: 100%;

        .empty {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100%;
            width: 100%;
            text-align: center;
        }

        .groups {
            flex-grow: 2;
            overflow: auto;

            .group {
                display: flex;
                flex-direction: column;
                gap: 10px;
                margin-bottom: 20px;
                font-size: 15px;

                .group-name {
                    font-weight: 600;
                }

                .purchase {
                    display: flex;
                    align-items: center;
                    gap: 10px;
                    justify-content: space-between;
                    padding: 10px 15px;
                    background: white;
                    border-radius: 8px;

                    .quantity {
                        flex-shrink: 0;
                        display: flex;
                        width: 25px;
                        height: 25px;
                        justify-content: center;
                        align-items: center;
                        background: var(--primary-color);
                        color: var(--primary-text-color);
                        border-radius: 50%;

                        font-family: Montserrat, sans-serif;
                        font-size: 12px;
                        font-weight: 700;
                        line-height: 20px;
                    }

                    .left {
                        display: flex;
                        flex-direction: column;
                        gap: 8px;
                        flex-grow: 2;

                        .title {
                            font-size: 15px;
                            font-weight: 600;
                        }

                        .edit {
                            margin-top: 10px;
                            font-weight: 500;
                        }
                    }

                    .cancel {
                        padding: 10px;
                        border-radius: 8px;
                        cursor: pointer;

                        &:hover {
                            background: var(--secondary-hover-color);
                        }

                        i {
                            font-size: 20px;
                        }
                    }

                    .sub-items {
                        display: flex;
                        flex-direction: column;
                        gap: 2px;
                        font-size: 16px;
                    }
                }
            }
        }

        .recap {
            display: flex;
            justify-content: space-between;
            font-weight: 600;
            font-size: 16px;
            background: #F2F4F7;
        }
    }

    .show-cart {
        color: white;
        text-align: center;
        font-size: 16px;
        margin-top: 10px;

        @media (min-width: 900px) {
            display: none;
        }
    }

    @media (max-width: 900px) {
        flex-direction: column;
        flex-grow: 2;
        width: 100%;

        .stacked-groups {
            min-height: 0;
            margin-top: var(--safe-area-top);

            .group-option-component {
                padding: 20px;
                width: 100%;

                &.position-2, &.position-small {
                    height: 80px;
                }
            }
        }

        .cart {
            display: none;
            width: 100%;
        }

        &.display-cart {
            .stacked-groups {
                display: none;
            }

            .cart {
                display: flex;
            }
        }
    }
}

.step-selector {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px;
    background: white;
    border-radius: 12px;

    .step {
        padding: 10px 12px;
        background: #f2f4f7;
        border-radius: 8px;

        &.selected {
            background: var(--primary-color);
            color: var(--primary-text-color);
        }
    }
}


@-webkit-keyframes slideIn {
    from {
        transform: translateY(40%);
    }
    to {
        transform: translateY(0)
    }
}