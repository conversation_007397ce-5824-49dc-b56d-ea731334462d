.order-sidebar-payments-component {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    user-select: none;
    background: white;
    z-index: 500;

    padding: var(--safe-area-top) 0 var(--safe-area-bottom) 0;

    .quick-actions {
        display: flex;
        padding: 10px;
        align-items: stretch;
        justify-content: flex-start;
        gap: 10px;

        .action {
            display: flex;
            padding: 10px 20px;
            justify-content: center;
            align-items: center;
            gap: 10px;
            border-radius: 6px;
            background: #E8E8E8;
            cursor: pointer;

            font-family: Montserrat, sans-serif;
            font-size: 14px;
            font-weight: 500;

            &:hover {
                background: #cbcbcb;
            }

            &.disabled {
                opacity: 0.5;
                pointer-events: none;
            }

            .number {
                display: flex;
                width: 20px;
                height: 20px;
                justify-content: center;
                align-items: center;
                gap: 10px;
                flex-shrink: 0;
                border-radius: 50%;
                background: #FFF;
                box-sizing: border-box;
                font-size: 12px;
                font-weight: 500;
                line-height: 20px;
            }

            i {
                margin-top: 2px;
            }
        }
    }

    .hint {
        padding: 10px;
    }

    .payments {
        flex-grow: 2;
        display: flex;
        flex-direction: column;
        gap: 10px;
        padding: 10px;
        overflow: auto;

        &::-webkit-scrollbar {
            display: none;
        }

        .payment {
            display: flex;
            flex-direction: column;
            align-items: stretch;
            padding: 10px 15px;
            border-radius: 8px;
            background: #F2F4F7;
            gap: 10px;
            box-sizing: border-box;

            .top {
                display: flex;
                align-items: center;
                gap: 10px;

                .left {
                    flex-grow: 2;
                    display: flex;
                    align-items: center;
                    gap: 10px;
                    font-size: 15px;

                    .amounts {
                        display: flex;
                        flex-direction: column;
                        gap: 4px;

                        .overpaid {
                            font-size: 12px;
                        }

                        .amount {
                            font-weight: bold;
                        }
                    }
                }

                .delete {
                    flex-shrink: 0;
                    padding: 10px;
                    cursor: pointer;

                    &:hover {
                        background: #e6e9ec;
                        border-radius: 4px;
                    }

                    i {
                        font-size: 18px;
                    }
                }

                .order-actions {
                    all: unset;
                    flex-shrink: 0;
                    padding: 5px 10px;
                    cursor: pointer;

                    &:hover {
                        background: #e6e9ec;
                        border-radius: 4px;
                    }

                    i {
                        font-size: 18px;
                    }
                }

                .status {
                    padding: 10px;
                }
            }

            .bottom {
                display: grid;
                grid-template-columns: 1fr 1fr;
                grid-gap: 10px;
            }
        }
    }

    .keypad-container {
        position: fixed;
        background: rgba(0, 0, 0, 0.7);
        inset: 0;
        display: flex;
        align-items: center;
        justify-content: center;

        .keypad {
            display: flex;
            flex-direction: column;
            gap: 20px;
            background: white;
            width: 300px;
            border-radius: 8px;
            padding: 20px;

            .head {
                display: flex;
                align-items: center;
                justify-content: space-between;

                .title {
                    font-size: 18px;
                    font-weight: bold;
                }

                .close {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    height: 42px;
                    width: 42px;
                    background: #E8E8E8;
                    border-radius: 50%;
                }
            }


            .primary.button {
                height: 30px;
                font-size: 16px;
            }

            .selector {
                display: flex;
                align-items: center;
                justify-content: center;
                padding: 20px 0 30px 0;

                .amount {
                    width: 80px;
                    font-size: 24px;
                    font-weight: 500;
                    text-align: center;
                }

                .plus, .minus {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    height: 42px;
                    width: 42px;
                    background: #E8E8E8;
                    border-radius: 50%;
                    font-size: 18px;
                    font-weight: 400;
                }
            }

        }
    }
}