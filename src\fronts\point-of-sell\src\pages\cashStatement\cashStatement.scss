#cash-statement-page {
    height: 100%;
    display: grid;
    grid-template-columns: 1fr 1fr;

    @media (max-width: 900px) {
        grid-template-columns: 1fr;

        >.left, >.right {
            padding: 20px !important;
        }
    }

    >.left, >.right {
        flex-grow: 2;
    }

    >.left {
        display: flex;
        flex-direction: column;
        gap: 20px;
        background: white;
        align-items: center;
        justify-content: center;
        padding: 80px;
        box-sizing: border-box;
        text-align: center;

        h2 {
            margin: 0;
        }

        .big.button {
            padding: 15px 20px;
            width: 80%;
            font-weight: bold;

            &.green {
                background: #1D9F42;;
            }
        }

        .form-error {
            text-align: initial;
        }
    }

    .bottom {
        position: absolute;
        bottom: 20px;
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 20px;
        font-size: 14px;
        color: grey;
    }

    .right {
        padding: 80px;
        background: #F2F4F7;
        overflow: auto;

        .establishment-accounts {
            display: flex;
            flex-direction: column;
            gap: 20px;

            .establishment-account {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 20px 20px;
                background: white;
                border-radius: 8px;

                .left {
                    display: flex;
                    flex-direction: column;
                    gap: 4px;

                    .name {
                        font-weight: 600;
                    }

                    span.green {
                        color: #1D9F42;
                        font-weight: 600;
                    }
                }

                i {
                    font-size: 22px;
                }
            }
        }
    }
}

.displayed-receipt {
    position: fixed;
    inset: 0;
    z-index: 999;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: flex-start;
    justify-content: center;
    padding: 40px;
    overflow: auto;

    img {
        width: min(100%, 400px);
    }
}