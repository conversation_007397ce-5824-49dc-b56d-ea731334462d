<script lang="ts" src="./ToastManagerComponent.ts">
</script>

<style lang="sass">
@use './ToastManagerComponent.scss' as *
</style>

<template>
    <div class="toast-manager-component">
        <div class="toast" v-for="(toastState, index) of state">
            <div class="left">
                <i v-if="toastState.toast.type === 'SUCCESS'" class="fa-regular fa-circle-check"></i>
                <i v-else class="fa-regular fa-circle-xmark"></i>
            </div>
            <div class="center">
                <span class="title"> {{ toastState.toast.title }} </span>
                <span class="description"> {{ toastState.toast.description }} </span>
            </div>
            <div class="right" @click.stop="removeToast(index)">
                <i class="fa-regular fa-xmark"></i>
            </div>
        </div>
    </div>
</template>