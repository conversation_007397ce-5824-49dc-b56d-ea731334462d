#!/bin/sh
set -e

STATIC_FILES_EXPIRATION="${STATIC_FILES_EXPIRATION}"
if [ "${STATIC_FILES_EXPIRATION}" != "" ]
then
	STATIC_FILES_EXPIRATION="expires ${STATIC_FILES_EXPIRATION};"
fi

envsubst < /etc/nginx/conf.d/default.conf.base | sed -e "s/expires 8h;/${STATIC_FILES_EXPIRATION}/g" > /etc/nginx/conf.d/default.conf

echo "{" > "/var/www/config.json"

if [ -n "$mastodonApiEndpoint" ]; then
	echo "\"mastodonApiEndpoint\":\"$mastodonApiEndpoint\"," >> "/var/www/config.json"
fi
if [ -n "$remoteWsEndpoint" ]; then
	echo "\"remoteWsEndpoint\":\"$remoteWsEndpoint\"," >> "/var/www/config.json"
fi
if [ -n "$casFrontUrl" ]; then
	echo "\"casFrontUrl\":\"$casFrontUrl\"," >> "/var/www/config.json"
fi
if [ -n "$ticketingSimplifiedFrontUrl" ]; then
	echo "\"ticketingSimplifiedFrontUrl\":\"$ticketingSimplifiedFrontUrl\"," >> "/var/www/config.json"
fi
if [ -n "$ticketingFrontUrl" ]; then
	echo "\"ticketingFrontUrl\":\"$ticketingFrontUrl\"," >> "/var/www/config.json"
fi
if [ -n "$gmapsKey" ]; then
	echo "\"gmapsKey\":\"$gmapsKey\"," >> "/var/www/config.json"
fi
if [ -n "$develop" ]; then
	echo "\"develop\":\"$develop\"," >> "/var/www/config.json"
fi

version=`cat /var/www/version`
echo "\"version\":\"$version\"" >> "/var/www/config.json"
echo "}" >> "/var/www/config.json"

sed -f /usr/local/bin/docker-entrypoint-inject_config.sed /var/www/html/index.html > /var/www/html/index.html.tmp
cp /var/www/html/index.html.tmp /var/www/html/index.html && rm /var/www/html/index.html.tmp

exec "$@"
