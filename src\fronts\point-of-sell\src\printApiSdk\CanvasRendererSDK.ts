import FontUtils from "./FontUtils";

export type FormSize = {width: number; height: number};
export type FormPos = {x: number; y: number};
export type FormArea = {x1: number; y1: number; x2: number; y2: number};

export type PrintRendererSDKConfiguration = {
	allowTaintedImages?: boolean;
	targetSize: FormSize;
	formsMetricSystem?: "pixels" | "percent";
	clearColor?: string | null;
};

export type CanvasRenderFormOptions = {};

export type FormDirection = 0 | 90 | 180 | 270;

export type CanvasRenderTextFormOptions = CanvasRenderFormOptions & {
	horizontalAlign?: "left" | "center" | "right";
	verticalAlign?: "top" | "middle" | "bottom";

	fontSize?: number;
	fontFamily?: string;
	fontWeight?: string|number;
	fillColor?: string;

	direction?: FormDirection;
};
const CanvasRenderTextFormOptionsDefault: Required<CanvasRenderTextFormOptions> = {
	horizontalAlign: "center",
	verticalAlign: "middle",
	fontSize: -1,
	fontFamily: "Arial",
	fontWeight: "normal",
	fillColor: "black",
	direction: 0,
};

export type CanvasRenderImageFormOptions = CanvasRenderFormOptions & {
	upscaleMethod?: "contain" | "stretched"; // 'cover'
	direction?: FormDirection;
	imageSmoothingQuality?: "linear" | "pixelated";
};
const CanvasRenderImageFormOptionsDefault: Required<CanvasRenderImageFormOptions> = {
	upscaleMethod: "contain",
	direction: 0,
	imageSmoothingQuality: "linear",
};

export type CanvasRenderRectFormOptions = CanvasRenderFormOptions & {
	fillColor?: string;
};
const CanvasRenderRectFormOptionsDefault: Required<CanvasRenderRectFormOptions> = {
	fillColor: "black",
};

export default class CanvasRendererSDK {
	private config: Required<PrintRendererSDKConfiguration>;
	private canvas: HTMLCanvasElement;
	private canvasContext: CanvasRenderingContext2D;

	private renderedAreas: FormArea[] = [];

	constructor(config: PrintRendererSDKConfiguration) {
		let baseConfig: Required<PrintRendererSDKConfiguration> = {
			allowTaintedImages: false,
			targetSize: {width: 0, height: 0},
			formsMetricSystem: "pixels",
			clearColor: null,
		};

		this.config = {...baseConfig, ...config};

		this.canvas = document.createElement("canvas");
		this.canvas.width = this.config.targetSize.width;
		this.canvas.height = this.config.targetSize.height;
		this.canvasContext = <any>this.canvas.getContext("2d");

		if (this.config.clearColor) {
			this.canvasContext.rect(0, 0, this.config.targetSize.width, this.config.targetSize.height);
			this.canvasContext.fillStyle = this.config.clearColor;
			this.canvasContext.fill();
		}
	}

	getConfig(): Required<PrintRendererSDKConfiguration> {
		return {...this.config};
	}

	convertFormSizeToPixels(size: FormSize): FormSize {
		if (this.config.formsMetricSystem === "percent") {
			return {
				width: Math.round((size.width / 100) * this.config.targetSize.width),
				height: Math.round((size.height / 100) * this.config.targetSize.height),
			};
		}
		return size;
	}
	convertFormPosToPixels(size: FormPos): FormPos {
		if (this.config.formsMetricSystem === "percent") {
			return {
				x: Math.round((size.x / 100) * this.config.targetSize.width),
				y: Math.round((size.y / 100) * this.config.targetSize.height),
			};
		}
		return size;
	}

	protected loadImageFromBase64(base64: string): Promise<HTMLImageElement> {
		return new Promise((resolve, reject) => {
			let img = new Image();
			img.onload = () => {
				resolve(img);
			};
			img.onerror = reject;
			img.src = base64;
		});
	}

	renderImageFromBase64(base64: string, userPos: FormPos, userFormSize: FormSize, userOptions?: CanvasRenderImageFormOptions): Promise<void> {
		return this.loadImageFromBase64(base64).then((img) => {
			return this.renderImage(img, userPos, userFormSize, userOptions);
		});
	}

	public rotateImage(image: HTMLImageElement, direction: FormDirection): Promise<HTMLImageElement> {
		if (direction === 0) return Promise.resolve(image);

		return new Promise<HTMLImageElement>((resolve, reject) => {
			let rotatedCanvas = document.createElement("canvas");
			rotatedCanvas.width = direction === 90 || direction === 270 ? image.height : image.width;
			rotatedCanvas.height = direction === 90 || direction === 270 ? image.width : image.height;
			let rotatedContext: CanvasRenderingContext2D = <CanvasRenderingContext2D>rotatedCanvas.getContext("2d");

			rotatedContext.rotate((direction * Math.PI) / 180);
			if (direction === 90) rotatedContext.translate(0, -image.height);
			if (direction === 180) rotatedContext.translate(-image.width, -image.height);
			if (direction === 270) rotatedContext.translate(-image.width, 0);

			rotatedContext.drawImage(image, 0, 0);

			let rotatedImage = new Image();
			rotatedImage.onload = () => {
				resolve(rotatedImage);
			};
			rotatedImage.onerror = reject;
			rotatedImage.src = rotatedCanvas.toDataURL();
		});
	}

	renderImage(image: HTMLImageElement, userPos: FormPos, userFormSize: FormSize, userOptions?: CanvasRenderImageFormOptions): Promise<void> {
		let options: Required<CanvasRenderImageFormOptions> = {...CanvasRenderImageFormOptionsDefault, ...userOptions};

		return this.rotateImage(image, options.direction).then((image) => {
			let formSize = this.convertFormSizeToPixels(userFormSize);
			let pos = this.convertFormPosToPixels(userPos);
			this.addArea({x1: pos.x, x2: pos.x + formSize.width, y1: pos.y, y2: pos.y + formSize.height});

			// this.setTransformOnContext(90, pos, formSize);
			// pos.x = 0;
			// pos.y = 0;

			if (options.upscaleMethod === "stretched") {
				this.canvasContext.drawImage(image, 0, 0, image.width, image.height, pos.x, pos.y, formSize.width, formSize.height);
			} else if (options.upscaleMethod === "contain") {
				let imageRatioX = formSize.width / image.width;
				let imageRatioY = formSize.height / image.height;
				let selectedImageRatio = Math.min(imageRatioX, imageRatioY);

				let targetWidth = Math.floor(image.width * selectedImageRatio);
				let targetHeight = Math.floor(image.height * selectedImageRatio);

				let marginLeft = Math.floor((formSize.width - targetWidth) / 2);
				let marginTop = Math.floor((formSize.height - targetHeight) / 2);

				this.canvasContext.imageSmoothingEnabled = options.imageSmoothingQuality === "linear";

				this.canvasContext.drawImage(image, 0, 0, image.width, image.height, pos.x + marginLeft, pos.y + marginTop, targetWidth, targetHeight);
			}
		});
	}

	renderText(text: string, userPos: FormPos, userFormSize: FormSize, userOptions?: CanvasRenderTextFormOptions){
		let options: Required<CanvasRenderTextFormOptions> = {...CanvasRenderTextFormOptionsDefault, ...userOptions};

		let formSize = this.convertFormSizeToPixels(userFormSize);
		let pos = this.convertFormPosToPixels(userPos);

		let fullTextWithBr = text.replace(/(?:\r\n|\r|\n)/g, "<br>");

		if (options.fontSize === -1) {
			let formSizeForAutoFontSize = {
				width: options.direction === 90 ? formSize.height : formSize.width,
				height: options.direction === 90 ? formSize.width : formSize.height,
			};

			options.fontSize = FontUtils.computeBestFontSizeForElement(
				formSizeForAutoFontSize,
				fullTextWithBr,
				{
					family: options.fontFamily,
					weight: options.fontWeight ? ''+options.fontWeight : 'normal',
				},
				true
			);
		}

		let textLines = fullTextWithBr.split("<br>");

		// https://stackoverflow.com/questions/1134586/how-can-you-find-the-height-of-text-on-an-html-canvas
		let textSpacing = FontUtils.getTextIntrinsicSizes(textLines.join(), options.fontFamily, options.fontSize + "px");

		let computedTextSize = FontUtils.computeTextSizeOffscreenInPixels(
			fullTextWithBr,
			{
				family: options.fontFamily,
				weight: options.fontWeight ? ''+options.fontWeight : 'normal',
			},
			options.fontSize + "px"
		);
		let originalComputedTextSize: FormSize = {width: computedTextSize.width, height: computedTextSize.height};
		if (options.direction === 90 || options.direction === 270) {
			computedTextSize.width = originalComputedTextSize.height;
			computedTextSize.height = originalComputedTextSize.width;
		}

		let marginLeftTextAlignment: number = 0;
		let marginTopTextAlignment: number = 0;

		if (options.horizontalAlign === "center") marginLeftTextAlignment += (formSize.width - computedTextSize.width) / 2;
		else if (options.horizontalAlign === "right") marginLeftTextAlignment += formSize.width - computedTextSize.width;

		if (options.verticalAlign === "top") marginTopTextAlignment += 0;
		else if (options.verticalAlign === "middle") marginTopTextAlignment += (formSize.height - computedTextSize.height) / 2;
		else if (options.verticalAlign === "bottom") marginTopTextAlignment += formSize.height - computedTextSize.height;

		let realDrawAreaPos = {
			x: pos.x + marginLeftTextAlignment,
			y: pos.y + marginTopTextAlignment,
		};
		let realDrawAreaSize = {
			width: computedTextSize.width,
			height: computedTextSize.height,
		};

		// console.log('RECT', realDrawAreaPos, realDrawAreaSize)
		// this.canvasContext.fillStyle = 'blue';
		// this.canvasContext.fillRect(realDrawAreaPos.x,realDrawAreaPos.y,realDrawAreaSize.width, realDrawAreaSize.height);
		// this.renderRect(realDrawAreaPos,realDrawAreaSize,{fillColor:'blue'});

		this.canvasContext.rotate((options.direction * Math.PI) / 180);

		if (options.direction === 0) this.canvasContext.translate(pos.x + marginLeftTextAlignment, pos.y + marginTopTextAlignment + textSpacing.ascent);
		if (options.direction === 90) this.canvasContext.translate(pos.y + marginTopTextAlignment, -pos.x - marginLeftTextAlignment - textSpacing.descent);
		if (options.direction === 180)
			this.canvasContext.translate(-pos.x - marginLeftTextAlignment - originalComputedTextSize.width, -pos.y - marginTopTextAlignment - textSpacing.descent);
		if (options.direction === 270)
			this.canvasContext.translate(-originalComputedTextSize.width - pos.y - marginTopTextAlignment, pos.x + textSpacing.ascent + marginLeftTextAlignment);

		this.canvasContext.textAlign = "left";
		this.canvasContext.fillStyle = options.fillColor;
		const canvasFont = (options.fontWeight ?? 'normal') + " " + options.fontSize + "px " + options.fontFamily;
		this.canvasContext.font = canvasFont;
		// https://www.w3schools.com/tags/canvas_textbaseline.asp
		this.canvasContext.textBaseline = "alphabetic";

		for (let iLine = 0; iLine < textLines.length; ++iLine)
			this.canvasContext.fillText(textLines[iLine]!, 0, iLine * textSpacing.height);

		this.canvasContext.setTransform(1, 0, 0, 1, 0, 0);

		this.addArea({
			x1: realDrawAreaPos.x,
			x2: realDrawAreaPos.x + realDrawAreaSize.width,
			y1: realDrawAreaPos.y,
			y2: realDrawAreaPos.y + realDrawAreaSize.height,
		});
	}

	renderRect(userPos: FormPos, userFormSize: FormSize, userOptions?: CanvasRenderRectFormOptions): Promise<void> {
		let options: Required<CanvasRenderRectFormOptions> = {...CanvasRenderRectFormOptionsDefault, ...userOptions};

		let formSize = this.convertFormSizeToPixels(userFormSize);
		let pos = this.convertFormPosToPixels(userPos);
		this.addArea({x1: pos.x, x2: pos.x + formSize.width, y1: pos.y, y2: pos.y + formSize.height});

		this.canvasContext.fillStyle = options.fillColor;
		this.canvasContext.fillRect(pos.x, pos.y, formSize.width, formSize.height);

		return Promise.resolve();
	}

	getCanvasElement(): HTMLCanvasElement {
		return this.canvas;
	}

	exportToImage(): string {
		return this.canvas.toDataURL();
	}

	getRenderedAreas() {
		return this.renderedAreas;
	}

	protected addArea(area: FormArea) {
		area.x1 = Math.floor(area.x1);
		area.y1 = Math.floor(area.y1);
		area.x2 = Math.ceil(area.x2);
		area.y2 = Math.ceil(area.y2);

		if (area.x1 >= this.config.targetSize.width) return;
		if (area.y1 >= this.config.targetSize.height) return;

		let x2 = Math.min(area.x2, this.config.targetSize.width - 1);
		let y2 = Math.min(area.y2, this.config.targetSize.height - 1);

		this.renderedAreas.push({x1: area.x1, y1: area.y1, x2: x2, y2: y2});
	}
}
