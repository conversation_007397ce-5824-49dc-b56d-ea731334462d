<script lang="ts" src="./CustomerChipDatesFormComponent.ts">
</script>

<style lang="sass">
@use './CustomerChipDatesFormComponent.scss' as *
</style>

<template>
    <div class="customer-chip-form-component">
        <form-modal-or-drawer
            :state="opened"
            :title="'Modifier les dates de ' + $filters.Chip(customerChip.chipVisualId)"
            :subtitle="'Modifier les dates d\'appartenance de la puce ' + $filters.Chip(customerChip.chipVisualId)"
            @close="close()"
        >
            <template v-slot:content>
                <div>
                    Heure {{ getCurrentTimezone() }}
                </div>

                <div class="two-inputs">
                    <div class="input-group">
                        <label> Date de début </label>

                        <div class="fluid input">
                            <input :value="customerChipDatesData.startDate" class="fluid" placeholder="jj/mm/aaaa" v-cleave="{obj: customerChipDatesData, key: 'startDate', options: cleaveDate}" />
                        </div>
                    </div>
                    <div class="input-group">
                        <label> Heure </label>

                        <div class="fluid input">
                            <input :value="customerChipDatesData.startHour" class="fluid" placeholder="hh:mm" v-cleave="{obj: customerChipDatesData, key: 'startHour', options: cleaveHour}" />
                        </div>
                    </div>
                </div>

                <div class="two-inputs">
                    <div class="input-group">
                        <label> Date de fin </label>

                        <div class="fluid input">
                            <input :value="customerChipDatesData.endDate" class="fluid" placeholder="jj/mm/aaaa" v-cleave="{obj: customerChipDatesData, key: 'endDate', options: cleaveDate}" />
                        </div>
                    </div>
                    <div class="input-group">
                        <label> Heure </label>

                        <div class="fluid input">
                            <input :value="customerChipDatesData.endHour" class="fluid" placeholder="hh:mm" v-cleave="{obj: customerChipDatesData, key: 'endHour', options: cleaveHour}" />
                        </div>
                    </div>
                </div>

                <div class="form-error" v-if="error">
                    <i class="fa-solid fa-exclamation-circle"></i>
                    <div class="details">
                        <span class="title"> Erreur </span>
                        <span class="description">{{ error }}</span>
                    </div>
                </div>
            </template>

            <template v-slot:buttons>
                <button type="button" class="white button" @click="close()"> Annuler </button>
                <button type="button" class="button" @click="save()">
                    <i class="fa-regular fa-pen-line fa-flip-horizontal"></i>
                   valider
                </button>
            </template>
        </form-modal-or-drawer>
    </div>
</template>