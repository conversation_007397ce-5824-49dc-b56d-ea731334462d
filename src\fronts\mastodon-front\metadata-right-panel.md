# Metadata Right Panel Implementation Plan

## Overview
Implement a right panel view for displaying selected MetadataDescriptor information in the metadata page. The panel should integrate with the existing FilterTableLayoutComponent drawer functionality and follow established UI patterns from other pages in the mastodon-fronts project.

## Current State Analysis
- ✅ Basic metadata page with FilterTableLayoutComponent exists
- ✅ Table displays MetadataDescriptors correctly
- ❌ No selection functionality implemented
- ❌ Right panel drawer is disabled (`:drawer-opened="false"`)
- ❌ No right panel content in template

## Research Findings: Existing Right Panel Patterns

### 1. FilterTableLayoutComponent Drawer Integration
**Pattern used in**: `filter-table-layout.md`, `ticketing-partner/customers`, `cashless-partner/transactions`

**Key Implementation Elements**:
- `:drawer-opened="selectedObject !== null"` prop controls drawer visibility
- `v-slot:right` template contains panel content
- Row click handlers for selection: `@click="toggleSelected(item)"`
- Selection state managed via reactive property

### 2. Standard Right Panel Template Structure
**Pattern used in**: `ticketing-partner/customers.vue`, `cashless-partner/customers.vue`

**Template Structure**:
```vue
<template v-slot:right>
    <!-- Empty state -->
    <div v-if="!selectedObject" class="empty-right-panel">
        <img :src="$assets.selectHint" />
        Click message with line breaks
    </div>
    
    <!-- Selected object details -->
    <div v-else class="selected-[object-type]">
        <!-- Close button (mobile only) -->
        <div class="close" @click="selectedObject = null">
            <i class="fa-regular fa-xmark"></i>
            <span> Fermer </span>
        </div>
        
        <!-- Header section -->
        <div class="header">
            <div class="left">
                <h2>{{ selectedObject.title }}</h2>
                <span>{{ selectedObject.subtitle }}</span>
            </div>
            
            <!-- Actions dropdown (optional) -->
            <dropdown-button 
                button-class="grey button"
                icon="fa-regular fa-ellipsis-vertical"
                alignment="RIGHT"
                :actions="objectActions"
                @clicked="handleAction($event)"
            />
        </div>
        
        <!-- Details content -->
        <div class="[object-type]-details">
            <!-- Properties display -->
        </div>
    </div>
</template>
```

### 3. CSS Classes and Styling (Existing in Project)

**Global CSS Classes Available**:
- `.empty-right-panel` - Empty state styling (from `ticketing-partner/global.scss`)
- `.selected-*` - Container for selected object details
- `.close` - Close button (auto-hidden on desktop >1500px)
- `.header` - Header section with title and actions
- `.header .left` - Title area
- `.properties-table` - Key-value pairs display table
- `.properties-table .row` - Individual property row
- `.properties-table .title` - Property label
- `.properties-table .value` - Property value

**CSS Variables Available**:
- `--desktop-padding: 40px` - Standard desktop padding
- `--mobile-padding: 20px` - Standard mobile padding
- Various project-specific color variables

## Execution Plan

### Phase 1: Enable Selection Functionality
**File**: `/src/fronts/mastodon-front/src/pages/metadata/metadata.ts`

**Changes Required**:
1. **Add Row Click Handler**:
```typescript
// Update table row template in metadata.vue to include click handler
@click="toggleSelectedMetadata(metadata)"
```

2. **Update Drawer State**:
```typescript
// Change drawer-opened prop in metadata.vue
:drawer-opened="selectedMetadata !== null"
```

3. **Selection State Management** (already exists):
```typescript
// toggleSelectedMetadata method already implemented correctly
selectedMetadata: MetadataDescriptorApiOut|null = null;
```

### Phase 2: Implement Right Panel Template
**File**: `/src/fronts/mastodon-front/src/pages/metadata/metadata.vue`

**Template Structure** (following established patterns):
```vue
<template v-slot:right>
    <!-- Empty state -->
    <div v-if="!selectedMetadata" class="empty-right-panel">
        <img :src="$assets.selectHint" />
        Cliquez sur un descripteur pour <br/> le sélectionner
    </div>
    
    <!-- Selected metadata details -->
    <div v-else class="selected-metadata">
        <!-- Close button -->
        <div class="close" @click="selectedMetadata = null">
            <i class="fa-regular fa-xmark"></i>
            <span> Fermer </span>
        </div>
        
        <!-- Header with metadata name -->
        <div class="header">
            <div class="left">
                <h2>{{ selectedMetadata.descriptorTemplate.name }}</h2>
                <span>{{ selectedMetadata.descriptorTemplate.type }}</span>
            </div>
        </div>
        
        <!-- Metadata details -->
        <div class="metadata-details">
            <h3>Informations générales</h3>
            <div class="properties-table">
                <div class="row">
                    <span class="title">Identifiant</span>
                    <span class="value">{{ selectedMetadata.uid }}</span>
                </div>
                <div class="row">
                    <span class="title">Nom</span>
                    <span class="value">{{ selectedMetadata.descriptorTemplate.name }}</span>
                </div>
                <div class="row">
                    <span class="title">Type</span>
                    <span class="value">{{ selectedMetadata.descriptorTemplate.type }}</span>
                </div>
                <div class="row">
                    <span class="title">Requis</span>
                    <span class="value">
                        <span v-if="selectedMetadata.descriptorTemplate.required" class="label green">Oui</span>
                        <span v-else class="label grey">Non</span>
                    </span>
                </div>
                <div class="row" v-if="selectedMetadata.description">
                    <span class="title">Description</span>
                    <span class="value">{{ selectedMetadata.description }}</span>
                </div>
                <div class="row" v-if="selectedMetadata.descriptorTemplate.description">
                    <span class="title">Description du modèle</span>
                    <span class="value">{{ selectedMetadata.descriptorTemplate.description }}</span>
                </div>
                <div class="row">
                    <span class="title">Date de création</span>
                    <span class="value">{{ $filters.Date(selectedMetadata.creationDatetime) }}</span>
                </div>
            </div>
            
            <!-- Target information -->
            <h3 v-if="selectedMetadata.targetList && selectedMetadata.targetList.length > 0">Cibles</h3>
            <div v-if="selectedMetadata.targetList && selectedMetadata.targetList.length > 0" class="properties-table">
                <div class="row" v-for="target in selectedMetadata.targetList" :key="target.uid">
                    <span class="title">{{ target.name || 'Cible' }}</span>
                    <span class="value">{{ target.value || 'N/A' }}</span>
                </div>
            </div>
        </div>
    </div>
</template>
```

### Phase 3: Add Row Selection Visual Feedback
**File**: `/src/fronts/mastodon-front/src/pages/metadata/metadata.vue`

**Update Table Rows**:
```vue
<tr v-else v-for="metadata in filteredMetadataDescriptors" 
    :key="metadata.uid"
    :class="{selected: selectedMetadata && metadata.uid === selectedMetadata.uid}"
    @click="toggleSelectedMetadata(metadata)">
```

**CSS Class**: Use existing `.selected` class for row highlighting

### Phase 4: Styling Implementation
**File**: `/src/fronts/mastodon-front/src/pages/metadata/metadata.scss`

**Styling Strategy**: Use ONLY existing CSS patterns and classes

**Required Styles** (following existing patterns):
```scss
#metadata-page {
  // Existing styles...
  
  .selected-metadata {
    display: flex;
    flex-direction: column;
    position: relative;
    
    .close {
      display: none;
      margin-bottom: 20px;
      cursor: pointer;
      user-select: none;
      
      @media screen and (max-width: 1500px) {
        display: flex;
        gap: 10px;
        align-items: center;
      }
    }
    
    .header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: var(--desktop-padding, 40px) var(--desktop-padding, 40px) 20px var(--desktop-padding, 40px);
      border-bottom: 1px solid #E2E2E2;
      
      @media (max-width: 900px) {
        padding: var(--mobile-padding, 20px);
      }
      
      .left {
        display: flex;
        flex-direction: column;
        gap: 5px;
        
        h2 {
          margin: 0;
          font-size: 22px;
          font-weight: bold;
        }
        
        span {
          font-size: 16px;
          color: #575757;
        }
      }
    }
    
    .metadata-details {
      padding: var(--desktop-padding, 40px);
      
      @media (max-width: 900px) {
        padding: var(--mobile-padding, 20px);
      }
      
      h3 {
        margin: 20px 0 15px 0;
        font-size: 16px;
        font-weight: 600;
        color: black;
        
        &:first-child {
          margin-top: 0;
        }
      }
      
      .properties-table {
        display: flex;
        flex-direction: column;
        gap: 12px;
        margin-bottom: 20px;
        
        .row {
          display: flex;
          justify-content: space-between;
          align-items: flex-start;
          gap: 20px;
          
          .title {
            font-weight: 500;
            color: #575757;
            min-width: 120px;
            flex-shrink: 0;
          }
          
          .value {
            text-align: right;
            word-break: break-word;
            
            .label {
              padding: 4px 8px;
              border-radius: 4px;
              font-size: 12px;
              font-weight: 500;
              
              &.green {
                background-color: rgba(13, 184, 27, 0.2);
                color: #0db71b;
                border: 1px solid #0db71b;
              }
              
              &.grey {
                background-color: rgba(148, 148, 148, 0.2);
                color: #726f6f;
                border: 1px solid #A1A1A1;
              }
            }
          }
        }
      }
    }
  }
  
  // Existing status styles...
}
```

### Phase 5: Asset Integration
**File**: Check if select hint image exists

**Implementation**:
1. **Verify Asset Path**: Ensure `$assets.selectHint` or similar exists
2. **Fallback**: Use existing pattern from establishments page if needed
3. **Alternative**: Use Font Awesome icon if image not available

## Implementation Details

### Required Changes Summary

**Files to Modify**:
1. **metadata.vue** - Add right panel template and row click handlers
2. **metadata.scss** - Add styling for right panel components
3. **metadata.ts** - No changes required (selection logic already exists)

**Lines of Code**: Approximately 80-100 lines total

### Key Implementation Notes

1. **Selection Management**: 
   - Use existing `selectedMetadata` property
   - Use existing `toggleSelectedMetadata()` method
   - Update `:drawer-opened` prop

2. **Styling Approach**:
   - Use existing CSS classes where possible
   - Follow established padding/spacing patterns
   - Use existing color schemes and border styles
   - Maintain responsive design principles

3. **Content Display**:
   - Show essential MetadataDescriptor properties
   - Use properties-table pattern for key-value pairs
   - Display type-specific information
   - Include creation date and targets if available

4. **User Experience**:
   - Empty state with helpful message
   - Visual row selection feedback
   - Mobile-friendly close button
   - Consistent with other right panels

### Data Structure Reference

**MetadataDescriptorApiOut Properties**:
- `uid` - Unique identifier
- `descriptorTemplate.name` - Display name
- `descriptorTemplate.type` - Metadata type
- `descriptorTemplate.required` - Required flag
- `descriptorTemplate.description` - Template description
- `description` - Instance description
- `creationDatetime` - Creation timestamp
- `targetList` - Array of target objects

### Mobile Responsiveness

**Responsive Features**:
- Close button appears on screens <1500px
- Reduced padding on mobile devices
- Proper text wrapping and spacing
- Drawer overlay behavior on mobile

### Testing Strategy

**Manual Testing**:
1. Click on metadata rows to verify selection
2. Verify right panel opens/closes correctly
3. Test all property displays
4. Verify mobile responsiveness
5. Test empty state display

**Edge Cases**:
- MetadataDescriptors with no description
- MetadataDescriptors with no targets
- Very long property values
- Mobile drawer behavior

## Success Criteria

### Functional Requirements
- ✅ Click on table row selects metadata descriptor
- ✅ Right panel opens when metadata selected
- ✅ Right panel shows all relevant metadata properties
- ✅ Close button works correctly
- ✅ Empty state displays when nothing selected
- ✅ Visual feedback for selected rows

### Design Requirements
- ✅ Consistent with other right panels in project
- ✅ Uses existing CSS classes and patterns
- ✅ Proper spacing and typography
- ✅ Mobile responsive design
- ✅ Proper color scheme and borders

### Technical Requirements
- ✅ No new CSS variables or custom styling
- ✅ Follows established Vue.js patterns
- ✅ Proper TypeScript typing
- ✅ Performance optimized
- ✅ Accessible design

## Estimated Implementation Time
**Total**: 3-4 hours
- Phase 1 (Selection): 30 minutes
- Phase 2 (Template): 1.5 hours  
- Phase 3 (Visual feedback): 30 minutes
- Phase 4 (Styling): 1 hour
- Phase 5 (Asset integration): 30 minutes
- Testing and refinement: 30 minutes

This implementation will provide a comprehensive, consistent right panel experience that matches the established patterns throughout the mastodon-fronts project while displaying all relevant metadata descriptor information in an organized, user-friendly format.