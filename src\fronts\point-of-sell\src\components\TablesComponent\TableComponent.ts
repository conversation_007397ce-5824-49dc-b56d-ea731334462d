import {Component, Prop, Vue, Watch} from "vue-facing-decorator";
import {PosState} from "../../model/PosState";
import {
    DiningTableApiOut,
    OrderApiOut,
    OrderTransferRequestApiIn,
    ProductHttpOrdersContract,
    uuidScopeIot_deviceApp
} from "@groupk/mastodon-core";
import {AutoWired, UuidUtils} from "@groupk/horizon2-core";
import {translateResponseError} from "../../../../../shared/RepositoryExtensions";
import {LocalOrderRepository} from "../../repositories/LocalOrderRepository";
import {OrderRepository} from "../../../../../shared/repositories/OrderRepository";
import {AppBus} from "../../config/AppBus";
import {LocalOrder, LocalOrderTransfer} from "../../model/LocalOrder";

@Component({
    components: {},
    emits: ['show-modal-for-table', 'animate-mobile-cart', 'show-mobile-cart']
})
export default class TableComponent extends Vue {
    @Prop({required: true}) posState!: PosState;
    @Prop({required: true}) table!: DiningTableApiOut;
    @Prop({required: true}) localOrders!: LocalOrder[];
    @Prop({required: true}) serverOrders!: OrderApiOut[];
    @Prop({required: true}) orderTransfers!: LocalOrderTransfer[];

    filteredOrders: OrderApiOut[] = [];

    opened: boolean = false;

    @AutoWired(OrderRepository) accessor orderRepository!: OrderRepository;
    @AutoWired(LocalOrderRepository) accessor localOrderRepository!: LocalOrderRepository;
    @AutoWired(AppBus) accessor appBus!: AppBus;

    beforeMount() {
        // this.appBus.on('orderSaved', this.updateOrder);
        // this.appBus.on('orderTransferSaved', this.updateOrderTransfer);

        this.filteredOrders = this.getFilteredOrders();
    }

    unmounted() {
        // this.appBus.off('orderSaved', this.updateOrder);
        // this.appBus.off('orderTransferSaved', this.updateOrderTransfer);
    }

    @Watch('localOrders', {deep: true})
    onLocalOrdersChange() {
        this.filteredOrders = this.getFilteredOrders();
    }

    @Watch('serverOrders', {deep: true})
    onServerOrdersChange() {
        this.filteredOrders = this.getFilteredOrders();
    }

    // Computed property for filtered orders that automatically updates
    getFilteredOrders(): OrderApiOut[] {
        const transferredOrderIds = new Set(this.orderTransfers
            .filter(t => !t.canceled)
            .map(t => t.order.uid));

        const localOrders = this.localOrders
            .filter(localOrder => !localOrder.transferred)
            .filter(localOrder => !localOrder.order.transferredTo)
            .map(localOrder => localOrder.order)
            .filter(order => this.posState.orderExecutorModel.getOrderTotals(order).leftToPay !== 0);

        const localOrderIds = new Set(this.localOrders
            .filter(lo => !lo.transferred)
            .map(lo => lo.order.uid));

        const serverOrders = this.serverOrders.filter(order => {
                return !localOrderIds.has(order.uid) &&
                    this.posState.orderExecutorModel.getOrderTotals(order).leftToPay !== 0 &&
                    !order.transferredTo
            }
        );

        return [...localOrders, ...serverOrders]
            .filter(order => order.diningExtra?.tableUid === this.table.uid);
    }

    async selectOrder(order: OrderApiOut) {
        const correspondingLocalOrder = await this.localOrderRepository.findOne(order.uid);

        if (correspondingLocalOrder !== null && !correspondingLocalOrder.transferred) {
            // Order is local, we can display it
            this.posState.currentOrder = await this.localOrderRepository.findOne(order.uid);
            this.toggleMobileCart();
        } else {
            // Order is on another POS, have to ask for transfer
            const orderCorrespondingCashStatementSession = this.posState.currentCashStatement.sessions.find((session) => session.uid === order.cashStatementSessionUid);
            if (!orderCorrespondingCashStatementSession) throw new Error('Cannot find corresponding cash statement session');

            const response = await this.orderRepository.callContract('requestTransfer', {establishmentUid: this.posState.establishmentUid}, new OrderTransferRequestApiIn({
                orderUid: order.uid,
                targetIotDevice: UuidUtils.scopedToVisual(this.posState.iotDevice.uid, uuidScopeIot_deviceApp),
                creatorIotDevice: orderCorrespondingCashStatementSession.iotDeviceUid
            }));

            if (response.isSuccess()) {
                this.appBus.emit('displayToast', {
                    title: 'Une demande de transfert à été envoyée',
                    description: 'Vous pourrez accéder à la commande lorsque la demande est acceptée',
                    color: 'green',
                    closable: true,
                    duration: 3000,
                });
            } else {
                const translation = translateResponseError<typeof ProductHttpOrdersContract, 'requestTransfer'>(response, {
                    order_not_found: 'Cette commande n\'existe plus',
                    invalid_data: undefined,
                    cash_statement_closed: 'Le ticket Z de cette commande est clos',
                    order_transfer_already_pending_on_order: 'Cette commande à déjà été demandée par une autre caisse',
                    order_transfer_cant_itself: 'Cette commande vous appartient déjà',
                    order_transfer_order_not_on_creator: 'Cette commande vous appartient déjà'
                })
                this.appBus.emit('displayToast', {
                    title: 'Demande échouée',
                    description: `La caisse n'a pas réussi à transférer cette commande (${translation})`,
                    color: 'red',
                    closable: true,
                    duration: 3000,
                })
            }
        }
    }

    async createOrderForTable(table: DiningTableApiOut) {
        const tableOrder = this.filteredOrders[0];
        if (tableOrder) {
            this.selectOrder(tableOrder);
        } else {
            const order = this.posState.orderExecutorModel.createOrder({
                toCountry: this.posState.toCountry,
                sellerEstablishmentAccountUid: this.posState.currentEstablishmentAccountUid,
                cashStatementSessionUid: this.posState.currentCashStatementSession.uid,
                roundPricesAtEnd: false
            });
            this.posState.currentOrder = new LocalOrder({
                uid: order.uid,
                order: order
            });
            this.posState.currentOrder.diningExtra.tableUid = table.uid;
            this.posState.currentPage = 'sell';
            await this.localOrderRepository.saveAndResync(this.posState.currentOrder);
        }
    }

    toggleMobileCart() {
        this.$emit('animate-mobile-cart');
        setTimeout(() => {
            this.$emit('show-mobile-cart');
        }, 150)
    }

    // Variables for long press handling
    longPressTimeout: number | null = null;
    longPressDuration: number = 500; // Duration in milliseconds for long press

    // Handle long press start
    handleLongPressStart(table: DiningTableApiOut, event: MouseEvent | TouchEvent) {
        // Clear any existing timeout
        if (this.longPressTimeout !== null) {
            clearTimeout(this.longPressTimeout);
        }

        // Set a timeout for the long press duration
        this.longPressTimeout = window.setTimeout(() => {
            // Handle the long press action here
            this.$emit('show-modal-for-table', table);
            this.longPressTimeout = null;
            event.preventDefault();
        }, this.longPressDuration);
    }

    // Handle long press end
    handleLongPressEnd() {
        // Clear the timeout if the press ends before the duration
        if (this.longPressTimeout !== null) {
            clearTimeout(this.longPressTimeout);
            this.longPressTimeout = null;
        }
    }

    getTimeSinceDate(isoDate: string): string {
        const now = new Date();
        const date = new Date(isoDate);
        const diffMs = now.getTime() - date.getTime();
        const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
        const diffMinutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));
        return `${diffHours.toString().padStart(2, '0')}:${diffMinutes.toString().padStart(2, '0')}`;
    }

    getOrderTotals(order: OrderApiOut) {
        return this.posState.orderExecutorModel.getOrderTotals(order);
    }
}