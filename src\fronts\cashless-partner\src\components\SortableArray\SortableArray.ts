import {Plugins, Sortable, SortableStartEvent, SortableStopEvent} from "@shopify/draggable";
import {Component, Prop, Ref, Vue, Watch} from "vue-facing-decorator";
import {randomUUID} from "@groupk/horizon2-front";

@Component({
	emits: ['sorted']
})
export default class SortableArray extends Vue {
	@Prop() elements!: unknown[];
	@Prop({default: null}) containerClass!: string | null;
	@Prop({default: null}) handleClass!: string | null;
	@Prop() draggableClass!: string;
	@Prop({default: false}) deleteWhenDragOutside!: boolean;

	orderBeforeSort: unknown[] = [];

	sortable: Sortable | undefined = undefined;
	refreshKey: string = randomUUID();

	@Ref() container!: HTMLDivElement;

	@Watch('elements')
	elementsWatched() {
		this.initSortable();
	}

	mounted() {
		this.initSortable();
	}

	unmounted() {
		this.sortable?.destroy()
	}

	initSortable() {
		if (this.sortable) this.sortable.destroy();
		this.sortable = new Sortable(this.container, {
			draggable: '.' + this.draggableClass,
			handle: (this.handleClass ? '.' + this.handleClass : undefined) as any,
			sortAnimation: {
				duration: 200,
				easingFunction: 'ease-in-out',
			},
			plugins: [Plugins.SortAnimation]
		});

		let deleting: boolean = false;
		if (this.deleteWhenDragOutside) {
			this.sortable.on('drag:over:container', (e) => {
				deleting = false
				e.sensorEvent.container.classList.remove('deleting')
			});

			this.sortable.on('drag:out:container', (e) => {
				deleting = true;
				e.sensorEvent.container.classList.add('deleting');
			});
		}

		this.sortable.on('sortable:start', (e: SortableStartEvent) => {
			this.orderBeforeSort = [...this.elements];
			document.documentElement.style.setProperty('--sortable-array-mirror-width', e.dragEvent.source.offsetWidth + "px");
		});

		this.sortable.on('sortable:stop', (e: SortableStopEvent) => {
			this.sortable?.destroy();
			this.sortable = undefined;

			if (deleting) {
				// Dropzone is delete-zone
				this.elements.splice(e.oldIndex, 1);
			} else if (e.oldIndex !== e.newIndex) {
				const element = this.elements.splice(e.oldIndex, 1)[0];
				if (!element) return;

				const injectTo = e.newIndex >= this.elements.length ? this.elements.length - 1 : e.newIndex;
				if (e.newIndex >= this.elements.length)
					this.elements.push(element);
				else
					this.elements.splice(injectTo, 0, element);
			}

			this.$emit('sorted', {
				newOrder: this.elements,
				oldOrder: this.orderBeforeSort
			});

			deleting = false;
			this.refreshKey = randomUUID();
			this.$nextTick(() => {
				if(this.container !== null) {
					this.initSortable();
				}
			});
		});
	}
}