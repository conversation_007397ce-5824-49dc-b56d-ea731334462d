<script lang="ts" src="./ReservitIntegrationComponent.ts"/>

<style lang="sass" scoped>
@import './ReservitIntegrationComponent.scss'
</style>

<template>
    <div class="reservit-integration-component">
        <form-modal-or-drawer
            :state="opened"
            title="Configurer l'intégration Reservit"
            subtitle="-"
            @close="close()"
        >
            <template v-slot:content>
                <div class="input-group">
                    <label> Environnement </label>

                    <div class="button-group">
                        <div :class="{active: reservitApp.platform === AppReservitPlatform.DEMO}" @click="reservitApp.platform = AppReservitPlatform.DEMO">
                            Démo
                        </div>
                        <div :class="{active: reservitApp.platform === AppReservitPlatform.PROD}" @click="reservitApp.platform = AppReservitPlatform.PROD">
                            Production
                        </div>
                    </div>
                </div>

                <div class="input-group">
                    <label> Token </label>
                    <input v-model="reservitApp.token" />
                </div>
            </template>
            <template v-slot:buttons>
                <button class="white button" @click="close()"> Fermer </button>
                <button class="button" @click="save()"> Valider </button>
            </template>
        </form-modal-or-drawer>
    </div>
</template>