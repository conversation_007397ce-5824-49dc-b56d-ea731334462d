<script lang="ts" src="./categories.ts">
</script>

<style scoped lang="sass">
@use './categories.scss' as *
</style>

<template>
    <div id="categories-page" class="page">
        <forbidden-message v-if="forbidden"></forbidden-message>
        <layout v-else :drawerOpened="selectedCategory" :display-on-mid-screen="true">
            <template v-slot:content>
                <content-header
                    :parameters="headerParameters"
                    @search="categorySearch = $event"
                ></content-header>

                <div class="page-content">

                    <div class="loading-container" v-if="loading">
                        <div class="loader"></div>
                    </div>

                    <template v-else v-for="topLevelCategory of getTopLevelCategories()">
                        <span class="category-group"> {{ topLevelCategory.name }} </span>
                        <div class="categories">
<!--                            <div v-if="getChildCategories(topLevelCategory.uid).length === 0">-->
<!--                                Aucune catégorie dans {{ topLevelCategory.name }}-->
<!--                            </div>-->
                            <div class="category drag-container" v-for="category in getChildCategories(topLevelCategory.uid)" :key="category.uid+refreshKey" :data-uid="category.uid">
                                <div class="category-header">
                                    <span class="title">{{ category.name }}</span>

                                    <div class="category-buttons" v-if="canWrite">
                                        <div class="edit-button" @click="showImportProductsModal = category">
                                            <i class="fa-regular fa-arrow-up-to-line"></i>
                                        </div>
                                        <div class="edit-button" @click="showCreationModal = true; editingCategory = category;">
                                            <i class="fa-regular fa-pen-line"></i>
                                        </div>
                                    </div>
                                </div>

                                <div class="products swappable" :data-uid="category.uid">
                                    <div class="empty-category" v-if="getCategoryProducts(category).length === 0">
                                        Aucun produit dans la catégorie
                                    </div>

                                    <div class="product copy" v-for="product in getCategoryProducts(category)" :data-id="product.id">
                                        <span class="name" style="display: flex; align-items: center; gap: 8px;">
                                            <img alt="icon" v-if="product.img" :src="product.img" />
                                           <span>{{ product.name }}  {{ $filters.Money(product.prices[0]) }}</span>
                                        </span>
                                        <i class="fa-regular fa-grip-dots-vertical"></i>
                                    </div>
                                </div>

                                <button class="mobile-only black button" @click="selectedCategory = category">
                                    <i class="fa-regular fa-cog"></i>
                                    Gérer des produits
                                </button>

                                <div class="delete-zone swappable" :data-group="'a'+category.uid">
                                    Glisser ici pour supprimer
                                </div>
                            </div>

                            <div class="new-category" @click="defaultParent = topLevelCategory.uid; showCreationModal = true;">
                                <i class="fa-regular fa-circle-plus"></i>
                                <span> Créer une catégorie dans "{{ topLevelCategory.name }}" </span>
                            </div>
                        </div>
                    </template>
                </div>
            </template>

            <template v-slot:right>
                <div class="right-view">
                    <template v-if="!selectedCategory">
                        <div class="header">
                            <div class="left">
                                <h2> Produits </h2>
                                <span> Glissez-déposez les produits dans les catégories de votre choix afin de les organiser </span>
                            </div>
                        </div>

                        <div class="search">
                            <input v-model="productSearch" type="text" placeholder="Rechercher un produit..." />
                        </div>

                        <div class="products drag-container">
                            <div class="product og" v-for="product in getFilteredProducts()" :data-id="product.id">
                                <div class="top">
                                    <img v-if="product.img" :src="product.img" />
                                    {{ product.name }}
                                </div>
                                <div class="bottom">
                                    {{ $filters.Money(product.prices[0]) }}
                                </div>
                            </div>
                        </div>
                    </template>
                   <template v-else>

                       <div class="close" @click="selectedCategory = null">
                           <i class="fa-regular fa-xmark"></i>
                           <span> Fermer </span>
                       </div>

                       <div class="header">
                           <div class="left">
                               <h2> {{ selectedCategory.name }} </h2>
                               <span> Sélectionnez les produits que vous souhaitez voir apparaitre dans cette catégorie </span>
                           </div>
                       </div>

                       <div class="search">
                           <input v-model="productSearch" type="text" placeholder="Rechercher un produit..." />
                       </div>

                       <div class="products">
                           <div
                               class="product og"
                               :class="{selected: selectedCategory.productIds.includes(product.id)}"
                               v-for="product in getFilteredProducts()" :data-id="product.id"
                               @click="toggleProductInCategory(selectedCategory, product)"
                           >
                               <div class="top">
                                   <img v-if="product.img" :src="product.img" />

                                   {{ product.name }}
                               </div>
                               <div class="bottom">
                                   {{ $filters.Money(product.prices[0]) }}
                               </div>
                           </div>
                       </div>
                   </template>
                </div>
            </template>
        </layout>

        <category-form
            v-if="showCreationModal"
            :state="showCreationModal"
            :parent-categories="getTopLevelCategories()"
            :editing-category="editingCategory"
            :parent-category="defaultParent"
            :establishment-uid="establishmentUid"
            @created="createdCategory($event)"
            @updated="updatedCategory($event)"
            @deleted="deletedCategory($event)"
            @close="showCreationModal = false; editingCategory = null; defaultParent = null;"
        ></category-form>

        <import-product-in-category
            ref="importProduct"
            v-if="showImportProductsModal"
            :products="products"
            :category="showImportProductsModal"
            @close="showImportProductsModal = null"
        ></import-product-in-category>
    </div>
</template>