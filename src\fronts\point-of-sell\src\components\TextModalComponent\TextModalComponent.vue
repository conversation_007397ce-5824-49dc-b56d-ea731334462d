<script lang="ts" src="./TextModalComponent.ts" />

<style lang="sass" scoped>
@import './TextModalComponent.scss'
</style>

<template>
    <div class="text-modal-component">
        <div class="modal">
            <div class="head">
                <span class="title"> {{ title }} </span>

                <div class="close" @click="close()">
                    <i class="fa-regular fa-xmark"></i>
                </div>
            </div>
            <textarea placeholder="Écrivez..."></textarea>

            <button class="primary button" @click="close()"> Valider </button>
        </div>
    </div>
</template>