<script lang="ts" src="./StripeAppSelectorComponent.ts" />

<style lang="sass" scoped>
@import './StripeAppSelectorComponent.scss'
</style>

<template>
    <div class="stripe-app-configuration-form-component">
        <form-modal-or-drawer
            :state="opened"
            :title="'Compte Stripe'"
            :subtitle="'Reliez votre compte stripe à Billetterie pour commencer à accepter des paiements'"
            @close="close()"
        >
            <template v-slot:content>
                <div class="available-apps">
                    <div
                        class="app"
                        :class="{selected: selectedApp && selectedApp.uid === app.uid}"
                        v-for="app of apps"
                        @click="selectedApp = app"
                    >

                        <div class="logo" :style="`background-image: url(${$assets.stripe});`"></div>

                        <div class="data">
                            <span class="name"> {{ app.name }} </span>
                            <span class="subtitle"> Choisir ce compte </span>
                        </div>

                        <i class="fa-solid fa-circle-dot" v-if="selectedApp && selectedApp.uid === app.uid"></i>
                        <i class="fa-regular fa-circle" v-else></i>
                    </div>

                    <div class="app" @click="connectWithStripe()">
                        <i class="fa-regular fa-plus"></i>

                        <div class="data">
                            <span class="name"> Nouveau </span>
                            <span class="subtitle"> Connecter un compte Stripe </span>
                        </div>
                    </div>
                </div>
            </template>

            <template v-slot:buttons>
                <button type="button" class="white button" @click="close()"> Fermer </button>
                <button type="button" class="button" :class="{loading: loading, disabled: loading || selectedApp === null}" @click="validate()">
                    Choisir le compte {{ selectedApp ? `"${selectedApp.name}"` : '' }}
                </button>
            </template>
        </form-modal-or-drawer>

        <div class="connecting-dimmer" v-if="connectingAccount">
            <div class="loading-container">
                <div class="loader"></div>
            </div>
        </div>
    </div>
</template>