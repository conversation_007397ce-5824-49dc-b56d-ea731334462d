.order-payments-quick-edit-component {
    display: flex;
    flex-direction: column;
    gap: 20px;

    .header {
        display: flex;
        flex-direction: column;
        gap: 4px;

        .title {
            font-size: 20px;
            font-weight: 700;
            margin: 0;
        }

        .subtitle {
            font-size: 15px;
        }
    }

    .text-separator {
        font-size: 15px;
        font-weight: 600;
    }

    .current-payments {
        display: flex;
        align-items: center;
        gap: 10px;
        flex-wrap: wrap;

        .current {
            display: flex;
            align-items: center;
            gap: 10px;
            border-radius: 8px;
            border: 1px solid #000;
            padding: 10px;

            .method {
                font-size: 15px;
                font-weight: 700;
            }

            .amount {
                color: #3D3D3D;
                font-size: 15px;
                font-weight: 600;
            }
        }
    }

    .payment-methods {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        grid-gap: 10px;

        .payment-method {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 80px;
            border-radius: 8px;
            border: 1px solid #E8E8E8;
        }
    }

    .buttons {
        display: flex;
        justify-content: flex-end;
    }
}