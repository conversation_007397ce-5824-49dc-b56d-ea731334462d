// import CanvasRendererSDK from "../CanvasRendererSDK";
// import {PrinterLanguage} from "../PrinterHelper";
//
// let canvasRendererSdk: null | CanvasRendererSDK = null;
// // TODO register font
//
// let height = 368;
// let width = 400;
// let printRenderSDK = new CanvasRendererSDK({
// 	// targetSize:{width:648, height:1016},
// 	targetSize: {width: width, height: height},
// 	// targetSize:{width:648, height:2496},
// 	formsMetricSystem: "percent",
// 	clearColor: "white",
// });
//
// console.log({width: width, height: height});
//
// let canvas = printRenderSDK.getCanvasElement();
// let container = document.getElementById("container");
// if (container) container.appendChild(canvas);
//
// // printRenderSDK.renderRect({x: 0, y: 30}, {width: 100, height: 40}, {fillColor:'red'});
// // printRenderSDK.renderText('🍔agf', {x: 0, y: 30}, {width: 100, height: 40}, {direction: 0, verticalAlign: 'middle', horizontalAlign: 'left'});
// printRenderSDK.renderText(
// 	"🍔Hga",
// 	{x: 0, y: 30},
// 	{width: 100, height: 40},
// 	{
// 		// direction: 90,
// 		// fontSize:50,
// 		// verticalAlign: 'top',
// 		// verticalAlign: 'middle',
// 		verticalAlign: "bottom",
//
// 		horizontalAlign: "left",
// 		// horizontalAlign: 'center',
// 		// horizontalAlign: 'right',
// 	}
// );
//
// // printRenderSDK.renderText('Hello world', 'Arial', -1, 'white' , {x:0, y:50},{width:100, height:100});
// printRenderSDK.renderText("Hello world", {x: 0, y: 0}, {width: 20, height: 100}, {direction: 270, verticalAlign: "middle", horizontalAlign: "left"});
// // printRenderSDK.renderText('Hello world', {x:0, y:0},{width:20, height:100}, {direction:0});
//
// let imgTest: HTMLImageElement | null = <HTMLImageElement | null>document.getElementById("imgTest");
//
// if (imgTest)
// 	printRenderSDK.rotateImage(imgTest, 270).then((rotatedImage) => {
// 		console.log("converted");
// 		if (container) container.appendChild(rotatedImage);
// 	});
//
// /*
// let repetitions = height/100 < 15 ? 10 : 100;
// for(let i = 0; i < repetitions;++i){
// 	printRenderSDK.renderText('P:'+(i+1), {x:0, y:i*(100/repetitions)},{width:80, height:100/repetitions}, {horizontalAlign:'left'});
// }
// */
//
// // printRenderSDK.renderRect( {x:0, y:0},{width:100, height:5}, {fillColor:'black'});
// // printRenderSDK.renderRect( {x:0, y:5},{width:95, height:5}, {fillColor:'black'});
// // printRenderSDK.renderRect( {x:0, y:10},{width:90, height:5}, {fillColor:'black'});
//
// // if(imgTest){
// // 	printRenderSDK.renderImage(imgTest, {x:40, y:10},{width:60, height:60}, {direction:90, upscaleMethod:'contain',imageSmoothingQuality:'pixelated'});
// // }
//
// (document.getElementById("toImageCanvas") as HTMLButtonElement).addEventListener("click", function () {
// 	let renderTarget = document.getElementById("renderTarget");
// 	if (renderTarget) {
// 		let renderImg = new Image();
// 		if (canvasRendererSdk) renderImg.src = canvasRendererSdk.exportToImage();
// 		else renderImg.src = printRenderSDK.exportToImage();
//
// 		renderTarget.appendChild(renderImg);
// 	}
// });
//
// (document.getElementById("toEvolis") as HTMLButtonElement).addEventListener("click", function () {
// 	let evolisExporter = PrinterLanguageConverterFactory.getForPrinterLanguage(PrinterLanguage.EVOLIS);
// 	evolisExporter.convertFromBase64(canvasRendererSdk ? canvasRendererSdk.exportToImage() : printRenderSDK.exportToImage()).then((outputBytes: string) => {
// 		console.log("output data:" + outputBytes.length);
// 		let base64 = btoa(outputBytes);
// 		console.log("output b64:" + base64.length);
// 		let decodedBase64 = atob(base64);
// 		console.log("output b64 decoded:" + decodedBase64.length);
// 	});
// });
//
// declare var $: any;
//
// function sendToServer(outputBytes: string) {
// 	console.log(btoa(outputBytes));
//
// 	$.ajax({
// 		url: "http://192.168.1.39:31300/api/v1/print",
// 		method: "POST",
// 		dataType: "json",
// 		contentType: "application/json; charset=utf-8",
// 		data: JSON.stringify([
// 			{
// 				// targetPrinter: 'CITIZEN_GAUCHE',
// 				targetPrinter: "CITIZEN_DROITE_2",
// 				// targetPrinter: 'CITIZEN_CL-S400DTZ',
// 				// rawContent:'test',
// 				rawContent: btoa(outputBytes),
// 			},
// 		]),
// 	}).done(console.log);
// }
//
// function renderFinalDataToPrinter(targetLanguage: PrinterLanguage) {
// 	let zpl2Exporter = PrinterLanguageConverterFactory.getForPrinterLanguage(targetLanguage);
// 	zpl2Exporter
// 		.convertFromBase64(
// 			canvasRendererSdk ? canvasRendererSdk.exportToImage() : printRenderSDK.exportToImage(),
// 			canvasRendererSdk ? canvasRendererSdk.getRenderedAreas() : printRenderSDK.getRenderedAreas(),
// 			{
// 				loadedMediaType: "fanfold",
// 			}
// 		)
// 		.then((outputBytes: string) => {
// 			// zpl2Exporter.convertFromBase64(printRenderSDK.exportToImage()).then((outputBytes : string)=>{
// 			console.log("output data:" + outputBytes.length);
// 			console.log(printRenderSDK.getRenderedAreas());
// 			// console.log(outputBytes);
// 			sendToServer(outputBytes);
// 		});
// }
//
// (document.getElementById("toZpl2") as HTMLButtonElement).addEventListener("click", function () {
// 	renderFinalDataToPrinter(PrinterLanguage.ZPL2);
// });
// (document.getElementById("toEscPos") as HTMLButtonElement).addEventListener("click", function () {
// 	renderFinalDataToPrinter(PrinterLanguage.ESCPOS);
// });
//
// console.log(PrinterHinter.getBestDescriptorWithStr("TBD-NT210_U_1", {loadedMediaWidth: 79}));
//
// /*
// type LayerText = any;
// type LayerImage = any;
// type Layer = {elements:any[]};
//
// let layer = {elements:[]};
//
// let text : any = {};
// text.type = 'text';
// text.data = 'Journée n°1';
// text.size = {w: 20, h: 0};
// text.font = 1;
// text.direction = 90;
// text.position = {x: 65, y: 40};
// layer.elements.push(text);
//
// let text2 : any = {};
// text2.type = 'text';
// text2.data = 'Tours - Nogent le Rotrou';
// text2.size = {w: 40, h: 0};
// text2.font = 1;
// text2.direction = 90;
// text2.position = {x: 57, y: 26};
// layer.elements.push(text2);
//
// let text3 : any = {};
// text3.type = 'text';
// text3.data = 'SAMEDI 22 SEPTEMBRE 2018 - 20H';
// text3.size = {w: 35, h: 0};
// text3.font = 1;
// text3.direction = 90;
// text3.position = {x: 50, y: 35};
// layer.elements.push(text3);
//
// let text4 : any = {};
// text4.type = 'text';
// text4.data = 'HALLE MONCONSEIL - TOURS NORD';
// text4.size = {w: 40, h: 0};
// text4.font = 1;
// text4.direction = 90;
// text4.position = {x: 40, y: 30};
// layer.elements.push(text4);
//
// let text5 : any = {};
// text5.type = 'text';
// text5.data = 'TARIF : 8e';
// text5.size = {w: 15, h: 0};
// text5.font = 1;
// text5.direction = 90;
// text5.position = {x: 30, y: 26};
// layer.elements.push(text5);
//
//
// let text6 : any = {};
// text6.type = 'text';
// text6.data = 'RANG N - PLACE 74';
// text6.size = {w: 20, h: 0};
// text6.font = 1;
// text6.direction = 90;
// text6.position = {x: 30, y: 50};
// layer.elements.push(text6);
//
//
// let text7 : any = {};
// text7.type = 'text';
// text7.data = 'WWW.TOURSBASKETMETROPOLE.COM';
// text7.size = {w: 33, h: 0};
// text7.font = 1;
// text7.direction = 90;
// text7.position = {x: 23, y: 35};
// layer.elements.push(text7);
//
// let img : any = {};
// img.type = 'image';
// img.data = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAASwAAAEsCAYAAAB5fY51AAARj0lEQVR4Xu3dsVIjSRLG8ebiHgCzTSK0xpj4yJAsMHkDNAEPsB54E3jDO0CM9AjjMZbG0PjgrYGD16beQGdt7F1MZ9bqI6mrnP3/IspRq0vVXa0MmuxKHex2u10HAAn8q/QGAGgFAQtAGgQsAGkQsACkQcACkAYBC0AaBCwAaRCwAKRBwAKQBgELQBoELABpELAApEHAApAGAQtAGgQsAGkQsACkQcACkAYBC0AaBCwAaRCwAKRBwAKQBgELQBoELABpELAApEHAApDGv0tviDIMQ/fHH3+U3tak4+Pj7vDwsPS2/6Ee72w2K73lJ09PT912ux3dFt2fpe/77sOHD6W3NSn6eJX5UK+XFijfD9mukuVyueu6LmVbr9elw/uJeryK2WxWrT+rLRaLUrfNij5eZT7U66WFpnw/VNwSAkiDgAUgDQIWgDQIWADSIGABSKPaYw2em5ub/3tKfLPZdPf396W3/eTjx4+jr/d93y2Xy9FtHqu/6XTaXV5ejm67ubnpFovF6LZo1jENw2COvQXq+VPOa/R8ZP5+hCulEaN4aduaaVGLOj5rHzXNH92f4ldMy6vnL7o/i3r91dLK+LglBJAGAQtAGgQsAGkQsACkQcACkEbzAevg4CC0RdvtdqNtNpuZY/j+/Xup273M5/NqxxvNOn/eIyHr9drcr9Z8LJfL0P5Upet939a65gMWAPyJgAUgDQIWgDQIWADSIGABSKOJxc+ZWRmh7XZr1u9W6l8Pw+B+lkXJWB0dHZljV45X0fd96PlT+1OOSRkf/h4C1hvN5/PR1xeLRbder0e3KR4fH7vHx8fS235ijc+zXq/NL6qV+o4+3tPT0+709LT0tr9N7S/ymPB23BICSIOABSANAhaANAhYANIgYAFIgyzhG1mLdCeTyejrJVZ/zdTUDqbUsL+7uzN/1v3Lly+jr3u8/iw1x4e/ELDe6OLiovSWvXj9/YoBy6vKYAWEx8dH83kwJSB4/XlqjQ9/4ZYQQBoELABpELAApEHAApAGAQtAGs0HrFKt7n1btFKN7LHmZaSsfbyfPvdqnFsWi4W5z+3tbWjNb6/mvDUGrwb7p0+fzP2sfazHJzrx/LXCGrfaWtd8wAKAPxGwAKRBwAKQBgELQBoELABpNLGW8Pn5ufSWd7fv4teSvu+7Dx8+jG57fX01M4VWaeJhGMLHqPDqolvHFF0jXjl/3viOj4+brsP+K34/VE0ErN9//730lnTOzs7Mha7z+dz88lip5dVq5T7aUItV43y1Wpn146NrxKvnTxlfC37F74eKW0IAaRCwAKRBwAKQBgELQBoELABpVMsSnpycuOVwW2Y9ntA5JX6HYTAX3J6fn5sZP2ufvu/Nz/LGZ9lsNqHje3l5GX29NuX8ff36tVutVqPbomv2W37V70e4Hd7FcrncdV032tbrtbmftc9isTD38Vj9eU0ZX3R/3vHOZrPiZ0b1p4juD3/hlhBAGgQsAGkQsACkQcACkAYBC0Aepf/KR/GyZjVbC+OLzppFU7JcrZ+/1vuLzmK2kqWOxl9YANIgYAFIg4AFIA0CFoA0CFgA0qi2+Lnve7MM7dPTU7fdbke31apx7o3P4/2Kcy3e+bN4NeePj49HX++c4/XmQqmZrs5v9PUS3Z/Cm49ahmFooyZ+KY1YQ3Ta1mvRSp831qLTytGLgT2lfvc9Xoualre00p+1jzoflujxeU05fypuCQGkQcACkAYBC0AaBCwAaRCwAKRxsLN+KjfYZrPpHh4eRrd5aVErleql5T3KYwjX19fmZ1m/XDydTrurq6vRbdGPcXjnz6rN/ttvv3XT6XR0W/Txnp6edn3fj27zarBbY1Ae43h5eek2m83otrOzM3N81q93v7y8dD9+/BjdpszvZDIx50Phjc+bj+j5DVdKI0aJTrOqaeBSirbG+KIf41DGV/N4PUp/ymMcalMo89sKa9zq/EbjlhBAGgQsAGkQsACkQcACkAYBC0Aa1QLWxcVFt9vtRtvt7W13cHAw2qx9ZrOZuY/36ILVn9eUKg7L5VIan2I+n+99/ryfRff6y2qxWEjza50Hr3369Mn8rNK+Uc16XKRLPr/VAhYAvBUBC0AaBCwAaRCwAKRBwAKQRrWa7h6lZrVXg92rLx2doYterGyNb7vdStnK6P4s3kJ0ZbFyTcr153l9fTXPe+Q59xweHppjODo6MscR/f0IV1ps+KspLXbdt1miFyuri0+j+1Moi5WjFz+3cry1tHL9ReOWEEAaBCwAaRCwAKRBwAKQBgELQBrVarp77u7uzEcArJraKmuBp1ez+vPnz+b4rNMXXfPbe0zCq8G+Wq1GX1d/al2Zj/l8vne6fLFYmJ/17du3bhiG0W1WDXuvP+/6s0yn0+7y8nJ0mze+i4uL0de93zxQqDXxo6+/cKU0Yg0108BK2jZ6fEp/apra4vXnNUXNxxCU/mqOz6LOhzK+Fq4/FbeEANIgYAFIg4AFIA0CFoA0CFgA8ij9Vz6KmgVR+lMWd3otOgsSnZVSsj4epb9W5tfSSn+l87Fvs6jjax1/YQFIg4AFIA0CFoA0CFgA0iBgAUijmZru+9Y433ex6p+UmtpejW6Lt/jUqyFufc4wDG59dsu+4+7Emt/RNeLVmv0teH5+Lr3lJ971gv9SSiNGUdOspbTuvv0poh9D8JT6rdGU+VCPtxb1+rOoj3HUOn/Rx9sKbgkBpEHAApAGAQtAGgQsAGkQsACkUa2mu1fj/PT0tOv7fnSbVZPcE/3T6I+Pj2aN7uVyOfr6ZDLpptPp6DaPdbybzaa7v78f3XZzc7N3Stzr7+zszJwP61ED73ija/YrNdjVGucW9TGE6Jrz1j7qbwpYvBr2VZXSiBkpjyGorZboNLWallco1R88yvyq1S6U/jxKf62fv5q4JQSQBgELQBoELABpELAApEHAApBH6b/yUWpmpTzRWRClvxayPq236POnXn/RTdHC/Krfj2j8hQUgDQIWgDQIWADSIGABSIOABSCNJmq6e6wa4uriU4tXM13h9afUTPd4/Sm8xbHK5yiLbaNr2Hs155XxRS+mjqZ+P5TfFPB+kyFcKY0YJTqtrKZZS/3WaErNdLU/hfLYgKf1tLwyPrU/RfT4PKV+x1r09efhlhBAGgQsAGkQsACkQcACkAYBC0Aa1R5rODk5MeufK4Zh6D5+/Fh620+UMXz+/HnvGuLT6bS7uroa3ealm5XxKenraJvNpnt4eBjdtu+5ew+bzca8XrzxWfPhXX/n5+dm7XZL9PnzjtdjHa/3GwBVldKIrVIfk1DUTCu3QEnLq/PRelOOV0nzt3L+lPEpx6vilhBAGgQsAGkQsACkQcACkAYBC0Aa1QLWarXqDg4O9m6K9Xrd7Xa70Vb6vLGmVFBQWWPwUtTz+bx4DPv0p5w/L43v9Wc17/GOmv0plPn4/v178Rj2adHHe3FxYe6jVPBQVQtYAPBWBCwAaRCwAKRBwAKQBgELQBrVFj9H6/vezE68vr6amT0lo6HU6FZrYFvjOzw8lGqcW9TxWbwa4vv21b3D/Eb31wqlhr03H0pGXLleZKXFhlHUxZ0KZfGuR1n87DVlsah6/pSm1Jyvudi75vxaoudDPX+1+vOacj2ruCUEkAYBC0AaBCwAaRCwAKRBwAKQRtrHGrwa2NE1tZX+PEoNdq8mfgs159Ua+wrvWJUxKP31fR86HzXd3d2Z42v+NwVKacQoahpY6U9Js0b3V5Py2IWa9rao85u1qT9Vr/TnUfpTHuNoBbeEANIgYAFIg4AFIA0CFoA0CFgA0mgiYCk1pj3RNbVvb2+L+0e1aNYxffnyxdwn+vy13lqoyLBcLovnuMb1Yn2O8rjIe2giYAHA30HAApAGAQtAGgQsAGkQsACk0fziZ6VmdXQNdq8vawzDMJgLTL0a2NYYvJrpx8fHo6+/B6XmfAvU86cckzIf3vWiiL6evfn9x9V0j64hriw+VZul5vFGUxbHtr74WT1/0f1ZWjl/yvhqFgfglhBAGgQsAGkQsACkQcACkAYBC0AaTTzWcHd3161Wq9FtVo3pyWQy+nrXdd3NzU1oDXavRre1KPTl5WX09ffg1ej2Fjm3wJrfzWbT3d/fj267ubnZu464V3P++vp67/42m43UX/T1opy/1EppxChq2rYF0Y9JRD/WoDyG4FH6i57f6DS62l9p/DX685ql9flQcUsIIA0CFoA0CFgA0iBgAUiDgAUgj9J/5aPUzDK0kOXyRGcdo5uS5YpeDPyrqnX+1O9brfGp+AsLQBoELABpELAApEHAApAGAQtAGk0sfvYoNbWPjo72/jXfvu9Da8R7Wqj53Qqlhr03H/vOeyfOb/T41Jrpym8evL6+mvtFjy9cKY0YJTrN6rUWHpOIpj52oTRlPtS0t9Jf9Hwoj5lEjy/6+9HK+KJxSwggDQIWgDQIWADSIGABSIOABSCN5h9rUGpWezXirRrnm82me3h4GN12fn4eWiPeo9T89mqcW+OeTqfd1dXV6LavX7+a50/h1Zy35terwe7Nh7XPdDrtLi8vR7d5vwEQPe/K/HrXczRrfH3fm3O1bz38NymlEaNEp0XVNL/SnzI+VWn8+47P2kdNe0f3Z1HnQxmfR+lPOX/RLXp86vmLxi0hgDQIWADSIGABSIOABSANAhaAPEr/lYdPyarUzNKU+h1rShYuur+a50/JYnqi+/NEH6/SambR+QsLQBoELABpELAApEHAApAGAQtAGtUWP2euSV61ZnUwpca5UvPbm9/n5+fR1z3DMJhj8Gr2W/tE9+fVdFd45y/6+kv9mwKlNGIUdbFyC631tHw0JS1fc36V+YjuT51fSyuLvS3q+KJxSwggDQIWgDQIWADSIGABSIOABSCNao81eLya5LV4NeI9Vp3ryWQy+vp78GqmW9Qa50pNcmV+o+cjur/o+T05OQmtmb7ZbMy5ur6+NvtU5reqUhoxSitpUUvN8UU/1hDdn6fU71hTzl/0fET351Eea1CVzv2+x1vad9/+onFLCCANAhaANAhYANIgYAFIg4AFII/Sf+WjqFmaUoZi32apOT4lq9LK+Kx9orOO0VlRtVlqLvZWWs35qIm/sACkQcACkAYBC0AaBCwAaRCwAKTRxOJnvI1XM71WDfbWeTXYn56euu12O7rNqunuLTb3arArNeK98Vnz6y2YVvo7PDw0xx5dc95VSiNGqZmW95ql5viiH2uIPl6lqWlvpT/lsYbo/rymXC/q+BRKf+r3Ixq3hADSIGABSIOABSANAhaANAhYANLgsYZ3Mp1Ou6urq9FtXsrZqqnd971Z89sT3Z/Fq3Gu1Jyvyathb/FqxN/d3XWr1Wp0m3XOh2Ew5+r8/HzvGvsepT9quv/NtKi1j9os0eOrmeb3RPenUB4bUNP8Sn8K9bEQpT/l+vNazf6icUsIIA0CFoA0CFgA0iBgAUiDgAUgjeYD1m63C221LJfL7uDgYO9mjXs2m5n7WKvoO+f8ef15rQXr9Tp0fufzefG4x8556VqrMT7LYrEwx3B7e2v2Vxr/WLMqPLyH5gMWAPyJgAUgDQIWgDQIWADSIGABSKOJxc8t1BCPXpyr1ui2Mn7b7dbMxry+vpr7Wfv0fW9u88Zn8WrEe31F1xBX+js6Oto70+X1p/DmVxF9vN78/uNqurfelMWiNRfvKjW6PUp/6vwq/SmLbWv2p7Sai7Ojj1fpT8UtIYA0CFgA0iBgAUiDgAUgDQIWgDQOduqKzD29vLx0P378KL2tSaenp13f96PbrNrdk8mkm06no9u+ffvWDcMwus2i9ndxcTH6ukfpT51fpT9vPiw1+1N486uoebxKf6pqAQsA3opbQgBpELAApEHAApAGAQtAGgQsAGkQsACkQcACkAYBC0AaBCwAaRCwAKRBwAKQBgELQBoELABpELAApEHAApAGAQtAGgQsAGkQsACkQcACkAYBC0AaBCwAaRCwAKRBwAKQBgELQBoELABp/AdpVDbACqAuPQAAAABJRU5ErkJggg==';
// img.size = {w: 36, h: 19};
// img.position = {x: 19, y: 74};
// layer.elements.push(img);
//
// let layers = [layer];
// */
//
// let data = {
// 	size: {h: 60, w: 100},
// 	layers: [
// 		{
// 			elements: [
// 				{
// 					type: "text",
// 					direction: 0,
// 					size: {h: 8, w: 65},
// 					position: {x: 5, y: 5},
// 					data: "{{platformName}}",
// 					fontFamily: "Arial",
// 					fillColor: "black",
// 					fontWeight: "normal",
// 					horizontalAlign: "left",
// 					verticalAlign: "middle",
// 					fontSize: -1,
// 				},
// 				{
// 					type: "text",
// 					direction: 0,
// 					size: {h: 5, w: 20},
// 					position: {x: 75, y: 5},
// 					data: "Cadre réservé",
// 					fontFamily: "Arial",
// 					fillColor: "black",
// 					fontWeight: "normal",
// 					horizontalAlign: "left",
// 					verticalAlign: "middle",
// 					fontSize: -1,
// 					bold: true,
// 					color: "black",
// 					center: true,
// 				},
// 				{type: "image", direction: 0, size: {w: 20, h: 33.333333333333336}, position: {x: 75, y: 10}, data: "{{qrCode}}"},
// 				{
// 					type: "text",
// 					direction: 0,
// 					size: {h: 8, w: 90},
// 					position: {x: 5, y: 30},
// 					data: "{{establishmentName}}",
// 					fontFamily: "Arial",
// 					fillColor: "black",
// 					fontWeight: "normal",
// 					horizontalAlign: "left",
// 					verticalAlign: "middle",
// 					fontSize: -1,
// 					bold: true,
// 					color: "#47709a",
// 				},
// 				{
// 					type: "text",
// 					direction: 0,
// 					size: {h: 5, w: 90},
// 					position: {x: 5, y: 40},
// 					data: "{{ownerName}}",
// 					fontFamily: "Arial",
// 					fillColor: "black",
// 					fontWeight: "normal",
// 					horizontalAlign: "left",
// 					verticalAlign: "middle",
// 					fontSize: -1,
// 					bold: true,
// 					color: "black",
// 				},
// 				{
// 					type: "text",
// 					direction: 0,
// 					size: {h: 5, w: 90},
// 					position: {x: 5, y: 48},
// 					data: "{{reference}}",
// 					fontFamily: "Arial",
// 					fillColor: "black",
// 					fontWeight: "normal",
// 					horizontalAlign: "left",
// 					verticalAlign: "middle",
// 					fontSize: -1,
// 					bold: true,
// 					color: "black",
// 				},
// 			],
// 		},
// 		{
// 			elements: [
// 				{type: "box", direction: 0, size: {h: 21, w: 100}, position: {x: 0, y: 69}, fill: true, fillColor: "#47709a"},
// 				{
// 					type: "text",
// 					direction: 0,
// 					size: {h: 12, w: 30},
// 					position: {x: 20, y: 74},
// 					data: "Code de retrait : ",
// 					fontFamily: "Arial",
// 					fillColor: "black",
// 					fontWeight: "normal",
// 					horizontalAlign: "left",
// 					verticalAlign: "middle",
// 					fontSize: -1,
// 					bold: true,
// 					color: "white",
// 				},
// 				{type: "box", direction: 0, size: {h: 12, w: 10}, position: {x: 50, y: 74}, fill: true, fillColor: "black"},
// 				{
// 					type: "text",
// 					direction: 0,
// 					size: {h: 12, w: 10},
// 					position: {x: 50, y: 74},
// 					data: "{{shortCode}}",
// 					fontFamily: "Arial",
// 					fillColor: "white",
// 					fontWeight: "800",
// 					horizontalAlign: "left",
// 					verticalAlign: "middle",
// 					fontSize: -1,
// 					bold: true,
// 					color: "white",
// 					center: true,
// 				},
// 				{
// 					type: "text",
// 					direction: 0,
// 					size: {h: 10, w: 100},
// 					position: {x: 0, y: 90},
// 					data: "Ce billet numérique comporte un code de retrait vous permettant de récupérer votre commande\nRemarque : Le QR Code est réservé à l'organisateur",
// 					fontFamily: "Arial",
// 					fillColor: "black",
// 					fontWeight: "800",
// 					horizontalAlign: "left",
// 					verticalAlign: "middle",
// 					fontSize: -1,
// 					color: "black",
// 				},
// 			],
// 		},
// 	],
// };
//
// /*
// for (let layer of data.layers) {
// 	for (let element of layer.elements) {
// 		if (element.type === 'text') {
// 			printRenderSDK.renderText(
// 				element.data,
// 				element.position, {
// 					// width: element.size.w, height: 10,
// 					width: element.size.w, height: element.size.h,
// 				},
// 				{
// 					fontFamily: element.fontFamily,
// 					fontSize: element.fontSize,
// 					fontWeight: element.fontWeight,
// 					fillColor: element.fillColor,
// 					verticalAlign: element.verticalAlign,
// 					horizontalAlign: element.horizontalAlign,
// 				}
// 			);
// 		}
//
// 		if (element.type === 'image') {
// 			console.log('RENDER IMAGE');
// 			printRenderSDK.renderImageFromBase64(
// 				"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAMCAYAAABWdVznAAAAGUlEQVQoU2P8////fwYSAOOoBiJCa0SGEgAZby/d6279iAAAAABJRU5ErkJggg==",
// 				// element.data,
// 				element.position, {
// 					width: element.size.w, height: element.size.h
// 				}
// 			);
// 		}
//
// 		if (element.type === 'box') {
// 			console.log('RENDER IMAGE');
// 			printRenderSDK.renderRect(
// 				// element.data,
// 				element.position, {
// 					width: element.size.w, height: element.size.h
// 				},
// 				{
// 					fillColor: element.fillColor,
// 				}
// 			);
// 		}
// 	}
// }
// */
//
// // let textSize = getTextHeight('🍔Hg', 'Arial', '110px');
// // console.log('textSize', textSize);
//
// let receiptRenderer = new ReceiptRenderer({
// 	margins: {top: 40 / 18, right: 40 / 18, bottom: 40 / 18, left: 0},
// });
// receiptRenderer.setHeader({lines: ["Centre de Création Contemporaine Olivier", "Jardin François 1er", "37000 Tours", "France", "Tel: 02 47 66 50 00"]});
//
// receiptRenderer.setFooter({lines: ["https://cccod.fr", "Version 9.0.3", "", "MERCI DE VOTRE VISITE"]});
//
// receiptRenderer.setIdentification({lines: ["N° Ticket: 8de35f71-7eca-45ef-9511-41b0a574bc8c", new Date().toUTCString(), "Terminal: Caisse 2"]});
//
// for (let i = 0; i < 30; ++i)
// 	receiptRenderer.addBoughtProduct({
// 		quantity: i,
// 		name: "CCCOD LE PASS *2 DUO",
// 		unitPrice: 1000,
// 		unitPriceWithTax: 1200,
// 		tax: 2000,
// 	});
// receiptRenderer.addPayment({
// 	name: "Espèces",
// 	value: 360,
// 	hideValue: false,
// });
//
// receiptRenderer.getCanvasRenderer({targetSize: {height: -8, width: width}, clearColor: "white"}).then((canvasSdk: CanvasRendererSDK) => {
// 	canvasRendererSdk = canvasSdk;
// 	let canvas = canvasSdk.getCanvasElement();
// 	document.body.appendChild(canvas);
// });
