import {Component, Prop, Vue} from "vue-facing-decorator";
import {LocalOrder, LocalOrderTransfer} from "../../model/LocalOrder";
import {PosState} from "../../model/PosState";
import {CheckboxComponent, DropdownButtonAction, DropdownButtonComponent} from "@groupk/vue3-interface-sdk";
import {AutoWired, UuidUtils} from "@groupk/horizon2-core";
import {LocalOrderRepository} from "../../repositories/LocalOrderRepository";
import OrderPaymentsQuickEditComponent from "../OrderPaymentsQuickEditComponent/OrderPaymentsQuickEditComponent.vue";
import {OrderPaymentApiOut, OrderPaymentStatus, uuidScopeProductPurchaseStatus} from "@groupk/mastodon-core";
import {PrinterRepository} from "../../repositories/PrinterRepository";
import {AppBus} from "../../config/AppBus";
import {OrderRepository} from "../../../../../shared/repositories/OrderRepository";
import {LocalOrderTransferRepository} from "../../repositories/LocalOrderTransferRepository";
import OrderUtils from "../../class/OrderUtils";

@Component({
	components: {
		'dropdown-button': DropdownButtonComponent,
		'checkbox-component': CheckboxComponent,
		'order-payments-quick-edit': OrderPaymentsQuickEditComponent,
	},
	emits: ['unlock-transfer']
})
export default class OrderLineComponent extends Vue {
	@Prop({required: true}) posState!: PosState;
	@Prop({required: true}) localOrder!: LocalOrder;
	@Prop({required: true}) localOrderTransfer!: LocalOrderTransfer|null;

	showQuickEdit: boolean = false;
	showError: boolean = false;

	OrderUtils = OrderUtils;

	@AutoWired(LocalOrderRepository) accessor localOrderRepository!: LocalOrderRepository;
	@AutoWired(LocalOrderTransferRepository) accessor localOrderTransferRepository!: LocalOrderTransferRepository;
	@AutoWired(OrderRepository) accessor orderRepository!: OrderRepository;
	@AutoWired(PrinterRepository) accessor printerRepository!: PrinterRepository;
	@AutoWired(AppBus) accessor appBus!: AppBus;

	get getOrderTotals() {
		return this.posState.orderExecutorModel.getOrderTotals(this.localOrder.order);
	}

	dropdownClicked(action: DropdownButtonAction) {
		if(action.id === 'force-sync') this.forceSync();
		else if(action.id === 'quick') this.showQuickEdit = true;
		else if(action.id === 'receipt') OrderUtils.printReceipt(this.localOrder, 'DISPLAY');
		else if(action.id === 'send-receipt') OrderUtils.printReceipt(this.localOrder, 'SEND');
		else if(action.id === 'note') OrderUtils.printNote(this.localOrder);
		else if(action.id === 'customer-link') this.posState.linkCustomerToOrder = this.localOrder;
		else if(action.id === 'transfer-order') this.posState.showTransferOrderMenu = this.localOrder;
		else if(action.id === 'show-debug') this.posState.showDebugInformations = this.localOrder;
		else if(action.id === 'transfer-reservit') this.posState.showReservitTransferOrderMenu = this.localOrder;
		else if(UuidUtils.isVisual(action.id, uuidScopeProductPurchaseStatus)) OrderUtils.addPurchaseStatus(action.id, null);
	}

	async transfertDropdownClicked(action: DropdownButtonAction, localOrderTransfer: LocalOrderTransfer) {
		if(action.id === 'cancel') {
			if(!localOrderTransfer.transferUid) return;
			await this.orderRepository.callContract('cancelTransfer', {
				establishmentUid: this.posState.establishmentUid,
				transferUid: localOrderTransfer.transferUid
			}, undefined);

			localOrderTransfer.canceled = true;
			await this.localOrderTransferRepository.save(localOrderTransfer);
		} else {
			this.$emit('unlock-transfer', localOrderTransfer);
		}
	}

	forceSync(){
		this.localOrderRepository.saveAndResync(this.localOrder);
	}

	getOrderPaymentMethods() {
		return this.localOrder.order.payments
			.filter((payment) => !this.haveBeenRefund(payment) && payment.amount > 0)
			.map((payment) => payment.method)
			.filter((methodUid, index, array) => array.indexOf(methodUid) === index)
			.map((methodUid) => this.posState.getMethodWithUid(methodUid).name);
	}

	haveBeenRefund(payment: OrderPaymentApiOut) {
		let total = 0;
		for(let orderPayment of this.localOrder.order.payments) {
			if(orderPayment.parents.includes(payment.uid)) total += orderPayment.amount;
		}
		return -total === payment.amount;
	}

	havePendingPayment() {
		return this.localOrder.order.payments.find((payment) => payment.status === OrderPaymentStatus.PENDING) !== undefined;
	}

	getTransferDropdownValues(transfer: LocalOrderTransfer): DropdownButtonAction[] {
		const actions: DropdownButtonAction[] = [];
		if(transfer.fetchedFromTargetDatetime === null && transfer.transferUid) {
			actions.push({
				name: 'Annuler le transfert',
				id: 'cancel',
				icon: 'fa-regular fa-ban'
			});
		}
		actions.push({
			name: 'Débloquer le transfert',
			id: 'receipt',
			icon: 'fa-regular fa-flag'
		})
		return actions;
	}
}