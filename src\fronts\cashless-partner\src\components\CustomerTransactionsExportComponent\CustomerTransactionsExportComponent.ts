import {Component, Prop, Vue} from "vue-facing-decorator";
import {DropdownComponent, FormModalOrDrawerComponent, InputDateComponent,} from "@groupk/vue3-interface-sdk";
import {
	AutoWired,
	QueryFilterGroup,
	QueryFilterGroupClause,
	QueryOperator,
	TypedQuerySearch,
	VisualScopedUuid
} from "@groupk/horizon2-core";
import {CustomerRepository} from "../../../../../shared/repositories/CustomerRepository";
import {ExportFileType, MultiFormatExporter} from "../../../../../shared/utils/MultiFormatExporter";
import {TransactionsRepository} from "../../../../../shared/repositories/TransactionsRepository";
import {CustomerChipRepository} from "../../../../../shared/repositories/CustomerChipRepository";
import {
	CashlessHttpCustomerChipContract,
	CashlessHttpSimpleProductContract,
	UuidScopeCustomer_customer
} from "@groupk/mastodon-core";
import {ProductsRepository} from "../../../../../shared/repositories/ProductsRepository";
import {
	CashlessHttpTransactionContractSearchConfig,
	SimpleProductApiOut,
	TransactionApiOut,
	TransactionStatus,
	ApplicationPermission,
	EstablishmentAccountPermissionModel,
	CashlessHttpTransactionContract,
} from "@groupk/mastodon-core";
import {DateFilter, HourFilter} from "../../../../../shared/filters/DateFilter";
import {AppState} from "../../../../../shared/AppState";

export function CustomerTransactionsExportComponentHasRequiredPermissions(ownedPermissions: ApplicationPermission[]): boolean {
	return EstablishmentAccountPermissionModel.hasPermissionsForContracts(ownedPermissions, [
		CashlessHttpTransactionContract.search,
		CashlessHttpSimpleProductContract.list,
		CashlessHttpCustomerChipContract.listCustomerChips,
	]);
}

@Component({
	components: {
		dropdown: DropdownComponent,
		'form-modal-or-drawer': FormModalOrDrawerComponent,
		'date-input': InputDateComponent
	},
	emits: ['close', 'chose-export']
})
export default class CustomerTransactionsExportComponent extends Vue {
	@Prop({required: true}) customerUid!: VisualScopedUuid<UuidScopeCustomer_customer>;

	exportDates: { start: string, end: string } = {start: '', end: ''};

	selectedFileType: ExportFileType = 'xlsx';

	exportError: string|null = null;
	opened: boolean = false;
	loading: boolean = false;
	exporting: boolean = false;

	@AutoWired(CustomerRepository) accessor customerRepository!: CustomerRepository;
	@AutoWired(TransactionsRepository) accessor transactionsRepository!: TransactionsRepository;
	@AutoWired(CustomerChipRepository) accessor customerChipRepository!: CustomerChipRepository;
	@AutoWired(ProductsRepository) accessor productsRepository!: ProductsRepository;
	@AutoWired(AppState) accessor appState!: AppState;

	mounted() {
		setTimeout(() => this.opened = true, 0);
	}

	close() {
		this.opened = false;
		setTimeout(() => this.$emit('close'), 300);
	}

	async validate() {
		this.exportError = null;
		if(!this.exportDates.start || !this.exportDates.end) {
			this.exportError = 'Les dates sont invalides';
			return;
		}

		this.exporting = true;

		const exportDateStart = new Date(this.exportDates.start);
		const exportDateEnd = new Date(this.exportDates.end);

		// GET ALL CHIPS BETWEEN DATES
		// ADD CHIPS IN FILTERS INSIDE 'OR' GROUP
		try {
			const customerChips = (await this.customerChipRepository.callContract(
			"listCustomerChips",
			{establishmentUid: this.appState.requireUrlEstablishmentUid(), customerUid: this.customerUid},
			undefined)
			).success();

			let chipsFilters: QueryFilterGroup[] = [];

			for(let chip of customerChips) {
				const chipStartingDate = new Date(chip.startingDatetime);
				const chipEndingDate = chip.endingDatetime ? new Date(chip.endingDatetime) : null;

				let chipFilterGroup: QueryFilterGroup = {
					group: QueryFilterGroupClause.AND,
					filters: []
				};

				// Start date condition
				if (chipStartingDate < exportDateStart) {
					chipFilterGroup.filters.push({
						name: 'creationDatetime',
						operator: QueryOperator.MORE_OR_EQUAL,
						value: exportDateStart.toISOString()
					});
				} else {
					chipFilterGroup.filters.push({
						name: 'creationDatetime',
						operator: QueryOperator.MORE_OR_EQUAL,
						value: chipStartingDate.toISOString()
					});
				}

				// End date condition
				if (!chipEndingDate || chipEndingDate > exportDateEnd) {
					chipFilterGroup.filters.push({
						name: 'creationDatetime',
						operator: QueryOperator.LESS_OR_EQUAL,
						value: exportDateEnd.toISOString()
					});
				} else if (chipEndingDate) {
					chipFilterGroup.filters.push({
						name: 'creationDatetime',
						operator: QueryOperator.LESS_OR_EQUAL,
						value: chipEndingDate.toISOString()
					});
				}

				chipFilterGroup.filters.push({
					group: QueryFilterGroupClause.OR,
					filters: [{
						name: 'publicDebitedChipId',
						operator: QueryOperator.EQUAL,
						value: chip.chipVisualId
					}, {
						name: 'publicCreditedChipId',
						operator: QueryOperator.EQUAL,
						value: chip.chipVisualId
					}]
				});

				chipsFilters.push(chipFilterGroup);
			}


			let transactionsFilters!: any;
			if(chipsFilters.length === 1) {
				transactionsFilters = chipsFilters[0];
			} else {
				transactionsFilters = {
					group: QueryFilterGroupClause.OR,
					filters: chipsFilters
				};
			}

			let transactions: TransactionApiOut[] = [];
			let partialTransactions: TransactionApiOut[] = [];
			do {
				const params: TypedQuerySearch<typeof CashlessHttpTransactionContractSearchConfig> = {
					filter: transactionsFilters,
				};

				if(partialTransactions[49]) {
					params.cursorAfter = partialTransactions[49].uid
				}

				partialTransactions = (await this.transactionsRepository.callContract('search', {establishmentUid: this.appState.requireUrlEstablishmentUid()}, params)).success();

				transactions = transactions.concat(partialTransactions);
			} while(partialTransactions.length === 50);


			const products = (await this.productsRepository.callContract('list', {establishmentUid: this.appState.requireUrlEstablishmentUid()}, undefined)).success();

			const jsonData: Record<string, unknown>[] = [];
			for(let transaction of transactions) {
				if(transaction.status === TransactionStatus.CANCELED) continue;

				let productsTotal = 0;
				if(transaction.products) {
					for(let product of transaction.products) {
						const productPrice = parseFloat((product.price / 100).toFixed(2));
						productsTotal += product.price * product.qty;
						jsonData.push({
							'Nom du produit': this.requireProductWithId(product.id, products).name,
							'Quantite': product.qty,
							'Prix unitaire': productPrice,
							'Total': productPrice * product.qty,
							'Puce': transaction.chipVisualId,
							'Statut': transaction.status,
							'Date': DateFilter(transaction.creationDatetime) + ' ' + HourFilter(transaction.creationDatetime),
							'ID transaction': transaction.uid
						});
					}
				}

				if((transaction.signedAmount - productsTotal) !== 0) {
					jsonData.push({
						'Nom du produit': 'MANUEL',
						'Quantite': 1,
						'Prix unitaire': '',
						'Total': parseFloat(((transaction.signedAmount - productsTotal) / 100).toFixed(2)),
						'Puce': transaction.chipVisualId,
						'Statut': transaction.status,
						'Date': DateFilter(transaction.creationDatetime) + ' ' + HourFilter(transaction.creationDatetime),
						'ID transaction': transaction.uid
					});
				}
			}

			MultiFormatExporter.downloadData([
				{name: 'Nom du produit', type: "AUTO"},
				{name: 'Quantite', type: "AUTO"},
				{name: 'Prix unitaire', type: "MONEY"},
				{name: 'Total', type: "MONEY"},
				{name: 'Puce', type: "AUTO"},
				{name: 'Statut', type: "AUTO"},
				{name: 'Date', type: "AUTO"},
				{name: 'ID transaction', type: "AUTO"},
			], jsonData, this.selectedFileType);

		} catch(err) {
			this.exportError = 'Une erreur inconnue est survenue';
		}

		this.exporting = false;
	}

	requireProductWithId(productId: number, products: SimpleProductApiOut[]) {
		const product = products.find((product) => product.id === productId);
		if(!product) throw new Error('missing_product');
		return product;
	}
}