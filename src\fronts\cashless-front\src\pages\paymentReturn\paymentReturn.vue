<script lang="ts" src="./paymentReturn.ts">
</script>

<style lang="sass">
@import 'paymentReturn'
</style>

<template>
    <div id="payment-return-page">
        <div class="centered-content">
            <div class="loading-container" v-if="loading">
                <div class="loader"></div>
            </div>

            <template v-else>
                <div class="order-confirmation">
                    <i class="fa-solid fa-circle-check"></i>

                    <div class="head">
                        <span class="title"> Paiement réussi ! </span>
                        <span class="infos">
                            Le solde à bien été ajouté à votre bracelet <template v-if="funding.publicChipId"> {{ $filters.Chip(funding.publicChipId)  }} </template>
                        </span>
                    </div>

                    <div class="recap">
                        <div class="data">
                            <span class="value"> {{ funding.uid.substring(23, 30) }} </span>
                            <span class="title"> Référence </span>
                        </div>
                        <div class="data">
                            <span class="value"> {{ $filters.Money(funding.cashlessAmount) }} </span>
                            <span class="title"> Total TTC </span>
                        </div>
                    </div>

                    <a :href="getIndexUrl()" class="big grey button">
                        <i class="fa-regular fa-arrow-left"></i>
                        Retour à mon solde
                    </a>
                </div>
            </template>
        </div>
    </div>
</template>