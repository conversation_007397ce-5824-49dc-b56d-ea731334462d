<script lang="ts" src="./KeypadComponent.ts">
</script>

<style lang="sass" scoped>
@import './KeypadComponent.scss'
</style>

<template>
    <div class="keypad-component">
        <slot name="display"></slot>
        <div class="line" v-if="!simpleMode">
            <div class="key full" @click="clickedKey(KeypadKey.TRASH)">
                <i class="fa-regular fa-trash-alt"></i>
            </div>
        </div>
        <div class="line" v-if="!simpleMode && !hideOrderRelatedKeys">
            <div class="key" @click="clickedKey(KeypadKey.MAX)"> Max </div>
            <div class="key" @click="clickedKey(KeypadKey.HALF)"> 50% </div>
            <div class="key" @click="clickedKey(KeypadKey.QUARTER)"> 25% </div>
        </div>
        <div class="line">
            <div class="key" @click="clickedKey(KeypadKey.ONE)"> 1 </div>
            <div class="key" @click="clickedKey(KeypadKey.TWO)"> 2 </div>
            <div class="key" @click="clickedKey(KeypadKey.THREE)"> 3 </div>
        </div>
        <div class="line">
            <div class="key" @click="clickedKey(KeypadKey.FOUR)"> 4 </div>
            <div class="key" @click="clickedKey(KeypadKey.FIVE)"> 5 </div>
            <div class="key" @click="clickedKey(KeypadKey.SIX)"> 6 </div>
        </div>
        <div class="line">
            <div class="key" @click="clickedKey(KeypadKey.SEVEN)"> 7 </div>
            <div class="key" @click="clickedKey(KeypadKey.EIGHT)"> 8 </div>
            <div class="key" @click="clickedKey(KeypadKey.NINE)"> 9 </div>
        </div>
        <div class="line">
            <div class="key" @click="clickedKey(KeypadKey.ZERO_ZERO)" v-if="!simpleMode">
                <i class="fa-regular fa-00"></i>
            </div>
            <div v-else-if="nextKey"  class="key" @click="clickedKey(KeypadKey.BACKSPACE)">
                <i class="fa-regular fa-delete-left"></i>
            </div>
            <div v-else></div>
            <div class="key" @click="clickedKey(KeypadKey.ZERO)"> 0 </div>
            <div v-if="!simpleMode || !nextKey" class="key" @click="clickedKey(KeypadKey.BACKSPACE)">
                <i class="fa-regular fa-delete-left"></i>
            </div>
            <div v-if="simpleMode && nextKey" class="key" @click="clickedKey(KeypadKey.NEXT)">
                Suiv. &nbsp; <i class="fa-regular fa-arrow-right"></i>
            </div>
        </div>
    </div>
</template>