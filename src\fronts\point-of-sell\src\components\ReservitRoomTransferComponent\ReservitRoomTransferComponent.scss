.reservit-room-transfer-component {
    position: relative;
    height: 100%;

    .loading-bookings {
        position: absolute;
        z-index: 100;
        inset: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        background: rgba(0, 0, 0, 0.2);
    }

    .scroll-area {
        overflow: auto;
        padding-bottom: 80px;
        box-sizing: border-box;
        height: 100%;
    }

    .top {
        display: flex;
        flex-direction: column;
        gap: 15px;
        padding: 20px;
        position: sticky;
        top: 0;
        background: white;

        &.vertical {
            flex-direction: row;
            align-items: center;
        }

        .head {
            display: flex;
            flex-direction: column;
            gap: 4px;

            .title {
                font-size: 20px;
                font-weight: 600;
            }

            .subtitle {
                font-size: 15px;
            }
        }

        .input-group {
            position: relative;
            flex-grow: 2;

            input {
                font-size: 16px;
                padding: 15px;
            }

            .loader {
                position: absolute;
                right: 15px;
                top: 50%;
                transform: translateY(-50%);
                height: 15px;
                width: 15px;
                border-width: 2px;

                &:after {
                    height: 15px;
                    width: 15px;
                    border-width: 2px;
                }
            }
        }

        .room-name {
            font-size: 20px;
            font-weight: 600;
        }
    }

    .rooms {
        display: flex;
        flex-direction: column;
        gap: 10px;
        padding: 0 20px;

        .room {
            display: flex;
            align-items: center;
            gap: 10px;
            background: var(--secondary-hover-color);
            padding: 15px;
            border-radius: 8px;

            .data {
                flex-grow: 2;
                display: flex;
                flex-direction: column;
                gap: 4px;

                .name {
                    font-weight: 500;
                }

                .id {
                    font-size: 12px;
                }
            }
        }
    }

    .bookings {
        display: flex;
        flex-direction: column;
        gap: 15px;
        padding: 10px;

        .no-bookings {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 30px 20px;
            background: var(--secondary-hover-color);
            border-radius: 8px;
            text-align: center;

            i {
                font-size: 40px;
                color: #999;
                margin-bottom: 15px;
            }

            span {
                font-size: 16px;
                color: #666;
            }
        }

        .booking {
            display: flex;
            flex-direction: column;
            background: var(--secondary-hover-color);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

            .booking-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 15px;
                background: rgba(0, 0, 0, 0.03);

                .guest-info {
                    display: flex;
                    align-items: center;
                    gap: 12px;

                    .guest-avatar {
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        width: 40px;
                        height: 40px;
                        background: #D9D9D9;
                        border-radius: 50%;
                        font-weight: 600;
                        color: #555;
                    }


                    .data {
                        display: flex;
                        flex-direction: column;
                        gap: 4px;

                        .guest-name {
                            font-weight: 600;
                            font-size: 16px;
                        }

                        .booking-info {
                            display: flex;
                            align-items: center;

                            i {
                                width: 16px;
                                color: #666;
                            }

                            span {
                                font-size: 14px;
                            }
                        }
                    }
                }

                .booking-status {
                    padding: 6px 12px;
                    border-radius: 20px;
                    font-size: 12px;
                    font-weight: 600;

                    &.status-upcoming {
                        background-color: #E3F5FF;
                        color: #0078D4;
                    }

                    &.status-active {
                        background-color: #E5F5E5;
                        color: #107C10;
                    }

                    &.status-completed {
                        background-color: #F3F2F1;
                        color: #605E5C;
                    }
                }
            }

            .booking-details {
                padding: 15px;
                display: flex;
                flex-direction: column;
                gap: 12px;

                .booking-dates {
                    display: flex;
                    flex-direction: column;
                    gap: 8px;

                    .date-range {
                        display: flex;
                        align-items: center;
                        justify-content: space-between;
                        border-radius: 6px;
                        padding: 0 15px;

                        .date-item {
                            display: flex;
                            flex-direction: column;
                            gap: 4px;

                            .date-label {
                                font-size: 12px;
                                color: #666;
                                font-weight: 500;
                            }

                            .date-value {
                                font-size: 14px;
                                font-weight: 600;
                            }
                        }

                        .date-separator {
                            display: flex;
                            align-items: center;
                            color: #666;
                            padding: 0 10px;
                        }
                    }
                }

                .date-item, .booking-info {
                    display: flex;
                    align-items: center;
                    gap: 10px;

                    i {
                        width: 16px;
                        color: #666;
                    }

                    span {
                        font-size: 14px;
                    }
                }

                .booking-actions {
                    display: flex;
                    justify-content: flex-end;
                    margin-top: 10px;

                    .button {
                        width: 100%;
                        padding: 15px;
                    }
                }
            }
        }
    }

    .keypad-container {
        position: fixed;
        background: rgba(0, 0, 0, 0.7);
        inset: 0;
        display: flex;
        align-items: center;
        justify-content: center;

        .keypad {
            display: flex;
            flex-direction: column;
            gap: 20px;
            background: white;
            width: 300px;
            border-radius: 8px;
            padding: 20px;

            .keypad-component {
                border: none;
                grid-gap: 5px;

                .line {
                    border: none;
                    grid-gap: 5px;

                    .key {
                        border: none;
                        background: #F2F4F7;
                        border-radius: 6px;
                    }
                }
            }

            .head {
                display: flex;
                align-items: center;
                justify-content: space-between;

                .title {
                    font-size: 18px;
                    font-weight: bold;
                }

                .close {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    height: 42px;
                    width: 42px;
                    background: #E8E8E8;
                    border-radius: 50%;
                }
            }


            .primary.button {
                height: 30px;
                font-size: 16px;
            }

            input {
                font-size: 24px;
                font-weight: 500;
                text-align: center;
            }

            .selector {
                display: flex;
                align-items: center;
                justify-content: center;
                padding: 20px 0 30px 0;

                .amount {
                    width: 80px;
                    font-size: 24px;
                    font-weight: 500;
                    text-align: center;
                }

                .plus, .minus {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    height: 42px;
                    width: 42px;
                    background: #E8E8E8;
                    border-radius: 50%;
                    font-size: 18px;
                    font-weight: 400;
                }
            }

            .customers {
                display: flex;
                flex-direction: column;
                gap: 10px;

                .customer {
                    display: flex;
                    gap: 10px;
                    align-items: center;
                    padding: 15px;
                    border-radius: 8px;
                    cursor: pointer;
                    user-select: none;
                    background: #F2F2F2;

                    .profile-picture {
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        border-radius: 50%;
                        width: 42px;
                        height: 42px;
                        flex-shrink: 0;
                        background: #D9D9D9;
                        font-weight: 500;
                    }

                    .infos {
                        flex-grow: 2;
                        display: flex;
                        flex-direction: column;

                        .name {
                            color: #000;
                            font-size: 16px;
                            font-weight: 600;
                            text-transform: capitalize;
                        }

                        .email {
                            font-size: 14px;
                            font-weight: 400;
                            word-break: break-all;
                        }
                    }

                    i {
                        font-size: 20px;

                        &.fa-circle-dot {
                            color: var(--primary-color)
                        }
                    }
                }
            }
        }
    }
}
