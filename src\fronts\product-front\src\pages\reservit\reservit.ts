import {Component, Vue} from "vue-facing-decorator";
import {
    ContentHeaderParameters,
    FilterTableLayoutComponent,
    ForbiddenMessageComponent, SavedFilter, TableColumn, TablePagination
} from "@groupk/vue3-interface-sdk";
import SidebarStateListener from "../../../../../shared/utils/SidebarStateListener";
import {AutoWired, ScopedUuid, TypedQuerySearch, Uuid} from "@groupk/horizon2-core";
import {OrderExternalTransferRepository} from "../../../../../shared/repositories/OrderExternalTransferRepository";
import {
    OrderExternalTransferApiOut, ProductHttpProductContractSearchConfig,
    ProductOrderExternalTransferContractSearchConfig
} from "@groupk/mastodon-core";
import {AppState} from "../../../../../shared/AppState";
import {TableColumnsRepository} from "../../../../../shared/repositories/TableColumnsRepository";
import {SavedFiltersRepository} from "../../../../../shared/repositories/SavedFiltersRepository";
import {UuidScopeProduct_order} from "@groupk/mastodon-core";
import {AppBus} from "../../AppBus";
import ToastManagerComponent from "../../components/ToastManagerComponent/ToastManagerComponent.vue";

@Component({
    components: {
        'forbidden-message': ForbiddenMessageComponent,
        'filter-table-layout': FilterTableLayoutComponent,
        'toast-manager': ToastManagerComponent,
    }
})
export default class ReservitPage extends Vue {

    headerParameters: ContentHeaderParameters = {
        header: 'Reservit',
        subtitle: 'Intégration avec Reservit',
        actions: [],
        hideSearch: true,
        searchPlaceholder: ''
    }

    reservitTransfers: OrderExternalTransferApiOut[] = [];
    allowedFilters = ProductOrderExternalTransferContractSearchConfig;
    filters: TypedQuerySearch<typeof ProductOrderExternalTransferContractSearchConfig> = {};
    appliedFilters: TypedQuerySearch<typeof ProductHttpProductContractSearchConfig> = {};
    savedFilters: SavedFilter[] = [];

    selectedTransfer: OrderExternalTransferApiOut|null = null;

    tableKey = 'product-reservit-transfers';
    tableColumns = [
        {title: 'ID', name: 'uid', displayed: true, mobileHidden: false} as const,
        {title: 'Statut', name: 'success', displayed: true, mobileHidden: false} as const,
        {title: 'Date d\'exécution', name: 'executionDatetime', displayed: true, mobileHidden: false} as const,
        {title: 'Date de création', name: 'creationDatetime', displayed: true, mobileHidden: false} as const,
    ] satisfies TableColumn[];

    pagination: TablePagination = {
        totalResults: 0,
        resultsPerPage: 50,
        currentPage: 1,
        estimateTotal: false
    }

    forbidden: boolean = false;
    replayingTransfer: ScopedUuid<UuidScopeProduct_order>|null = null;
    loading: boolean = true;

    @AutoWired(OrderExternalTransferRepository) accessor orderExternalTransferRepository!: OrderExternalTransferRepository;
    @AutoWired(SidebarStateListener) accessor sidebarStateListener!: SidebarStateListener;
    @AutoWired(TableColumnsRepository) accessor tableColumnsRepository!: TableColumnsRepository;
    @AutoWired(SavedFiltersRepository) accessor savedFiltersRepository!: SavedFiltersRepository;
    @AutoWired(AppState) accessor appState!: AppState;
    @AutoWired(AppBus) accessor appBus!: AppBus;

    beforeMount() {
        this.sidebarStateListener.setHiddenSidebar(false);
        this.sidebarStateListener.setMinimizedSidebar(false);

        let savedPreferences = this.tableColumnsRepository.getColumnPreferences(this.tableKey, this.tableColumns);
        if (savedPreferences) this.tableColumns = savedPreferences;
    }

    async mounted() {
        await this.search(this.filters);

        this.loading = false;
    }

    saveColumnPreferences(columns: TableColumn[]) {
        this.tableColumnsRepository.saveColumnsPreferences(this.tableKey, columns);
    }

    load() {
        return {
            pageTitle: 'Reservit - Produit'
        };
    }

    async search(filters: TypedQuerySearch<typeof ProductOrderExternalTransferContractSearchConfig>, cursor: {after?: Uuid, before?: Uuid}|null = null) {
        this.loading = true;

        this.filters = {...filters};

        if (!cursor) {
            const data = (await this.orderExternalTransferRepository.callContract('searchCount', {establishmentUid: this.appState.requireUrlEstablishmentUid()}, filters)).success();
            this.pagination.totalResults = data.rows;
            this.pagination.currentPage = 1;
            this.pagination.estimateTotal = data.estimate;
        }

        filters.elementsPerPage = this.pagination.resultsPerPage;
        if (cursor && cursor.after) filters.cursorAfter = cursor.after;
        if (cursor && cursor.before) filters.cursorBefore = cursor.before;


        this.reservitTransfers = (await this.orderExternalTransferRepository.callContract('search', {establishmentUid: this.appState.requireUrlEstablishmentUid()}, filters)).success().list;

        this.loading = false;
    }

    nextPage() {
        this.pagination.currentPage++;
        this.search(this.filters, {after: this.reservitTransfers[this.reservitTransfers.length - 1].uid})
    }

    previousPage() {
        this.pagination.currentPage--;
        this.search(this.filters, {before: this.reservitTransfers[0].uid})
    }

    saveFilter(name: string) {
        if (this.filters.filter) {
            this.savedFilters.push({
                name: name,
                filter: this.filters.filter
            });
            this.savedFiltersRepository.saveFilters(this.tableKey, this.savedFilters);
        }
    }

    selectFilter(savedFilter: SavedFilter) {
        this.filters.filter = savedFilter.filter as any;
        this.search(this.filters);
    }

    deleteFilter(index: number) {
        this.savedFilters.splice(index, 1);
        this.savedFiltersRepository.saveFilters(this.tableKey, this.savedFilters);
    }

    toggleSelectedTransfer(transfer: OrderExternalTransferApiOut) {
        const selection = window.getSelection();
        if (selection && !selection.isCollapsed) {
            return;
        }
        if(this.selectedTransfer && this.selectedTransfer.uid === transfer.uid) this.selectedTransfer = null;
        else this.selectedTransfer = transfer;
    }

    async replayTransfer(transfer: OrderExternalTransferApiOut) {
        this.replayingTransfer = transfer.uid;

        const response = await this.orderExternalTransferRepository.callContract('resend', {establishmentUid: this.appState.requireUrlEstablishmentUid(), orderUid: transfer.uid}, undefined);
        if(!response.isSuccess()) {
            this.appBus.emit('emit-toast', {
                title: 'Erreur',
                description: 'Une erreur technique est survenue.',
                duration: 3000,
                type: 'ERROR',
                closable: true
            });
        } else {

            this.appBus.emit('emit-toast', {
                title: 'Demande envoyée',
                description: 'Le transfer va être rejoué.',
                duration: 3000,
                type: 'SUCCESS',
                closable: true
            });
        }

        this.replayingTransfer = null;
    }
}