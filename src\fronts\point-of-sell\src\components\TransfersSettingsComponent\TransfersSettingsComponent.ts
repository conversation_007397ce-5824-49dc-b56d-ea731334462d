import {Component, Vue} from "vue-facing-decorator";
import {
    OrderTransferApiOut,
    ProductHttpOrdersContract,
    UuidScopeIot_deviceApp,
    uuidScopeIot_deviceApp
} from "@groupk/mastodon-core";
import {OrderRepository} from "../../../../../shared/repositories/OrderRepository";
import {AutoWired, ScopedUuid, UuidUtils, VisualScopedUuid} from "@groupk/horizon2-core";
import {PosState} from "../../model/PosState";
import {UuidScopeProduct_orderTransfer} from "@groupk/mastodon-core";
import {AppBus} from "../../config/AppBus";
import {OrderTransferWorker} from "../../class/OrderTransferWorker";
import {LocalOrderRepository} from "../../repositories/LocalOrderRepository";
import {translateResponseError} from "../../../../../shared/RepositoryExtensions";

@Component({
    components: {}
})
export default class TransfersSettingsComponent extends Vue {
    transfers: OrderTransferApiOut[] = [];

    isOffline: boolean = false;
    cancelingTransfer: VisualScopedUuid<UuidScopeProduct_orderTransfer>|null = null;
    loading: boolean = true;

    @AutoWired(OrderRepository) accessor orderRepository!: OrderRepository;
    @AutoWired(LocalOrderRepository) accessor localOrderRepository!: LocalOrderRepository;
    @AutoWired(PosState) accessor posState!: PosState;
    @AutoWired(AppBus) accessor appBus!: AppBus;
    @AutoWired(OrderTransferWorker) accessor orderTransferWorker!: OrderTransferWorker;

    async mounted() {
        this.isOffline = !navigator.onLine;

        if(!this.isOffline) {
            await this.getPendingTransfers();
        }
        this.loading = false;
    }

    async getPendingTransfers() {
        this.transfers = (await this.orderRepository.callContract('listPendingTransfers', {establishmentUid: this.posState.establishmentUid}, undefined)).success().list.filter((transfer) => {
            return transfer.receivedFromCreatorDatetime === null && (transfer.originIotDevice === UuidUtils.scopedToVisual(this.posState.iotDevice.uid, uuidScopeIot_deviceApp) || transfer.creatorIotDevice === UuidUtils.scopedToVisual(this.posState.iotDevice.uid, uuidScopeIot_deviceApp))
        });
    }

    async acceptTransfer(transfer: OrderTransferApiOut) {
        const correspondingLocalOrder = await this.localOrderRepository.findOne(transfer.orderUid);

        if(!correspondingLocalOrder || correspondingLocalOrder.transferred || correspondingLocalOrder.error) {
            await this.cancelTransfer(transfer);
        } else {
            if(this.posState.currentOrder && this.posState.currentOrder.uid === correspondingLocalOrder.uid) this.posState.currentOrder = null;

            await this.orderTransferWorker.acceptTransfer(transfer, correspondingLocalOrder);

            await this.getPendingTransfers();
        }
    }

    async cancelTransfer(transfer: OrderTransferApiOut) {
        this.cancelingTransfer = transfer.uid;
        const response = await this.orderRepository.callContract('cancelTransfer', {establishmentUid: this.posState.establishmentUid, transferUid: transfer.uid}, undefined);

        if(response.isSuccess()) {
            const index = this.transfers.findIndex((t) => t.uid === transfer.uid);
            if(index !== -1) this.transfers.splice(index, 1);
        } else {
            const error = translateResponseError<typeof ProductHttpOrdersContract, 'cancelTransfer'>(response, {
                order_transfer_already_canceled: 'Ce transfert a déjà été annulé',
                order_transfer_already_fetched: undefined
            });
            this.appBus.emit('displayToast', {
                title: `L'annulation a échoué`,
                description: `Le transfert n'a pas pû être annulé (${error})`,
                color: 'red',
                closable: true,
                duration: 3000,
            });

            if(response.isError()) {
                const error = response.error();
                if(error && 'error' in error && error.error === 'order_transfer_already_canceled') {
                    const index = this.transfers.findIndex((t) => t.uid === transfer.uid);
                    if(index !== -1) this.transfers.splice(index, 1);
                }
            }
        }

        this.cancelingTransfer = null;
    }

    iotDeviceAppScopedToVisual(iodDeviceAppScopedUid: ScopedUuid<UuidScopeIot_deviceApp>) {
        return UuidUtils.scopedToVisual(iodDeviceAppScopedUid, uuidScopeIot_deviceApp);
    }
}