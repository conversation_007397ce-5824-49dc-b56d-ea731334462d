import {Component, Vue} from "vue-facing-decorator";
import SidebarComponent from "../../components/SidebarComponent/SidebarComponent.vue";
import SellComponent from "../../components/SellComponent/SellComponent.vue";
import {PosState} from "../../model/PosState";
import OrderListComponent from "../../components/OrderListComponent/OrderListComponent.vue";
import SettingsComponent from "../../components/SettingsComponent/SettingsComponent.vue";
import {PosProfile} from "../../model/PosProfile";
import {AutoWired, GetInstance, ScopedUuid, SetInstance, UuidUtils} from "@groupk/horizon2-core";
import {ProductRepository} from "../../../../../shared/repositories/ProductRepository";
import {
	AppType,
	CustomerApiOut,
	EstablishmentDeviceAppV2ApiOut,
	PosProfileApiOut,
	uuidScopeEstablishment,
	UuidScopeEstablishment
} from "@groupk/mastodon-core";
import {OptionBuilder} from "vue-facing-decorator/dist/optionBuilder";
import {Router} from "@groupk/horizon2-front";
import {CategoryRepository} from "../../../../../shared/repositories/CategoryRepository";
import {PurchaseStatusRepository} from "../../../../../shared/repositories/PurchaseStatusRepository";
import {PosWorker} from "../../class/PosWorker";
import {LocalOrderRepository} from "../../repositories/LocalOrderRepository";
import {CashStatementRepository} from "../../../../../shared/repositories/CashStatementRepository";
import PosErrorComponent, {PosError} from "../../components/PosErrorComponent/PosErrorComponent.vue";
import ToastManagerComponent from "../../components/ToastManagerComponent/ToastManagerComponent.vue";
import {AppBus} from "../../config/AppBus";
import {ErrorHandler} from "../../class/ErrorHandler";
import ClosingCashStatementComponent
	from "../../components/ClosingCashStatementComponent/ClosingCashStatementComponent.vue";
import {LocalCashStatementRepository} from "../../repositories/LocalCashStatementRepository";
import {PaymentMethodRepository} from "../../../../../shared/repositories/PaymentMethodRepository";
import {PosProfileRepository} from "../../../../../shared/repositories/PosProfileRepository";
import {BillingRegionRepository} from "../../../../../shared/repositories/BillingRegionRepository";
import StatisticsComponent from "../../components/StatisticsComponent/StatisticsComponent.vue";
import QuickActionsComponent from "../../components/QuickActionsComponent/QuickActionsComponent.vue";
import {EstablishmentAccountRepository} from "../../../../../shared/repositories/EstablishmentAccountRepository";
import {ColorUtils} from "../../class/ColorUtils";
import {ProductEstablishmentRepository} from "../../../../../shared/repositories/ProductEstablishmentRepository";
import EmptyCashKeeperComponent from "../../components/CashKeeperActionsComponent/CashKeeperActionsComponent.vue";
import KeypadComponent from "../../components/KeypadComponent/KeypadComponent.vue";
import {KeypadKey} from "../../components/KeypadComponent/KeypadComponent";
import AccountSwitchComponent from "../../components/AccountSwitchComponent/AccountSwitchComponent.vue";
import {WorkClockRepository} from "../../../../../shared/repositories/WorkClockRepository";
import {StockRepository} from "../../../../../shared/repositories/StockRepository";
import {IotUtils} from "../../class/IotUtils";
import {ContextRepository} from "../../../../../shared/repositories/ContextRepository";
import {DiscountTemplateRepository} from "../../../../../shared/repositories/DiscountTemplateRepository";
import MobileNavigationComponent from "../../components/MobileNavigationComponent/MobileNavigationComponent.vue";
import MobileOrderListComponent from "../../components/MobileOrderListComponent/MobileOrderListComponent.vue";
import MobileStatisticsComponent from "../../components/MobileStatisticsComponent/MobileStatisticsComponent.vue";
import {ModalOrDrawerComponent} from "@groupk/vue3-interface-sdk";
import {ExecutionFlowRepository} from "../../../../../shared/repositories/ExecutionFlowRepository";
import {PointOfSaleRepository} from "../../../../../shared/repositories/PointOfSaleRepository";
import {DisplaysNative, NativeHelper} from "@groupk/native-bridge";
import TablesComponent from "../../components/TablesComponent/TablesComponent.vue";
import {DiningRepository} from "../../../../../shared/repositories/DiningRepository";
import {EstablishmentUrlBuilder} from "../../class/EstablishmentUrlBuilder";
import {MetadataDescriptorRepository} from "../../../../../shared/repositories/MetadataDescriptorRepository";
import {OrderTransferWorker} from "../../class/OrderTransferWorker";
import {AuthStateModel} from "../../../../../shared/AuthStateModel";
import {MainConfig} from "../../../../../shared/MainConfig";
import OrderDebugModalComponent from "../../components/OrderDebugModalComponent/OrderDebugModalComponent.vue";
import OrderTransferToIotComponent from "../../components/OrderTransferToIotComponent/OrderTransferToIotComponent.vue";
import CustomersListComponent from "../../components/CustomersListComponent/CustomersListComponent.vue";
import {LocalOrder} from "../../model/LocalOrder";
import CashDrawerCashCountComponent
	from "../../components/CashDrawerCashCountComponent/CashDrawerCashCountComponent.vue";
import {ProductUtils} from "../../class/ProductUtils";
import {EstablishmentDeviceRepository} from "../../../../../shared/repositories/EstablishmentDeviceRepository";
import {AppRepository} from "../../../../../shared/repositories/AppRepository";
import {ReservitAppRepository} from "../../../../../shared/repositories/ReservitAppRepository";
import ReservitRoomTransferComponent
	from "../../components/ReservitRoomTransferComponent/ReservitRoomTransferComponent.vue";
import PurchasePrintProgressModalComponent
	from "../../components/PurchasePrintProgressModalComponent/PurchasePrintProgressModalComponent.vue";

@Component({
	components: {
		sidebar: SidebarComponent,
		sell: SellComponent,
		statistics: StatisticsComponent,
		'order-list': OrderListComponent,
		'tables': TablesComponent,
		settings: SettingsComponent,
		'pos-error': PosErrorComponent,
		'toast-manager': ToastManagerComponent,
		'closing-cash-statement': ClosingCashStatementComponent,
		'quick-actions': QuickActionsComponent,
		'cash-keeper': EmptyCashKeeperComponent,
		'keypad': KeypadComponent,
		'account-switch': AccountSwitchComponent,
		'mobile-navigation': MobileNavigationComponent,
		'mobile-order-list': MobileOrderListComponent,
		'mobile-statistics': MobileStatisticsComponent,
		'order-debug-modal': OrderDebugModalComponent,
		'order-transfer-to-iot': OrderTransferToIotComponent,
		'reservit-room-transfer': ReservitRoomTransferComponent,
		'customers-list-component': CustomersListComponent,
		'cash-drawer-cash-count': CashDrawerCashCountComponent,
		'modal-or-drawer': ModalOrDrawerComponent,
		'purchase-print-progress-modal': PurchasePrintProgressModalComponent,
	}
})
export default class PosView extends Vue {
	posState: PosState = new PosState();
	lockedProfileSwitchState: {profile: PosProfileApiOut, resolve: Function, reject: Function}|null = null;

	isMobileScreenSize: boolean = false;

	error: PosError = null;
	loading: boolean = true;

	@AutoWired(ProductRepository) accessor productRepository!: ProductRepository;
	@AutoWired(PaymentMethodRepository) accessor paymentMethodRepository!: PaymentMethodRepository;
	@AutoWired(BillingRegionRepository) accessor billingRegionRepository!: BillingRegionRepository;
	@AutoWired(DisplaysNative) accessor displaysNative!: DisplaysNative;
	@AutoWired(ContextRepository) accessor contextRepository!: ContextRepository;
	@AutoWired(DiscountTemplateRepository) accessor discountTemplateRepository!: DiscountTemplateRepository;
	@AutoWired(CategoryRepository) accessor categoryRepository!: CategoryRepository;
	@AutoWired(PurchaseStatusRepository) accessor purchaseStatusRepository!: PurchaseStatusRepository;
	@AutoWired(CashStatementRepository) accessor cashStatementRepository!: CashStatementRepository;
	@AutoWired(PosProfileRepository) accessor posProfileRepository!: PosProfileRepository;
	@AutoWired(AppRepository) accessor appRepository!: AppRepository;
	@AutoWired(ReservitAppRepository) accessor reservitAppRepository!: ReservitAppRepository;
	@AutoWired(DiningRepository) accessor diningRepository!: DiningRepository;
	@AutoWired(EstablishmentDeviceRepository) accessor establishmentDeviceRepository!: EstablishmentDeviceRepository;
	@AutoWired(ExecutionFlowRepository) accessor executionFlowRepository!: ExecutionFlowRepository;
	@AutoWired(PointOfSaleRepository) accessor pointOfSaleRepository!: PointOfSaleRepository;
	@AutoWired(LocalCashStatementRepository) accessor localCashStatementRepository!: LocalCashStatementRepository;
	@AutoWired(ProductEstablishmentRepository) accessor productEstablishmentRepository!: ProductEstablishmentRepository;
	@AutoWired(StockRepository) accessor stockRepository!: StockRepository;
	@AutoWired(EstablishmentAccountRepository) accessor establishmentAccountRepository!: EstablishmentAccountRepository;
	@AutoWired(MetadataDescriptorRepository) accessor metadataDescriptorRepository!: MetadataDescriptorRepository;
	@AutoWired(LocalOrderRepository) accessor localOrderRepository!: LocalOrderRepository;
	@AutoWired(WorkClockRepository) accessor workClockRepository!: WorkClockRepository;
	@AutoWired(PosProfile) accessor posProfile!: PosProfile;
	@AutoWired(Router) accessor router!: Router;
	@AutoWired(AuthStateModel) accessor authCenter!: AuthStateModel;
	@AutoWired(AppBus) accessor appBus!: AppBus;
	@AutoWired(MainConfig) accessor mainConfig!: MainConfig;

	constructor(optionBuilder: OptionBuilder, vueInstance: any) {
		super(optionBuilder, vueInstance);

		SetInstance(PosState, this.posState);

		let regexMatch = this.router.lastRouteRegexMatches;

		if (regexMatch && regexMatch[1]) {
			this.posState.establishmentUid = UuidUtils.scopedToVisual<UuidScopeEstablishment>(regexMatch[1] as ScopedUuid<UuidScopeEstablishment>, uuidScopeEstablishment);
		}

		const posWorker = new PosWorker(this.posState);
		posWorker.scheduleNextSync(0);
		SetInstance(PosWorker, posWorker);

		this.loading = true;
	}

	async mounted() {
		SetInstance(PosState, this.posState);

		const preLoaderDiv = document.getElementById('pre-loader');
		if(preLoaderDiv) preLoaderDiv.remove();

		if(NativeHelper.instance.isModuleAvailable(DisplaysNative)) {
			const devices = await this.displaysNative.status();
			if (devices.list[0]) {
				await this.displaysNative.enable(devices.list[0].id, window.location.origin + '/establishment/' + UuidUtils.visualToScoped(this.posState.establishmentUid) + '/customer-display');
			}
		}

		this.listenAccountChanges();

		if(window.innerWidth < 900) this.isMobileScreenSize = true;
		window.addEventListener('resize', () => {
			if(window.innerWidth < 900) this.isMobileScreenSize = true;
			else this.isMobileScreenSize = false;
		})


		// First check for cash statement
		try {
			const cashStatement = (await this.cashStatementRepository.callContract('currentCashStatement', {establishmentUid: this.posState.establishmentUid }, undefined)).success();
			if(cashStatement === null) {
				window.location.href = '/establishment/' + UuidUtils.visualToScoped(this.posState.establishmentUid) + '/cash-statement';
				return;
			}
			this.posState.currentCashStatement = cashStatement;
		} catch(err) {
			this.error = 'UNKNOWN_ERROR';
			GetInstance(ErrorHandler).logToSentry({
				title: 'Chargement de la caisse',
				description: 'Des données n\'ont pas pu être chargées (cashStatement)',
				error: err as Error
			});
			console.log(err);
			throw new Error('UNKNOWN_ERROR');
		}

		const cashStatementSession = this.localCashStatementRepository.getSession();
		if(!cashStatementSession || !this.localCashStatementRepository.isSessionActive(cashStatementSession, this.posState.currentCashStatement)) {
			window.location.href = '/establishment/' + UuidUtils.visualToScoped(this.posState.establishmentUid) + '/cash-statement';
			return;
		}
		this.posState.currentCashStatementSession = cashStatementSession;

		try {
			let iotDeviceApp: EstablishmentDeviceAppV2ApiOut|null = await IotUtils.getCurrentIotDevice(this.posState.establishmentUid);
			if(!iotDeviceApp) {
				window.location.href = EstablishmentUrlBuilder.buildUrl('/iot-onboarding');
				return;
			} else {
				this.posState.iotDevice = iotDeviceApp;
			}
		} catch(err) {
			this.error = 'UNKNOWN_ERROR';
			GetInstance(ErrorHandler).logToSentry({
				title: 'Chargement de la caisse',
				description: 'Des données n\'ont pas pu être chargées (iotDevice)',
				error: err as Error
			});
			console.log(err);
			throw new Error('UNKNOWN_ERROR');
		}

		try {
			this.posState.pointOfSaleConfiguration = (await this.pointOfSaleRepository.callContract(
				'resolveConfigForIotDevice',
				{establishmentUid: this.posState.establishmentUid, iotDeviceUid: this.posState.iotDevice.uid},
				undefined
			)).success();

			if(!this.posState.pointOfSaleConfiguration) {
				this.error = 'POINT_OF_SALE';
				return;
			}
		} catch(err) {
			this.error = 'POINT_OF_SALE';
			return;
		}

		const billingRegions = (await this.billingRegionRepository.callContract('list', {establishmentUid: this.posState.establishmentUid}, undefined)).success();

		this.posState.registerOrderExecutorModel(this.posState.pointOfSaleConfiguration.purchaseStatuses, billingRegions, this.posState.pointOfSaleConfiguration.executionFlow, this.posState.pointOfSaleConfiguration.pointOfSale);

		const token = this.authCenter.getStateSync();
		if(token === null) {
			this.error = 'AUTHENTICATION';
			throw new Error('AUTHENTICATION');
		} else if('establishmentAccounts' in token) {
			const establishmentAccount = token.establishmentAccounts.find((account) => account.establishmentUid === this.posState.establishmentUid);
			if(!establishmentAccount) {
				this.error = 'ESTABLISHMENT_ACCOUNT';
				throw new Error('ESTABLISHMENT_ACCOUNT');
			}
			this.posState.currentEstablishmentAccountUid = establishmentAccount.establishmentAccountUid;

			if(!this.posState.pointOfSale.establishmentAccountsUid.includes(establishmentAccount.establishmentAccountUid)) {
				this.posState.pointOfSale.establishmentAccountsUid.push(establishmentAccount.establishmentAccountUid);
			}
		} else {
			this.posState.currentEstablishmentAccountUid = token.establishmentAccountUid;

			if(!this.posState.pointOfSale.establishmentAccountsUid.includes(token.establishmentAccountUid)) {
				this.posState.pointOfSale.establishmentAccountsUid.push(token.establishmentAccountUid);
			}
		}

		const cancelStatus = this.posState.pointOfSaleConfiguration.purchaseStatuses.find((status) => status.cancel === true);
		if(!cancelStatus) {
			this.error = 'CANCEL_STATUS';
			throw new Error('CANCEL_STATUS');
		}

		try {
			const apps = (await this.appRepository.callContract('list', {establishmentUid: this.posState.establishmentUid}, undefined)).success();
			const reservitApp = apps.list.find((app) => app.type === AppType.RESERVIT);
			if(reservitApp) {
				this.posState.reservit = {
					app: reservitApp,
					configuration: null
				}

				try {
					this.posState.reservit.configuration = (await this.reservitAppRepository.callContract('getConfiguration', {establishmentUid: this.posState.establishmentUid, appUid: reservitApp.uid}, undefined)).success();
				} catch(err) {
					GetInstance(ErrorHandler).logEverywhere({
						title: 'Chargement de la caisse',
						description: 'Reservit indisponible',
						error: err as Error
					})
				}

			}

			this.posState.posProfiles = (await this.posProfileRepository.callContract('list', {establishmentUid: this.posState.establishmentUid}, undefined)).success();
			this.posState.stocks = (await this.stockRepository.callContract('search', {establishmentUid: this.posState.establishmentUid}, {})).success();
			this.posState.productEstablishment = (await this.productEstablishmentRepository.callContract('get', {establishmentUid: this.posState.establishmentUid}, undefined)).success();
			this.posState.paymentMethods = (await this.paymentMethodRepository.callContract('list', {establishmentUid: this.posState.establishmentUid}, undefined)).success();
			this.posState.products = (await this.productRepository.callContract('list', {establishmentUid: this.posState.establishmentUid}, undefined)).success();
			await ProductUtils.getMissingProductRevisions();
			this.posState.contexts = (await this.contextRepository.callContract('list', {establishmentUid: this.posState.establishmentUid}, undefined)).success();
			this.posState.discountTemplates = (await this.discountTemplateRepository.callContract('list', {establishmentUid: this.posState.establishmentUid}, undefined)).success();
			this.posState.categories = (await this.categoryRepository.callContract('list', {establishmentUid: this.posState.establishmentUid}, undefined)).success();
			this.posState.establishmentAccounts = (await this.establishmentAccountRepository.callContract('list', {establishmentUid: this.posState.establishmentUid}, undefined)).success();
			this.posState.lastWorkClocks = (await this.workClockRepository.callContract('getLastForAll', {establishmentUid: this.posState.establishmentUid}, undefined)).success();
			this.posState.metadataDescriptors = (await this.metadataDescriptorRepository.callContract('listDescriptors', {establishmentUid: this.posState.establishmentUid}, undefined)).success();
			this.posState.diningAreas = (await this.diningRepository.callContract('list', {establishmentUid: this.posState.establishmentUid}, undefined)).success().list;
			const devicesData = (await this.establishmentDeviceRepository.callContract('list', {establishmentUid: this.posState.establishmentUid}, undefined)).success();
			this.posState.iotDeviceApps = devicesData.appList;
			this.posState.iotDevices = devicesData.list;

			for(const category of this.posState.categories) {
				category.productsConfig = category.productsConfig.filter((config) => {
					const index = this.posState.products.findIndex((product) => product.uid === config.productUid);
					return index !== -1;
				});
			}

		} catch(err) {
			this.error = 'UNKNOWN_ERROR';
			GetInstance(ErrorHandler).logToSentry({
				title: 'Chargement de la caisse',
				description: 'Des données n\'ont pas pu être chargées',
				error: err as Error
			})
			console.log(err);
			throw new Error('UNKNOWN_ERROR');
		}

		this.appBus.emit('changeEstablishmentAccount', this.posState.currentEstablishmentAccountUid);

		const orderTransferWorker = new OrderTransferWorker(this.posState);
		SetInstance(OrderTransferWorker, orderTransferWorker);

		this.loading = false;
	}

	listenAccountChanges() {
		this.appBus.on('changeEstablishmentAccount', async (establishmentAccountUid) => {
			if(establishmentAccountUid === null) {
				this.posState.forceProfileSwitch = true;
			} else {
				const establishmentAccountProfile = this.posState.getEstablishmentAccountProfile(establishmentAccountUid);
				if(establishmentAccountProfile && establishmentAccountProfile.securityCode !== null) {
					try {
						await new Promise((resolve, reject) => {
							this.lockedProfileSwitchState =  {
								profile: establishmentAccountProfile,
								resolve: resolve,
								reject: reject
							}
						});
					} catch(err) {
						return;
					}
				}
				this.posState.currentEstablishmentAccountUid = establishmentAccountUid;
				this.posState.forceProfileSwitch = false;
				if(this.posProfile.color) {
					document.documentElement.style.setProperty('--primary-color', this.posProfile.color);
					document.documentElement.style.setProperty('--pos-color', this.posProfile.color);
					document.documentElement.style.setProperty('--primary-button-hover-color', this.posProfile.color);

					if(ColorUtils.isDark(this.posProfile.color)) {
						document.documentElement.style.setProperty(`--primary-text-color`, 'white');
						document.documentElement.style.setProperty(`--pos-current-text-color`, 'white');
						document.documentElement.style.setProperty(`--pos-current-selected-color`, 'rgba(255, 255, 255, 0.2)');
						document.documentElement.style.setProperty(`--pos-current-selected-text-color`, 'white');
						document.documentElement.style.setProperty('--primary-hover-text-color', 'white');
					} else {
						document.documentElement.style.setProperty(`--primary-text-color`, 'black');
						document.documentElement.style.setProperty(`--pos-current-text-color`, 'black');
						document.documentElement.style.setProperty(`--pos-current-selected-color`, 'rgba(0, 0, 0, 0.2)');
						document.documentElement.style.setProperty(`--pos-current-selected-text-color`, 'black');
						document.documentElement.style.setProperty('--primary-hover-text-color', 'black');
					}
				}

				this.$forceUpdate();
			}
		});
	}

	updateOrderCustomer(localOrder: LocalOrder, customer: CustomerApiOut) {
		localOrder.order.customerUid = customer.uid;

		this.localOrderRepository.saveAndResync(localOrder);

		this.posState.linkCustomerToOrder = null;
	}

	currentSecurityCode: string = '';
	clickedSecurityCodeKey(key: number) {
		if(!this.lockedProfileSwitchState || !this.lockedProfileSwitchState.profile.securityCode) return;
		if(key === KeypadKey.BACKSPACE) {
			this.currentSecurityCode = this.currentSecurityCode.slice(0, -1);
		} else {
			if(this.currentSecurityCode.length < this.lockedProfileSwitchState.profile.securityCode.length) {
				this.currentSecurityCode += key;
			}
		}

		if(this.currentSecurityCode === this.lockedProfileSwitchState.profile.securityCode) {
			this.lockedProfileSwitchState.resolve();
			this.lockedProfileSwitchState = null;
			this.currentSecurityCode = '';
		}
	}

	cancelLockedProfile() {
		if(!this.lockedProfileSwitchState || !this.lockedProfileSwitchState.profile.securityCode) return;
		this.lockedProfileSwitchState.reject();
		this.lockedProfileSwitchState = null;
		this.currentSecurityCode = '';
	}
}
