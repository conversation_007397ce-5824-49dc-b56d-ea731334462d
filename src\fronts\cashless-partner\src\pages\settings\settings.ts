import {Component, Vue} from "vue-facing-decorator";
import {
	DropdownComponent, ForbiddenMessageComponent, InputDateComponent,
	LayoutContentWithRightPanelComponent,
	LeftNavigationItem,
	LeftNavigationLayoutComponent
} from "@groupk/vue3-interface-sdk";
import {AutoWired} from "@groupk/horizon2-core";
import SidebarStateListener from "../../../../../shared/utils/SidebarStateListener";
import {
	ApplicationPermission,
	CashlessHttpCurrencyContract, CashlessHttpEstablishmentContract,
	CashlessHttpKioskFundingContract,
	EstablishmentAccountApiOut,
	EstablishmentAccountPermission,
	EstablishmentAccountPermissionModel,
	EstablishmentAccountPermissionsApiIn,
	MastodonEstablishmentAccountContractAggregate,
	MastodonPermissions
} from "@groupk/mastodon-core";
import {EstablishmentAccountRepository} from "../../../../../shared/repositories/EstablishmentAccountRepository";
import {PaymentPermissions} from "@groupk/mastodon-core";
import {CashlessAppId, CashlessPermissions} from "@groupk/mastodon-core";
import EstablishmentAccountFormComponent
	, {
	EstablishmentAccountFormComponentHasRequiredPermissions
} from "../../components/EstablishmentAccountFormComponent/EstablishmentAccountFormComponent.vue";
import {FormModalOrDrawerComponent} from "@groupk/vue3-interface-sdk";
import {CashlessEstablishmentRepository} from "../../../../../shared/repositories/CashlessEstablishmentRepository";
import {AppBus} from "../../config/AppBus";
import ToastManagerComponent from "../../components/ToastManagerComponent/ToastManagerComponent.vue";
import {CashlessEstablishmentRefundApi} from "@groupk/mastodon-core";
import {
	CashlessFrontAppId, CashlessFrontPermissions,
	CashlessFrontPermissionsTranslations
} from "../../../../../shared/mastodonCoreFront/cashless/CashlessFrontPermissions";
import {CleaveDirective} from "../../../../../shared/directives/CleaveDirective";
import {CashlessEstablishmentData} from "../../../../../shared/mastodonCoreFront/cashless/CashlessEstablishmentData";
import {AppState} from "../../../../../shared/AppState";
import {ComponentUtils} from "../../../../../shared/utils/ComponentUtils";
import SettingsAccountsComponent
	from "../../../../../shared/components/SettingsAccountsComponent/SettingsAccountsComponent.vue";

@Component({
	directives: {
		cleave: CleaveDirective,
	},
	components: {
		'left-navigation': LeftNavigationLayoutComponent,
		'layout': LayoutContentWithRightPanelComponent,
		'dropdown': DropdownComponent,
		'form-modal-or-drawer': FormModalOrDrawerComponent,
		'establishment-account-form': EstablishmentAccountFormComponent,
		'input-date': InputDateComponent,
		'toast-manager': ToastManagerComponent,
		'forbidden-message': ForbiddenMessageComponent,
		'accounts-settings': SettingsAccountsComponent,
	}
})
export default class SettingsView extends Vue {

	cashlessEstablishment!: CashlessEstablishmentData;
	establishmentAccounts: EstablishmentAccountApiOut[] = [];
	selectedEstablishmentAccount: EstablishmentAccountApiOut|null = null;
	currentPage: string = 'partners';

	showAccountCreationModal: boolean = false;
	updatingAccount: boolean = false;
	updateError: string|null = null;
	loading: boolean = true;
	savingCashlessEstablishment: boolean = false;

	selectedRole: string|null = null;
	roles: {id: string|null, title: string, description: string}[] = [{
		id: null,
		title: 'Aucun',
		description: 'Aucun accès, ni en lecture ni en écriture'
	}, {
		id: 'readonly',
		title: 'Lecture seule',
		description: 'Donne accès à toutes les données en lecture mais bloque toute modification ou ajout.'
	}, {
		id: 'receptionist',
		title: 'Réceptionniste',
		description: 'Accès complet en lecture et écriture'
	}]

	showRoleDetailsModal: string|null = null;

	navigation: LeftNavigationItem[] = [{
		id: 'partners',
		title: 'Comptes',
		subtitle: 'Ajouter des collaborateurs',
		icon: 'fa-regular fa-key fa-fw'
	}, {
		id: 'global',
		title: 'Cashless',
		subtitle: 'Paramètres Cashless',
		icon: 'fa-regular fa-cog fa-fw'
	}];

	EstablishmentAccountPermissionModel = EstablishmentAccountPermissionModel;

	CashlessAppId = CashlessAppId;
	CashlessFrontAppId = CashlessFrontAppId;
	CashlessPermissions = CashlessPermissions;
	CashlessFrontPermissions = CashlessFrontPermissions;
	permissionTranslations = CashlessFrontPermissionsTranslations;

	permissionModel!: EstablishmentAccountPermissionModel;

	cleaveDate = {
		date: true,
		delimiter: '/',
		datePattern: ['d', 'm', 'Y']
	}

	refundErrors: Record<string, string> = {};

	disableCreation: boolean = false;
	forbidden: boolean = false;

	@AutoWired(EstablishmentAccountRepository) accessor establishmentAccountRepository!: EstablishmentAccountRepository;
	@AutoWired(CashlessEstablishmentRepository) accessor cashlessEstablishmentRepository!: CashlessEstablishmentRepository;
	@AutoWired(AppState) accessor appState!: AppState;
	@AutoWired(SidebarStateListener) accessor sidebarStateListener!: SidebarStateListener;
	@AutoWired(AppBus) accessor appBus!: AppBus;

	beforeMount() {
		this.sidebarStateListener.setHiddenSidebar(false);
		this.sidebarStateListener.setMinimizedSidebar(true);

		this.permissionModel = new EstablishmentAccountPermissionModel();
		this.permissionModel.registerBackPermissionContract(MastodonPermissions);
		this.permissionModel.registerBackPermissionContract(PaymentPermissions);
		this.permissionModel.registerBackPermissionContract(CashlessPermissions);
		this.permissionModel.registerFrontPermissionContract(CashlessFrontPermissions);
	}

	async mounted() {
		if(!ComponentUtils.hasPermissions((ownedPermissions: ApplicationPermission[]) => {
			return EstablishmentAccountPermissionModel.hasPermissionsForContracts(ownedPermissions, [
				MastodonEstablishmentAccountContractAggregate.list,
				CashlessHttpEstablishmentContract.get,
			])
		})) {
			this.forbidden = true;
			this.loading = false;
			return;
		}

		if(!ComponentUtils.hasPermissions(EstablishmentAccountFormComponentHasRequiredPermissions)) {
			this.disableCreation = true;
		}

		this.establishmentAccounts = (await this.establishmentAccountRepository.callContract('list', {establishmentUid: this.appState.requireUrlEstablishmentUid()}, undefined)).success();
		this.cashlessEstablishment = new CashlessEstablishmentData((await this.cashlessEstablishmentRepository.callContract('get', {establishmentUid: this.appState.requireUrlEstablishmentUid()}, undefined)).success());
		this.loading = false;
	}

	toggleSelectedAccount(establishmentAccount: EstablishmentAccountApiOut) {
		this.selectedEstablishmentAccount = establishmentAccount;
		this.selectedRole = this.getRole(this.selectedEstablishmentAccount);
		this.updateError = null;
	}

	getRole(establishmentAccount: EstablishmentAccountApiOut): string|null {
		for(let role of CashlessFrontPermissions.roles) {
			for(let permission of establishmentAccount.roles) {
				const roles = CashlessFrontPermissions.roles.map((role) => role.id) as string[];
				if(permission.application === CashlessFrontPermissions.applicationId && roles.includes(permission.name)) {
					return permission.name;
				}
			}
		}
		return null;
	}

	async updateAccountRole(establishmentAccount: EstablishmentAccountApiOut) {
		this.updatingAccount = true;
		this.updateError = null;
		const permissionApiIn = new EstablishmentAccountPermissionsApiIn({});

		for(let permission of establishmentAccount.roles) {
			if(this.permissionModel.doesRoleExist(permission.application, permission.name)) {
				permissionApiIn.rolesToRemove.push(permission);
			}
		}

		if(this.selectedRole) {
			permissionApiIn.rolesToAdd.push(new EstablishmentAccountPermission({
				application: CashlessFrontPermissions.applicationId,
				name: this.selectedRole
			}));
		}

		const response = await this.establishmentAccountRepository.callContract('update_permissions', {
			establishmentUid: this.appState.requireUrlEstablishmentUid(),
			establishmentAccountUid: establishmentAccount.uid
		}, permissionApiIn);

		if(response.isSuccess()) {
			const updatedEstablishmentAccount = response.success();
			const index = this.establishmentAccounts.findIndex((account) => account.uid === updatedEstablishmentAccount.uid);
			if(index !== -1) this.establishmentAccounts.splice(index, 1, updatedEstablishmentAccount);

			this.selectedEstablishmentAccount = null;
			this.selectedRole = null;
		} else if(response.isError()) {
			const error = response.error();
			if(error) {
				this.updateError = 'Le rôle semble mal configuré';
			} else {
				this.updateError = 'Une erreur inconnue est survenue';
			}
		}

		this.updatingAccount = false;
	}

	createdAccount(account: EstablishmentAccountApiOut) {
		this.establishmentAccounts.push(account);
		this.showAccountCreationModal = false;
	}

	addAdminChip() {
		if(!this.cashlessEstablishment.adminPublicChipIds) this.cashlessEstablishment.adminPublicChipIds = [];
		this.cashlessEstablishment.adminPublicChipIds.push('');
	}

	setAdminPublicChip(event: Event, index: number) {
		if(!this.cashlessEstablishment.adminPublicChipIds) return;
		if(event.target && event.target instanceof HTMLInputElement){
			this.cashlessEstablishment.adminPublicChipIds[index] = event.target.value.toUpperCase();
		}
	}

	async saveCashlessEstablishment() {
		this.refundErrors = {};

		if(this.cashlessEstablishment.refunds) {
			if(this.cashlessEstablishment.refunds.startingDatetime.length < 10) {
				this.refundErrors['startingDatetime'] = 'La date est invalide';
			}
			if(this.cashlessEstablishment.refunds.endingDatetime.length < 10) {
				this.refundErrors['endingDatetime'] = 'La date est invalide';
			}
		}

		if(Object.keys(this.refundErrors).length > 0) return;

		this.savingCashlessEstablishment = true;

		const response = await this.cashlessEstablishmentRepository.callContract('update', {establishmentUid: this.appState.requireUrlEstablishmentUid()}, this.cashlessEstablishment.toApi())
		if(response.isSuccess()) {
			this.appBus.emit('emit-toast', {
				title: 'Sauvegarde réussie',
				description: 'Les paramètres on bien été appliqués',
				duration: 3000,
				type: 'SUCCESS',
				closable: true
			});
		} else {
			const error = response.error();
			this.appBus.emit('emit-toast', {
				title: 'La sauvegarde à échoué',
				description: error && 'error' in error ? error.error : 'Une erreur inconnue est survenue',
				duration: 3000,
				type: 'SUCCESS',
				closable: true
			});
		}

		this.savingCashlessEstablishment = false;
	}

	enableRefunds() {
		this.cashlessEstablishment.refunds = new CashlessEstablishmentRefundApi({
			endingDatetime: '',
			startingDatetime: '',
			feesAmount: 0,
			minAmount: 0,
			nonRefundableCreditPaymentMethodList: undefined
		})
	}
}