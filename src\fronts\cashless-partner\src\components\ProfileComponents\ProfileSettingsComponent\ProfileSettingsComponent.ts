import {Component, Prop, Vue} from "vue-facing-decorator";
import {
	DropdownComponent, DropdownValue, FormModalOrDrawerComponent,
	HoverableInfosComponent,
	SelectMultipleDropdownComponent, ToggleComponent,
} from "@groupk/vue3-interface-sdk";
import {
	PaymentMethodApiOut,
	PaymentMethodSettings_type,
	PaymentMethodSettingsCaisseAp,
	PaymentMethodSettingsCashKeeper,
	PaymentMethodSettingsPhysicalCashlessApp2App,
	PaymentMethodSettingsPhysicalNeptingApp2App,
	PaymentMethodSettingsPhysicalPaxFrV6App2App,
	PaymentProtocol,
	ProfilePaymentMethodApi,
	ProfileTerminalProductPaymentMethod, SimpleProductApiOut,
	uuidScopeCashless_chipUid, uuidScopeIot_deviceApp,
	uuidScopePayment_method
} from "@groupk/mastodon-core";
import {AutoWired, ScopedUuid, UuidUtils, VisualScopedUuid} from "@groupk/horizon2-core";
import {ProfilesRepository} from "../../../../../../shared/repositories/ProfilesRepository";
import {AppState} from "../../../../../../shared/AppState";
import {AppBus} from "../../../config/AppBus";
import ToastManagerComponent from "../../ToastManagerComponent/ToastManagerComponent.vue";
import {ProfileData} from "../../../../../../shared/mastodonCoreFront/cashless/ProfileData";
import {ProfileTerminalKeypadData} from "../../../../../../shared/mastodonCoreFront/cashless/ProfileTerminalKeypadData";
import {ProfilePosData} from "../../../../../../shared/mastodonCoreFront/cashless/ProfilePosData";
import {ProfileKioskData} from "../../../../../../shared/mastodonCoreFront/cashless/ProfileKioskData";
import {
	ProfileTerminalProductData
} from "../../../../../../shared/mastodonCoreFront/cashless/ProfileTerminalProductData";
import {UuidScopePayment_method} from "@groupk/mastodon-core";
import {UuidScopeCashless_chipUid} from "@groupk/mastodon-core";
import {ProfileKioskProductData} from "../../../../../../shared/mastodonCoreFront/cashless/ProfileKioskProductData";
import {
	ProfileKioskProductItemData
} from "../../../../../../shared/mastodonCoreFront/cashless/ProfileKioskProductItemData";
import {IotDeviceConfigShellySwitch} from "@groupk/mastodon-core";
import StringOrNullInputComponent
	from "../../../../../../shared/components/StringOrNullInputComponent/StringOrNullInputComponent.vue";

@Component({
	components: {
		'dropdown': DropdownComponent,
		'toggle': ToggleComponent,
		'hoverable-info': HoverableInfosComponent,
		'select-multiple-dropdown': SelectMultipleDropdownComponent,
		'toast-manager': ToastManagerComponent,
		'form-modal-or-drawer': FormModalOrDrawerComponent,
		'string-or-null-input': StringOrNullInputComponent,
	}
})
export default class ProfileSettingsComponent extends Vue {
	@Prop({required: true}) profile!: ProfileData;
	@Prop({required: true}) paymentMethods!: PaymentMethodApiOut[];
	@Prop({required: true}) products!: SimpleProductApiOut[];

	showPaymentMethodModal: { paymentMethodUid: string, virtualChipUid: string }|null = null;
	editingPaymentMethod: ProfileTerminalProductPaymentMethod|null = null;
	paymentMethodModalError: string|null = null;

	showProductKioskModal: ProfileKioskProductItemData|null = null;
	productKioskModalError: string|null = null;

	showPaymentMethodConfigModal: {
		paymentMethodUid: string, 
		selectedSettingsType: PaymentProtocol | null,
		settings: {
			name: string,
			// CaisseAp
			ip?: string,
			port?: number,
			// CashKeeper
			securitySeed?: number,
			address?: string,
			masterPort?: number,
			officePort?: number,
			// NeptingApp2App
			webserviceUrl?: string,
			merchantCode?: string,
			storeId?: string,
			terminalType?: number,
			loginInterval?: number,
		}
	}|null = null;
	editingPaymentMethodConfig: ProfilePaymentMethodApi|null = null;
	paymentMethodConfigModalError: string|null = null;

	saving: boolean = false;

	IotDeviceConfigShellySwitch = IotDeviceConfigShellySwitch;

	@AutoWired(ProfilesRepository) accessor profileRepository!: ProfilesRepository;
	@AutoWired(AppState) accessor appState!: AppState;
	@AutoWired(AppBus) accessor appBus!: AppBus;

	get paymentMethodsDropdownValues() {
		return this.paymentMethods.map((method) =>  {
			return {
				name: method.name,
				value: method.uid
			}
		})
	}

	get paymentMethodsScopedDropdownValues() {
		return this.paymentMethods.map((method) =>  {
			return {
				name: method.name,
				value: UuidUtils.visualToScoped(method.uid)
			}
		})
	}

	get settingsTypeDropdownValues() {
		return [
			{ name: 'CaisseAp', value: PaymentProtocol.PHYSICAL_CAISEAP },
			{ name: 'CashKeeper', value: PaymentProtocol.PHYSICAL_CASHKEEPER },
			{ name: 'Nepting App2App', value: PaymentProtocol.PHYSICAL_NEPTING_APP2APP },
			{ name: 'PAX FR V6 App2App', value: PaymentProtocol.PHYSICAL_PAXFRV6_APP2APP },
			{ name: 'Cashless App2App', value: PaymentProtocol.PHYSICAL_CASHLESS_APP2APP },
		];
	}

	async updateProfile() {
		if(!this.profile.uid) return;

		this.saving = true;

		try {
			await this.profileRepository.callContract(
				'update',
				{establishmentUid: this.appState.requireUrlEstablishmentUid(), deviceProfileUid: this.profile.uid},
				this.profile.toApiIn()
			);

			this.appBus.emit('emit-toast', {
				title: 'Sauvegarde réussie',
				description: 'Le point de vente a bien été mis à jour',
				duration: 3000,
				type: 'SUCCESS',
				closable: true
			});
		} catch(err) {
			this.appBus.emit('emit-toast', {
				title: 'La sauvegarde a échoué',
				description: 'Le point de vente n\'a pas pû être sauvegardé correctement',
				duration: 3000,
				type: 'ERROR',
				closable: true
			});
		}
		this.saving = false;
	}

	addDebitKeypad() {
		this.profile.terminalKeypadDebit = new ProfileTerminalKeypadData();
	}

	addCreditKeypad() {
		this.profile.terminalKeypadCredit = new ProfileTerminalKeypadData();
	}

	addPos() {
		this.profile.pos = new ProfilePosData();
		this.profile.allowDevicePosManaged = true;
	}

	addKiosk() {
		this.profile.kiosk = new ProfileKioskData();
		this.profile.allowDeviceKiosk = true;
	}

	addKioskProduct() {
		this.profile.kioskProduct = new ProfileKioskProductData();
		this.profile.allowKioskProduct = true;
	}

	showProductToKioskModal() {
		if(!this.profile.kioskProduct) return;
		this.showProductKioskModal = new ProfileKioskProductItemData();
	}

	addProductToKiosk(product: ProfileKioskProductItemData) {
		if(!product.productId) {
			this.productKioskModalError = 'Veuillez sélectionner un produit';
			return;
		}

		if(product.iotDeviceUid && !UuidUtils.isVisual(product.iotDeviceUid, uuidScopeIot_deviceApp)) {
			this.productKioskModalError = 'L\'uid de l\'appareil est invalide';
			return;
		}

		if(!this.profile.kioskProduct) return;
		this.profile.kioskProduct.products.push(product);
		this.showProductKioskModal = null;
	}

	removeProductFromKiosk(index: number) {
		if(!this.profile.kioskProduct) return;
		this.profile.kioskProduct.products.splice(index, 1);
	}

	initializeIotDeviceConfig(product: ProfileKioskProductItemData) {
		product.iotDeviceConfig = new IotDeviceConfigShellySwitch({
			number: 1,
			on: false,
			toggle: 0
		});
	}

	setAdminPublicChip(event: Event, index: number) {
		if(event.target && event.target instanceof HTMLInputElement){
			this.profile.adminPublicChipIds[index] = event.target.value.toUpperCase();
		}
	}

	requireTerminalProduct() {
		if(!this.profile.terminalProduct) {
			this.profile.terminalProduct = new ProfileTerminalProductData();
		}

		return this.profile.terminalProduct;
	}

	toggleShowPaymentMethodModal() {
		this.editingPaymentMethod = null;
		this.showPaymentMethodModal = {
			paymentMethodUid: '',
			virtualChipUid: ''
		}
	}

	editPaymentMethod(paymentMethod: ProfileTerminalProductPaymentMethod) {
		this.editingPaymentMethod = paymentMethod;
		this.showPaymentMethodModal = {
			paymentMethodUid: paymentMethod.paymentMethodUid as string,
			virtualChipUid: paymentMethod.virtualChipUid as string
		}
	}

	deletePaymentMethod(paymentMethod: ProfileTerminalProductPaymentMethod) {
		const terminalProduct = this.requireTerminalProduct();
		if (terminalProduct.paymentMethodList) {
			const index = terminalProduct.paymentMethodList.indexOf(paymentMethod);
			if (index !== -1) {
				terminalProduct.paymentMethodList.splice(index, 1);
			}
		}
	}

	validatePaymentMethodModal() {
		if(!this.showPaymentMethodModal) return;

		if(!UuidUtils.isVisual(this.showPaymentMethodModal.paymentMethodUid, uuidScopePayment_method)) {
			this.paymentMethodModalError = 'L\'uid de la méthode de paiement est invalide';
			return;
		}

		if(!UuidUtils.isVisual(this.showPaymentMethodModal.virtualChipUid, uuidScopeCashless_chipUid)) {
			this.paymentMethodModalError = 'L\'uid de la puce virtuelle est invalide';
			return;
		}

		const terminalProduct = this.requireTerminalProduct();
		if(!terminalProduct.paymentMethodList) {
			terminalProduct.paymentMethodList = []
		}

		if (this.editingPaymentMethod) {
			// Update existing payment method
			this.editingPaymentMethod.paymentMethodUid = this.showPaymentMethodModal.paymentMethodUid as VisualScopedUuid<UuidScopePayment_method>;
			this.editingPaymentMethod.virtualChipUid = this.showPaymentMethodModal.virtualChipUid as VisualScopedUuid<UuidScopeCashless_chipUid>;
			this.editingPaymentMethod = null;
		} else {
			// Add new payment method
			terminalProduct.paymentMethodList.push(new ProfileTerminalProductPaymentMethod({
				paymentMethodUid: this.showPaymentMethodModal.paymentMethodUid as VisualScopedUuid<UuidScopePayment_method>,
				virtualChipUid: this.showPaymentMethodModal.virtualChipUid as VisualScopedUuid<UuidScopeCashless_chipUid>,
			}));
		}

		this.showPaymentMethodModal = null;
	}

	toggleShowPaymentMethodConfigModal() {
		this.editingPaymentMethodConfig = null;
		this.showPaymentMethodConfigModal = {
			paymentMethodUid: '',
			selectedSettingsType: null,
			settings: {
				name: '',
			}
		}
	}

	editPaymentMethodConfig(paymentMethodConfig: ProfilePaymentMethodApi) {
		this.editingPaymentMethodConfig = paymentMethodConfig;

		type SettingsType = {
			name: string;
			ip?: string;
			port?: number;
			securitySeed?: number;
			address?: string;
			masterPort?: number;
			officePort?: number;
			webserviceUrl?: string;
			merchantCode?: string;
			storeId?: string;
			terminalType?: number;
			loginInterval?: number;
		}

		const settings: SettingsType = {
			name: paymentMethodConfig.settings?.name || '',
		};

		// Populate settings based on the type
		if (paymentMethodConfig.settings) {
			const protocol = paymentMethodConfig.settings.protocol;

			if (protocol === PaymentProtocol.PHYSICAL_CAISEAP) {
				const caisseApSettings = paymentMethodConfig.settings as PaymentMethodSettingsCaisseAp;
				settings.ip = caisseApSettings.ip;
				settings.port = caisseApSettings.port;
			} else if (protocol === PaymentProtocol.PHYSICAL_CASHKEEPER) {
				const cashKeeperSettings = paymentMethodConfig.settings as PaymentMethodSettingsCashKeeper;
				settings.securitySeed = cashKeeperSettings.securitySeed;
				settings.address = cashKeeperSettings.address;
				settings.masterPort = cashKeeperSettings.masterPort;
				settings.officePort = cashKeeperSettings.officePort;
			} else if (protocol === PaymentProtocol.PHYSICAL_NEPTING_APP2APP) {
				const neptingSettings = paymentMethodConfig.settings as PaymentMethodSettingsPhysicalNeptingApp2App;
				settings.webserviceUrl = neptingSettings.webserviceUrl;
				settings.merchantCode = neptingSettings.merchantCode;
				settings.storeId = neptingSettings.storeId;
				settings.terminalType = neptingSettings.terminalType;
				settings.loginInterval = neptingSettings.loginInterval;
			}
		}

		this.showPaymentMethodConfigModal = {
			paymentMethodUid: paymentMethodConfig.paymentMethodUid as string,
			selectedSettingsType: paymentMethodConfig.settings?.protocol || null,
			settings
		}
	}

	deletePaymentMethodConfig(paymentMethodConfig: ProfilePaymentMethodApi) {
		if (this.profile.paymentMethodConfigs) {
			const index = this.profile.paymentMethodConfigs.indexOf(paymentMethodConfig);
			if (index !== -1) {
				this.profile.paymentMethodConfigs.splice(index, 1);
			}
		}
	}

	get productsDropdownValues(): DropdownValue[] {
		return this.products.map((product) => {
			return {
				name: product.name,
				value: product.id
			}
		});
	}

	validatePaymentMethodConfigModal() {
		if(!this.showPaymentMethodConfigModal) return;

		if(!UuidUtils.isScoped(this.showPaymentMethodConfigModal.paymentMethodUid, uuidScopePayment_method)) {
			this.paymentMethodConfigModalError = 'L\'uid de la méthode de paiement est invalide';
			return;
		}

		if(!this.showPaymentMethodConfigModal.selectedSettingsType) {
			this.paymentMethodConfigModalError = 'Veuillez sélectionner un type de configuration';
			return;
		}

		if(!this.showPaymentMethodConfigModal.settings.name) {
			this.paymentMethodConfigModalError = 'Le nom est obligatoire';
			return;
		}

		// Create settings object based on the selected type
		let settings: PaymentMethodSettings_type;
		const { name } = this.showPaymentMethodConfigModal.settings;

		switch(this.showPaymentMethodConfigModal.selectedSettingsType) {
			case PaymentProtocol.PHYSICAL_CAISEAP:
				if(!this.showPaymentMethodConfigModal.settings.ip) {
					this.paymentMethodConfigModalError = 'L\'adresse IP est obligatoire';
					return;
				}
				if(!this.showPaymentMethodConfigModal.settings.port) {
					this.paymentMethodConfigModalError = 'Le port est obligatoire';
					return;
				}
				settings = new PaymentMethodSettingsCaisseAp({
					name,
					ip: this.showPaymentMethodConfigModal.settings.ip,
					port: this.showPaymentMethodConfigModal.settings.port
				});
				break;
			case PaymentProtocol.PHYSICAL_CASHKEEPER:
				if(!this.showPaymentMethodConfigModal.settings.securitySeed) {
					this.paymentMethodConfigModalError = 'Le security seed est obligatoire';
					return;
				}
				if(!this.showPaymentMethodConfigModal.settings.address) {
					this.paymentMethodConfigModalError = 'L\'adresse est obligatoire';
					return;
				}
				if(!this.showPaymentMethodConfigModal.settings.masterPort) {
					this.paymentMethodConfigModalError = 'Le master port est obligatoire';
					return;
				}
				if(!this.showPaymentMethodConfigModal.settings.officePort) {
					this.paymentMethodConfigModalError = 'L\'office port est obligatoire';
					return;
				}
				settings = new PaymentMethodSettingsCashKeeper({
					name,
					securitySeed: this.showPaymentMethodConfigModal.settings.securitySeed,
					address: this.showPaymentMethodConfigModal.settings.address,
					masterPort: this.showPaymentMethodConfigModal.settings.masterPort,
					officePort: this.showPaymentMethodConfigModal.settings.officePort
				});
				break;
			case PaymentProtocol.PHYSICAL_NEPTING_APP2APP:
				settings = new PaymentMethodSettingsPhysicalNeptingApp2App({
					name,
					webserviceUrl: this.showPaymentMethodConfigModal.settings.webserviceUrl,
					merchantCode: this.showPaymentMethodConfigModal.settings.merchantCode,
					storeId: this.showPaymentMethodConfigModal.settings.storeId,
					terminalType: this.showPaymentMethodConfigModal.settings.terminalType,
					loginInterval: this.showPaymentMethodConfigModal.settings.loginInterval
				});
				break;
			case PaymentProtocol.PHYSICAL_PAXFRV6_APP2APP:
				settings = new PaymentMethodSettingsPhysicalPaxFrV6App2App({
					name
				});
				break;
			case PaymentProtocol.PHYSICAL_CASHLESS_APP2APP:
				settings = new PaymentMethodSettingsPhysicalCashlessApp2App({
					name
				});
				break;
			default:
				this.paymentMethodConfigModalError = 'Type de configuration non supporté';
				return;
		}

		if(!this.profile.paymentMethodConfigs) {
			this.profile.paymentMethodConfigs = []
		}

		if (this.editingPaymentMethodConfig) {
			// Update existing payment method config
			this.editingPaymentMethodConfig.paymentMethodUid = this.showPaymentMethodConfigModal.paymentMethodUid as ScopedUuid<UuidScopePayment_method>;
			this.editingPaymentMethodConfig.settings = settings;
			this.editingPaymentMethodConfig = null;
		} else {
			// Add new payment method config
			this.profile.paymentMethodConfigs.push(new ProfilePaymentMethodApi({
				paymentMethodUid: this.showPaymentMethodConfigModal.paymentMethodUid as ScopedUuid<UuidScopePayment_method>,
				settings
			}));
		}

		this.showPaymentMethodConfigModal = null;
	}

	requireProductWithId(productId: number) {
		const product = this.products.find((product) => product.id === productId);
		if(!product) throw new Error('missing_product');
		return product;
	}
}
