import type {Component} from "@vue/runtime-core";
import type {VueRouteOptions} from "@groupk/horizon2-front";
import type {Router, RouterRouteModuleLoader} from "@groupk/horizon2-front";
import {RouterRouteLoadedDescriptor} from "@groupk/horizon2-front";
import {AppVueJsRoute} from "../../../shared/routing/AppVueJsRoute";
import {CashlessManualDataLSKey} from "./components/CustomerInfoFormComponent/CustomerInfoFormComponent";
import {GetInstance, ScopedUuid, UuidUtils} from "@groupk/horizon2-core";
import SpecificState from "./SpecificState";
import {AppState} from "../../../shared/AppState";
import {uuidScopeEstablishment, UuidScopeEstablishment} from "@groupk/mastodon-core";
import {CashlessCustomerRepository} from "../../../shared/repositories/CashlessCustomerRepository";

class SpecificAppVueJsRoute<TState> extends AppVueJsRoute<TState> {
    async load(router: Router, state: TState | undefined): Promise<RouterRouteLoadedDescriptor|void|undefined> {
        const appState = GetInstance(AppState);

        let regexMatch = router.lastRouteRegexMatches;
        if (regexMatch && regexMatch[1]) {
            appState.establishmentUid = UuidUtils.scopedToVisual(regexMatch[1] as ScopedUuid<UuidScopeEstablishment>, uuidScopeEstablishment);
        }

        const rawManualData = localStorage.getItem(CashlessManualDataLSKey);
        if(rawManualData) {
            const specificState = GetInstance(SpecificState);
            const cashlessCustomerRepository = GetInstance(CashlessCustomerRepository);

            const manualData = JSON.parse(rawManualData);
            specificState.customer = {
                firstname: manualData.firstname,
                lastname: manualData.lastname,
                email: manualData.email
            }

            try {
                const wallet = (await cashlessCustomerRepository.callContract('getWallet', {
                    establishmentUid: appState.requireUrlEstablishmentUid(),
                    chipVisualId: manualData.idPublic
                }, undefined)).success();

                specificState.currentWallet = wallet.item;
            } catch(err) {

            }
        } else {
            const urlParams = new URLSearchParams(window.location.search);
            if(urlParams.has('chipId')) {
                const cashlessCustomerRepository = GetInstance(CashlessCustomerRepository);
                const specificState = GetInstance(SpecificState);

                const wallet = (await cashlessCustomerRepository.callContract('getWallet', {
                    establishmentUid: appState.requireUrlEstablishmentUid(),
                    chipVisualId: urlParams.get('chipId')!
                }, undefined)).success();

                specificState.currentWallet = wallet.item;
            }
        }

        return super.load(router, state);
    }
}

export function VueSpecificAppRouteFactory<TState>(rootComponent: Component, userOptions: Partial<VueRouteOptions> = {}): RouterRouteModuleLoader<TState> {
    return new SpecificAppVueJsRoute(rootComponent, userOptions);
}
