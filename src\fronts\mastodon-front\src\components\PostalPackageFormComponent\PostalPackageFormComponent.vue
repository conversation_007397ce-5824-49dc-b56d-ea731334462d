<script lang="ts" src="./PostalPackageFormComponent.ts" />

<style lang="sass" scoped>
@import './PostalPackageFormComponent.scss'
</style>

<template>
    <div class="postal-package-form-component">
        <form-modal-or-drawer
            :state="opened"
            :title="editingPostalPackage ? 'Modifier le tracking du colis' : 'Ajouter un tracking de colis'"
            :subtitle="editingPostalPackage ? 'Modifier le tracking du colis' : 'Ajouter un tracking de colis'"
            @close="close()"
        >
            <template v-slot:content>
                <div class="input-group">
                    <label> Transporteur </label>

                    <div class="fluid input">
                        <dropdown
                            :values="getTransporterProviderDropdownValues()"
                            :searchable="true"
                            :default-selected="postalPackage.transportProvider"
                            placeholder="Transporteur"
                            @update="postalPackage.transportProvider = $event"
                        ></dropdown>
                    </div>
                </div>

                <div class="input-group">
                    <label> ID de tracking </label>

                    <div class="fluid input">
                        <string-or-null-input :value="postalPackage.trackingId" @change="postalPackage.trackingId = $event"></string-or-null-input>
                    </div>
                </div>

                <div class="input-group">
                    <label> Commentaire </label>

                    <div class="fluid input">
                        <input v-model="postalPackage.comment" class="fluid" placeholder="Commentaire" />
                    </div>
                </div>
            </template>

            <template v-slot:buttons>
                <button type="button" class="white button" @click="close()"> Annuler </button>
                <button type="button" class="button" :class="{loading: saving, disabled: saving}" @click="create()">
                    <i v-if="!editingPostalPackage" class="fa-regular fa-circle-plus"></i>
                    <i v-else class="fa-regular fa-circle-plus" ></i>
                    {{ editingPostalPackage ? 'Modifier le tracking' : 'Créer le tracking' }}
                </button>
            </template>
        </form-modal-or-drawer>
    </div>
</template>