import {OrderPaymentApiOut} from "@groupk/mastodon-core";
import {AutoWired, BufferUtils, EventEmitter, Uuid, UuidUtils} from "@groupk/horizon2-core";
import {PhysicalPaymentProtocol} from "./PhysicalPaymentProtocol";
import {
	ActionStatus,
	createPaymentRequest,
	decodePaymentResult,
	PaymentRequest
} from "@groupk/caisse-protocol";
import {PaymentMethodSettingsCaisseAp} from "@groupk/mastodon-core";
import {CommunicationKnownTagContainer} from "@groupk/caisse-protocol";
import {CONCERT_DESCRIPTORS} from "@groupk/caisse-protocol";
import {SocketNative} from "@groupk/native-bridge";

export interface CashlessProtocolCaissePrivateFieldRequest {
	forceSync?: boolean|undefined;
	expectedChip?: number|undefined;
	identifyDelay?: number|undefined;
	internalTxId?: Uuid|undefined;
}
// export interface CashlessProtocolCaissePrivateFieldResponse {
// 	tx?: ScopedUuid<UuidScopeCashlessTransaction>;
// 	chip?: number;
// 	balance?: number;
// }

export class CaisseApPaymentProcessor extends EventEmitter<{
	'totalPaidChanged': number,
	'totalRefundChanged': number,
	'done': boolean
}> implements PhysicalPaymentProtocol {
	settings: PaymentMethodSettingsCaisseAp;

	config = {
		displayLivePaidAmount: false,
		autoCloseOnSuccess: false,
		cancelable: false
	};

	@AutoWired(SocketNative) accessor nativeSocket!: SocketNative;

	constructor(settings: PaymentMethodSettingsCaisseAp) {
		super();
		this.settings = settings;
	}

	async initiatePayment(payment: OrderPaymentApiOut) {
		const tcpWrapper = await this.nativeSocket.tcpConnectWrapper(this.settings.ip, this.settings.port);

		let receivedResponse: boolean = false;
		tcpWrapper.on('connected', () => {
			console.log('connected');
			const caisseApPayment: PaymentRequest = {
				currency: '978',
				amount: payment.amount,
				// delayWaitPayment: 60,
				manualTags: [
					new CommunicationKnownTagContainer(CONCERT_DESCRIPTORS.CF, JSON.stringify({
						internalTxId: UuidUtils.visualToUuid(payment.uid),
					} satisfies CashlessProtocolCaissePrivateFieldRequest))
				]
			}

			const timeout = setTimeout(async () => {
				try {
					tcpWrapper.close();
				} catch(err) {
					console.log(err);
				}
			}, (caisseApPayment.delayWaitPayment ?? 60)*1000 + 30000);

			tcpWrapper.on('data', (bytes) => {
				console.log('data', bytes);
				receivedResponse = true;
				let response: string = String.fromCharCode(...(typeof bytes === 'string' ? BufferUtils.hex2Buffer(bytes) : bytes));
				try {
					const result = decodePaymentResult(response);
					if(result.status === ActionStatus.OperationDone) {
						this.emit('done', true);
					} else {
						this.emit('done', false);
					}
				} catch(err) {
					console.log(err);
					this.emit('done', false);
				}

				clearTimeout(timeout);
				tcpWrapper.close();
			});

			const data = createPaymentRequest(caisseApPayment);
			let payload: number[] = [];
			for (let i = 0; i < data.length; i++) {
				payload.push(data.charCodeAt(i));
			}
			tcpWrapper.send(payload);
		});

		tcpWrapper.on('closed', () => {
			console.log('closed');
			if(!receivedResponse) this.emit('done', false);
		});
	}

	cancel() {
	}
}
