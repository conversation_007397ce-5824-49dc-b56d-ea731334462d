import {Component, Prop, Vue} from "vue-facing-decorator";
import {AutoWired} from "@groupk/horizon2-core";
import {PosState} from "../../model/PosState";

@Component({})
export default class MobileBottomCartComponent extends Vue {
	@Prop() opened!: boolean;
	@Prop() posState!: PosState;

	get getOrderLeftToPay(){
		if(!this.posState.currentOrder) return 0;
		return this.posState.orderExecutorModel.getOrderTotals(this.posState.currentOrder.order).leftToPay;
	}

	get getPurchasesQuantity(){
		if(!this.posState.currentOrder) return 0;
		let quantity = 0;
		for(let purchase of this.posState.currentOrder.order.purchases) {
			for(let item of purchase.items) {
				quantity += item.quantity;
			}
		}
		return quantity;
	}

}