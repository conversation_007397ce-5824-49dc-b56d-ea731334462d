import {Component, Prop, Vue} from "vue-facing-decorator";
import {FormModalOrDrawerComponent, ModalOrDrawerComponent} from "@groupk/vue3-interface-sdk";
import {
	SimpleProductApiOut,
	SimpleProductCategoryApiIn,
	SimpleProductCategoryApiOut
} from "@groupk/mastodon-core";
import {AutoWired} from "@groupk/horizon2-core";
import {CategoriesRepository} from "../../../../../shared/repositories/CategoriesRepository";
import {AppState} from "../../../../../shared/AppState";

@Component({
	components: {
		'form-modal-or-drawer': FormModalOrDrawerComponent,
		'modal-or-drawer': ModalOrDrawerComponent,
	},
	emits: ['created', 'updated']
})
export default class ImportProductInCategoryComponent extends Vue {
	@Prop() category!: SimpleProductCategoryApiOut;
	@Prop() products!: SimpleProductApiOut[];

	opened: boolean = false;
	showModalVerification: boolean = false;
	importing: boolean = false;
	error: string|null = null;

	importProductsNameSplitted: { [index: string]: SimpleProductApiOut | null } = {};
	importProductsNameCount: number | null = null;
	productMatchedToImport: SimpleProductApiOut[] = [];

	@AutoWired(CategoriesRepository) accessor categoriesRepository!: CategoriesRepository;
	@AutoWired(AppState) accessor appState!: AppState;

	mounted() {
		setTimeout(() => this.opened = true, 0);
	}

	close() {
		this.opened = false;
		setTimeout(() => this.$emit('close'), 300);
	}

	async importProducts(event?: Event, dryRun: boolean = false) {
		if (event && event.target) {
			this.productMatchedToImport = [];
			this.importProductsNameSplitted = {};
			this.importProductsNameCount = null;

			let importedProductsName = (<HTMLInputElement>event.target).value;
			if (importedProductsName === '') return;
			let productsName: string[] = importedProductsName.split('\n');
			this.importProductsNameCount = productsName.length;
			for (let productName of productsName) {
				this.importProductsNameSplitted[productName.trim().toLowerCase()] = null;
			}
		}

		for (const [key, value] of Object.entries(this.importProductsNameSplitted)) {
			for(const product of this.products) {
				if (product.name.trim().toLowerCase() === key) {
					if (dryRun) {
						this.importProductsNameSplitted[key] = product;
						this.productMatchedToImport.push(product);
					} else {
						const index = this.category.productIds.indexOf(product.id);
						if(index === -1) this.category.productIds.push(product.id);
					}
					break;
				}
			}
		}

		if(!dryRun) {
			this.importing = true;

			const categoryApiIn = new SimpleProductCategoryApiIn(this.category);
			const response = await this.categoriesRepository.callContract('update', {establishmentUid: this.appState.requireUrlEstablishmentUid(), categoryUid: this.category.uid}, categoryApiIn);
			if(response.isSuccess()) {
				this.close();
			} else {
				const error = response.error();
				if(error && 'error' in error && error.error === 'invalid_data') {
					this.error = error.error_details;
				} else if(error && 'error' in error && error.error === 'unknown_product') {
					this.error = 'Produit inconnu';
				}else if(error && 'error' in error && error.error === 'unknown_parent') {
					this.error = 'Catégorie parent inconnu';
				} else {
					this.error = 'Erreur inconnue'
				}
			}

			this.importing = false;
		}

	}
}