.customer-info-form-component {
    display: flex;
    flex-direction: column;
    gap: 30px;
    background: white;
    padding: 30px 20px 50px 20px;
    position: relative;

    .establishment {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 10px;

        img {
            height: 64px;
            width: 64px;
            border-radius: 6px;
        }

        .name {
            font-size: 18px;
            font-weight: 500;
        }
    }

    .form {
        display: flex;
        flex-direction: column;
        gap: 30px;

        .title {
            font-size: 20px;
            font-weight: 500;
        }

        .input {
            display: flex;
            flex-direction: column;
            gap: 10px;

            label {
                font-size: 18px;
            }

            .description {
                color: #575757;
            }

            .info {
                color: #2c7ffc;
                cursor: pointer;
            }

            input {
                all: unset;
                font-size: 18px;
                padding: 10px 15px;

                border: 1px solid black;
                border-radius: 8px;
                width: 100%;
                box-sizing: border-box;
            }
        }

        .add-button {
            display: flex;
            align-items: center;
            justify-content: center;
            background: #2AB9D9;
            height: 50px;
            color: white;
            font-size: 16px;
            font-weight: bold;
            border-radius: 100px;
            cursor: pointer;

            &:hover {
                filter: brightness(0.96);
            }

            &.disabled {
                background: #F2F2F2;
                color: rgba(0, 0, 0, 0.47);
                font-weight: normal;
                pointer-events: none;
            }
        }
    }
}