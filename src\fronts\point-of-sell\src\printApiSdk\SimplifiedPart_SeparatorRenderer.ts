import Can<PERSON><PERSON><PERSON>er<PERSON><PERSON> from "./CanvasRendererSDK";
import {SimplifiedPart_TextRenderer} from "./SimplifiedPart_TextRenderer";
import {SimplifiedRenderPart_Common} from "./SimplifiedRenderPart_Common";
import {SimplifiedPrintingPart_Separator, SimplifiedPrintingPart_Text, SimplifiedPrintingPartRendererConfig} from "./SimplifiedPrintingPartData";

export class SimplifiedPart_SeparatorRenderer extends SimplifiedRenderPart_Common{
	private textRenderer : SimplifiedPart_TextRenderer;

	constructor(config : SimplifiedPrintingPartRendererConfig) {
		super(config);
		this.textRenderer = new SimplifiedPart_TextRenderer(config);
	}


	private convertToTextPart(_separatorPart : SimplifiedPrintingPart_Separator) : SimplifiedPrintingPart_Text{
		return {
			type: 'TEXT',
			text: '----------------------------------------',
			textAlign:'center'
		};
	}

	render(renderer: CanvasRendererSDK, y : number, baseLineHeight: number, separatorPart : SimplifiedPrintingPart_Separator) : number{
		return this.textRenderer.render(renderer, y, baseLineHeight, this.convertToTextPart(separatorPart));
	}

	estimateHeight(baseLineHeight: number, separatorPart : SimplifiedPrintingPart_Separator): number{
		return this.textRenderer.estimateHeight(baseLineHeight, this.convertToTextPart(separatorPart));
	}

}
