.mobile-order-list-component {
    display: flex;
    flex-direction: column;
    gap: 10px;
    padding: 20px 20px 160px 20px;

    .buttons {
        margin: 0;
    }

    .button-group {
        display: flex;

        button {
            all: unset;
            display: flex;
            align-items: center;
            gap: 10px;
            background: white;
            padding: 12px;

            &.selected {
                background: var(--primary-color);
                color: var(--primary-text-color);
            }

            &:first-child {
                border-radius: 8px 0 0 8px;
            }

            &:last-child {
                border-radius: 0 8px 8px 0;
            }
        }
    }

    .pages {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        gap: 5px;

        span {
            font-size: 16px;
            margin-right: 10px;
        }

        .button {
            i {
                font-size: 16px;
            }
        }

        .button:hover {
            background: var(--secondary-hover-color);
        }

        .button.no-pointer-event {
            pointer-events: none;
        }
    }

    .orders {
        display: flex;
        flex-direction: column;
        gap: 10px;
    }

    .empty {
        font-style: italic;
        text-align: center;
        margin-top: 20px;
    }
}

.right {
    flex-shrink: 0;
    background: white;
    position: absolute;
    inset: 0;
    width: auto;
    display: none;
    z-index: 550;

    &.opened {
        display: initial;
    }
}