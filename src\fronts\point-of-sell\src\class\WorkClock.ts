import {AutoWired, UuidUtils} from "@groupk/horizon2-core";
import {PosState} from "../model/PosState";
import {WorkClockRepository} from "../../../../shared/repositories/WorkClockRepository";
import {uuidScopeIot_deviceApp, uuidScopeProductWorkClock, WorkClockApiOut} from "@groupk/mastodon-core";
import {<PERSON>rrorHandler} from "./ErrorHandler";
import {LocalWorkClock} from "../model/LocalWorkClock";
import {randomUUID} from "@groupk/horizon2-front";
import {LocalWorkClockRepository} from "../repositories/LocalWorkClockRepository";

export class WorkClock {
	@AutoWired(PosState) accessor posState!: PosState;
	@AutoWired(WorkClockRepository) accessor workClockRepository!: WorkClockRepository;
	@AutoWired(LocalWorkClockRepository) accessor localWorkClockRepository!: LocalWorkClockRepository;
	@AutoWired(ErrorHandler) accessor errorHandler!: ErrorHandler;

	async clockInOut() {
		const isClockedIn = this.posState.isClockedIn();
		const localWorkClock = new LocalWorkClock({
			uid: UuidUtils.randomVisualScopedUUID(uuidScopeProductWorkClock, randomUUID()),
			datetime: new Date().toISOString(),
			clockIn: !isClockedIn,
			establishmentAccountUid: this.posState.currentEstablishmentAccountUid,
			iotDeviceUid: UuidUtils.scopedToVisual(this.posState.iotDevice.uid, uuidScopeIot_deviceApp)
		});

		this.updateLastWorkClocks(localWorkClock);
		await this.localWorkClockRepository.save(localWorkClock);
	}

	updateLastWorkClocks(workClock: WorkClockApiOut) {
		const existingIndex = this.posState.lastWorkClocks.findIndex((workClock) => workClock.establishmentAccountUid === workClock.establishmentAccountUid);
		if(existingIndex !== -1) {
			this.posState.lastWorkClocks.splice(existingIndex, 1, workClock);
		} else {
			this.posState.lastWorkClocks.push(workClock);
		}
	}
}