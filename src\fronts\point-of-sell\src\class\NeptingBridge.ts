import {
	AutoWired,
	Currency,
	EnumUtils,
	hydrate,
	Hz<PERSON><PERSON>r,
	NotNull,
	Promise<PERSON>reateSync,
	Uuid
} from '@groupk/horizon2-core';
import {SystemInfoNative} from '@groupk/native-bridge';
import {
	NeptingApp2AppExtendedResult,
	NeptingApp2AppLoginResponse,
	NeptingApp2AppLogoutResponse,
	NeptingApp2AppParserModel,
	NeptingApp2AppSoftwareDetails,
	NeptingApp2AppTicketMode,
	NeptingApp2AppTransactionResponse,
	PaymentMethodDataPhysicalNeptingApp2App,
	NeptingApp2AppFetchOfflineTrsResponse, PaymentMethodDataStatus, PaymentMethodSettingsPhysicalNeptingApp2App
} from '@groupk/mastodon-core';
import * as Sentry from '@sentry/browser';
import {App2AppNativeRequest, App2AppNativeResponse} from '@groupk/horizon2-core';
import {LocalStorageKeys} from "./LocalStorageKeys";
import {MainConfig} from "../../../../shared/MainConfig";

export const NeptingApp2AppExtendedResultTranslated: {[k in NeptingApp2AppExtendedResult] : string} = {
	[NeptingApp2AppExtendedResult.Sys_Unknown]: 'Etat non traduit (Sys_Unknown)',
	[NeptingApp2AppExtendedResult.Sys_Nothing]: 'Etat non traduit (Sys_Nothing)',
	[NeptingApp2AppExtendedResult.Sys_Accepted]: 'Etat non traduit (Sys_Accepted)',
	[NeptingApp2AppExtendedResult.Sys_Refused]: 'Etat non traduit (Sys_Refused)',
	[NeptingApp2AppExtendedResult.Sys_Error]: 'Etat non traduit (Sys_Error)',
	[NeptingApp2AppExtendedResult.Sys_Fatal]: 'Etat non traduit (Sys_Fatal)',
	[NeptingApp2AppExtendedResult.Sys_MandatoryParameterMissing]: 'Erreur implémentation (Sys_MandatoryParameterMissing)',
	[NeptingApp2AppExtendedResult.Sys_BadRequest]: 'Erreur implémentation (Sys_BadRequest)',
	[NeptingApp2AppExtendedResult.Sys_ChangesDone]: 'Etat non traduit (Sys_ChangesDone)',
	[NeptingApp2AppExtendedResult.Sys_UpdateNeeded]: 'Mise à jour requise',
	[NeptingApp2AppExtendedResult.AcceptedOffline]: 'Accepté sans internet',
	[NeptingApp2AppExtendedResult.AcceptedOnline]: 'Accepté avec internet',
	[NeptingApp2AppExtendedResult.CurrencyNotManaged]: 'Devise non gérée',
	[NeptingApp2AppExtendedResult.ContextError]: 'Erreur de contexte',
	[NeptingApp2AppExtendedResult.TransactionNotAllowed]: 'Transaction non permise',
	[NeptingApp2AppExtendedResult.PanLengthIncorrect]: 'Mauvais PAN',
	[NeptingApp2AppExtendedResult.LuhnKeyIncorrect]: 'Clef de Luhn incorrecte',
	[NeptingApp2AppExtendedResult.EndOfValidityError]: 'Carte expirée',
	[NeptingApp2AppExtendedResult.BinForbidden]: 'BIN interdit',
	[NeptingApp2AppExtendedResult.BinRefused]: 'BIN refusé',
	[NeptingApp2AppExtendedResult.CardForbidden]: 'Carte interdite',
	[NeptingApp2AppExtendedResult.CardRefused]: 'Carte refusé',
	[NeptingApp2AppExtendedResult.AuthorizationIncident]: 'Indicent d\'autorisation',
	[NeptingApp2AppExtendedResult.AuthorizationRefused]: 'Autorisation refusée',
	[NeptingApp2AppExtendedResult.AuthorizationForbidden]: 'Carte refusée après autorisation',
	[NeptingApp2AppExtendedResult.AmountTooHigh]: 'Montant trop élevé',
	[NeptingApp2AppExtendedResult.AmountTooLow]: 'Montant trop faible',
	[NeptingApp2AppExtendedResult.TrsToReverseNotFound]: 'Transaction introuvable',
	[NeptingApp2AppExtendedResult.TrsAlreadyVoided]: 'Transaction déjà annulée',
	[NeptingApp2AppExtendedResult.EffectiveDateNotReached]: 'Date de fin de validité non atteinte',
	[NeptingApp2AppExtendedResult.UserAbort]: 'Abandon utilisateur',
	[NeptingApp2AppExtendedResult.MerchantAbort]: 'Abandon marchand',
	[NeptingApp2AppExtendedResult.CvvInvalid]: 'CVV Invalide',
	[NeptingApp2AppExtendedResult.CardRemoved]: 'Carte retirée',
	[NeptingApp2AppExtendedResult.OfflineTrs]: 'Transaction dégradée',
	[NeptingApp2AppExtendedResult.ForeignCard]: 'Carte étrangère',
	[NeptingApp2AppExtendedResult.AIDUnknown]: 'AID inconnu',
	[NeptingApp2AppExtendedResult.DueDateAfterEndOfValidity]: 'Etat non traduit (DueDateAfterEndOfValidity)',
	[NeptingApp2AppExtendedResult.BINUnknown]: 'BIN Inconnu',
	[NeptingApp2AppExtendedResult.MuteCard]: 'Carte muette',
	[NeptingApp2AppExtendedResult.ThreeDSecureAuthentificationNeeded]: 'Etat non traduit (ThreeDSecureAuthentificationNeeded)',
	[NeptingApp2AppExtendedResult.NotHandledCard]: 'Carte non gérée',
	[NeptingApp2AppExtendedResult.DuplicatedTrs]: 'Transaction dupliquée',
	[NeptingApp2AppExtendedResult.PrintingIssue]: 'Erreur d\'impression',
	[NeptingApp2AppExtendedResult.LunchCardNotAllowed]: 'Carte TRD non autorisée',
	[NeptingApp2AppExtendedResult.INCORRECT_PASSWORD]: 'Identifiants incorrects',
	[NeptingApp2AppExtendedResult.LOCKED_ACCOUNT]: 'Compte bloqué',
	[NeptingApp2AppExtendedResult.SESSION_TIMEOUT]: 'Session expirée',
	[NeptingApp2AppExtendedResult.AUTHENTICATION_ERROR]: 'Erreur d\'autentification',
	[NeptingApp2AppExtendedResult.UNKNOWN_ACCOUNT]: 'Compte inconnu',
	[NeptingApp2AppExtendedResult.NO_ACCESS]: 'Accès non autorisé',
	[NeptingApp2AppExtendedResult.EXPIRED_PASSWORD]: 'Mot de passe expiré',
	[NeptingApp2AppExtendedResult.UNKNOWN_TERMINAL]: 'Terminal inconnu',
	[NeptingApp2AppExtendedResult.UNKNOWN_TERMINAL_MODEL]: 'Modèle de terminal inconnu',
	[NeptingApp2AppExtendedResult.INACTIVE_TERMINAL]: 'Terminal inactif',
	[NeptingApp2AppExtendedResult.STORE_UNKNOWN]: 'Ilot inconnu',
	[NeptingApp2AppExtendedResult.STORE_EMPTY]: 'Ilot vide',
	[NeptingApp2AppExtendedResult.UnknownKey]: 'Clef de crypto inconnue',
	[NeptingApp2AppExtendedResult.MACError]: 'Erreur d\'intégrité entre le terminal et le serveur',
	[NeptingApp2AppExtendedResult.SignError]: 'erreur de signature (ePay)(SoftPOS)',
	[NeptingApp2AppExtendedResult.ALREADY_EXIST]: 'element déjà existant',
	[NeptingApp2AppExtendedResult.Sys_Forbidden]: 'opération interdite',
	[NeptingApp2AppExtendedResult.Sys_Inactive]: 'activation contrat échoué',
	[NeptingApp2AppExtendedResult.Sys_Timeout]: 'temps écoulé (librairie)',
	[NeptingApp2AppExtendedResult.Sys_ConnectionError]: 'erreur de connexion avec terminal',
	[NeptingApp2AppExtendedResult.Sys_CommunicationError]: 'erreur de connexion entre le terminal et la caisse détecté par la librairie',
	[NeptingApp2AppExtendedResult.Sys_Busy]: 'terminal occupé',
	[NeptingApp2AppExtendedResult.Sys_NepsaConnectionError]: 'erreur de connexion au serveur',
	[NeptingApp2AppExtendedResult.Reference_NotFound]: 'référence non trouvée',
	[NeptingApp2AppExtendedResult.Reference_NotClosed]: 'référence non cloturée',
	[NeptingApp2AppExtendedResult.Sys_NepsaTimeoutCompletion]: 'temps ecoulé sur la finalisation',
	[NeptingApp2AppExtendedResult.OfflineRequirementsNotMet]: 'conditions dégradées non permises',
	[NeptingApp2AppExtendedResult.InvalidMerchantTransactionID]: 'identifiant de transaction invalide',
	[NeptingApp2AppExtendedResult.InvalidMerchantPrivateData]: 'private data invalide',
	[NeptingApp2AppExtendedResult.CompletionNotReceived]: 'dernier message serveur non reçu par le terminal',
	[NeptingApp2AppExtendedResult.Sys_NepsaTimeout]: 'dialogue avec serveur écoulé',
	[NeptingApp2AppExtendedResult.Sys_POSCommunicationError]: 'erreur de dialogue entre le terminal et la caisse détecté par le terminal',
	[NeptingApp2AppExtendedResult.Sys_PeriphError]: 'erreur de dialogue entre le terminal et un périphérique',
	[NeptingApp2AppExtendedResult.POSMATE_CONNECTION_ERROR]: 'erreur de connection avec le terminal',
	[NeptingApp2AppExtendedResult.NEPSA_CONNECTION_ERROR]: 'erreur de connection avec le serveur',
	[NeptingApp2AppExtendedResult.NEPSA_TIMEOUT_ON_COMPLETION]: 'temps écoulé sur le risque de completion entre la caisse et le serveur (SDK)',
	[NeptingApp2AppExtendedResult.BLUETOOTH_DISABLED]: 'Bluetooth incatif (SDK)',
	[NeptingApp2AppExtendedResult.SPM20_MIGRATION]: 'migration depuis client FAPI vers Nepting déclenché (SDK)',
}

const hiddenExtendedResults : NeptingApp2AppExtendedResult[] = [
	NeptingApp2AppExtendedResult.Sys_Refused,
	NeptingApp2AppExtendedResult.Sys_Accepted,
	NeptingApp2AppExtendedResult.Sys_Error,
	NeptingApp2AppExtendedResult.AcceptedOffline,
	NeptingApp2AppExtendedResult.AcceptedOnline,
];

export interface NeptingBridgeListener{
	error(err : any, extra: {request: App2AppNativeRequest|undefined}) : void;
}

export class NeptingBridgeException extends HzError{
	request: App2AppNativeRequest|undefined;
	response?: App2AppNativeResponse|undefined;

	constructor(message: string, request: App2AppNativeRequest|undefined, response: App2AppNativeResponse|undefined = undefined, cause: unknown|undefined = undefined) {
		super(message, cause);
		this.request = request;
		this.response = response;
	}
}

export interface NeptingBridgeLoginStatus{
	merchantLabel?: string|undefined;
}

interface NeptingBridgeLoginRequest{
	merchantCode: string | undefined;
	softwareDetails: NeptingApp2AppSoftwareDetails;
	webserviceUrl: string | undefined;
}
interface NeptingBridgeLoginRequestWithDate extends NeptingBridgeLoginRequest{
	datetime: string;
}

export interface NeptingOfflineState{
	offline: boolean;
	stuckTransactions: number;
	lastToggleDate: string;
}

export type NeptingManualTransactionState = null|'force_online'|'force_offline';

export class NeptingBridge {
	@AutoWired(SystemInfoNative) private accessor systemInfoNative !: SystemInfoNative;
	@AutoWired(MainConfig) private accessor mainConfig !: MainConfig;

	private neptingInstalled : boolean = false;
	private nativeVersionName : string = '';
	private loginStatus: NeptingBridgeLoginStatus|null = null;
	private merchantCode: string|null = null;
	private webserviceUrl: string|null = null;
	private currentRequest: {
		// timeoutNative: ReturnType<typeof setTimeout>;
		reject: (reason: any)=>void;
		request: App2AppNativeRequest,
	}|null = null;
	private offlineStateCache: NeptingOfflineState = {offline: false, stuckTransactions: 0, lastToggleDate: new Date().toISOString()};
	private manualTransactionState: NeptingManualTransactionState = null;

	constructor() {
		const offlineStateCache = this.getOfflineStateFromLocalStorage();
		if(offlineStateCache) this.offlineStateCache = offlineStateCache;
		this.manualTransactionState = window.localStorage.getItem(LocalStorageKeys.CACHE_NEPTING_MANUAL_TRANSACTION_STATE) as NeptingManualTransactionState;
	}

	private getLastConnectionRequest(): NeptingBridgeLoginRequestWithDate|undefined{
		const stored = window.localStorage.getItem(LocalStorageKeys.CACHE_NEPTING_LAST_LOGIN_REQUEST);
		if(stored){
			return JSON.parse(stored);
		}
		return undefined;
	}
	private storeLastConnectionRequest(login: NeptingBridgeLoginRequest|undefined){
		if(login === undefined){
			window.localStorage.removeItem(LocalStorageKeys.CACHE_NEPTING_LAST_LOGIN_REQUEST);
			return;
		}

		const withDate : NeptingBridgeLoginRequestWithDate = {
			...login,
			datetime: new Date().toISOString(),
		}

		window.localStorage.setItem(LocalStorageKeys.CACHE_NEPTING_LAST_LOGIN_REQUEST, JSON.stringify(withDate));
	}
	private getOfflineStateFromLocalStorage(): NeptingOfflineState|undefined{
		const stored = window.localStorage.getItem(LocalStorageKeys.CACHE_NEPTING_OFFLINE_STATE);
		if(stored){
			return JSON.parse(stored) ?? undefined;
		}
		return undefined;
	}
	private storeOfflineStateInLocalStorage(){
		window.localStorage.setItem(LocalStorageKeys.CACHE_NEPTING_OFFLINE_STATE, JSON.stringify(this.offlineStateCache));
	}

	isAvailable(){
		return this.neptingInstalled;
	}
	getLoginStatus(){
		return this.loginStatus;
	}
	getMerchantCode(){
		return this.merchantCode;
	}
	setMerchantCode(merchantCode: string|null, webserviceUrl: string|null){
		this.merchantCode = merchantCode;
		this.webserviceUrl = webserviceUrl;
	}

	setManualTransactionState(state: NeptingManualTransactionState){
		this.manualTransactionState = state;
		if(state) window.localStorage.setItem(LocalStorageKeys.CACHE_NEPTING_MANUAL_TRANSACTION_STATE, state);
		else window.localStorage.removeItem(LocalStorageKeys.CACHE_NEPTING_MANUAL_TRANSACTION_STATE);
	}
	getManualTransactionState(){
		return this.manualTransactionState;
	}

	private appResultListener : ((result: App2AppNativeResponse)=>void)|undefined = undefined;

	async init(){
		const installedApps = await this.systemInfoNative.getInstalledApps();
		for(const app of installedApps){
			if(app.package.startsWith('com.nepting')){
				this.neptingInstalled = true;
				break;
			}
		}

		if(this.neptingInstalled) {
			const appInfo = await this.systemInfoNative.getAppInfo();
			this.nativeVersionName = appInfo.versionName;

			this.systemInfoNative.on('appResult', (result)=>{
				console.debug('Nepting>', result);
				if(!NeptingApp2AppParserModel.isResponseFromNepting(result.package)) return;

				if(this.appResultListener) this.appResultListener(result);
				else{
					Sentry.captureException(new NeptingBridgeException('Nepting result without listener', undefined, result));
				}
			});
		}
	}

	getNeptingMerchantCodeFromConfiguration(){
		const paymentMethods: any = null;
		// const paymentMethods = this.mainConfig.getDeviceConfiguration().paymentMethodConfigs;
		let merchantCodes: {merchantCode: string, webserviceUrl: string|undefined}[] = [];

		if(paymentMethods){
			for(const paymentMethod of paymentMethods){
				if(paymentMethod.settings instanceof PaymentMethodSettingsPhysicalNeptingApp2App){
					if(paymentMethod.settings.merchantCode)
						merchantCodes.push({merchantCode: paymentMethod.settings.merchantCode, webserviceUrl: paymentMethod.settings.webserviceUrl});
				}
			}
		}

		return merchantCodes;
	}

	forceCancelCurrent(){
		if(this.currentRequest){
			alert('CANCEL CURRENT');
		}
	}

	private requestToNepting(request: App2AppNativeRequest, listener: NeptingBridgeListener|null) : Promise<App2AppNativeResponse>{
		if(this.currentRequest !== null) return Promise.reject(`nepting_request_already_in_progress`);
		if(!this.neptingInstalled) return Promise.reject(`nepting_not_installed`);

		console.debug('NEPTING>Requesting', request);
		const {promise, resolve, reject} = PromiseCreateSync<App2AppNativeResponse>();

		// CLEAR TIMEOUT
		const safeResolve = (params : App2AppNativeResponse)=>{
			this.currentRequest = null;
			resolve(params);
		}
		const safeReject = (error : any)=>{
			if(listener) listener.error(error, {request});
			this.currentRequest = null;
			reject(error);
		}

		const currentRequest : NotNull<NeptingBridge['currentRequest']> = {
			reject: safeReject,
			request,
		}

		this.appResultListener = (result)=>{
			if(result.resultStatus === 'SUCCESS' || result.resultStatus === 'CANCELED')
				safeResolve(result);
			else
				safeReject(`unknown_result(${result.resultStatus})`);
		};

		this.currentRequest = currentRequest;

		this.systemInfoNative.launchApp(request.appPackage, request.extra).catch(function () {
			safeReject(`unable_start`);
		});

		return promise;
	}

	private getSoftwareDetails(){
		return {
			name: 'POINT_OF_SALE',
			version: this.nativeVersionName,
		};
	}

	private setOfflineState(isOffline: boolean, pendingTransactions: number){
		const previousState = this.offlineStateCache.offline;

		console.debug('nepting>OFFLINE?', previousState, isOffline, pendingTransactions);

		this.offlineStateCache = {
			offline: isOffline,
			stuckTransactions: pendingTransactions,
			lastToggleDate: previousState === isOffline ? this.offlineStateCache.lastToggleDate : new Date().toISOString(),
		}
		this.storeOfflineStateInLocalStorage();
	}
	getOfflineState(): Readonly<NeptingOfflineState>{
		return this.offlineStateCache;
	}

	private updateOfflineStateFromResponse(neptingResponse: NeptingApp2AppTransactionResponse|NeptingApp2AppLoginResponse|NeptingApp2AppFetchOfflineTrsResponse){
		let offline = false;
		if('EXTENDED_RESULT_TEXT' in neptingResponse){
			offline = NeptingApp2AppParserModel.parseExtendedResultText(neptingResponse.EXTENDED_RESULT_TEXT)?.
			find(extendedResult=>extendedResult === NeptingApp2AppExtendedResult.OfflineTrs) !== undefined;
		}else{
			offline = neptingResponse.GLOBAL_STATUS !== PaymentMethodDataStatus.SUCCESS;
		}

		this.setOfflineState(offline, neptingResponse.OFFLINE_TRS_COUNT ? parseInt(neptingResponse.OFFLINE_TRS_COUNT, 10) : 0);
	}

	async createPaymentRequest(amount: number,
							   requestUid: Uuid,
							   listener: NeptingBridgeListener,
							   context?: {checkPrevious?: boolean},
	) : Promise<{
		raw: NeptingApp2AppTransactionResponse,
		unified: PaymentMethodDataPhysicalNeptingApp2App,
	}>{
		const manualTransactionState = this.getManualTransactionState();
		const privateData : string = requestUid;

		let existingPayment : {request: App2AppNativeRequest, neptingResponse: NeptingApp2AppTransactionResponse, rawResult: App2AppNativeResponse}|null = null;
		if(context?.checkPrevious){
			const previousTransactionRequest = NeptingApp2AppParserModel.requestLastTransaction();
			const lastTransactionResult = await this.requestToNepting(previousTransactionRequest, listener)
			console.log('LAST TRANSACTION', lastTransactionResult);
			let lastTransaction : NeptingApp2AppTransactionResponse|null = null;
			try {
				lastTransaction = hydrate(NeptingApp2AppTransactionResponse, lastTransactionResult.extras);
				this.updateOfflineStateFromResponse(lastTransaction);
			}catch (e){}

			if(lastTransaction && lastTransaction.MERCHANT_PRIVATE_DATA === privateData){ // payment already done, intercept
				existingPayment = {request: previousTransactionRequest, neptingResponse: lastTransaction, rawResult: lastTransactionResult};
			}
		}

		let paymentPromise: Promise<{request: App2AppNativeRequest, neptingResponse: NeptingApp2AppTransactionResponse, rawResult: App2AppNativeResponse}>;
		if(existingPayment){
			paymentPromise = Promise.resolve(existingPayment);
		}else{
			const paymentRequest = NeptingApp2AppParserModel.requestTransaction({
				amount: Math.abs(amount),
				mode: amount < 0 ? 'debit' : 'credit',
				currency: Currency.EUR,
				forceOffline: manualTransactionState === 'force_offline' ? true : undefined,
				forceOnline: manualTransactionState === 'force_online' ? true : undefined,
				transactionId: requestUid.replaceAll('-', '').slice(-12),
				privateData: privateData,
				ticketMode: NeptingApp2AppTicketMode.RETURN_CUSTOMER_ONLY_NOT_PRINT,
				softwareDetails: this.getSoftwareDetails(),
			});

			paymentPromise = this.requestToNepting(paymentRequest, listener).then((rawResult)=>{
				let neptingResponse : NeptingApp2AppTransactionResponse;
				try {
					neptingResponse = hydrate(NeptingApp2AppTransactionResponse, rawResult.extras);
				}catch (e){
					throw new NeptingBridgeException('ERROR FROM NEPTING NATIVE', paymentRequest, rawResult, e);
				}

				this.updateOfflineStateFromResponse(neptingResponse);
				return {request: paymentRequest, rawResult, neptingResponse};
			});
		}

		return paymentPromise.then(({request, neptingResponse, rawResult})=>{
			if(
				neptingResponse.GLOBAL_STATUS === PaymentMethodDataStatus.SUCCESS
				|| neptingResponse.GLOBAL_STATUS === PaymentMethodDataStatus.ABORTED
				|| neptingResponse.GLOBAL_STATUS === PaymentMethodDataStatus.REFUSED
				|| neptingResponse.GLOBAL_STATUS === PaymentMethodDataStatus.ERROR
			){
				const response = NeptingApp2AppParserModel.parseTransactionResponse(neptingResponse);
				console.debug('Nepting>', response)
				return {
					raw: neptingResponse,
					unified: response,
				};
			}else{
				throw new NeptingBridgeException('ERROR FROM NEPTING NATIVE', request, rawResult);
			}
		});
	}

	shouldRelogin(merchantCode: string|undefined, webserviceUrl: string|undefined) : boolean{
		let lastLoginRequest = this.getLastConnectionRequest();
		if(lastLoginRequest !== undefined && new Date().getTime() - new Date(lastLoginRequest.datetime).getTime() >= 23*60*60*1000){
			lastLoginRequest = undefined;
		}
		if(lastLoginRequest === undefined) return true;

		const newLoginRequest = this.buildLoginRequest({merchantCode, webserviceUrl});
		return newLoginRequest.merchantCode !== lastLoginRequest.merchantCode
			|| newLoginRequest.webserviceUrl !== lastLoginRequest.webserviceUrl
			|| newLoginRequest.softwareDetails.version !== lastLoginRequest.softwareDetails.version
			|| newLoginRequest.softwareDetails.name !== lastLoginRequest.softwareDetails.name
			;
	}
	private buildLoginRequest(overrides : {merchantCode?: string|undefined, webserviceUrl?: string|undefined} = {}) : NeptingBridgeLoginRequest{
		const potentialMerchantCodes = this.getNeptingMerchantCodeFromConfiguration();
		if(potentialMerchantCodes.length >= 2){
			alert(`Conflit code marchant nepting (${potentialMerchantCodes.length})`);
		}else {
			const potentialMerchantCode = potentialMerchantCodes[0] ?? null;
			if (potentialMerchantCode) {

			}
		}
		const potentialMerchantCode = potentialMerchantCodes[0]!;

		return {
			merchantCode: overrides.merchantCode ?? this.merchantCode ?? potentialMerchantCode.merchantCode ?? undefined,
			webserviceUrl: overrides.webserviceUrl ?? this.webserviceUrl ?? potentialMerchantCode.webserviceUrl ?? undefined,
			softwareDetails: this.getSoftwareDetails()
		};
	}
	login() : Promise<NeptingApp2AppLoginResponse>{
		const loginRequest: NeptingBridgeLoginRequest = this.buildLoginRequest();
		const loginRequestNative = NeptingApp2AppParserModel.requestLogin(loginRequest);
		return this.requestToNepting(loginRequestNative, null).then((result)=>{
			try {
				const r = hydrate(NeptingApp2AppLoginResponse, result.extras);
				this.loginStatus = {
					merchantLabel: r.MERCHANT_LABEL,
				};
				this.merchantCode = loginRequest.merchantCode ?? this.merchantCode;
				this.webserviceUrl = loginRequest.webserviceUrl ?? this.webserviceUrl;
				this.updateOfflineStateFromResponse(r);
				this.storeLastConnectionRequest(loginRequest);
				return r;
			}catch (e){
				throw new NeptingBridgeException('ERROR FROM NEPTING NATIVE', loginRequestNative, result, e);
			}
		});
	}
	logout(){
		const loginRequest = NeptingApp2AppParserModel.requestLogout();
		return this.requestToNepting(loginRequest, null).then((result)=>{
			this.loginStatus = null;
			try {
				this.storeLastConnectionRequest(undefined);
				return hydrate(NeptingApp2AppLogoutResponse, result.extras);
			}catch (e){
				throw new NeptingBridgeException('ERROR FROM NEPTING NATIVE', loginRequest, result, e);
			}
		});
	}
	fetchOfflineTrs(){
		const fetchOfflineTrsRequest = NeptingApp2AppParserModel.requestFetchOfflineTrs({timeoutMs: 30*1000});
		return this.requestToNepting(fetchOfflineTrsRequest, null).then((result)=>{
			this.loginStatus = null;
			try {
				const r = hydrate(NeptingApp2AppFetchOfflineTrsResponse, result.extras);
				this.updateOfflineStateFromResponse(r);
				return r;
			}catch (e){
				throw new NeptingBridgeException('ERROR FROM NEPTING NATIVE', fetchOfflineTrsRequest, result, e);
			}
		});
	}

	static computeErrorFromExtendedResult(extendedResult: number[]|undefined) : string|undefined{
		if(extendedResult === undefined) return undefined;

		return extendedResult?.filter(e=>!hiddenExtendedResults.includes(e)).map(e=>{
			if(EnumUtils.isValue(NeptingApp2AppExtendedResult, e))
				return NeptingApp2AppExtendedResultTranslated[e] ?? '';
			else{
				return `Valeur inconnue (${e})`
			}
		}).join(', ') ?? '';
	}

}