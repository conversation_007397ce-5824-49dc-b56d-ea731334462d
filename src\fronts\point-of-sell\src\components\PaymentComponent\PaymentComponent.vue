<script lang="ts" src="./PaymentComponent.ts">
</script>

<style lang="sass" scoped>
@import './PaymentComponent.scss'
</style>

<template>
    <div class="payment-component">
        <div v-if="!processor || !currentPayment">
            Initialisation...
        </div>

        <nepting-processor
            v-else-if="processor instanceof NeptingPaymentProcessor" ref="cardPaymentModal"
            @close="close()"
        ></nepting-processor>

        <div class="modal-dimmer" v-else>
            <div class="modal" @click.stop>

                <template v-if="error === 'CONNECTION'">
                    <i class="fa-regular fa-signal-stream-slash state-icon"></i>

                    <div class="title">
                        <h3> Connexion au terminal de paiement impossible </h3>
                    </div>

                    <button class="big black button" @click="close()">
                        Fermer
                    </button>
                </template>

                <template v-else>
                    <template v-if="currentPayment.status === 'PENDING'">
                        <div class="loading">
                            <div class="loader"></div>
                        </div>

                        <div class="title">
                            <h3> Paiement en cours </h3>
                            <span> Le paiement a bien été initialisé sur le terminal de paiement, en attente du règlement par le client. </span>
                        </div>

                        <span class="live-payment" v-if="processor.config.displayLivePaidAmount">
                            {{ $filters.Money(paid - refund) }} sur {{ $filters.Money(currentPayment.amount) }}
                        </span>
                        <button v-if="processor.config.cancelable" class="big black button" @click="cancel()">
                            Annuler le paiement
                            <span v-if="processor.config.displayLivePaidAmount" class="small"> Et rendre {{ $filters.Money(paid - refund) }} </span>
                        </button>
                        <button v-else class="big black button" @click="cancel()">
                            Fermer
                        </button>
                    </template>
                    <template v-else-if="currentPayment.status === 'SUCCESS'">
                        <i class="fa-regular fa-circle-check state-icon"></i>

                        <div class="title">
                            <h3> Paiement réussi </h3>
                            <span> Le paiement a été encaissé avec succès. </span>
                        </div>

                        <button class="big black button" @click="close()">
                            Fermer
                        </button>
                    </template>
                    <template v-else>
                        <i class="fa-regular fa-circle-xmark state-icon"></i>

                        <div class="title">
                            <h3> Paiement échoué </h3>
                            <span> Une erreur est survenue et le paiement a échoué </span>
                        </div>

                        <button class="big black button" @click="close()">
                            Fermer
                        </button>
                    </template>
                </template>
            </div>
        </div>
    </div>
</template>