#login-page {
    height: 100%;
    width: 100%;
    display: flex;

    .step {
        position: absolute;
        padding: 80px;
        box-sizing: border-box;
        max-height: 100%;
        overflow: auto;

        @media (max-width: 900px) {
            padding: 80px 20px;
            width: 100%;
            max-width: 100%;
        }
    }

    .step1 {
        transform: translateX(0);
        opacity: 1;
        transition: transform .5s cubic-bezier(0.22, 1, 0.36, 1), opacity .5s cubic-bezier(0.22, 1, 0.36, 1);
        z-index: 200;
        &.hidden {
            transform: translateX(-100%);
            opacity: 0;
        }
    }

    .step2 {
        transform: translateX(50%);
        opacity: 0;
        transition: transform .5s cubic-bezier(0.22, 1, 0.36, 1), opacity .5s cubic-bezier(0.22, 1, 0.36, 1);
        z-index: 199;

        &.show {
            transform: translateX(0);
            opacity: 1;
        }
    }

    >.left {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: stretch;
        width: 100%;
        max-width: 600px;

        .loading-container {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .title-group {
            display: flex;
            align-items: flex-start;
            gap: 4px;

            .logo {
                height: 60px;
                margin-bottom: 20px;
            }


            h3 {
                font-size: 20px;
            }
            span  {
                font-size: 15px;
            }
        }

        input {
            border: solid 1px #A1A1A1;
        }

        .form {
            width: 100%;
        }

        .or-separator {
            display: flex;
            justify-content: center;
            margin: 10px;
            font-size: 16px;
            font-weight: bold;
        }

        .connected-account {
            display: flex;
            padding: 15px 20px;
            align-items: center;
            justify-content: space-between;
            gap: 15px;
            border-radius: 8px;
            border: 1px solid #000;
            cursor: pointer;
            user-select: none;

            &:hover {
                background-color: #f5f5f5;
            }
            
            .user {
                display: flex;
                align-items: center;
                gap: 15px;

                .profile-picture {
                    width: 44px;
                    height: 44px;
                    background-color: #D9D9D9;
                    flex-shrink: 0;
                    border-radius: 50%;
                }

                .info {
                    display: flex;
                    flex-direction: column;

                    .continue-as {
                        font-family: Montserrat, sans-serif;
                        font-size: 15px;
                    }

                    .name {
                        font-family: Montserrat, sans-serif;
                        font-size: 15px;
                        font-weight: 700;
                        text-transform: capitalize;
                    }
                }
            }

            &.email {
                .name {
                    text-transform: none !important;
                    word-break: break-all;
                }
            }

            i {
                font-size: 18px;
            }
        }

        .select-establishment {
            width: 100%;
            max-height: 100%;
            overflow: auto;

            .welcome-firstname {
                text-transform: capitalize;
                font-size: 20px;
            }


            .establishments {
                display: flex;
                flex-direction: column;
                align-items: stretch;
                gap: 15px;
                margin-top: 30px;

                .establishment {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    padding: 20px;
                    border-radius: 8px;
                    border: 2px solid black;
                    cursor: pointer;
                    user-select: none;

                    &:hover {
                        background-color: #f5f5f5;
                    }

                    .info {
                        display: flex;
                        flex-direction: column;
                        gap: 4px;

                        .name {
                            font-family: Montserrat, sans-serif;
                            font-size: 15px;
                            font-weight: 700;
                            word-break: break-all;
                        }

                        .platforms-text {
                            font-family: Montserrat, sans-serif;
                            font-size: 15px;
                        }
                    }

                    .right {
                        display: flex;
                        align-items: center;
                        gap: 20px;

                        .platforms-icon {
                            display: flex;

                            .platform-icon {
                                height: 30px;
                                width: 30px;
                                border-radius: 50%;
                                margin-left: -15px;
                                background-position: center;
                                background-size: cover;
                                box-shadow: 0 4px 4px rgba(0, 0, 0, 0.20);
                            }
                        }

                        i {
                            font-size: 18px;
                        }
                    }
                }

                .new-establishment {
                    all: unset;
                    color: black;
                    font-family: Montserrat, sans-serif;
                    font-size: 15px;
                    text-decoration-line: underline;
                    padding: 10px 15px;
                    border-radius: 8px;
                    text-align: center;
                    cursor: pointer;

                    &:hover {
                        background-color: #ececec;
                        text-decoration-line: none;
                    }
                }
            }
        }

        .signup-link {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 5px;
            margin-top: 30px;

            .title {
                font-size: 15px;
            }

            .link {
                all: unset;
                cursor: pointer;
                font-size: 16px;
                font-weight: 600;
                text-decoration-line: underline;
                padding: 10px 20px;
                border-radius: 8px;

                &:hover {
                    background: var(--secondary-hover-color);
                }
            }
        }
    }

    >.right {
        flex-grow: 2;
        background-position: left;
        background-size: cover;
        z-index: 300;

        @media (max-width: 900px) {
            display: none;
        }
    }

    .littl-preview {
        flex-grow: 2;
        background: #F0FAF4;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 80px;

        @media (max-width: 900px) {
            display: none;
        }

        .littl-container {
            max-width: min(100%, 600px);
            max-height: 100%;

            .head {
                display: flex;
                flex-direction: column;
                gap: 4px;

                .title {
                    font-size: 22px;
                    font-weight: 700;
                }

                .subtitle {
                    font-size: 18px;
                    font-weight: 500;
                }
            }
        }

        video {
            max-width: 100%;
            max-height: 100%;
        }
    }

    .forgot-password {
        all: unset;
        font-size: 15px;
        text-decoration: underline;
        cursor: pointer;
        text-align: right;
    }
}